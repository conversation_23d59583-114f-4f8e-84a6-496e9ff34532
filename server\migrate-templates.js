import { query } from './config/database.js';

async function migrateTemplates() {
    console.log('🔄 [Migration] Création de la table assignment_templates...');

    try {
        // 1. Créer la table assignment_templates
        console.log('📋 [Migration] Création de la table...');
        await query(`
            CREATE TABLE IF NOT EXISTS assignment_templates (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
                post_id UUID NOT NULL REFERENCES standard_posts(id) ON DELETE CASCADE,
                type VARCHAR(20) NOT NULL CHECK (type IN ('event', 'repeat', 'schedule')),
                day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
                start_time TIME NOT NULL,
                end_time TIME NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NULL,
                is_active BO<PERSON>EAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

                CONSTRAINT valid_time_range CHECK (end_time > start_time),
                CONSTRAINT valid_date_range CHECK (end_date IS NULL OR end_date >= start_date),
                CONSTRAINT valid_type_end_date CHECK (
                    (type = 'repeat' AND end_date IS NULL) OR
                    (type = 'schedule' AND end_date IS NOT NULL) OR
                    (type = 'event')
                )
            )
        `);

        // 2. Créer les index
        console.log('🔍 [Migration] Création des index...');
        await query(`
            CREATE INDEX IF NOT EXISTS idx_assignment_templates_employee ON assignment_templates(employee_id);
            CREATE INDEX IF NOT EXISTS idx_assignment_templates_post ON assignment_templates(post_id);
            CREATE INDEX IF NOT EXISTS idx_assignment_templates_type ON assignment_templates(type);
            CREATE INDEX IF NOT EXISTS idx_assignment_templates_day ON assignment_templates(day_of_week);
            CREATE INDEX IF NOT EXISTS idx_assignment_templates_dates ON assignment_templates(start_date, end_date);
        `);

        // 3. Ajouter des templates d'exemple
        console.log('📝 [Migration] Ajout de templates d\'exemple...');

        // Récupérer Jean Dupont et un poste
        const employees = await query("SELECT id, name FROM employees WHERE name = 'Jean Dupont' LIMIT 1");
        const posts = await query("SELECT id, label FROM standard_posts LIMIT 1");

        if (employees.rows.length > 0 && posts.rows.length > 0) {
            const employeeId = employees.rows[0].id;
            const postId = posts.rows[0].id;

            // Template repeat pour Lundi
            await query(`
                INSERT INTO assignment_templates (employee_id, post_id, type, day_of_week, start_time, end_time, start_date, end_date)
                VALUES ($1, $2, 'repeat', 1, '08:00:00', '16:00:00', '2025-06-01', NULL)
                ON CONFLICT DO NOTHING
            `, [employeeId, postId]);

            // Template repeat pour Mardi
            await query(`
                INSERT INTO assignment_templates (employee_id, post_id, type, day_of_week, start_time, end_time, start_date, end_date)
                VALUES ($1, $2, 'repeat', 2, '08:00:00', '16:00:00', '2025-06-01', NULL)
                ON CONFLICT DO NOTHING
            `, [employeeId, postId]);

            // Template repeat pour Mercredi
            await query(`
                INSERT INTO assignment_templates (employee_id, post_id, type, day_of_week, start_time, end_time, start_date, end_date)
                VALUES ($1, $2, 'repeat', 3, '08:00:00', '16:00:00', '2025-06-01', NULL)
                ON CONFLICT DO NOTHING
            `, [employeeId, postId]);

            console.log(`✅ [Migration] Templates créés pour ${employees.rows[0].name} → ${posts.rows[0].label}`);
        }

        // 4. Vérifier le résultat
        const result = await query(`
            SELECT
                at.id,
                e.name as employee_name,
                p.label as post_label,
                at.type,
                at.day_of_week,
                at.start_time,
                at.end_time,
                at.start_date,
                at.end_date
            FROM assignment_templates at
            LEFT JOIN employees e ON at.employee_id = e.id
            LEFT JOIN standard_posts p ON at.post_id = p.id
            WHERE at.is_active = true
            ORDER BY e.name, at.day_of_week
        `);

        console.log('📊 [Migration] Templates créés:');
        console.table(result.rows);

        console.log('✅ [Migration] Migration terminée avec succès !');

    } catch (error) {
        console.error('❌ [Migration] Erreur:', error);
        throw error;
    }
}

// Exécuter la migration
migrateTemplates()
    .then(() => {
        console.log('🎉 [Migration] Table assignment_templates créée !');
        process.exit(0);
    })
    .catch((error) => {
        console.error('💥 [Migration] Échec:', error);
        process.exit(1);
    });
