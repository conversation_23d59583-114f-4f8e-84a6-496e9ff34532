// 🔗 Helpers pour URLs sécurisées
// Évite les requêtes vers /null et les URLs malformées

/**
 * ✅ HELPER : URL sécurisée avec fallback
 * Évite les src="null" et href="null" qui causent des requêtes parasites
 */
export function safeUrl(url: string | null | undefined, fallback: string = ''): string {
  // Vérifier les valeurs problématiques
  if (!url || url === 'null' || url === 'undefined' || url.trim() === '') {
    return fallback;
  }
  
  // Vérifier que l'URL est valide
  try {
    // Pour les URLs relatives, ajouter un domaine temporaire pour validation
    const testUrl = url.startsWith('/') ? `http://localhost${url}` : url;
    new URL(testUrl);
    return url;
  } catch {
    console.warn(`⚠️ [safeUrl] URL invalide détectée: "${url}", fallback utilisé: "${fallback}"`);
    return fallback;
  }
}

/**
 * ✅ HELPER : Avatar sécurisé avec placeholder
 */
export function safeAvatarUrl(url: string | null | undefined): string {
  return safeUrl(url, '/placeholder-avatar.svg');
}

/**
 * ✅ HELPER : Image sécurisée avec placeholder
 */
export function safeImageUrl(url: string | null | undefined): string {
  return safeUrl(url, '/placeholder-image.svg');
}

/**
 * ✅ HELPER : Lien sécurisé (retourne # si invalide pour éviter navigation)
 */
export function safeLinkUrl(url: string | null | undefined): string {
  return safeUrl(url, '#');
}

/**
 * ✅ HELPER : URL API sécurisée
 * Construit une URL API en évitant les segments null
 */
export function buildApiUrl(baseUrl: string, ...segments: (string | number | null | undefined)[]): string {
  const validSegments = segments
    .filter(segment => segment !== null && segment !== undefined && segment !== '')
    .map(segment => String(segment).trim())
    .filter(segment => segment !== 'null' && segment !== 'undefined');
  
  const path = validSegments.join('/');
  const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
  
  return `${cleanBaseUrl}/${path}`;
}

/**
 * ✅ HELPER : Validation d'URL stricte
 * Retourne true si l'URL est valide et sécurisée
 */
export function isValidUrl(url: string | null | undefined): boolean {
  if (!url || typeof url !== 'string') return false;
  
  // Rejeter les valeurs problématiques
  const problematicValues = ['null', 'undefined', '', '#', 'javascript:', 'data:'];
  if (problematicValues.some(bad => url.toLowerCase().includes(bad))) {
    return false;
  }
  
  try {
    const testUrl = url.startsWith('/') ? `http://localhost${url}` : url;
    const parsed = new URL(testUrl);
    
    // Vérifier les protocoles autorisés
    const allowedProtocols = ['http:', 'https:', 'mailto:', 'tel:'];
    return allowedProtocols.includes(parsed.protocol);
  } catch {
    return false;
  }
}

/**
 * ✅ HELPER : Nettoyer les paramètres d'URL
 * Supprime les tokens sensibles des URLs de log
 */
export function sanitizeUrlForLogging(url: string): string {
  try {
    const parsed = new URL(url);
    
    // Supprimer les paramètres sensibles
    const sensitiveParams = ['token', 'password', 'secret', 'key', 'auth'];
    sensitiveParams.forEach(param => {
      if (parsed.searchParams.has(param)) {
        parsed.searchParams.set(param, '[REDACTED]');
      }
    });
    
    return parsed.toString();
  } catch {
    // Si l'URL n'est pas parsable, masquer complètement
    return '[INVALID_URL]';
  }
}

/**
 * ✅ HELPER : Construire URL avec paramètres sécurisés
 */
export function buildUrlWithParams(
  baseUrl: string, 
  params: Record<string, string | number | boolean | null | undefined>
): string {
  if (!isValidUrl(baseUrl)) {
    throw new Error(`URL de base invalide: ${baseUrl}`);
  }
  
  const url = new URL(baseUrl);
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      url.searchParams.set(key, String(value));
    }
  });
  
  return url.toString();
}

/**
 * ✅ HELPER : Vérifier si une URL pointe vers localhost/dev
 */
export function isDevUrl(url: string): boolean {
  try {
    const parsed = new URL(url);
    const devHosts = ['localhost', '127.0.0.1', '0.0.0.0'];
    return devHosts.includes(parsed.hostname) || parsed.hostname.endsWith('.local');
  } catch {
    return false;
  }
}

/**
 * ✅ HELPER : Créer un placeholder SVG inline
 */
export function createPlaceholderSvg(width: number = 100, height: number = 100, text: string = '?'): string {
  return `data:image/svg+xml;base64,${btoa(`
    <svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
      <rect width="100%" height="100%" fill="#f3f4f6"/>
      <text x="50%" y="50%" text-anchor="middle" dy="0.3em" font-family="Arial" font-size="24" fill="#9ca3af">
        ${text}
      </text>
    </svg>
  `)}`;
}

// ✅ CONSTANTES UTILES
export const PLACEHOLDER_URLS = {
  avatar: createPlaceholderSvg(40, 40, '👤'),
  image: createPlaceholderSvg(200, 150, '🖼️'),
  icon: createPlaceholderSvg(24, 24, '?'),
} as const;

// ✅ EXPORT PAR DÉFAUT
export default {
  safeUrl,
  safeAvatarUrl,
  safeImageUrl,
  safeLinkUrl,
  buildApiUrl,
  isValidUrl,
  sanitizeUrlForLogging,
  buildUrlWithParams,
  isDevUrl,
  createPlaceholderSvg,
  PLACEHOLDER_URLS
};
