[{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2339",
	"severity": 8,
	"message": "Property 'style' does not exist on type 'Element'.",
	"source": "ts",
	"startLineNumber": 1981,
	"startColumn": 31,
	"endLineNumber": 1981,
	"endColumn": 36
},{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2339",
	"severity": 8,
	"message": "Property 'style' does not exist on type 'Element'.",
	"source": "ts",
	"startLineNumber": 1981,
	"startColumn": 65,
	"endLineNumber": 1981,
	"endColumn": 70
},{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2339",
	"severity": 8,
	"message": "Property 'style' does not exist on type 'Element'.",
	"source": "ts",
	"startLineNumber": 1982,
	"startColumn": 71,
	"endLineNumber": 1982,
	"endColumn": 76
},{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2339",
	"severity": 8,
	"message": "Property 'style' does not exist on type 'Element'.",
	"source": "ts",
	"startLineNumber": 1983,
	"startColumn": 35,
	"endLineNumber": 1983,
	"endColumn": 40
},{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2353",
	"severity": 8,
	"message": "Object literal may only specify known properties, and 'closeSettingsModalHelper' does not exist in type 'TeamCalendarAppType'.",
	"source": "ts",
	"startLineNumber": 13577,
	"startColumn": 5,
	"endLineNumber": 13577,
	"endColumn": 29
},{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2339",
	"severity": 8,
	"message": "Property 'closeModal' does not exist on type 'ModalFunctionalitiesManager'.",
	"source": "ts",
	"startLineNumber": 13578,
	"startColumn": 81,
	"endLineNumber": 13578,
	"endColumn": 91
},{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2339",
	"severity": 8,
	"message": "Property 'closeModal' does not exist on type 'ModalFunctionalitiesManager'.",
	"source": "ts",
	"startLineNumber": 13579,
	"startColumn": 34,
	"endLineNumber": 13579,
	"endColumn": 44
}]
Et voici les logs:
[18/07/2025 08:38:18] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 08:38:18] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 08:38:18] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 08:38:18] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 08:38:18] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:18.041Z"
}
[18/07/2025 08:38:18] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 08:38:18] [BACKEND] [INFO] [LOGS] Client connecté: 1752842298039
[18/07/2025 08:37:12] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[18/07/2025 08:37:12] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[18/07/2025 08:37:12] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 08:37:12] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 08:37:12] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 08:37:12] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[18/07/2025 08:37:12] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[18/07/2025 08:37:12] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 08:37:12] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 08:37:12] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 08:37:12] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 08:37:12] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 08:37:12] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 08:37:12] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 08:37:12] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 08:37:12] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 08:37:12] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 08:37:12] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 08:37:12] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[18/07/2025 08:37:12] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 08:37:12] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 08:37:12] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[18/07/2025 08:37:12] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 08:37:12] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 08:37:12] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 08:37:12] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[18/07/2025 08:37:12] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 08:37:12] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 08:37:12] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 08:37:12] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 08:37:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:37:11.959Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:37:12] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 08:37:12] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 08:37:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:37:11.972Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:37:12] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 08:37:12] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 08:37:12] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 08:37:12] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[18/07/2025 08:37:12] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:37:12.187Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[18/07/2025 08:37:12] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 08:37:12] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 08:37:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:37:12.302Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:37:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:37:12.126Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:37:12] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 139ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:37:12.326Z",
  "originalArgs": [
    "✅ Requête exécutée en 139ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[18/07/2025 08:37:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:37:12.159Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:37:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:37:12.340Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:37:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:37:12.345Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:37:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:37:12.158Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:37:12] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 08:37:13] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 08:37:13] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 08:37:13] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":0,"posts":0}
[18/07/2025 08:37:13] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":true,"hasOpenFunction":true,"hasHandleFunction":true}
[18/07/2025 08:37:13] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":0}
[18/07/2025 08:37:14] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 08:37:14] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 08:37:14] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.setupAssignmentContextModal disponible
[18/07/2025 08:37:14] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 08:37:14] [FRONTEND] [ERROR] ❌ [VALIDATION] ModalManager.app non initialisé
[18/07/2025 08:37:14] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[18/07/2025 08:37:14] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":false}
[18/07/2025 08:37:14] [FRONTEND] [LOG] 🔧 [setupAssignmentContextModal] Configuration du modal contextuel...
[18/07/2025 08:37:14] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 08:37:14] [FRONTEND] [ERROR] ❌ [VALIDATION] Éléments DOM manquants
[18/07/2025 08:37:14] [FRONTEND] [ERROR] ❌ [setupAssignmentContextModal] Éléments manquants: {"closeBtn":false,"cancelBtn":false,"confirmBtn":false}
[18/07/2025 08:37:14] [FRONTEND] [LOG] ✅ [VALIDATION] setupAssignmentContextModal exécuté sans erreur
[18/07/2025 08:37:14] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":false,"listeners":true}
[18/07/2025 08:37:14] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[18/07/2025 08:37:14] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[18/07/2025 08:37:14] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[18/07/2025 08:37:15] [FRONTEND] [LOG] - Vérifier que la modale assignment-context-modal existe dans le DOM
[18/07/2025 08:37:15] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 08:37:15] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 08:37:15] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":true,"detectDropDateFunction":true,"showConfirmationMenuFunction":true,"handleRegularAssignmentDropFunction":true,"regularAssignments":0,"employees":0}
[18/07/2025 08:37:15] [FRONTEND] [LOG] ✅ [DIAGNOSTIC] Toutes les fonctions nécessaires sont disponibles
[18/07/2025 08:37:15] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 08:37:15] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 08:37:15] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 08:37:15] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 1 (Date locale): 2025-07-24
[18/07/2025 08:37:15] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[18/07/2025 08:37:15] [FRONTEND] [ERROR] ❌ [DATE-TEST] Test 2 (String date): attendu 2025-07-24, obtenu 2025-07-23
[18/07/2025 08:37:15] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 3 (Date ISO avec timezone): 2025-07-24
[18/07/2025 08:37:15] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 08:37:15] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 08:37:15] [FRONTEND] [ERROR] ❌ [DATE-TEST] Données insuffisantes pour le test
[18/07/2025 08:37:15] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 08:37:15] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 08:37:16] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 08:37:16] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 08:37:16] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 08:37:16] [FRONTEND] [LOG] 🔍 [detectDropDateFromPosition] Analyse position pour employé test-employee
[18/07/2025 08:37:16] [FRONTEND] [LOG] 📍 [detectDropDateFromPosition] Position souris: (500, 300)
[18/07/2025 08:37:16] [FRONTEND] [LOG] ⚠️ [detectDropDateFromPosition] Fallback vers aujourd'hui: 2025-07-18
[18/07/2025 08:37:16] [FRONTEND] [LOG] ✅ [DATE-TEST] Date détectée: 2025-07-18
[18/07/2025 08:37:16] [FRONTEND] [LOG] ✅ [DATE-TEST] Format de date valide
[18/07/2025 08:37:16] [FRONTEND] [LOG] ✅ [DATE-TEST] Date raisonnable (0 jours de différence)
[18/07/2025 08:37:16] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 08:37:16] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 08:37:16] [FRONTEND] [LOG] ❌ Logique de fork avec dates: ÉCHOUÉ
[18/07/2025 08:37:16] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 08:37:16] [FRONTEND] [LOG] ✅ Détection de date avec timezone: PASSÉ
[18/07/2025 08:37:16] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 08:37:16] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 08:37:16] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 0
[18/07/2025 08:37:16] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 08:37:16] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 08:37:16] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":0,"regularShifts":0,"gripsInDOM":0,"gripsWithEvents":0,"gripsResponsive":0,"issues":[]}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🚀 SERVEUR API DÉMARRÉ AVEC PROTECTION UUID
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.196Z",
  "originalArgs": [
    "🚀 SERVEUR API DÉMARRÉ AVEC PROTECTION UUID"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] ============================================================
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.146Z",
  "originalArgs": [
    "============================================================"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] ============================================================
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.197Z",
  "originalArgs": [
    "============================================================"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] ✅ [Logger] Console patchée pour intégration unifiée
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.122Z",
  "originalArgs": [
    "✅ [Logger] Console patchée pour intégration unifiée"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.209Z",
  "originalArgs": [
    ""
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 📍 URL: http://localhost:3001
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.199Z",
  "originalArgs": [
    "📍 URL: http://localhost:3001"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] [query] Executing query: CREATE TABLE IF NOT EXISTS logs (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  source VARCHAR(50) NOT NULL,
  level VARCHAR(50) NOT NULL,
  message TEXT NOT NULL,
  data JSONB,
  priority INTEGER DEFAULT 0
) {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.130Z",
  "originalArgs": [
    "[query] Executing query: CREATE TABLE IF NOT EXISTS logs (\n  id SERIAL PRIMARY KEY,\n  session_id VARCHAR(255) NOT NULL,\n  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,\n  source VARCHAR(50) NOT NULL,\n  level VARCHAR(50) NOT NULL,\n  message TEXT NOT NULL,\n  data JSONB,\n  priority INTEGER DEFAULT 0\n)",
    {}
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] ============================================================
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.206Z",
  "originalArgs": [
    "============================================================"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🏥 Health Check: http://localhost:3001/health
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.200Z",
  "originalArgs": [
    "🏥 Health Check: http://localhost:3001/health"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] ✅ Serveur prêt à recevoir les requêtes
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.208Z",
  "originalArgs": [
    "✅ Serveur prêt à recevoir les requêtes"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] ⏰ Démarré le: 2025-07-18T12:38:09.207Z
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.207Z",
  "originalArgs": [
    "⏰ Démarré le: 2025-07-18T12:38:09.207Z"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔍 Logs détaillés: ACTIVÉS
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.204Z",
  "originalArgs": [
    "🔍 Logs détaillés: ACTIVÉS"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] ⚠️  Gestion erreurs: ROBUSTE
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.205Z",
  "originalArgs": [
    "⚠️  Gestion erreurs: ROBUSTE"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🛡️  Protection UUID: ACTIVÉE
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.203Z",
  "originalArgs": [
    "🛡️  Protection UUID: ACTIVÉE"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.466Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] ============================================================
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.468Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.472Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.471Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.469Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.476Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.484Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.487Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.483Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.486Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.489Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] ✅ [Logger] Connexion PostgreSQL (distant) validée
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.503Z",
  "originalArgs": [
    "✅ [Logger] Connexion PostgreSQL (distant) validée"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] [query] Executing query: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 0 {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.526Z",
  "originalArgs": [
    "[query] Executing query: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 0",
    {}
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.491Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.498Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.492Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 384ms: SELECT 1 FROM logs LIMIT 1 {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.502Z",
  "originalArgs": [
    "✅ Requête exécutée en 384ms:",
    "SELECT 1 FROM logs LIMIT 1",
    {}
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 395ms: CREATE TABLE IF NOT EXISTS logs (
  id SERIAL PRIM {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.525Z",
  "originalArgs": [
    "✅ Requête exécutée en 395ms:",
    "CREATE TABLE IF NOT EXISTS logs (\n  id SERIAL PRIM",
    {}
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 81ms: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.606Z",
  "originalArgs": [
    "✅ Requête exécutée en 81ms:",
    "ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority",
    {}
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] ✅ Requête exécutée en 81ms: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority {}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.632Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.464Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.458Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.644Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.454Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:09] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:09.654Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 08:38:17] [FRONTEND] [DEBUG] [vite] connected.
[18/07/2025 08:38:17] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[18/07/2025 08:38:17] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[18/07/2025 08:38:17] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[18/07/2025 08:38:17] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[18/07/2025 08:38:17] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[18/07/2025 08:38:17] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[18/07/2025 08:38:17] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[18/07/2025 08:38:17] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[18/07/2025 08:38:17] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[18/07/2025 08:38:17] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[18/07/2025 08:38:17] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[18/07/2025 08:38:17] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[18/07/2025 08:38:17] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[18/07/2025 08:38:17] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[18/07/2025 08:38:17] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[18/07/2025 08:38:17] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 08:38:17] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[18/07/2025 08:38:17] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[18/07/2025 08:38:17] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[18/07/2025 08:38:17] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 08:38:17] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 08:38:17] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 08:38:17] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 08:38:17] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 08:38:17] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 08:38:17] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 08:38:17] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 08:38:17] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 08:38:18] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 08:38:18] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 08:38:18] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 08:38:18] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 08:38:18] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 08:38:18] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[18/07/2025 08:38:18] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 08:38:18] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 08:38:18] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[18/07/2025 08:38:18] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 08:38:18] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 08:38:18] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 08:38:18] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[18/07/2025 08:38:18] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 08:38:18] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 08:38:18] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 08:38:18] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 08:38:18] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 08:38:18] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 08:38:18] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 08:38:18] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 08:38:18] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[18/07/2025 08:38:18] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:18.041Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[18/07/2025 08:38:18] [BACKEND] [INFO] ✅ Requête exécutée en 144ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 144ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 144ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:18.186Z"
}
[18/07/2025 08:38:19] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 08:38:19] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":true,"hasOpenFunction":true,"hasHandleFunction":true}
[18/07/2025 08:38:19] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":0}
[18/07/2025 08:38:19] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":0,"posts":0}
[18/07/2025 08:38:19] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 08:38:20] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 08:38:20] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.setupAssignmentContextModal disponible
[18/07/2025 08:38:20] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 08:38:20] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 08:38:20] [FRONTEND] [ERROR] ❌ [VALIDATION] ModalManager.app non initialisé
[18/07/2025 08:38:20] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[18/07/2025 08:38:20] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":false}
[18/07/2025 08:38:20] [FRONTEND] [ERROR] ❌ [VALIDATION] Éléments DOM manquants
[18/07/2025 08:38:20] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 08:38:20] [FRONTEND] [LOG] 🔧 [setupAssignmentContextModal] Configuration du modal contextuel...
[18/07/2025 08:38:20] [FRONTEND] [ERROR] ❌ [setupAssignmentContextModal] Éléments manquants: {"closeBtn":false,"cancelBtn":false,"confirmBtn":false}
[18/07/2025 08:38:20] [FRONTEND] [LOG] ✅ [VALIDATION] setupAssignmentContextModal exécuté sans erreur
[18/07/2025 08:38:20] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":false,"listeners":true}
[18/07/2025 08:38:20] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[18/07/2025 08:38:20] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[18/07/2025 08:38:20] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[18/07/2025 08:38:20] [FRONTEND] [LOG] - Vérifier que la modale assignment-context-modal existe dans le DOM
[18/07/2025 08:38:20] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 08:38:20] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 08:38:20] [FRONTEND] [LOG] ✅ [DIAGNOSTIC] Toutes les fonctions nécessaires sont disponibles
[18/07/2025 08:38:20] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":true,"detectDropDateFunction":true,"showConfirmationMenuFunction":true,"handleRegularAssignmentDropFunction":true,"regularAssignments":0,"employees":0}
[18/07/2025 08:38:21] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 08:38:21] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 08:38:21] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 1 (Date locale): 2025-07-24
[18/07/2025 08:38:21] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[18/07/2025 08:38:21] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 08:38:21] [FRONTEND] [ERROR] ❌ [DATE-TEST] Test 2 (String date): attendu 2025-07-24, obtenu 2025-07-23
[18/07/2025 08:38:21] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 3 (Date ISO avec timezone): 2025-07-24
[18/07/2025 08:38:21] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 08:38:21] [FRONTEND] [ERROR] ❌ [DATE-TEST] Données insuffisantes pour le test
[18/07/2025 08:38:21] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 08:38:21] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 08:38:21] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 08:38:21] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 08:38:21] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 08:38:21] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 08:38:21] [FRONTEND] [LOG] 🔍 [detectDropDateFromPosition] Analyse position pour employé test-employee
[18/07/2025 08:38:21] [FRONTEND] [LOG] 📍 [detectDropDateFromPosition] Position souris: (500, 300)
[18/07/2025 08:38:21] [FRONTEND] [LOG] ⚠️ [detectDropDateFromPosition] Fallback vers aujourd'hui: 2025-07-18
[18/07/2025 08:38:21] [FRONTEND] [LOG] ✅ [DATE-TEST] Date détectée: 2025-07-18
[18/07/2025 08:38:21] [FRONTEND] [LOG] ✅ [DATE-TEST] Date raisonnable (0 jours de différence)
[18/07/2025 08:38:21] [FRONTEND] [LOG] ✅ [DATE-TEST] Format de date valide
[18/07/2025 08:38:21] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 08:38:21] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 08:38:21] [FRONTEND] [LOG] ❌ Logique de fork avec dates: ÉCHOUÉ
[18/07/2025 08:38:21] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 08:38:21] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 08:38:21] [FRONTEND] [LOG] ✅ Détection de date avec timezone: PASSÉ
[18/07/2025 08:38:21] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 08:38:22] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 08:38:22] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 08:38:22] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 0
[18/07/2025 08:38:22] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":0,"regularShifts":0,"gripsInDOM":0,"gripsWithEvents":0,"gripsResponsive":0,"issues":[]}
[18/07/2025 08:38:25] [FRONTEND] [DEBUG] [vite] connected.
[18/07/2025 08:38:25] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[18/07/2025 08:38:25] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[18/07/2025 08:38:25] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[18/07/2025 08:38:25] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[18/07/2025 08:38:25] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[18/07/2025 08:38:25] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[18/07/2025 08:38:25] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[18/07/2025 08:38:25] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[18/07/2025 08:38:25] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[18/07/2025 08:38:25] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[18/07/2025 08:38:25] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[18/07/2025 08:38:25] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[18/07/2025 08:38:25] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[18/07/2025 08:38:25] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[18/07/2025 08:38:25] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[18/07/2025 08:38:25] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 08:38:25] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[18/07/2025 08:38:25] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[18/07/2025 08:38:25] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[18/07/2025 08:38:25] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 08:38:25] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 08:38:25] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 08:38:25] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 08:38:25] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 08:38:25] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 08:38:25] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 08:38:25] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 08:38:25] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 08:38:25] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 08:38:25] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 08:38:25] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 08:38:25] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 08:38:25] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 08:38:25] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[18/07/2025 08:38:25] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 08:38:25] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 08:38:25] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[18/07/2025 08:38:26] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 08:38:26] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 08:38:26] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 08:38:26] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 08:38:26] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 08:38:26] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 08:38:26] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 08:38:26] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 08:38:26] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:26.043Z"
}
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[18/07/2025 08:38:26] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 43ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 43ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:26.086Z"
}
[18/07/2025 08:38:26] [BACKEND] [INFO] ✅ Requête exécutée en 43ms: 
      SELECT e.*, et.name as template_name 
      {}
[18/07/2025 08:38:26] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[18/07/2025 08:38:26] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:26.148Z"
}
[18/07/2025 08:38:26] [BACKEND] [INFO] ✅ Requête exécutée en 50ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 50ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 50ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:26.199Z"
}
[18/07/2025 08:38:26] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:26.243Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 36ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 36ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:26.280Z"
}
[18/07/2025 08:38:26] [BACKEND] [INFO] ✅ Requête exécutée en 36ms: SELECT * FROM standard_posts ORDER BY label {}
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:26.342Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 39ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 39ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:26.382Z"
}
[18/07/2025 08:38:26] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[18/07/2025 08:38:26] [BACKEND] [INFO] ✅ Requête exécutée en 39ms: SELECT * FROM app_settings ORDER BY setting_key {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:26.400Z"
}
[18/07/2025 08:38:26] [BACKEND] [INFO] ✅ Requête exécutée en 57ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 57ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 57ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:26.455Z"
}
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"238511c1-020d-4121-8572-04b5a29da77f","employee_id":"42b53805-18ba-425e-bb00-52bb8d6ce76b","post_id":"0e619465-1f9e-41dd-a60c-7b58b9108138","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-29T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"238511c1-020d-4121-8572-04b5a29da77f","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-07-29","endDate":null,"excludedDates":[]}
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"dd0d79f2-5643-464c-9524-14b8660f2238","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-22","endDate":"2025-07-22","excludedDates":[]}
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"b3b5ede1-df0f-407d-b064-cd4c641cced1","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"0e619465-1f9e-41dd-a60c-7b58b9108138","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-24T04:00:00.000Z","end_date":"2025-07-27T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"dd0d79f2-5643-464c-9524-14b8660f2238","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"0e619465-1f9e-41dd-a60c-7b58b9108138","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-22T04:00:00.000Z","end_date":"2025-07-22T04:00:00.000Z","excluded_dates":[]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:26.564Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:26.562Z"
}
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"b3b5ede1-df0f-407d-b064-cd4c641cced1","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-24","endDate":"2025-07-27","excludedDates":[]}
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"0305b6b2-9802-4a0e-b257-eabcc243c705","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-17","endDate":"2025-07-21","excludedDates":[]}
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"0305b6b2-9802-4a0e-b257-eabcc243c705","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"0e619465-1f9e-41dd-a60c-7b58b9108138","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-17T04:00:00.000Z","end_date":"2025-07-21T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"0a01d0eb-f35d-4a13-9440-0592296c0274","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-23","endDate":"2025-07-23","excludedDates":[]}
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"0a01d0eb-f35d-4a13-9440-0592296c0274","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"0e619465-1f9e-41dd-a60c-7b58b9108138","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-23T04:00:00.000Z","end_date":"2025-07-23T04:00:00.000Z","excluded_dates":[]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 57ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 57ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:26.621Z"
}
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"e349d5af-83db-4285-a29f-912cf6edf7e4","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"0e619465-1f9e-41dd-a60c-7b58b9108138","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-28T04:00:00.000Z","end_date":"2025-07-28T04:00:00.000Z","excluded_dates":[]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [GET /api/employee-order] Ordre récupéré (array): [{"id":"42b53805-18ba-425e-bb00-52bb8d6ce76b","name":"Jean Dupont","order":0},{"id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","name":"Marie Martin","order":1},{"id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","name":"Lucas Bernard","order":2},{"id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","name":"Pierre Durand","order":3},{"id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","name":"Sophie Leblanc","order":4}]
  Data: {
  "originalArgs": [
    "✅ [GET /api/employee-order] Ordre récupéré (array):",
    [
      {
        "id": "42b53805-18ba-425e-bb00-52bb8d6ce76b",
        "name": "Jean Dupont",
        "order": 0
      },
      {
        "id": "604a1d0c-2141-4543-b7f9-7fc9adfa7b9a",
        "name": "Marie Martin",
        "order": 1
      },
      {
        "id": "cf78e945-72c7-48f3-a72d-bd0e245a284d",
        "name": "Lucas Bernard",
        "order": 2
      },
      {
        "id": "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
        "name": "Pierre Durand",
        "order": 3
      },
      {
        "id": "59f5df3a-33ef-425c-bfa2-adca818cf94f",
        "name": "Sophie Leblanc",
        "order": 4
      }
    ]
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:26.623Z"
}
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"e349d5af-83db-4285-a29f-912cf6edf7e4","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-28","endDate":"2025-07-28","excludedDates":[]}
[18/07/2025 08:38:26] [FRONTEND] [LOG] 📋 [loadState] 6/6 assignations régulières valides chargées et normalisées
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[18/07/2025 08:38:26] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T12:38:26.724Z"
}
[18/07/2025 08:38:26] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[18/07/2025 08:38:26] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[18/07/2025 08:38:26] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[18/07/2025 08:38:26] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [loadEmployeeOrder] Ordre API récupéré: Jean Dupont (0), Marie Martin (1), Lucas Bernard (2), Pierre Durand (3), Sophie Leblanc (4)
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [loadEmployeeOrder] Employés réorganisés selon l'ordre api
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0e619465-1f9e-41dd-a60c-7b58b9108138 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0e619465-1f9e-41dd-a60c-7b58b9108138
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7e33acc4-c7ca-49b6-861f-56183c65b89c configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7e33acc4-c7ca-49b6-861f-56183c65b89c
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c1480200-8166-4711-b9b5-94adf4c41893
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c1480200-8166-4711-b9b5-94adf4c41893 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 31b2921b-6573-4880-9d46-79a222203e33 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 31b2921b-6573-4880-9d46-79a222203e33
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3958a6c5-871b-4ab4-87e8-d165f35f0328 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3958a6c5-871b-4ab4-87e8-d165f35f0328
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0e619465-1f9e-41dd-a60c-7b58b9108138 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0e619465-1f9e-41dd-a60c-7b58b9108138
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7e33acc4-c7ca-49b6-861f-56183c65b89c configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7e33acc4-c7ca-49b6-861f-56183c65b89c
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c1480200-8166-4711-b9b5-94adf4c41893 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c1480200-8166-4711-b9b5-94adf4c41893
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 31b2921b-6573-4880-9d46-79a222203e33 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 31b2921b-6573-4880-9d46-79a222203e33
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3958a6c5-871b-4ab4-87e8-d165f35f0328 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3958a6c5-871b-4ab4-87e8-d165f35f0328
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0e619465-1f9e-41dd-a60c-7b58b9108138 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0e619465-1f9e-41dd-a60c-7b58b9108138
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7e33acc4-c7ca-49b6-861f-56183c65b89c configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7e33acc4-c7ca-49b6-861f-56183c65b89c
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c1480200-8166-4711-b9b5-94adf4c41893 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c1480200-8166-4711-b9b5-94adf4c41893
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 31b2921b-6573-4880-9d46-79a222203e33 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 31b2921b-6573-4880-9d46-79a222203e33
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3958a6c5-871b-4ab4-87e8-d165f35f0328 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3958a6c5-871b-4ab4-87e8-d165f35f0328
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0e619465-1f9e-41dd-a60c-7b58b9108138 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0e619465-1f9e-41dd-a60c-7b58b9108138
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7e33acc4-c7ca-49b6-861f-56183c65b89c configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7e33acc4-c7ca-49b6-861f-56183c65b89c
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c1480200-8166-4711-b9b5-94adf4c41893 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c1480200-8166-4711-b9b5-94adf4c41893
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 31b2921b-6573-4880-9d46-79a222203e33 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 31b2921b-6573-4880-9d46-79a222203e33
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3958a6c5-871b-4ab4-87e8-d165f35f0328
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3958a6c5-871b-4ab4-87e8-d165f35f0328 configuré comme draggable
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 08:38:27] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 08:38:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[18/07/2025 08:38:28] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton d'historique...
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Boutons ajoutés (emergency fix buttons supprimés)
[18/07/2025 08:38:28] [FRONTEND] [LOG] 📋 [init] Application des attributions régulières après initialisation
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[18/07/2025 08:38:28] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W28
[18/07/2025 08:38:28] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 238511c1-020d-4121-8572-04b5a29da77f commence après cette semaine (startDate: 2025-07-29, semaine se termine: 2025-07-19)
[18/07/2025 08:38:28] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution dd0d79f2-5643-464c-9524-14b8660f2238 commence après cette semaine (startDate: 2025-07-22, semaine se termine: 2025-07-19)
[18/07/2025 08:38:28] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution b3b5ede1-df0f-407d-b064-cd4c641cced1 commence après cette semaine (startDate: 2025-07-24, semaine se termine: 2025-07-19)
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 0305b6b2-9802-4a0e-b257-eabcc243c705 a une intersection avec cette semaine (2025-07-17 → 2025-07-21) vs (2025-07-13 → 2025-07-19)
[18/07/2025 08:38:28] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 0a01d0eb-f35d-4a13-9440-0592296c0274 commence après cette semaine (startDate: 2025-07-23, semaine se termine: 2025-07-19)
[18/07/2025 08:38:28] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution e349d5af-83db-4285-a29f-912cf6edf7e4 commence après cette semaine (startDate: 2025-07-28, semaine se termine: 2025-07-19)
[18/07/2025 08:38:28] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions actives sur 6 total
[18/07/2025 08:38:28] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"0305b6b2-9802-4a0e-b257-eabcc243c705","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","selectedDays":[1,2,3,4,5],"startDate":"2025-07-17","endDate":"2025-07-21","excludedDates":[]}
[18/07/2025 08:38:28] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 0305b6b2-9802-4a0e-b257-eabcc243c705: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0305b6b2-9802-4a0e-b257-eabcc243c705","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 08:38:28] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 08:38:28] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution 0305b6b2-9802-4a0e-b257-eabcc243c705
[18/07/2025 08:38:28] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0305b6b2-9802-4a0e-b257-eabcc243c705","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 08:38:28] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-14 avant startDate 2025-07-17 (original: 2025-07-17)
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"0305b6b2-9802-4a0e-b257-eabcc243c705","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 08:38:28] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 ignoré pour attribution 0305b6b2-9802-4a0e-b257-eabcc243c705
[18/07/2025 08:38:28] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0305b6b2-9802-4a0e-b257-eabcc243c705","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"0305b6b2-9802-4a0e-b257-eabcc243c705","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 08:38:28] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-15 avant startDate 2025-07-17 (original: 2025-07-17)
[18/07/2025 08:38:28] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 ignoré pour attribution 0305b6b2-9802-4a0e-b257-eabcc243c705
[18/07/2025 08:38:28] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0305b6b2-9802-4a0e-b257-eabcc243c705","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"0305b6b2-9802-4a0e-b257-eabcc243c705","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 08:38:28] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-16 avant startDate 2025-07-17 (original: 2025-07-17)
[18/07/2025 08:38:28] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 ignoré pour attribution 0305b6b2-9802-4a0e-b257-eabcc243c705
[18/07/2025 08:38:28] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0305b6b2-9802-4a0e-b257-eabcc243c705","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"0305b6b2-9802-4a0e-b257-eabcc243c705","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-17
[18/07/2025 08:38:28] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 validé, shifts existants: 0
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-17 pour Marie Martin
[18/07/2025 08:38:28] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0305b6b2-9802-4a0e-b257-eabcc243c705","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"0305b6b2-9802-4a0e-b257-eabcc243c705","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-18
[18/07/2025 08:38:28] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 validé, shifts existants: 0
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-18 pour Marie Martin
[18/07/2025 08:38:28] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0305b6b2-9802-4a0e-b257-eabcc243c705","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 08:38:28] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution 0305b6b2-9802-4a0e-b257-eabcc243c705
[18/07/2025 08:38:28] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 08:38:28] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 2 attributions appliquées pour la semaine 2025-W28
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 2 shifts créés
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 0 validés, 0 corrigés
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[18/07/2025 08:38:28] [FRONTEND] [WARN] ⚠️ [renderUnifiedCalendar] Conteneur available-posts-container non trouvé, création...
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneur available-posts-container créé
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0e619465-1f9e-41dd-a60c-7b58b9108138 -> "08:00-16:00"
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0305b6b2-9802-4a0e-b257-eabcc243c705
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0e619465-1f9e-41dd-a60c-7b58b9108138 -> "08:00-16:00"
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0305b6b2-9802-4a0e-b257-eabcc243c705
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 64 zones trouvées
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 64 zones de drop disponibles
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0e619465-1f9e-41dd-a60c-7b58b9108138 configuré comme draggable
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0e619465-1f9e-41dd-a60c-7b58b9108138
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7e33acc4-c7ca-49b6-861f-56183c65b89c configuré comme draggable
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7e33acc4-c7ca-49b6-861f-56183c65b89c
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c1480200-8166-4711-b9b5-94adf4c41893 configuré comme draggable
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c1480200-8166-4711-b9b5-94adf4c41893
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 31b2921b-6573-4880-9d46-79a222203e33 configuré comme draggable
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 31b2921b-6573-4880-9d46-79a222203e33
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3958a6c5-871b-4ab4-87e8-d165f35f0328 configuré comme draggable
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3958a6c5-871b-4ab4-87e8-d165f35f0328
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0e619465-1f9e-41dd-a60c-7b58b9108138 configuré comme draggable
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0e619465-1f9e-41dd-a60c-7b58b9108138
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7e33acc4-c7ca-49b6-861f-56183c65b89c configuré comme draggable
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7e33acc4-c7ca-49b6-861f-56183c65b89c
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c1480200-8166-4711-b9b5-94adf4c41893 configuré comme draggable
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c1480200-8166-4711-b9b5-94adf4c41893
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 31b2921b-6573-4880-9d46-79a222203e33
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 31b2921b-6573-4880-9d46-79a222203e33 configuré comme draggable
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3958a6c5-871b-4ab4-87e8-d165f35f0328 configuré comme draggable
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3958a6c5-871b-4ab4-87e8-d165f35f0328
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (64 zones)
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 08:38:28] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[18/07/2025 08:38:28] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 64 zones trouvées
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 64 zones de drop disponibles
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0e619465-1f9e-41dd-a60c-7b58b9108138 configuré comme draggable
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0e619465-1f9e-41dd-a60c-7b58b9108138
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7e33acc4-c7ca-49b6-861f-56183c65b89c configuré comme draggable
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7e33acc4-c7ca-49b6-861f-56183c65b89c
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c1480200-8166-4711-b9b5-94adf4c41893 configuré comme draggable
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c1480200-8166-4711-b9b5-94adf4c41893
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 31b2921b-6573-4880-9d46-79a222203e33 configuré comme draggable
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 31b2921b-6573-4880-9d46-79a222203e33
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3958a6c5-871b-4ab4-87e8-d165f35f0328 configuré comme draggable
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3958a6c5-871b-4ab4-87e8-d165f35f0328
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0e619465-1f9e-41dd-a60c-7b58b9108138 configuré comme draggable
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0e619465-1f9e-41dd-a60c-7b58b9108138
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7e33acc4-c7ca-49b6-861f-56183c65b89c configuré comme draggable
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7e33acc4-c7ca-49b6-861f-56183c65b89c
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c1480200-8166-4711-b9b5-94adf4c41893 configuré comme draggable
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c1480200-8166-4711-b9b5-94adf4c41893
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 31b2921b-6573-4880-9d46-79a222203e33 configuré comme draggable
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 31b2921b-6573-4880-9d46-79a222203e33
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3958a6c5-871b-4ab4-87e8-d165f35f0328 configuré comme draggable
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3958a6c5-871b-4ab4-87e8-d165f35f0328
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 2 validés, 0 corrigés
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (64 zones)
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[18/07/2025 08:38:29] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":2,"regularShifts":2,"regularAssignments":6}
[18/07/2025 08:38:29] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 1: {"employee":"Marie Martin","date":"2025-07-17","assignmentId":"0305b6b2-9802-4a0e-b257-eabcc243c705","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","text":"08:00-16:00"}
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 2
[18/07/2025 08:38:29] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 2: {"employee":"Marie Martin","date":"2025-07-18","assignmentId":"0305b6b2-9802-4a0e-b257-eabcc243c705","postId":"0e619465-1f9e-41dd-a60c-7b58b9108138","text":"08:00-16:00"}
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 08:38:29] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":4}
[18/07/2025 08:38:29] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":true,"hasOpenFunction":true,"hasHandleFunction":true}
[18/07/2025 08:38:29] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] État de la modale: {"hidden":true,"classes":["hidden","fixed","inset-0","bg-black/40","backdrop-blur-sm","z-50","flex","items-center","justify-center","p-4"],"style":""}
[18/07/2025 08:38:29] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":5,"posts":5}
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.setupAssignmentContextModal disponible
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [VALIDATION] handleAssignmentContextConfirm disponible
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.app initialisé
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[18/07/2025 08:38:29] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":true}
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [VALIDATION] Tous les éléments DOM sont présents
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🔧 [setupAssignmentContextModal] Configuration du modal contextuel...
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [setupAssignmentContextModal] Modal contextuel configuré avec listeners nettoyés
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [VALIDATION] setupAssignmentContextModal exécuté sans erreur
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🎉 [VALIDATION] Toutes les validations sont passées avec succès !
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [VALIDATION] La correction de la modale devrait maintenant fonctionner
[18/07/2025 08:38:29] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":true,"domElements":true,"listeners":true}
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🧪 [VALIDATION] Pour tester pratiquement:
[18/07/2025 08:38:29] [FRONTEND] [LOG] 1. Effectuez un drag & drop d'un poste vers un employé
[18/07/2025 08:38:29] [FRONTEND] [LOG] 3. Cliquez sur "Confirmer" et vérifiez les logs
[18/07/2025 08:38:29] [FRONTEND] [LOG] 2. Vérifiez que la modale s'ouvre
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 08:38:29] [FRONTEND] [LOG] 4. Ou utilisez testButtonClicks() pour un test automatique
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 08:38:29] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":true,"detectDropDateFunction":true,"showConfirmationMenuFunction":true,"handleRegularAssignmentDropFunction":true,"regularAssignments":6,"employees":5}
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [DIAGNOSTIC] Toutes les fonctions nécessaires sont disponibles
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 1 (Date locale): 2025-07-24
[18/07/2025 08:38:29] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 08:38:29] [FRONTEND] [ERROR] ❌ [DATE-TEST] Test 2 (String date): attendu 2025-07-24, obtenu 2025-07-23
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 3 (Date ISO avec timezone): 2025-07-24
[18/07/2025 08:38:29] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test avec date: 2025-07-25
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🧪 [DATE-TEST] Simulation du changement permanent...
[18/07/2025 08:38:29] [FRONTEND] [LOG] - Assignment: 238511c1-020d-4121-8572-04b5a29da77f
[18/07/2025 08:38:29] [FRONTEND] [LOG] - De: Jean Dupont
[18/07/2025 08:38:29] [FRONTEND] [LOG] - Vers: Marie Martin
[18/07/2025 08:38:29] [FRONTEND] [LOG] ✅ [showRegularAssignmentConfirmationMenu] Date automatiquement détectée: 2025-07-25
[18/07/2025 08:38:29] [FRONTEND] [LOG] - Date: 2025-07-25
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🎯 [showRegularAssignmentConfirmationMenu] Affichage menu pour: {"assignment":"238511c1-020d-4121-8572-04b5a29da77f","from":"Jean Dupont","to":"Marie Martin","post":"Poste Matin","referenceDate":"2025-07-25","minDate":"2025-07-25","todayKey":"2025-07-18"}
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 08:38:29] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 08:38:29] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 08:38:29] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 08:38:29] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 08:38:30] [FRONTEND] [LOG] 🔍 [detectDropDateFromPosition] Analyse position pour employé test-employee
[18/07/2025 08:38:30] [FRONTEND] [LOG] 📍 [detectDropDateFromPosition] Position souris: (500, 300)
[18/07/2025 08:38:30] [FRONTEND] [LOG] ✅ [detectDropDateFromPosition] Date calculée via position: 2025-07-14 (jour 1)
[18/07/2025 08:38:30] [FRONTEND] [LOG] ✅ [DATE-TEST] Date détectée: 2025-07-14
[18/07/2025 08:38:30] [FRONTEND] [LOG] ✅ [DATE-TEST] Format de date valide
[18/07/2025 08:38:30] [FRONTEND] [LOG] ✅ [DATE-TEST] Date raisonnable (4 jours de différence)
[18/07/2025 08:38:30] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 08:38:30] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 08:38:30] [FRONTEND] [LOG] ✅ Logique de fork avec dates: PASSÉ
[18/07/2025 08:38:30] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 08:38:30] [FRONTEND] [LOG] ✅ Détection de date avec timezone: PASSÉ
[18/07/2025 08:38:30] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 08:38:30] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 08:38:30] [FRONTEND] [LOG] ✅ [DATE-TEST] Date configurée dans le modal: 2025-07-25
[18/07/2025 08:38:30] [FRONTEND] [LOG] 🧪 [DATE-TEST] Simulation du processus de fork...
[18/07/2025 08:38:30] [FRONTEND] [LOG] ✅ [DATE-TEST] Date de test correcte: 2025-07-25
[18/07/2025 08:38:30] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 08:38:30] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 08:38:30] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 2
[18/07/2025 08:38:30] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Test responsivité 0305b6b2-9802-4a0e-b257-eabcc243c705: {"hasMouseEvents":false,"isVisible":true,"hasCorrectCursor":true,"hasValidDimensions":true,"rect":{"width":197.85000610351562,"height":6}}
[18/07/2025 08:38:30] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Test responsivité 0305b6b2-9802-4a0e-b257-eabcc243c705: {"hasMouseEvents":false,"isVisible":true,"hasCorrectCursor":true,"hasValidDimensions":true,"rect":{"width":197.85000610351562,"height":6}}
[18/07/2025 08:38:30] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":2,"regularShifts":2,"gripsInDOM":2,"gripsWithEvents":0,"gripsResponsive":2,"issues":[]}