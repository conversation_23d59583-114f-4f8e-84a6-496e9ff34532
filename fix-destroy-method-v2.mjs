#!/usr/bin/env node

/**
 * Script robuste pour corriger la méthode destroy dans _preloadedRange
 */

import fs from 'fs';

console.log('🔧 [FIX-DESTROY-V2] Correction robuste de la méthode destroy...');

const filePath = './src/teamCalendarApp.ts';
let lines = fs.readFileSync(filePath, 'utf8').split('\n');

console.log('📝 [FIX-DESTROY-V2] Analyse du fichier ligne par ligne...');

let inPreloadedRange = false;
let inDestroyMethod = false;
let destroyMethodLines = [];
let correctedLines = [];
let destroyMethodAdded = false;

for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Détecter le début de _preloadedRange
    if (line.includes('_preloadedRange:') && line.includes('start: Date | null')) {
        console.log(`🔍 [FIX-DESTROY-V2] Ligne ${i + 1}: Début de _preloadedRange détecté`);
        inPreloadedRange = true;
        // Remplacer par la version corrigée
        correctedLines.push('  _preloadedRange: { start: null, end: null },');
        continue;
    }
    
    // Détecter le début de la méthode destroy
    if (inPreloadedRange && line.includes('destroy: function()')) {
        console.log(`🔍 [FIX-DESTROY-V2] Ligne ${i + 1}: Début de destroy détecté`);
        inDestroyMethod = true;
        // Commencer à collecter les lignes de destroy
        destroyMethodLines.push(line);
        continue;
    }
    
    // Collecter les lignes de destroy
    if (inDestroyMethod) {
        destroyMethodLines.push(line);
        
        // Détecter la fin de la méthode destroy
        if (line.includes('console.log(\'✅ [destroy] Nettoyage terminé\');')) {
            console.log(`🔍 [FIX-DESTROY-V2] Ligne ${i + 1}: Fin de destroy détectée`);
            inDestroyMethod = false;
            inPreloadedRange = false;
            // Ne pas ajouter ces lignes au fichier corrigé
            continue;
        }
        
        // Détecter la fin de _preloadedRange
        if (line.includes('},') && !inDestroyMethod) {
            console.log(`🔍 [FIX-DESTROY-V2] Ligne ${i + 1}: Fin de _preloadedRange détectée`);
            inPreloadedRange = false;
            continue;
        }
        
        // Continuer à ignorer les lignes de destroy
        continue;
    }
    
    // Si on est dans _preloadedRange mais pas dans destroy, ignorer
    if (inPreloadedRange) {
        continue;
    }
    
    // Ajouter la ligne normale
    correctedLines.push(line);
}

// Ajouter la méthode destroy au niveau principal
console.log('📝 [FIX-DESTROY-V2] Ajout de destroy au niveau principal...');

const destroyMethod = [
  '',
  '  destroy: function() {',
  '    console.log(\'🧹 [destroy] Nettoyage de l\\\'instance TeamCalendarApp...\');',
  '    // Supprimer les listeners',
  '    if (this._centralizedDragOverHandler) {',
  '      document.removeEventListener(\'dragover\', this._centralizedDragOverHandler);',
  '    }',
  '    if (this._centralizedDropHandler) {',
  '      document.removeEventListener(\'drop\', this._centralizedDropHandler);',
  '    }',
  '    // Nettoyer les éléments DOM',
  '    if (this.elements.employeeListContainer) {',
  '      this.elements.employeeListContainer.innerHTML = \'\';',
  '    }',
  '    // Réinitialiser les flags',
  '    this._eventListenersAttached = false;',
  '    this._attachRetryCount = 0;',
  '    this.config._isInitialized = false;',
  '    console.log(\'✅ [destroy] Nettoyage terminé\');',
  '  },',
  ''
];

// Chercher un bon endroit pour insérer destroy (après attachAllEventListeners)
let insertIndex = -1;
for (let i = 0; i < correctedLines.length; i++) {
    if (correctedLines[i].includes('attachAllEventListeners: function()')) {
        // Trouver la fin de la fonction attachAllEventListeners
        let braceCount = 0;
        let j = i;
        while (j < correctedLines.length) {
            const line = correctedLines[j];
            if (line.includes('{')) braceCount++;
            if (line.includes('}')) braceCount--;
            if (braceCount === 0 && line.includes('},')) {
                insertIndex = j + 1;
                break;
            }
            j++;
        }
        break;
    }
}

if (insertIndex === -1) {
    // Fallback: chercher après render
    for (let i = 0; i < correctedLines.length; i++) {
        if (correctedLines[i].includes('render: function()')) {
            let braceCount = 0;
            let j = i;
            while (j < correctedLines.length) {
                const line = correctedLines[j];
                if (line.includes('{')) braceCount++;
                if (line.includes('}')) braceCount--;
                if (braceCount === 0 && line.includes('},')) {
                    insertIndex = j + 1;
                    break;
                }
                j++;
            }
            break;
        }
    }
}

if (insertIndex !== -1) {
    console.log(`✅ [FIX-DESTROY-V2] Insertion de destroy à la ligne ${insertIndex + 1}`);
    correctedLines.splice(insertIndex, 0, ...destroyMethod);
    destroyMethodAdded = true;
} else {
    console.log('❌ [FIX-DESTROY-V2] Impossible de trouver un endroit pour insérer destroy');
}

// Écrire le fichier corrigé
const correctedContent = correctedLines.join('\n');
fs.writeFileSync(filePath, correctedContent);

console.log('✅ [FIX-DESTROY-V2] Correction terminée !');
console.log('\n📋 RÉSUMÉ DES CORRECTIONS :');
console.log(`✅ 1. _preloadedRange corrigé : { start: null, end: null }`);
if (destroyMethodAdded) {
    console.log('✅ 2. Méthode destroy ajoutée au niveau principal');
} else {
    console.log('❌ 2. Méthode destroy non ajoutée');
}

console.log('\n🚀 Prochaines étapes :');
console.log('1. Redémarrer le serveur : npm run dev');
console.log('2. Recharger la page (Ctrl+F5)');
console.log('3. Tester les fonctions de diagnostic DOM');

console.log('\n✅ [FIX-DESTROY-V2] Script terminé !'); 