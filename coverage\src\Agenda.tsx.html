
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/Agenda.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> / <a href="index.html">src</a> Agenda.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/547</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/547</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" >import React, { useEffect, useRef } from 'react'</span></span></span>
<span class="cstat-no" title="statement not covered" >import TeamCalendarApp from './teamCalendarApp.ts'</span>
<span class="cstat-no" title="statement not covered" >import './sidebar.css'</span>
<span class="cstat-no" title="statement not covered" >import './styles/fullscreen.css'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >interface AgendaProps {}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >const AgendaFullscreen: React.FC&lt;AgendaProps&gt; = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const appRef = useRef&lt;any&gt;(null)</span>
<span class="cstat-no" title="statement not covered" >  const initRef = useRef(false)</span>
<span class="cstat-no" title="statement not covered" >  const mountedRef = useRef(true)</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    // ✅ Protection contre la double initialisation MAIS permettre la réinitialisation si nécessaire</span>
<span class="cstat-no" title="statement not covered" >    if (initRef.current &amp;&amp; (window as any).team?.employees?.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      console.warn('⚠️ [Agenda] Initialisation déjà effectuée avec succès - IGNORÉ');</span>
<span class="cstat-no" title="statement not covered" >      return;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    if (initRef.current) {</span>
<span class="cstat-no" title="statement not covered" >      console.log('🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)');</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    initRef.current = true;</span>
<span class="cstat-no" title="statement not covered" >    mountedRef.current = true;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const initApp = async () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      try {</span>
<span class="cstat-no" title="statement not covered" >        console.log('🚀 [Agenda] Initialisation TeamCalendarApp...');</span>
<span class="cstat-no" title="statement not covered" >        await TeamCalendarApp.init()</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        if (mountedRef.current) {</span>
<span class="cstat-no" title="statement not covered" >          console.log('🎉 [Agenda] Application fullscreen initialisée avec succès')</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      } catch (error) {</span>
<span class="cstat-no" title="statement not covered" >        if (mountedRef.current) {</span>
<span class="cstat-no" title="statement not covered" >          console.error('❌ [Agenda] Erreur lors de l\'initialisation:', error)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const timer = setTimeout(initApp, 200)</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    return () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      mountedRef.current = false;</span>
<span class="cstat-no" title="statement not covered" >      clearTimeout(timer);</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      // ✅ Log du nettoyage - TeamCalendarApp gère son propre cleanup</span>
<span class="cstat-no" title="statement not covered" >      console.log('🧹 [Agenda] Nettoyage composant Agenda');</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }, [])</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    return () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      mountedRef.current = false;</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" >  }, []);</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="fullscreen-app h-screen w-screen overflow-hidden bg-gradient-to-br from-slate-100 to-sky-100 text-slate-900 flex" </span>
<span class="cstat-no" title="statement not covered" >         style={{fontFamily: 'Inter, "Noto Sans", sans-serif'}}&gt;</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      {/* SIDEBAR FIXE RÉTRACTABLE */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;aside className="sidebar-fixed group fixed left-0 top-0 h-full bg-white/95 backdrop-blur-md border-r border-slate-200/60 shadow-2xl z-50 w-16 hover:w-64 transition-all duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="sidebar-content flex flex-col h-full"&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* Logo/Brand */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="sidebar-brand h-16 flex items-center justify-center border-b border-slate-200/50 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="size-8 text-blue-600 group-hover:scale-110 transition-transform duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;path d="M44 4H30.6666V17.3334H17.3334V30.6666H4V44H44V4Z" fill="currentColor" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;span className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 font-bold text-slate-800 whitespace-nowrap"&gt;</span>
<span class="cstat-no" title="statement not covered" >              TeamCalendar</span>
<span class="cstat-no" title="statement not covered" >            &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* Navigation */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;nav className="sidebar-nav flex-1 p-2 space-y-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;a href="#calendar" className="sidebar-link active flex items-center p-3 rounded-xl bg-blue-100/80 text-blue-700 shadow-md"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="material-icons-outlined text-xl"&gt;calendar_view_week&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap font-medium"&gt;Calendrier&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            &lt;a href="#employees" className="sidebar-link flex items-center p-3 rounded-xl text-slate-600 hover:bg-slate-100/80 hover:text-slate-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="material-icons-outlined text-xl"&gt;people&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap font-medium"&gt;Employés&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            &lt;a href="/logs" className="sidebar-link flex items-center p-3 rounded-xl text-slate-600 hover:bg-slate-100/80 hover:text-slate-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="material-icons-outlined text-xl"&gt;bug_report&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap font-medium"&gt;Logs&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            &lt;button id="sidebar-settings-btn" className="sidebar-link flex items-center p-3 rounded-xl text-slate-600 hover:bg-slate-100/80 hover:text-slate-800 w-full text-left"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="material-icons-outlined text-xl"&gt;settings&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap font-medium"&gt;Paramètres&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/nav&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* Pied de sidebar */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="sidebar-footer p-4 border-t border-slate-200/50 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-full size-8 flex items-center justify-center text-white font-bold text-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                A</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-sm font-medium text-slate-700 whitespace-nowrap"&gt;Admin&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-xs text-slate-500 whitespace-nowrap"&gt;En ligne&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/aside&gt;</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      {/* CONTENU PRINCIPAL AVEC MARGE SIDEBAR */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="main-content flex flex-col w-full h-full ml-16"&gt;</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        {/* HEADER FIXE */}</span>
<span class="cstat-no" title="statement not covered" >        &lt;header className="header-fixed h-16 flex items-center justify-between px-6 bg-white/95 backdrop-blur-md border-b border-slate-300/60 shadow-sm flex-shrink-0 z-40"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex items-center gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;h2 className="text-slate-900 text-xl font-semibold"&gt;Calendrier d'Équipe&lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex items-center gap-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >            {/* Navigation temporelle */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;nav className="hidden md:flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button className="nav-btn text-slate-600 hover:text-blue-600 text-sm font-medium px-3 py-2 rounded-lg hover:bg-slate-200/70"&gt;Aujourd'hui&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button className="nav-btn text-blue-600 bg-blue-100/80 text-sm font-semibold px-3 py-2 rounded-lg shadow-sm"&gt;Semaine&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button className="nav-btn text-slate-600 hover:text-blue-600 text-sm font-medium px-3 py-2 rounded-lg hover:bg-slate-200/70"&gt;Mois&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/nav&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            {/* Actions header */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="settings-btn" className="header-btn flex items-center justify-center rounded-lg h-10 w-10 text-slate-500 hover:bg-slate-200/70 hover:text-slate-700"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="material-icons-outlined text-xl"&gt;settings&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-full size-10 flex items-center justify-center text-white font-bold"&gt;</span>
<span class="cstat-no" title="statement not covered" >                A</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/header&gt;</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        {/* ZONE DE CONTENU COMPARTIMENTÉE */}</span>
<span class="cstat-no" title="statement not covered" >        &lt;main className="content-area flex-1 flex flex-col overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* BARRE DE CONTRÔLES FIXE */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;section className="controls-bar h-20 flex items-center justify-between px-6 py-4 bg-white/80 backdrop-blur-sm border-b border-slate-200/40 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="prev-week-btn" className="control-btn p-2.5 rounded-full hover:bg-slate-200/80 text-slate-500 hover:text-slate-700"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="material-icons-outlined text-xl"&gt;chevron_left&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;h2 id="current-week-display" className="week-display text-slate-900 text-lg font-semibold min-w-[300px] text-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >                </span>
<span class="cstat-no" title="statement not covered" >              &lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="next-week-btn" className="control-btn p-2.5 rounded-full hover:bg-slate-200/80 text-slate-500 hover:text-slate-700"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="material-icons-outlined text-xl"&gt;chevron_right&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button className="action-btn flex items-center gap-2 text-slate-600 bg-white hover:bg-slate-50 border border-slate-300 font-medium py-2 px-4 rounded-lg text-sm shadow-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="material-icons-outlined text-lg"&gt;upload&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                Exporter</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="add-shift-button" className="action-btn flex items-center gap-2 text-white bg-blue-600 hover:bg-blue-700 font-medium py-2 px-4 rounded-lg text-sm shadow-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="material-icons-outlined text-lg"&gt;add&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                Ajouter shift</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/section&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* GRILLE CALENDRIER ADAPTATIVE */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;section className="calendar-grid flex-1 overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="calendar-container h-full border border-slate-300/40 rounded-lg shadow-lg bg-white/70 mx-6 mb-6" id="schedule-container"&gt;</span>
<span class="cstat-no" title="statement not covered" >              </span>
<span class="cstat-no" title="statement not covered" >              {/* GRILLE UNIFIÉE : EMPLOYÉS + JOURS DANS LA MÊME STRUCTURE */}</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="calendar-unified-grid h-full overflow-hidden" id="schedule-grid-scrollable-content"&gt;</span>
<span class="cstat-no" title="statement not covered" >                </span>
<span class="cstat-no" title="statement not covered" >                {/* HEADER ROW : Employé(e) + Jours de la semaine */}</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="calendar-header grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-300/40 bg-slate-50/80 h-16 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  {/* Header Employé */}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="header-employee flex items-center justify-start pl-6 border-r border-slate-300/40"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;p className="text-slate-700 text-sm font-semibold tracking-wider"&gt;Employé(e)&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  </span>
<span class="cstat-no" title="statement not covered" >                  {/* Headers Jours - Ces éléments seront remplis par JavaScript */}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div id="day-header-0" className="header-day flex items-center justify-center border-r border-slate-300/40 text-xs font-semibold text-slate-600"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div id="day-header-1" className="header-day flex items-center justify-center border-r border-slate-300/40 text-xs font-semibold text-slate-600"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div id="day-header-2" className="header-day flex items-center justify-center border-r border-slate-300/40 text-xs font-semibold text-slate-600"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div id="day-header-3" className="header-day flex items-center justify-center border-r border-slate-300/40 text-xs font-semibold text-slate-600"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div id="day-header-4" className="header-day flex items-center justify-center border-r border-slate-300/40 text-xs font-semibold text-slate-600"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div id="day-header-5" className="header-day flex items-center justify-center border-r border-slate-300/40 text-xs font-semibold text-slate-600"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div id="day-header-6" className="header-day flex items-center justify-center text-xs font-semibold text-slate-600 border-r border-slate-300/40"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                </span>
<span class="cstat-no" title="statement not covered" >                {/* SECTION EMPLOYÉS : Chaque ligne = employé + ses 7 colonnes de jours */}</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="employees-section flex-1 overflow-y-auto min-h-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div id="employee-rows-container" className="space-y-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {/* Les lignes d'employés seront injectées ici par JavaScript */}</span>
<span class="cstat-no" title="statement not covered" >                    {/* Structure attendue pour chaque employé :</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div class="employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px]"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;div class="employee-info"&gt;Info employé&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;div class="day-cell"&gt;Cellule jour 0&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;div class="day-cell"&gt;Cellule jour 1&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                      ...etc</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    */}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                </span>
<span class="cstat-no" title="statement not covered" >                {/* SECTION POSTES DISPONIBLES : Remplacée par la grille unifiée JavaScript */}</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="posts-section border-t border-slate-200 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  {/* Header Postes */}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="posts-header grid grid-cols-[240px_repeat(7,1fr)] h-14 border-b border-slate-300/40 bg-slate-50/80"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="posts-title flex items-center pl-6 border-r border-slate-300/40"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;h3 className="text-slate-600 text-sm font-semibold tracking-wider"&gt;Postes disponibles&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {/* Colonnes vides pour les jours */}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="border-r border-slate-300/40"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="border-r border-slate-300/40"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="border-r border-slate-300/40"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="border-r border-slate-300/40"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="border-r border-slate-300/40"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="border-r border-slate-300/40"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="border-r border-slate-300/40"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  </span>
<span class="cstat-no" title="statement not covered" >                  {/* ✅ NOUVELLE GRILLE : Liste des postes avec grille complète (générée par renderUnifiedCalendar) */}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="posts-list grid grid-cols-[240px_repeat(7,1fr)] max-h-64 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {/* Le contenu sera entièrement remplacé par JavaScript renderUnifiedCalendar() */}</span>
<span class="cstat-no" title="statement not covered" >                    </span>
<span class="cstat-no" title="statement not covered" >                    {/* ✅ CONTENEUR VISIBLE : Maintenu pour compatibilité drag &amp; drop avec diagnostic */}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div id="available-posts-container" className="col-span-8 h-0 overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      {/* Les éléments draggables simples seront injectés ici pour le drag &amp; drop */}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                </span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/section&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* BARRE DE STATUTS FIXE EN BAS */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;section className="stats-bar h-16 px-6 py-3 bg-white/80 backdrop-blur-sm border-t border-slate-200/40 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div id="stats-output" className="text-sm text-slate-600 flex items-center"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/section&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/main&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      {/* MODAL PARAMÈTRES COMPLET */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;div id="settings-modal" className="hidden fixed inset-0 bg-black/40 backdrop-blur-sm z-50 flex items-center justify-center p-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl h-[90vh] flex flex-col"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;header className="flex items-center justify-between p-4 border-b border-slate-200 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="material-icons-outlined text-blue-600 text-2xl"&gt;settings&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;h3 className="text-lg font-semibold text-slate-800"&gt;Paramètres de l'Application&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;button id="settings-modal-close" className="p-2 rounded-full hover:bg-slate-200/70"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="material-icons-outlined text-slate-600"&gt;close&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/header&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="p-2 border-b border-slate-200 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex gap-2 flex-wrap"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="tab-posts" className="tab-btn active px-4 py-2 rounded-lg text-sm font-medium"&gt;Postes&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="tab-vacations" className="tab-btn px-4 py-2 rounded-lg text-sm font-medium"&gt;Vacances&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="tab-assignments" className="tab-btn px-4 py-2 rounded-lg text-sm font-medium"&gt;Attributions&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="tab-employee-templates" className="tab-btn px-4 py-2 rounded-lg text-sm font-medium"&gt;🗂️ Modèle de fiche&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="tab-employees" className="tab-btn px-4 py-2 rounded-lg text-sm font-medium"&gt;👤 Gestion employés&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="tab-general" className="tab-btn px-4 py-2 rounded-lg text-sm font-medium"&gt;Général&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* Contenu modal adaptatif */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex-1 overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            {/* Onglet Postes */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div id="tab-content-posts" className="tab-content h-full p-4 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div id="posts-config-container" className="space-y-3 mb-4"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="add-post-btn" className="w-full flex items-center justify-center gap-2 text-white bg-blue-600 hover:bg-blue-700 font-medium py-2.5 px-4 rounded-xl text-sm shadow-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="material-icons-outlined text-xl"&gt;add&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                Ajouter un nouveau poste</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            {/* Onglet Vacances */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div id="tab-content-vacations" className="tab-content hidden h-full p-4 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="mb-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;h4 className="font-medium text-slate-700 mb-3"&gt;Périodes de Vacances Globales&lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div id="global-vacations-list" className="space-y-2 mb-4"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;button id="add-vacation-btn" className="w-full flex items-center justify-center gap-2 text-white bg-green-600 hover:bg-green-700 font-medium py-2.5 px-4 rounded-xl text-sm shadow-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="material-icons-outlined text-xl"&gt;add&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  Ajouter une période</span>
<span class="cstat-no" title="statement not covered" >                &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            {/* Onglet Attributions */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div id="tab-content-assignments" className="tab-content hidden h-full p-4 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="mb-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;h4 className="font-medium text-slate-700 mb-3"&gt;Attributions Automatiques&lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div id="regular-assignments-list" className="space-y-2 mb-4"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="flex gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;button id="add-assignment-btn" className="flex-1 flex items-center justify-center gap-2 text-white bg-purple-600 hover:bg-purple-700 font-medium py-2.5 px-4 rounded-xl text-sm shadow-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span className="material-icons-outlined text-xl"&gt;add&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    Ajouter une attribution</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;button id="remove-assignments-btn" className="flex-1 flex items-center justify-center gap-2 text-white bg-red-600 hover:bg-red-700 font-medium py-2.5 px-4 rounded-xl text-sm shadow-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span className="material-icons-outlined text-xl"&gt;delete_sweep&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    Retirer attributions</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            {/* Onglet Modèle de fiche employé */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div id="tab-content-employee-templates" className="tab-content hidden h-full p-4 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="space-y-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="flex items-center justify-between"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;h4 className="font-medium text-slate-700 flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="material-icons-outlined text-blue-600"&gt;description&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                      Modèles de Fiches Employé</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;p className="text-sm text-slate-500 mt-1"&gt;Configurez les champs disponibles pour les fiches employé&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;button id="add-template-btn" className="flex items-center gap-2 text-white bg-blue-600 hover:bg-blue-700 font-medium py-2 px-4 rounded-lg text-sm shadow-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span className="material-icons-outlined text-lg"&gt;add&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    Nouveau modèle</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div id="employee-templates-list" className="space-y-3"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            {/* Onglet Gestion des employés */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div id="tab-content-employees" className="tab-content hidden h-full p-4 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="space-y-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="flex items-center justify-between"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;h4 className="font-medium text-slate-700 flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="material-icons-outlined text-green-600"&gt;people&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                      Gestion des Employés</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;p className="text-sm text-slate-500 mt-1"&gt;Modifiez les informations personnelles et avatars de vos employés&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;button id="add-employee-btn" className="flex items-center gap-2 text-white bg-green-600 hover:bg-green-700 font-medium py-2 px-4 rounded-lg text-sm shadow-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span className="material-icons-outlined text-lg"&gt;person_add&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    Nouvel employé</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="mb-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;label className="block text-sm font-medium text-slate-700 mb-2"&gt;Modèle de fiche à utiliser&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;select id="template-selector" className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"&gt;&lt;/select&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div id="employees-management-list" className="space-y-3"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            {/* Onglet Paramètres Généraux */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div id="tab-content-general" className="tab-content hidden h-full p-4 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="space-y-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;h4 className="font-medium text-slate-700 mb-3 flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span className="material-icons-outlined text-blue-600"&gt;calendar_view_week&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    Affichage de la Semaine</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="bg-slate-50 p-4 rounded-lg space-y-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;label className="block text-sm font-medium text-slate-700 mb-2"&gt;Premier jour de la semaine&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div id="week-start-container" className="grid grid-cols-4 gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;label className="flex items-center gap-2 cursor-pointer"&gt;&lt;input type="radio" name="week-start" value="monday" className="text-blue-600 focus:ring-blue-500" /&gt; &lt;span className="text-sm"&gt;Lundi&lt;/span&gt;&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;label className="flex items-center gap-2 cursor-pointer"&gt;&lt;input type="radio" name="week-start" value="tuesday" className="text-blue-600 focus:ring-blue-500" /&gt; &lt;span className="text-sm"&gt;Mardi&lt;/span&gt;&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;label className="flex items-center gap-2 cursor-pointer"&gt;&lt;input type="radio" name="week-start" value="wednesday" className="text-blue-600 focus:ring-blue-500" /&gt; &lt;span className="text-sm"&gt;Mercredi&lt;/span&gt;&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;label className="flex items-center gap-2 cursor-pointer"&gt;&lt;input type="radio" name="week-start" value="thursday" className="text-blue-600 focus:ring-blue-500" /&gt; &lt;span className="text-sm"&gt;Jeudi&lt;/span&gt;&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;label className="flex items-center gap-2 cursor-pointer"&gt;&lt;input type="radio" name="week-start" value="friday" className="text-blue-600 focus:ring-blue-500" /&gt; &lt;span className="text-sm"&gt;Vendredi&lt;/span&gt;&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;label className="flex items-center gap-2 cursor-pointer"&gt;&lt;input type="radio" name="week-start" value="saturday" className="text-blue-600 focus:ring-blue-500" /&gt; &lt;span className="text-sm"&gt;Samedi&lt;/span&gt;&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;label className="flex items-center gap-2 cursor-pointer"&gt;&lt;input type="radio" name="week-start" value="sunday" className="text-blue-600 focus:ring-blue-500" defaultChecked /&gt; &lt;span className="text-sm"&gt;Dimanche&lt;/span&gt;&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;p className="text-xs text-slate-500 mt-2"&gt;Modifie l'ordre d'affichage des jours dans le calendrier&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                </span>
<span class="cstat-no" title="statement not covered" >                &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;h4 className="font-medium text-slate-700 mb-3 flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span className="material-icons-outlined text-green-600"&gt;storage&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    Données de l'Application</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="bg-slate-50 p-4 rounded-lg space-y-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;button id="export-data-btn" className="flex-1 flex items-center justify-center gap-2 text-blue-600 bg-blue-50 hover:bg-blue-100 border border-blue-200 font-medium py-2.5 px-4 rounded-lg text-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;span className="material-icons-outlined text-lg"&gt;download&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                        Exporter</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;button id="import-data-btn" className="flex-1 flex items-center justify-center gap-2 text-green-600 bg-green-50 hover:bg-green-100 border border-green-200 font-medium py-2.5 px-4 rounded-lg text-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;span className="material-icons-outlined text-lg"&gt;upload&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                        Importer</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;button id="reset-data-btn" className="w-full flex items-center justify-center gap-2 text-red-600 bg-red-50 hover:bg-red-100 border border-red-200 font-medium py-2.5 px-4 rounded-lg text-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="material-icons-outlined text-lg"&gt;refresh&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                      Réinitialiser toutes les données</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;p className="text-xs text-slate-500"&gt;Les données sont sauvegardées automatiquement dans votre navigateur&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      {/* Modal pour le menu d'attribution lors du drop */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;div id="assignment-context-modal" className="hidden fixed inset-0 bg-black/40 backdrop-blur-sm z-50 flex items-center justify-center p-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="bg-white rounded-2xl shadow-2xl w-full max-w-md transform transition-all duration-300 ease-in-out"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;header className="flex items-center justify-between p-4 border-b border-slate-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="material-icons-outlined text-blue-600 text-2xl"&gt;schedule&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;h3 className="text-lg font-semibold text-slate-800"&gt;Type d'Attribution&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;button id="assignment-context-close" className="p-2 rounded-full hover:bg-slate-200/70"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="material-icons-outlined text-slate-600"&gt;close&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/header&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="p-6 space-y-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="bg-blue-50 p-4 rounded-lg border border-blue-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="flex items-center gap-3 mb-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="size-8 rounded-full bg-blue-100 flex items-center justify-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="material-icons-outlined text-blue-600 text-sm"&gt;person&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;p id="assignment-employee-name" className="text-sm font-medium text-slate-800"&gt;&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;p id="assignment-post-name" className="text-xs text-slate-500"&gt;&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;label className="block text-sm font-medium text-slate-700 mb-3"&gt;Choisissez le type d'attribution :&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="space-y-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;label className="flex items-start gap-3 p-4 rounded-lg border border-slate-200 hover:bg-blue-50 hover:border-blue-300 cursor-pointer transition-all duration-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;input type="radio" name="assignment-type" value="once" className="mt-1 text-blue-600 focus:ring-blue-500" defaultChecked /&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="flex items-start gap-3 flex-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="size-10 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="material-icons-outlined text-green-600 text-lg"&gt;event&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="text-sm font-semibold text-slate-800 flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        Attribution ponctuelle</span>
<span class="cstat-no" title="statement not covered" >                        &lt;span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"&gt;Cette semaine&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p className="text-xs text-slate-500 mt-1 leading-relaxed"&gt;Assigner uniquement pour cette semaine actuelle&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                </span>
<span class="cstat-no" title="statement not covered" >                &lt;label id="single-cell-option" className="hidden flex items-start gap-3 p-4 rounded-lg border border-slate-200 hover:bg-cyan-50 hover:border-cyan-300 cursor-pointer transition-all duration-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;input type="radio" name="assignment-type" value="single-cell" className="mt-1 text-blue-600 focus:ring-blue-500" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="flex items-start gap-3 flex-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="size-10 rounded-full bg-cyan-100 flex items-center justify-center flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="material-icons-outlined text-cyan-600 text-lg"&gt;crop_free&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="text-sm font-semibold text-slate-800 flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        Attribution unique (cellule)</span>
<span class="cstat-no" title="statement not covered" >                        &lt;span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800"&gt;Une cellule&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p className="text-xs text-slate-500 mt-1 leading-relaxed"&gt;Assigner uniquement sur cette cellule spécifique&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                </span>
<span class="cstat-no" title="statement not covered" >                &lt;label className="flex items-start gap-3 p-4 rounded-lg border border-slate-200 hover:bg-purple-50 hover:border-purple-300 cursor-pointer transition-all duration-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;input type="radio" name="assignment-type" value="regular-indefinite" className="mt-1 text-blue-600 focus:ring-blue-500" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="flex items-start gap-3 flex-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="size-10 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="material-icons-outlined text-purple-600 text-lg"&gt;repeat&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="text-sm font-semibold text-slate-800 flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        Attribution régulière permanente</span>
<span class="cstat-no" title="statement not covered" >                        &lt;span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"&gt;Indéfini&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p className="text-xs text-slate-500 mt-1 leading-relaxed"&gt;Assigner automatiquement chaque semaine&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                </span>
<span class="cstat-no" title="statement not covered" >                &lt;label className="flex items-start gap-3 p-4 rounded-lg border border-slate-200 hover:bg-amber-50 hover:border-amber-300 cursor-pointer transition-all duration-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;input type="radio" name="assignment-type" value="regular-limited" className="mt-1 text-blue-600 focus:ring-blue-500" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="flex items-start gap-3 flex-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="size-10 rounded-full bg-amber-100 flex items-center justify-center flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="material-icons-outlined text-amber-600 text-lg"&gt;schedule&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="text-sm font-semibold text-slate-800 flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        Attribution régulière temporaire</span>
<span class="cstat-no" title="statement not covered" >                        &lt;span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800"&gt;Limité&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p className="text-xs text-slate-500 mt-1 leading-relaxed"&gt;Assigner automatiquement jusqu'à une date précise&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            &lt;div id="regular-assignment-options" className="hidden space-y-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;label className="block text-sm font-medium text-slate-700 mb-2"&gt;Date de début de l'attribution&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;input type="date" id="assignment-start-date-context" className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-xs text-slate-500 mt-1"&gt;Par défaut : aujourd'hui&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              </span>
<span class="cstat-no" title="statement not covered" >              &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;label className="block text-sm font-medium text-slate-700 mb-2"&gt;Jours de la semaine&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="grid grid-cols-4 gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;label className="flex items-center gap-2 text-xs"&gt;&lt;input type="checkbox" className="rounded text-blue-600" value="1" /&gt; Lun&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;label className="flex items-center gap-2 text-xs"&gt;&lt;input type="checkbox" className="rounded text-blue-600" value="2" /&gt; Mar&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;label className="flex items-center gap-2 text-xs"&gt;&lt;input type="checkbox" className="rounded text-blue-600" value="3" /&gt; Mer&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;label className="flex items-center gap-2 text-xs"&gt;&lt;input type="checkbox" className="rounded text-blue-600" value="4" /&gt; Jeu&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;label className="flex items-center gap-2 text-xs"&gt;&lt;input type="checkbox" className="rounded text-blue-600" value="5" /&gt; Ven&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;label className="flex items-center gap-2 text-xs"&gt;&lt;input type="checkbox" className="rounded text-blue-600" value="6" /&gt; Sam&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;label className="flex items-center gap-2 text-xs"&gt;&lt;input type="checkbox" className="rounded text-blue-600" value="0" /&gt; Dim&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              </span>
<span class="cstat-no" title="statement not covered" >              &lt;div id="limited-assignment-date" className="hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;label className="block text-sm font-medium text-slate-700 mb-2"&gt;Date de fin&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;input type="date" id="assignment-end-date-context" className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;footer className="p-4 bg-slate-50/80 border-t border-slate-200 rounded-b-2xl flex gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;button id="assignment-context-cancel" className="flex-1 px-4 py-2 text-slate-600 bg-slate-200 hover:bg-slate-300 rounded-lg transition-colors"&gt;Annuler&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;button id="assignment-context-confirm" className="flex-1 px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"&gt;Confirmer&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/footer&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >  )</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >export default AgendaFullscreen </span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-07T17:35:30.510Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    