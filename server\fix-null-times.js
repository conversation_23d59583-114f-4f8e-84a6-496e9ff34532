import { query } from './config/database.js';

async function fixNullTimes() {
    console.log('🔧 [Fix] Correction des templates avec heures NULL...');
    
    try {
        // 1. Identifier les templates avec heures NULL
        const nullTemplates = await query(`
            SELECT id, employee_id, post_id, start_time, end_time
            FROM assignment_templates 
            WHERE start_time IS NULL OR end_time IS NULL
        `);
        
        console.log(`📋 [Fix] ${nullTemplates.rows.length} templates avec heures NULL trouvés`);
        
        if (nullTemplates.rows.length === 0) {
            console.log('✅ [Fix] Aucun template à corriger !');
            return;
        }
        
        // 2. Corriger avec des heures par défaut
        const result = await query(`
            UPDATE assignment_templates 
            SET 
                start_time = COALESCE(start_time, '08:00:00'),
                end_time = COALESCE(end_time, '16:00:00'),
                updated_at = NOW()
            WHERE start_time IS NULL OR end_time IS NULL
        `);
        
        console.log(`✅ [Fix] ${result.rowCount} templates corrigés avec heures par défaut (08:00-16:00)`);
        
        // 3. Vérifier le résultat
        const verification = await query(`
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN start_time IS NULL THEN 1 END) as null_start,
                COUNT(CASE WHEN end_time IS NULL THEN 1 END) as null_end
            FROM assignment_templates
        `);
        
        console.log('📊 [Fix] Vérification finale:');
        console.table(verification.rows);
        
        // 4. Afficher les templates corrigés
        const corrected = await query(`
            SELECT 
                at.id,
                e.name as employee_name,
                p.label as post_label,
                at.type,
                at.day_of_week,
                at.start_time,
                at.end_time
            FROM assignment_templates at
            LEFT JOIN employees e ON at.employee_id = e.id
            LEFT JOIN standard_posts p ON at.post_id = p.id
            WHERE at.is_active = true
            ORDER BY e.name, at.day_of_week
        `);
        
        console.log('\n📋 [Fix] Templates actifs après correction:');
        console.table(corrected.rows);
        
    } catch (error) {
        console.error('❌ [Fix] Erreur:', error);
        throw error;
    }
}

// Exécuter la correction
fixNullTimes()
    .then(() => {
        console.log('\n🎉 [Fix] Correction terminée !');
        process.exit(0);
    })
    .catch((error) => {
        console.error('💥 [Fix] Échec:', error);
        process.exit(1);
    });
