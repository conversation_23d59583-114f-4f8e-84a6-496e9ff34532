#!/usr/bin/env node

/**
 * Script pour nettoyer tous les processus Node.js avant démarrage
 * Évite les conflits de serveurs multiples
 */

import { spawn } from 'child_process';

console.log('🧹 [CLEANUP] Nettoyage des processus Node.js existants...');

const isWindows = process.platform === 'win32';

const killCommand = isWindows 
  ? ['taskkill', ['/F', '/IM', 'node.exe']] 
  : ['pkill', ['-f', 'node']];

const [command, args] = killCommand;

const cleanup = spawn(command, args, { stdio: 'pipe' });

cleanup.stdout.on('data', (data) => {
  const output = data.toString().trim();
  if (output && !output.includes('no task')) {
    console.log(`✅ [CLEANUP] ${output}`);
  }
});

cleanup.stderr.on('data', (data) => {
  const error = data.toString().trim();
  // Ignorer les erreurs "process not found" qui sont normales
  if (error && !error.includes('not found') && !error.includes('No such process')) {
    console.log(`⚠️  [CLEANUP] ${error}`);
  }
});

cleanup.on('close', (code) => {
  if (code === 0 || code === 128) { // 128 = pas de processus trouvé sur Unix
    console.log('✅ [CLEANUP] Nettoyage terminé - Prêt pour le démarrage');
  } else {
    console.log(`⚠️  [CLEANUP] Nettoyage terminé avec le code ${code}`);
  }
  
  console.log('💡 [CLEANUP] Attendez 2 secondes avant de démarrer le système...\n');
  process.exit(0);
}); 