{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["ESNext", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "types": [],

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Configuration ultra-permissive */
    "strict": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": false,
    "esModuleInterop": true, 
    "allowSyntheticDefaultImports": true,
    "noImplicitAny": false,
    "noImplicitReturns": false,
    "noImplicitThis": false,
    "noImplicitOverride": false,
    "noPropertyAccessFromIndexSignature": false,
    "allowJs": true,
    "checkJs": false,
    "exactOptionalPropertyTypes": false,
    "noUncheckedIndexedAccess": false,

  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }],
  "ts-node": {
    "transpileOnly": true,
    "compilerOptions": {
      "strict": false
    }
  }
}
