-- =====================================================
-- MIGRATION 005: ADD EXCLUDED DATES COLUMN
-- Date: 2025-06-20
-- Description: Ajouter le support des dates exclues
--              pour les remplacements ponctuels
-- =====================================================

-- Ajouter la colonne excluded_dates pour les remplacements ponctuels
ALTER TABLE regular_assignments 
ADD COLUMN excluded_dates DATE[] DEFAULT '{}';

-- Ajouter un index pour optimiser les requêtes sur les dates exclues
CREATE INDEX IF NOT EXISTS idx_regular_assignments_excluded_dates 
ON regular_assignments USING GIN(excluded_dates);

-- Commentaire sur la nouvelle colonne
COMMENT ON COLUMN regular_assignments.excluded_dates IS 'Dates exclues de l''attribution régulière (pour remplacements ponctuels)';

-- =====================================================
-- FIN DE LA MIGRATION 005
-- ===================================================== 