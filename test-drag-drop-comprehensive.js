/**
 * 🧪 SCRIPT DE TEST COMPLET POUR DRAG & DROP ATTRIBUTION
 * Tests exhaustifs de la fonctionnalité d'attribution régulière
 */

console.log('🧪 [TEST-DRAG-DROP] Démarrage des tests complets de drag & drop...');

// ✅ CONFIGURATION DES TESTS
const TEST_CONFIG = {
    WAIT_TIME: 2000,
    MAX_RETRIES: 3,
    LOG_LEVEL: 'DEBUG'
};

// ✅ SYSTÈME DE LOGGING AVANCÉ
const TestLogger = {
    logs: [],
    
    log(level, message, data = null) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level,
            message,
            data: data ? JSON.stringify(data, null, 2) : null
        };
        
        this.logs.push(logEntry);
        
        const emoji = {
            'DEBUG': '🔍',
            'INFO': 'ℹ️',
            'SUCCESS': '✅',
            'WARNING': '⚠️',
            'ERROR': '❌',
            'CRITICAL': '🚨'
        }[level] || '📝';
        
        console.log(`${emoji} [TEST-DRAG-DROP] ${message}`, data || '');
    },
    
    debug(message, data) { this.log('DEBUG', message, data); },
    info(message, data) { this.log('INFO', message, data); },
    success(message, data) { this.log('SUCCESS', message, data); },
    warning(message, data) { this.log('WARNING', message, data); },
    error(message, data) { this.log('ERROR', message, data); },
    critical(message, data) { this.log('CRITICAL', message, data); },
    
    exportLogs() {
        return {
            summary: {
                total: this.logs.length,
                byLevel: this.logs.reduce((acc, log) => {
                    acc[log.level] = (acc[log.level] || 0) + 1;
                    return acc;
                }, {})
            },
            logs: this.logs
        };
    }
};

// ✅ TESTS DE PRÉREQUIS
async function testPrerequisites() {
    TestLogger.info('Test 1: Vérification des prérequis');
    
    const checks = [
        {
            name: 'TeamCalendarApp disponible',
            test: () => window.teamCalendarApp !== undefined,
            critical: true
        },
        {
            name: 'modalFunctionalities disponible',
            test: () => window.modalFunctionalities !== undefined,
            critical: true
        },
        {
            name: 'Fonctions de drag & drop présentes',
            test: () => {
                const app = window.teamCalendarApp;
                return app.detectDropDateFromPosition && 
                       app.showConfirmationMenu && 
                       app.handleRegularAssignmentDrop;
            },
            critical: true
        },
        {
            name: 'Modal d\'attribution présent',
            test: () => document.getElementById('assignment-context-modal') !== null,
            critical: false
        },
        {
            name: 'Postes disponibles',
            test: () => {
                const app = window.teamCalendarApp;
                return app.data && app.data.posts && app.data.posts.length > 0;
            },
            critical: true
        },
        {
            name: 'Employés disponibles',
            test: () => {
                const app = window.teamCalendarApp;
                return app.data && app.data.employees && app.data.employees.length > 0;
            },
            critical: true
        }
    ];
    
    let passedChecks = 0;
    let criticalFailures = 0;
    
    for (const check of checks) {
        try {
            const result = check.test();
            if (result) {
                TestLogger.success(`✓ ${check.name}`);
                passedChecks++;
            } else {
                if (check.critical) {
                    TestLogger.critical(`✗ ${check.name} (CRITIQUE)`);
                    criticalFailures++;
                } else {
                    TestLogger.warning(`✗ ${check.name} (non critique)`);
                }
            }
        } catch (error) {
            TestLogger.error(`✗ ${check.name} - Erreur: ${error.message}`);
            if (check.critical) criticalFailures++;
        }
    }
    
    TestLogger.info(`Prérequis: ${passedChecks}/${checks.length} réussis, ${criticalFailures} échecs critiques`);
    
    return {
        passed: passedChecks,
        total: checks.length,
        criticalFailures,
        canProceed: criticalFailures === 0
    };
}

// ✅ TEST DE DÉTECTION DE DATE
async function testDateDetection() {
    TestLogger.info('Test 2: Détection de date depuis position');
    
    const app = window.teamCalendarApp;
    if (!app.detectDropDateFromPosition) {
        TestLogger.critical('Fonction detectDropDateFromPosition manquante');
        return { success: false, error: 'Fonction manquante' };
    }
    
    // Trouver une cellule de calendrier
    const calendarCells = document.querySelectorAll('[data-date], [data-day-key]');
    TestLogger.debug(`Cellules trouvées: ${calendarCells.length}`);
    
    if (calendarCells.length === 0) {
        TestLogger.warning('Aucune cellule de calendrier trouvée');
        return { success: false, error: 'Pas de cellules' };
    }
    
    let successfulDetections = 0;
    const testResults = [];
    
    // Tester sur les 3 premières cellules
    for (let i = 0; i < Math.min(3, calendarCells.length); i++) {
        const cell = calendarCells[i];
        const rect = cell.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        try {
            const detectedDate = app.detectDropDateFromPosition(centerX, centerY);
            const expectedDate = cell.dataset.date || cell.dataset.dayKey;
            
            const result = {
                cellIndex: i,
                position: { x: centerX, y: centerY },
                expected: expectedDate,
                detected: detectedDate,
                success: detectedDate === expectedDate
            };
            
            testResults.push(result);
            
            if (result.success) {
                TestLogger.success(`Détection réussie: ${detectedDate}`);
                successfulDetections++;
            } else {
                TestLogger.error(`Détection échouée: attendu ${expectedDate}, obtenu ${detectedDate}`);
            }
            
        } catch (error) {
            TestLogger.error(`Erreur détection cellule ${i}: ${error.message}`);
            testResults.push({
                cellIndex: i,
                error: error.message,
                success: false
            });
        }
    }
    
    const successRate = successfulDetections / testResults.length;
    TestLogger.info(`Détection de date: ${successfulDetections}/${testResults.length} réussies (${Math.round(successRate * 100)}%)`);
    
    return {
        success: successRate > 0.5,
        successRate,
        results: testResults
    };
}

// ✅ TEST D'OUVERTURE DU MODAL
async function testModalOpening() {
    TestLogger.info('Test 3: Ouverture du modal d\'attribution');
    
    const app = window.teamCalendarApp;
    const modalFunctions = window.modalFunctionalities;
    
    if (!modalFunctions || !modalFunctions.openAssignmentContextModal) {
        TestLogger.critical('modalFunctionalities.openAssignmentContextModal manquante');
        return { success: false, error: 'Fonction manquante' };
    }
    
    // Préparer des données de test
    const testData = {
        postData: app.data.posts[0] || { id: 'test-post', label: 'Test Post' },
        employeeId: app.data.employees[0]?.id || 'test-employee',
        employeeName: app.data.employees[0]?.name || 'Test Employee',
        dateKey: '2024-01-15',
        position: { x: 100, y: 100 }
    };
    
    TestLogger.debug('Données de test:', testData);
    
    try {
        // Tenter d'ouvrir le modal
        modalFunctions.openAssignmentContextModal(testData);
        
        // Attendre un peu pour que le modal s'affiche
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Vérifier si le modal est visible
        const modal = document.getElementById('assignment-context-modal');
        const isVisible = modal && !modal.classList.contains('hidden');
        
        if (isVisible) {
            TestLogger.success('Modal ouvert avec succès');
            
            // Fermer le modal pour nettoyer
            const closeBtn = modal.querySelector('[data-action="close"]') || 
                           modal.querySelector('.close') ||
                           modal.querySelector('[onclick*="close"]');
            
            if (closeBtn) {
                closeBtn.click();
                TestLogger.debug('Modal fermé automatiquement');
            }
            
            return { success: true, modal: modal };
        } else {
            TestLogger.error('Modal non visible après ouverture');
            return { success: false, error: 'Modal non visible' };
        }
        
    } catch (error) {
        TestLogger.error(`Erreur ouverture modal: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// ✅ TEST DE WORKFLOW COMPLET
async function testCompleteWorkflow() {
    TestLogger.info('Test 4: Workflow complet de drag & drop');
    
    const app = window.teamCalendarApp;
    
    if (!app.data.posts.length || !app.data.employees.length) {
        TestLogger.critical('Données insuffisantes pour le test');
        return { success: false, error: 'Pas de données' };
    }
    
    const testPost = app.data.posts[0];
    const testEmployee = app.data.employees[0];
    const testDate = '2024-01-15';
    
    TestLogger.debug('Test avec:', {
        post: testPost.label,
        employee: testEmployee.name,
        date: testDate
    });
    
    try {
        // Simuler un événement de drop
        const mockDropEvent = {
            clientX: 200,
            clientY: 300,
            preventDefault: () => {},
            stopPropagation: () => {}
        };
        
        // Tester la fonction de gestion de drop
        app.handleRegularAssignmentDrop(testPost, testEmployee.id, testDate, mockDropEvent);
        
        // Attendre que le modal s'ouvre
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Vérifier si le modal est ouvert
        const modal = document.getElementById('assignment-context-modal');
        const isModalOpen = modal && !modal.classList.contains('hidden');
        
        if (isModalOpen) {
            TestLogger.success('Workflow complet réussi - Modal ouvert');
            
            // Nettoyer en fermant le modal
            const closeBtn = modal.querySelector('[data-action="close"]') || 
                           modal.querySelector('.close');
            if (closeBtn) closeBtn.click();
            
            return { success: true };
        } else {
            TestLogger.error('Workflow échoué - Modal non ouvert');
            return { success: false, error: 'Modal non ouvert' };
        }
        
    } catch (error) {
        TestLogger.error(`Erreur workflow: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// ✅ FONCTION PRINCIPALE DE TEST
async function runComprehensiveDragDropTests() {
    TestLogger.info('🚀 Démarrage des tests complets de drag & drop');
    
    const results = {
        startTime: new Date(),
        tests: {},
        summary: {}
    };
    
    try {
        // Test 1: Prérequis
        results.tests.prerequisites = await testPrerequisites();
        
        if (!results.tests.prerequisites.canProceed) {
            TestLogger.critical('Tests arrêtés - Prérequis critiques non satisfaits');
            return results;
        }
        
        // Test 2: Détection de date
        results.tests.dateDetection = await testDateDetection();
        
        // Test 3: Ouverture modal
        results.tests.modalOpening = await testModalOpening();
        
        // Test 4: Workflow complet
        results.tests.completeWorkflow = await testCompleteWorkflow();
        
        // Calculer le résumé
        const testNames = Object.keys(results.tests);
        const successfulTests = testNames.filter(name => results.tests[name].success).length;
        const totalTests = testNames.length;
        
        results.summary = {
            total: totalTests,
            successful: successfulTests,
            failed: totalTests - successfulTests,
            successRate: Math.round((successfulTests / totalTests) * 100),
            endTime: new Date(),
            duration: new Date() - results.startTime
        };
        
        // Log final
        if (results.summary.successRate >= 75) {
            TestLogger.success(`🎉 Tests réussis: ${successfulTests}/${totalTests} (${results.summary.successRate}%)`);
        } else if (results.summary.successRate >= 50) {
            TestLogger.warning(`⚠️ Tests partiels: ${successfulTests}/${totalTests} (${results.summary.successRate}%)`);
        } else {
            TestLogger.error(`❌ Tests échoués: ${successfulTests}/${totalTests} (${results.summary.successRate}%)`);
        }
        
    } catch (error) {
        TestLogger.critical(`Erreur critique dans les tests: ${error.message}`);
        results.error = error.message;
    }
    
    results.logs = TestLogger.exportLogs();
    return results;
}

// ✅ EXPORT POUR UTILISATION MANUELLE
window.testDragDropComprehensive = {
    runComprehensiveDragDropTests,
    testPrerequisites,
    testDateDetection,
    testModalOpening,
    testCompleteWorkflow,
    TestLogger
};

// ✅ AUTO-EXÉCUTION
console.log('✅ [TEST-DRAG-DROP] Script chargé. Utilisez runComprehensiveDragDropTests() pour tester.');

// Attendre que l'application soit chargée puis lancer les tests
setTimeout(async () => {
    if (window.teamCalendarApp && window.modalFunctionalities) {
        console.log('🚀 [TEST-DRAG-DROP] Lancement automatique des tests...');
        const results = await runComprehensiveDragDropTests();
        
        console.log('📊 [TEST-DRAG-DROP] Résultats finaux:', results.summary);
        
        // Stocker les résultats pour inspection
        window._lastDragDropTestResults = results;
    } else {
        console.log('⏳ [TEST-DRAG-DROP] En attente du chargement de l\'application...');
    }
}, 3000);
