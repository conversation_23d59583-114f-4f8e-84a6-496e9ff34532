const { Pool } = require('pg');

console.log('🔧 [MIGRATION-LOGS] Correction automatique de la table logs');
console.log('='.repeat(60));

const pool = new Pool({
  host: '*************',
  port: 5432,
  database: 'glive_db',
  user: 'postgres',
  password: 'SebbZ12342323!!'
});

async function testConnection() {
  console.log('\n🔗 ÉTAPE 1: Test de connexion');
  console.log('-'.repeat(40));
  
  try {
    console.log('📡 Connexion à la base de données...');
    console.log(`   📍 Host: ${pool.options.host}:${pool.options.port}`);
    console.log(`   🗄️  Database: ${pool.options.database}`);
    console.log(`   👤 User: ${pool.options.user}`);
    console.log(`   🔐 Password: ${'*'.repeat(pool.options.password.length)}`);
    
    const client = await pool.connect();
    console.log('✅ Connexion réussie!');
    
    // Test de latence
    const startTime = Date.now();
    await client.query('SELECT 1');
    const latency = Date.now() - startTime;
    console.log(`⚡ Latence: ${latency}ms`);
    
    return client;
    
  } catch (error) {
    console.error('❌ ERREUR DE CONNEXION:');
    console.error(`   💥 Message: ${error.message}`);
    console.error(`   🔢 Code: ${error.code || 'N/A'}`);
    throw error;
  }
}

async function checkAndFixLogsTable(client) {
  console.log('\n📊 ÉTAPE 2: Vérification et correction de la table logs');
  console.log('-'.repeat(40));
  
  try {
    // Vérifier si la table logs existe
    const tableExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'logs'
      );
    `);
    
    if (!tableExists.rows[0].exists) {
      console.log('⚠️  Table logs n\'existe pas, création...');
      await client.query(`
        CREATE TABLE logs (
          id SERIAL PRIMARY KEY,
          session_id TEXT NOT NULL,
          source VARCHAR(50) NOT NULL,
          level VARCHAR(20) NOT NULL,
          message TEXT NOT NULL,
          data JSONB DEFAULT '{}',
          priority INTEGER DEFAULT 0,
          timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_logs_session_id ON logs(session_id);
        CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs(timestamp);
      `);
      console.log('✅ Table logs créée avec session_id en TEXT');
      return { created: true };
    }
    
    console.log('✅ Table logs trouvée');
    
    // Vérifier le type de session_id
    const columnCheck = await client.query(`
      SELECT data_type FROM information_schema.columns 
      WHERE table_schema = 'public' AND table_name = 'logs' AND column_name = 'session_id'
    `);
    
    const currentType = columnCheck.rows[0]?.data_type;
    console.log(`📋 Type actuel de session_id: ${currentType}`);
    
    if (currentType === 'uuid') {
      console.log('🔄 Migration nécessaire: session_id UUID → TEXT');
      
      // Compter les enregistrements existants
      const countResult = await client.query('SELECT COUNT(*) as total FROM logs');
      const recordCount = parseInt(countResult.rows[0].total);
      console.log(`📊 ${recordCount} enregistrements dans la table`);
      
      // Effectuer la migration
      await client.query('ALTER TABLE logs ALTER COLUMN session_id TYPE TEXT;');
      console.log('✅ Migration réussie!');
      
      return { migrated: true, recordCount };
    } else if (currentType === 'text') {
      console.log('✅ session_id est déjà en TEXT - aucune migration nécessaire');
      return { alreadyCorrect: true };
    } else {
      console.log(`⚠️  Type inattendu: ${currentType}`);
      return { unexpectedType: currentType };
    }
    
  } catch (error) {
    console.error('❌ Erreur lors de la vérification/correction:', error.message);
    throw error;
  }
}

async function testInsertion(client) {
  console.log('\n🧪 ÉTAPE 3: Test d\'insertion');
  console.log('-'.repeat(40));
  
  try {
    const testSessionId = `test-migration-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    console.log('📝 Test avec session_id de type chaîne...');
    console.log(`   🆔 Session ID: ${testSessionId}`);
    
    const insertResult = await client.query(`
      INSERT INTO logs(session_id, source, level, message, data, priority) 
      VALUES($1, $2, $3, $4, $5, $6) 
      RETURNING id, session_id, timestamp;
    `, [
      testSessionId,
      'migration-test',
      'info',
      'Test après migration - session_id accepte maintenant les chaînes',
      { migration: true, timestamp: new Date().toISOString() },
      1
    ]);
    
    const record = insertResult.rows[0];
    console.log('✅ Insertion réussie!');
    console.log(`   🆔 ID: ${record.id}`);
    console.log(`   📅 Timestamp: ${record.timestamp}`);
    
    // Test avec format serveur (qui causait l'erreur)
    const serverSessionId = `server-startup-${new Date().toISOString()}`;
    console.log('\n📝 Test avec format serveur...');
    
    const insertResult2 = await client.query(`
      INSERT INTO logs(session_id, source, level, message, data, priority) 
      VALUES($1, $2, $3, $4, $5, $6) 
      RETURNING id;
    `, [
      serverSessionId,
      'backend',
      'info',
      'Test format serveur qui causait erreur UUID',
      {},
      1
    ]);
    
    console.log('✅ Insertion format serveur réussie!');
    console.log(`   🆔 ID: ${insertResult2.rows[0].id}`);
    
    return { success: true };
    
  } catch (error) {
    console.error('❌ Erreur test insertion:', error.message);
    throw error;
  }
}

async function runMigration() {
  let client;
  
  try {
    console.log('🚀 Début de la migration automatique...\n');
    
    client = await testConnection();
    const migrationResult = await checkAndFixLogsTable(client);
    await testInsertion(client);
    
    console.log('\n🎉 MIGRATION TERMINÉE AVEC SUCCÈS!');
    console.log('='.repeat(60));
    
    if (migrationResult.created) {
      console.log('✅ Table logs créée avec session_id en TEXT');
    } else if (migrationResult.migrated) {
      console.log('✅ session_id migré de UUID vers TEXT');
      console.log(`📊 ${migrationResult.recordCount} enregistrements préservés`);
    } else if (migrationResult.alreadyCorrect) {
      console.log('✅ Table logs déjà configurée correctement');
    }
    
    console.log('✅ Les erreurs "invalid input syntax for type uuid" sont résolues');
    console.log('\n🔄 PROCHAINES ÉTAPES:');
    console.log('   1. Redémarrez votre serveur: npm run dev');
    console.log('   2. Testez la page logs: http://localhost:5173/logs');
    console.log('   3. Les erreurs 500 devraient disparaître');
    
  } catch (error) {
    console.error('\n💥 MIGRATION ÉCHOUÉE:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('   🚨 Impossible de se connecter au serveur PostgreSQL');
    } else if (error.code === '3D000') {
      console.error('   🚨 Base de données introuvable');
    } else if (error.code === '28P01') {
      console.error('   🚨 Authentification échouée');
    }
    
    process.exit(1);
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
    console.log('\n🔌 Connexion fermée');
  }
}

console.log('🚀 Lancement de la migration de la table logs...\n');
runMigration(); 