#!/usr/bin/env node

/**
 * Script de test pour vérifier les corrections DOM
 * Teste que les problèmes d'initialisation sont résolus
 */

import fs from 'fs';

console.log('🧪 [TEST-DOM] Test des corrections DOM...\n');

// Test 1: Vérifier que les fonctions corrigées existent
console.log('📋 Test 1: Vérification des fonctions corrigées');

const teamCalendarAppPath = './src/teamCalendarApp.ts';
const content = fs.readFileSync(teamCalendarAppPath, 'utf8');

const tests = [
    {
        name: 'verifyAndFixDom avec limite de tentatives',
        pattern: /MAX_RETRIES = 25/,
        description: 'Limite de tentatives pour éviter les boucles infinies'
    },
    {
        name: 'attachAllEventListeners avec circuit-breaker',
        pattern: /_eventListenersAttached/,
        description: 'Circuit-breaker pour éviter les doublons'
    },
    {
        name: 'createEmployeeListContainer',
        pattern: /createEmployeeListContainer/,
        description: 'Création automatique des conteneurs manquants'
    },
    {
        name: 'destroy function',
        pattern: /destroy:\s*function/,
        description: 'Fonction de nettoyage pour HMR'
    },
    {
        name: 'init avec vérification DOM',
        pattern: /domReady = await this\.verifyAndFixDom/,
        description: 'Vérification DOM avant initialisation'
    }
];

let passedTests = 0;
let totalTests = tests.length;

tests.forEach((test, index) => {
    const found = test.pattern.test(content);
    if (found) {
        console.log(`✅ ${index + 1}. ${test.name}`);
        console.log(`   ${test.description}`);
        passedTests++;
    } else {
        console.log(`❌ ${index + 1}. ${test.name}`);
        console.log(`   ${test.description} - NON TROUVÉ`);
    }
});

console.log(`\n📊 RÉSULTATS : ${passedTests}/${totalTests} tests passés`);

if (passedTests === totalTests) {
    console.log('🎉 TOUS LES TESTS PASSÉS ! Les corrections DOM sont en place.');
} else {
    console.log('⚠️ Certains tests ont échoué. Vérifiez les corrections.');
}

// Test 2: Vérifier la structure du DOM attendue
console.log('\n📋 Test 2: Structure DOM attendue');

const domStructure = [
    { id: 'employee-list-container', description: 'Conteneur des employés' },
    { id: 'available-posts-container', description: 'Conteneur des postes disponibles' },
    { class: 'employee-row', description: 'Lignes d\'employés' },
    { class: 'post-row-info', description: 'Informations des postes' }
];

console.log('✅ Structure DOM attendue :');
domStructure.forEach(item => {
    if (item.id) {
        console.log(`   - #${item.id} : ${item.description}`);
    } else if (item.class) {
        console.log(`   - .${item.class} : ${item.description}`);
    }
});

// Test 3: Créer un script de vérification pour le navigateur
const browserTestScript = `
// Script de vérification DOM pour le navigateur
console.log('🔍 [BROWSER-TEST] Vérification DOM...');

function checkDOMStructure() {
    const results = {
        passed: [],
        failed: []
    };
    
    // Vérifier les conteneurs requis
    const containers = [
        { id: 'employee-list-container', name: 'Conteneur des employés' },
        { id: 'available-posts-container', name: 'Conteneur des postes' }
    ];
    
    containers.forEach(container => {
        const element = document.getElementById(container.id);
        if (element) {
            results.passed.push(\`✅ \${container.name} (\${container.id})\`);
        } else {
            results.failed.push(\`❌ \${container.name} (\${container.id}) manquant\`);
        }
    });
    
    // Vérifier les éléments dynamiques
    const dynamicElements = [
        { selector: '.employee-row', name: 'Lignes d\'employés' },
        { selector: '.post-row-info', name: 'Informations des postes' }
    ];
    
    dynamicElements.forEach(item => {
        const elements = document.querySelectorAll(item.selector);
        if (elements.length > 0) {
            results.passed.push(\`✅ \${item.name} (\${elements.length} trouvés)\`);
        } else {
            results.failed.push(\`❌ \${item.name} (aucun trouvé)\`);
        }
    });
    
    return results;
}

function checkTeamCalendarApp() {
    const results = {
        passed: [],
        failed: []
    };
    
    // Vérifier l'instance unique
    if (window.__TCA__) {
        results.passed.push('✅ Instance unique TeamCalendarApp (__TCA__)');
    } else {
        results.failed.push('❌ Instance unique TeamCalendarApp manquante');
    }
    
    // Vérifier les fonctions critiques
    if (window.__TCA__ && window.__TCA__.verifyAndFixDom) {
        results.passed.push('✅ verifyAndFixDom disponible');
    } else {
        results.failed.push('❌ verifyAndFixDom manquant');
    }
    
    if (window.__TCA__ && window.__TCA__.attachAllEventListeners) {
        results.passed.push('✅ attachAllEventListeners disponible');
    } else {
        results.failed.push('❌ attachAllEventListeners manquant');
    }
    
    if (window.__TCA__ && window.__TCA__.destroy) {
        results.passed.push('✅ destroy disponible');
    } else {
        results.failed.push('❌ destroy manquant');
    }
    
    return results;
}

// Exécuter les tests
console.log('🔍 [BROWSER-TEST] Test de la structure DOM...');
const domResults = checkDOMStructure();
console.log('📊 Structure DOM :');
domResults.passed.forEach(result => console.log(result));
domResults.failed.forEach(result => console.log(result));

console.log('\\n🔍 [BROWSER-TEST] Test de TeamCalendarApp...');
const appResults = checkTeamCalendarApp();
console.log('📊 TeamCalendarApp :');
appResults.passed.forEach(result => console.log(result));
appResults.failed.forEach(result => console.log(result));

// Résumé
const totalPassed = domResults.passed.length + appResults.passed.length;
const totalFailed = domResults.failed.length + appResults.failed.length;

console.log(\`\\n📊 RÉSUMÉ : \${totalPassed} tests passés, \${totalFailed} échecs\`);

if (totalFailed === 0) {
    console.log('🎉 TOUS LES TESTS PASSÉS ! L\'application est prête.');
} else {
    console.log('⚠️ Certains tests ont échoué. Vérifiez les corrections.');
}

// Exposer les fonctions pour utilisation manuelle
window.checkDOMStructure = checkDOMStructure;
window.checkTeamCalendarApp = checkTeamCalendarApp;
console.log('\\n✅ Fonctions de test disponibles : checkDOMStructure(), checkTeamCalendarApp()');
`;

fs.writeFileSync('./public/test-dom-browser.js', browserTestScript);

console.log('\n📋 Test 3: Script de vérification navigateur créé');
console.log('   Fichier : public/test-dom-browser.js');
console.log('   Usage : Inclure dans la page et exécuter dans la console');

// Test 4: Instructions de test
console.log('\n📋 Test 4: Instructions de test manuel');

const instructions = `
🎯 INSTRUCTIONS DE TEST MANUEL :

1. Redémarrer le serveur :
   npm run dev

2. Ouvrir l'application dans le navigateur :
   http://localhost:5173

3. Ouvrir la console du navigateur (F12)

4. Vérifier les logs d'initialisation :
   - Plus de boucles infinies "verifyAndFixDom"
   - Plus de warnings "employeeListContainer manquant"
   - Messages "✅ Conteneurs DOM prêts"

5. Tester les fonctionnalités :
   - Drag & drop des postes vers les employés
   - Clics sur les employés
   - Navigation entre les semaines
   - Création/modification d'employés

6. Vérifier la structure DOM :
   - document.getElementById('employee-list-container') !== null
   - document.querySelectorAll('.employee-row').length > 0
   - document.querySelectorAll('.post-row-info').length > 0

7. Tester le HMR (Hot Module Replacement) :
   - Modifier un fichier
   - Vérifier que l'application se recharge sans erreurs
   - Vérifier qu'il n'y a qu'une seule instance

8. Si des problèmes persistent :
   - Exécuter checkDOMStructure() dans la console
   - Exécuter checkTeamCalendarApp() dans la console
   - Vérifier les logs d'erreur
`;

console.log(instructions);

console.log('\n✅ [TEST-DOM] Tests de vérification terminés !');
console.log('\n🚀 Prêt pour les tests manuels !'); 