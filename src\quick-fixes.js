/**
 * 🚀 CORRECTIONS RAPIDES - À exécuter dans la console du navigateur
 * 
 * Corrige instantanément les problèmes identifiés dans les logs :
 * 1. Erreurs CSS will-change
 * 2. Logs verbeux
 * 3. IDs temporaires (déjà corrigé dans le code)
 */

(function() {
    'use strict';
    
    console.log('🔧 [QUICK-FIXES] Chargement des corrections rapides...');
    
    // ✅ CORRECTION 1 : Système de logs optimisé
    const LogLevel = {
        ERROR: 0,   // ❌ Erreurs critiques uniquement
        WARN: 1,    // ⚠️ Avertissements + erreurs  
        INFO: 2,    // ℹ️ Informations importantes + précédent
        DEBUG: 3    // 🔍 Tous les logs (mode développeur)
    };
    
    let currentLogLevel = LogLevel.INFO;
    
    // Sauvegarder la fonction console.log originale
    const originalConsoleLog = console.log;
    const originalConsoleWarn = console.warn;
    const originalConsoleError = console.error;
    
    // Buffer pour regrouper les logs similaires
    const logBuffer = new Map();
    let lastFlush = Date.now();
    
    // Fonction pour regrouper les logs similaires
    function groupSimilarLogs(level, args) {
        const message = args.join(' ');
        const key = message.substring(0, 50); // Premiers 50 caractères comme clé
        
        if (!logBuffer.has(key)) {
            logBuffer.set(key, { count: 0, lastMessage: message, level });
        }
        
        const entry = logBuffer.get(key);
        entry.count++;
        entry.lastMessage = message;
        
        // Vider le buffer toutes les 1000ms
        const now = Date.now();
        if (now - lastFlush > 1000) {
            flushLogBuffer();
            lastFlush = now;
        }
    }
    
    function flushLogBuffer() {
        if (logBuffer.size === 0) return;
        
        const grouped = [];
        const individual = [];
        
        logBuffer.forEach((entry, key) => {
            if (entry.count > 3) {
                grouped.push(`📊 [GROUPED] ${entry.count}x: ${key}...`);
            } else {
                individual.push(entry.lastMessage);
            }
        });
        
        // Afficher les logs groupés
        grouped.forEach(msg => originalConsoleLog.call(console, msg));
        individual.forEach(msg => originalConsoleLog.call(console, msg));
        
        logBuffer.clear();
    }
    
    // Fonction helper pour les logs conditionnels
    function logConditional(level, ...args) {
        if (level <= currentLogLevel) {
            const prefixes = ['❌', '⚠️', 'ℹ️', '🔍'];
            const methods = [originalConsoleError, originalConsoleWarn, originalConsoleLog, originalConsoleLog];
            
            const message = args.join(' ');
            
            // Regrouper les logs verbeux
            if ((message.includes('[DRAG&DROP]') || message.includes('[REINTEGRATION]') || message.includes('[createShiftElement]')) && level >= LogLevel.DEBUG) {
                groupSimilarLogs(level, args);
            } else {
                methods[level].call(console, prefixes[level], ...args);
            }
        }
    }
    
    // Remplacer console.log pour filtrer les logs verbeux
    console.log = function(...args) {
        const message = args.join(' ');
        
        // Filtrer les logs très verbeux selon le niveau
        if (currentLogLevel < LogLevel.DEBUG) {
            if (message.includes('🔧 [renderEmployees]') || 
                message.includes('✅ [createShiftElement]') ||
                message.includes('🎯 [setupPostDragDrop]') ||
                message.includes('🔍 [ensureDropZonesExist]')) {
                return; // Ignorer ces logs en mode normal
            }
        }
        
        if (currentLogLevel < LogLevel.INFO) {
            if (message.includes('[DRAG&DROP]') || 
                message.includes('[REINTEGRATION]') ||
                message.includes('📊 [GROUPED]')) {
                return; // Ignorer ces logs en mode quiet
            }
        }
        
        originalConsoleLog.apply(console, args);
    };
    
    // ✅ CORRECTION 2 : Fonctions globales pour contrôler les logs
    window.setLogLevel = function(level) {
        currentLogLevel = level;
        flushLogBuffer(); // Vider le buffer lors du changement
        const levelNames = ['ERROR', 'WARN', 'INFO', 'DEBUG'];
        console.log(`📊 Niveau de logs défini à: ${levelNames[level]} (${level})`);
    };
    
    // Raccourcis pratiques
    window.logQuiet = function() {
        window.setLogLevel(LogLevel.ERROR);
        console.log('🔇 Mode silencieux activé - Erreurs uniquement');
    };
    
    window.logNormal = function() {
        window.setLogLevel(LogLevel.INFO);
        console.log('📢 Mode normal activé - Informations importantes');
    };
    
    window.logVerbose = function() {
        window.setLogLevel(LogLevel.DEBUG);
        console.log('🔍 Mode verbeux activé - Tous les logs');
    };
    
    // ✅ CORRECTION 3 : Supprimer les erreurs CSS will-change
    function fixCSSWillChangeErrors() {
        console.log('🎨 [QUICK-FIXES] Correction des erreurs CSS will-change...');
        
        // Ajouter des styles CSS valides pour corriger les erreurs
        const style = document.createElement('style');
        style.id = 'quick-fixes-css';
        style.textContent = `
            /* ✅ CORRECTION : Propriétés will-change valides */
            .shift-card {
                will-change: transform, opacity !important;
            }
            
            .shift-card:not(:hover):not(.dragging) {
                will-change: auto !important;
            }
            
            .drop-zone-active {
                will-change: background-color, border-color, transform !important;
            }
            
            .replacement-reintegrable {
                will-change: transform, box-shadow !important;
            }
            
            .tutorial-highlight-overlay-enhanced {
                will-change: backdrop-filter !important;
            }
            
            /* Suppression des will-change invalides */
            * {
                will-change: auto !important;
            }
            
            /* Ré-application sélective pour les éléments qui en ont besoin */
            .shift-card,
            .drop-zone-active,
            .replacement-reintegrable,
            .tutorial-highlight-overlay-enhanced {
                will-change: revert !important;
            }
        `;
        
        // Supprimer l'ancien style s'il existe
        const existing = document.getElementById('quick-fixes-css');
        if (existing) {
            existing.remove();
        }
        
        document.head.appendChild(style);
        console.log('✅ [QUICK-FIXES] Styles CSS will-change corrigés');
    }
    
    // ✅ CORRECTION 4 : Diagnostic rapide
    window.quickDiagnostic = function() {
        console.log('🔍 [DIAGNOSTIC] Analyse rapide du système...');
        
        if (!window.TeamCalendarApp) {
            console.log('❌ TeamCalendarApp non disponible');
            return;
        }
        
        const app = window.TeamCalendarApp;
        let tempIdCount = 0;
        let totalShifts = 0;
        let replacementShifts = 0;
        
        // Analyser les shifts
        Object.values(app.data?.schedule || {}).forEach(employee => {
            Object.values(employee || {}).forEach(shifts => {
                shifts.forEach(shift => {
                    totalShifts++;
                    if (shift.isReplacement) {
                        replacementShifts++;
                        if (shift.originalAssignmentId && shift.originalAssignmentId.startsWith('temp-')) {
                            tempIdCount++;
                        }
                    }
                });
            });
        });
        
        console.log('📊 [DIAGNOSTIC] Résultats:');
        console.log(`   📋 Total shifts: ${totalShifts}`);
        console.log(`   🔄 Remplacements: ${replacementShifts}`);
        console.log(`   ⚠️ IDs temporaires: ${tempIdCount}`);
        console.log(`   👥 Employés: ${app.data?.employees?.length || 0}`);
        console.log(`   📌 Postes: ${app.config?.standardPosts?.length || 0}`);
        
        if (tempIdCount > 0) {
            console.log(`⚠️ [DIAGNOSTIC] ${tempIdCount} remplacements avec IDs temporaires détectés`);
            console.log('   → Ces remplacements ne peuvent pas être réintégrés automatiquement');
            console.log('   → Ils fonctionnent comme des déplacements normaux');
        } else {
            console.log('✅ [DIAGNOSTIC] Aucun ID temporaire problématique détecté');
        }
    };
    
    // ✅ CORRECTION 5 : Nettoyage d'urgence
    window.emergencyCleanup = function() {
        console.log('🚨 [EMERGENCY] Nettoyage d\'urgence...');
        
        // Vider le buffer de logs
        logBuffer.clear();
        
        // Corriger les CSS
        fixCSSWillChangeErrors();
        
        // Mode silencieux
        window.logQuiet();
        
        // Forcer un re-render si possible
        if (window.TeamCalendarApp && typeof window.TeamCalendarApp.render === 'function') {
            window.TeamCalendarApp.render();
            console.log('🔄 [EMERGENCY] Interface re-rendue');
        }
        
        console.log('✅ [EMERGENCY] Nettoyage terminé');
    };
    
    // ✅ AUTO-EXÉCUTION : Appliquer les corrections de base
    fixCSSWillChangeErrors();
    
    // Mode normal par défaut (moins verbeux que debug)
    window.setLogLevel(LogLevel.INFO);
    
    console.log('✅ [QUICK-FIXES] Corrections rapides chargées !');
    console.log('');
    console.log('🎯 [QUICK-FIXES] Commandes disponibles:');
    console.log('   window.logQuiet()     - Mode silencieux');
    console.log('   window.logNormal()    - Mode normal (recommandé)');
    console.log('   window.logVerbose()   - Mode debug complet');
    console.log('   window.quickDiagnostic() - Diagnostic rapide');
    console.log('   window.emergencyCleanup() - Nettoyage d\'urgence');
    console.log('');
    
})(); 