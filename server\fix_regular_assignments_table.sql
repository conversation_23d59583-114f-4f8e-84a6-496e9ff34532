-- Script pour corriger la table regular_assignments
-- Exécutez ce script dans votre base de données PostgreSQL

-- Supprimer la table existante si elle existe (attention: cela supprime les données)
DROP TABLE IF EXISTS regular_assignments;

-- Créer la nouvelle table avec la bonne structure
CREATE TABLE regular_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    post_id UUID REFERENCES standard_posts(id) ON DELETE SET NULL,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0=dimanche, 6=samedi
    start_time TIME,
    end_time TIME,
    start_date DATE,
    end_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour améliorer les performances
CREATE INDEX idx_regular_assignments_employee ON regular_assignments(employee_id);
CREATE INDEX idx_regular_assignments_post ON regular_assignments(post_id);
CREATE INDEX idx_regular_assignments_day ON regular_assignments(day_of_week);
CREATE INDEX idx_regular_assignments_dates ON regular_assignments(start_date, end_date);

-- Trigger pour mettre à jour updated_at automatiquement
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_regular_assignments_updated_at 
    BEFORE UPDATE ON regular_assignments 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column(); 