// ✅ GESTIONNAIRE DE TOOLTIPS PROFESSIONNEL AMÉLIORÉ
// Résout les problèmes de persistance lors des opérations de conversion

class TooltipManager {
    private activeTooltips: Set<HTMLElement> = new Set();
    private isOperationInProgress: boolean = false;

    constructor() {
        // Nettoyage automatique lors des changements de page
        window.addEventListener('beforeunload', () => this.cleanupAll());
        
        // Nettoyage lors des opérations de navigation
        window.addEventListener('popstate', () => this.cleanupAll());
    }

    // ✅ Configuration d'un tooltip sans délai avec gestion d'état robuste
    setupReplacementTooltip(shiftElement: HTMLElement, shiftData: any, employeeId: string): void {
        // Éviter la configuration multiple
        if (shiftElement.dataset.tooltipConfigured === 'true') {
            return;
        }

        let currentTooltip: HTMLElement | null = null;
        let isMouseInside = false;

        const showTooltip = (e: MouseEvent) => {
            // Ne pas afficher pendant les opérations
            if (this.isOperationInProgress) {
                return;
            }

            // Vérifier si l'élément existe encore dans le DOM
            if (!document.contains(shiftElement)) {
                return;
            }

            isMouseInside = true;

            // Nettoyer les tooltips existants
            this.cleanupAll();

            // Créer le nouveau tooltip immédiatement
            currentTooltip = this.createReplacementTooltip(shiftData, employeeId);
            document.body.appendChild(currentTooltip);

            // Ajouter à la liste des tooltips actifs
            this.activeTooltips.add(currentTooltip);

            // Positionner le tooltip
            this.positionTooltip(currentTooltip, e, shiftElement);

            // Afficher avec animation
            requestAnimationFrame(() => {
                if (currentTooltip && isMouseInside && !this.isOperationInProgress) {
                    currentTooltip.classList.add('visible');
                }
            });
        };

        const hideTooltip = () => {
            isMouseInside = false;

            if (currentTooltip) {
                currentTooltip.classList.remove('visible');
                this.activeTooltips.delete(currentTooltip);

                setTimeout(() => {
                    if (currentTooltip && !isMouseInside) {
                        currentTooltip.remove();
                        currentTooltip = null;
                    }
                }, 200);
            }
        };

        // Événements de souris
        shiftElement.addEventListener('mouseenter', showTooltip);
        shiftElement.addEventListener('mouseleave', hideTooltip);

        // Nettoyage automatique lors des mutations DOM
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.removedNodes.forEach((node) => {
                    if (node === shiftElement || (node.nodeType === Node.ELEMENT_NODE && (node as Element).contains && (node as Element).contains(shiftElement))) {
                        hideTooltip();
                        observer.disconnect();
                    }
                });
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });

        // Marquer comme configuré
        shiftElement.dataset.tooltipConfigured = 'true';
    }

    // ✅ Création du contenu du tooltip
    private createReplacementTooltip(shiftData: any, currentEmployeeId: string): HTMLElement {
        const tooltip = document.createElement('div');
        tooltip.className = 'replacement-tooltip';

        // Styles intégrés pour éviter les conflits CSS
        tooltip.style.cssText = `
            position: fixed;
            z-index: 10000;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-left: 4px solid #f97316;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            max-width: 320px;
            font-family: Inter, -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            opacity: 0;
            transform: translateY(-8px);
            transition: all 0.2s ease-out;
            backdrop-filter: blur(8px);
        `;

        // Récupération des données
        const teamCalendarApp = (window as any).teamCalendarApp || (window as any).TeamCalendarApp;
        const originalAssignment = teamCalendarApp?.data?.regularAssignments?.find((ra: any) => ra.id === shiftData.originalAssignmentId);
        const originalEmployee = originalAssignment ? teamCalendarApp?.data?.employees?.find((emp: any) => emp.id === originalAssignment.employeeId) : null;
        const currentEmployee = teamCalendarApp?.data?.employees?.find((emp: any) => emp.id === currentEmployeeId);
        const postData = teamCalendarApp?.getPostById?.(shiftData.postId);

        // Date de création formatée
        const creationDate = shiftData.replacementDate || shiftData.createdAt || new Date().toISOString();
        const formattedCreationDate = new Date(creationDate).toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        const createdByUser = shiftData.createdBy || 'Système';

        // Contenu HTML
        tooltip.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <div style="font-size: 18px; margin-right: 8px;">🔄</div>
                <div style="font-weight: 600; color: #1f2937;">Remplacement Ponctuel</div>
            </div>
            
            <div style="space-y: 8px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
                    <span style="color: #6b7280; font-weight: 500;">Poste :</span>
                    <span style="color: #374151; font-weight: 600;">${postData?.label || 'Poste inconnu'}</span>
                </div>
                
                <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
                    <span style="color: #6b7280; font-weight: 500;">Horaires :</span>
                    <span style="color: #374151;">${postData?.hours || shiftData.text || 'Non défini'}</span>
                </div>
                
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                    <span style="color: #6b7280; font-weight: 500;">Actuellement :</span>
                    <div style="display: flex; align-items: center; gap: 6px;">
                        <div style="width: 24px; height: 24px; border-radius: 50%; background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: 600;">${currentEmployee?.name?.charAt(0) || 'U'}</div>
                        <span style="color: #374151;">${currentEmployee?.name || 'Employé inconnu'}</span>
                    </div>
                </div>
                
                ${originalEmployee ? `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                    <span style="color: #6b7280; font-weight: 500;">Origine :</span>
                    <div style="display: flex; align-items: center; gap: 6px;">
                        <div style="width: 24px; height: 24px; border-radius: 50%; background: linear-gradient(135deg, #10b981, #059669); color: white; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: 600;">${originalEmployee.name.charAt(0)}</div>
                        <span style="color: #374151;">${originalEmployee.name}</span>
                    </div>
                </div>
                ` : ''}
                
                <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
                    <span style="color: #6b7280; font-weight: 500;">Créé le :</span>
                    <span style="background: #dbeafe; color: #1e40af; padding: 2px 6px; border-radius: 4px; font-size: 12px;">${formattedCreationDate}</span>
                </div>
                
                <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
                    <span style="color: #6b7280; font-weight: 500;">Par :</span>
                    <span style="color: #374151;">${createdByUser}</span>
                </div>
                
                ${shiftData.replacementReason ? `
                <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
                    <span style="color: #6b7280; font-weight: 500;">Motif :</span>
                    <span style="color: #374151;">${shiftData.replacementReason}</span>
                </div>
                ` : ''}
            </div>
            
            ${originalEmployee ? `
            <div style="background: #dbeafe; padding: 8px; border-radius: 6px; margin-top: 12px; font-size: 12px; color: #1e40af;">
                💡 Glissez ce remplacement vers <strong>${originalEmployee.name}</strong> pour le réintégrer automatiquement
            </div>
            ` : ''}
        `;

        // Classe CSS pour l'animation
        const style = document.createElement('style');
        style.textContent = `
            .replacement-tooltip.visible {
                opacity: 1 !important;
                transform: translateY(0) !important;
            }
        `;
        if (!document.head.querySelector('style[data-tooltip-styles]')) {
            style.setAttribute('data-tooltip-styles', 'true');
            document.head.appendChild(style);
        }

        return tooltip;
    }

    // ✅ Positionnement intelligent du tooltip
    private positionTooltip(tooltip: HTMLElement, mouseEvent: MouseEvent, shiftElement: HTMLElement): void {
        const rect = shiftElement.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        let left = rect.left + rect.width / 2 - tooltipRect.width / 2;
        let top = rect.top - tooltipRect.height - 12;

        // Ajustements horizontaux
        if (left < 10) {
            left = 10;
        } else if (left + tooltipRect.width > viewportWidth - 10) {
            left = viewportWidth - tooltipRect.width - 10;
        }

        // Ajustements verticaux
        if (top < 10) {
            top = rect.bottom + 12;
        }

        if (top + tooltipRect.height > viewportHeight - 10) {
            top = rect.top + rect.height / 2 - tooltipRect.height / 2;
            left = rect.right + 12;

            if (left + tooltipRect.width > viewportWidth - 10) {
                left = rect.left - tooltipRect.width - 12;
            }

            if (left < 10) {
                left = Math.max(10, rect.left + rect.width / 2 - tooltipRect.width / 2);
                top = Math.max(10, rect.top - tooltipRect.height - 12);
            }
        }

        tooltip.style.left = `${left}px`;
        tooltip.style.top = `${top}px`;
    }

    // ✅ Nettoyage global de tous les tooltips
    cleanupAll(): void {
        this.activeTooltips.forEach(tooltip => {
            if (tooltip && tooltip.parentNode) {
                tooltip.classList.remove('visible');
                tooltip.remove();
            }
        });
        this.activeTooltips.clear();

        // Nettoyage de sécurité
        const orphanedTooltips = document.querySelectorAll('.replacement-tooltip');
        orphanedTooltips.forEach(tooltip => {
            tooltip.remove();
        });
    }

    // ✅ Marquer le début d'une opération critique
    startOperation(): void {
        this.isOperationInProgress = true;
        this.cleanupAll();
    }

    // ✅ Marquer la fin d'une opération critique
    endOperation(): void {
        this.isOperationInProgress = false;
    }

    // ✅ Nettoyage avec promesse pour les opérations asynchrones
    async cleanupBeforeOperation(): Promise<void> {
        this.startOperation();
        
        return new Promise(resolve => {
            requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                    resolve();
                });
            });
        });
    }
}

// ✅ Instance globale du gestionnaire de tooltips
const tooltipManager = new TooltipManager();

// ✅ Exposition globale pour compatibilité
(window as any).tooltipManager = tooltipManager;

export default tooltipManager; 