import { query } from './config/database.js';

async function runMigration() {
    console.log('🔄 [Migration] Début de la migration de la table standard_posts...');
    
    try {
        // 1. Vérifier la structure actuelle
        console.log('📋 [Migration] Vérification de la structure actuelle...');
        const structure = await query(`
            SELECT column_name, data_type, is_nullable, column_default 
            FROM information_schema.columns 
            WHERE table_name = 'standard_posts' 
            ORDER BY ordinal_position
        `);
        
        console.log('📊 [Migration] Structure actuelle:', structure.rows);
        
        // 2. Modifier la colonne duration pour permettre NULL
        console.log('🔧 [Migration] Modification de la colonne duration...');
        await query('ALTER TABLE standard_posts ALTER COLUMN duration DROP NOT NULL');
        
        // 3. Ajouter une valeur par défaut (480 minutes = 8 heures)
        await query('ALTER TABLE standard_posts ALTER COLUMN duration SET DEFAULT 480');

        // 4. Mettre à jour les enregistrements existants qui ont duration NULL
        console.log('🔄 [Migration] Mise à jour des enregistrements avec duration NULL...');
        const updateResult = await query(`
            UPDATE standard_posts
            SET duration = 480
            WHERE duration IS NULL
        `);
        console.log(`✅ [Migration] ${updateResult.rowCount} enregistrements mis à jour`);
        
        // 5. Vérifier et ajouter les colonnes manquantes
        console.log('🔍 [Migration] Vérification des colonnes...');
        
        // Vérifier si la colonne id existe
        const hasId = await query(`
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'standard_posts' AND column_name = 'id'
        `);
        
        if (hasId.rows.length === 0) {
            console.log('➕ [Migration] Ajout de la colonne id...');
            await query('ALTER TABLE standard_posts ADD COLUMN id UUID PRIMARY KEY DEFAULT gen_random_uuid()');
        }
        
        // Vérifier les autres colonnes essentielles
        const columns = ['label', 'hours', 'type', 'category', 'created_at', 'updated_at'];
        
        for (const column of columns) {
            const hasColumn = await query(`
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'standard_posts' AND column_name = $1
            `, [column]);
            
            if (hasColumn.rows.length === 0) {
                console.log(`➕ [Migration] Ajout de la colonne ${column}...`);
                
                switch (column) {
                    case 'label':
                        await query('ALTER TABLE standard_posts ADD COLUMN label VARCHAR(255) NOT NULL DEFAULT \'Poste\'');
                        break;
                    case 'hours':
                        await query('ALTER TABLE standard_posts ADD COLUMN hours VARCHAR(50)');
                        break;
                    case 'type':
                        await query('ALTER TABLE standard_posts ADD COLUMN type VARCHAR(50) DEFAULT \'sky\'');
                        break;
                    case 'category':
                        await query('ALTER TABLE standard_posts ADD COLUMN category VARCHAR(100)');
                        break;
                    case 'created_at':
                        await query('ALTER TABLE standard_posts ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()');
                        break;
                    case 'updated_at':
                        await query('ALTER TABLE standard_posts ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()');
                        break;
                }
            }
        }
        
        // 6. Insérer quelques postes par défaut si la table est vide
        console.log('📝 [Migration] Ajout des postes par défaut...');
        
        const existingPosts = await query('SELECT COUNT(*) as count FROM standard_posts');
        const postCount = parseInt(existingPosts.rows[0].count);
        
        if (postCount === 0) {
            console.log('➕ [Migration] Insertion des postes par défaut...');
            
            const defaultPosts = [
                { label: 'Réception', hours: '08:00-16:00', duration: 480, type: 'sky', category: 'Administration' },
                { label: 'Cuisine', hours: '06:00-14:00', duration: 480, type: 'amber', category: 'Restauration' },
                { label: 'Ménage', hours: '09:00-17:00', duration: 480, type: 'emerald', category: 'Entretien' },
                { label: 'Sécurité', hours: '22:00-06:00', duration: 480, type: 'red', category: 'Sécurité' },
                { label: 'Maintenance', hours: '08:00-16:00', duration: 480, type: 'purple', category: 'Technique' }
            ];
            
            for (const post of defaultPosts) {
                await query(`
                    INSERT INTO standard_posts (label, hours, duration, type, category, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
                `, [post.label, post.hours, post.duration, post.type, post.category]);
                
                console.log(`✅ [Migration] Poste "${post.label}" ajouté`);
            }
        } else {
            console.log(`ℹ️ [Migration] ${postCount} postes déjà présents, pas d'insertion`);
        }
        
        // 7. Afficher le résultat final
        console.log('📊 [Migration] Résultat final:');
        const finalPosts = await query('SELECT id, label, hours, duration, type, category FROM standard_posts ORDER BY label');
        console.table(finalPosts.rows);
        
        // 8. Vérifier la structure finale
        const finalStructure = await query(`
            SELECT column_name, data_type, is_nullable, column_default 
            FROM information_schema.columns 
            WHERE table_name = 'standard_posts' 
            ORDER BY ordinal_position
        `);
        
        console.log('🏗️ [Migration] Structure finale:');
        console.table(finalStructure.rows);
        
        console.log('✅ [Migration] Migration terminée avec succès !');
        
    } catch (error) {
        console.error('❌ [Migration] Erreur lors de la migration:', error);
        throw error;
    }
}

// Exécuter la migration
runMigration()
    .then(() => {
        console.log('🎉 [Migration] Migration complète !');
        process.exit(0);
    })
    .catch((error) => {
        console.error('💥 [Migration] Échec de la migration:', error);
        process.exit(1);
    });
