#!/usr/bin/env node

/**
 * Script de test pour valider l'intégration des utilitaires centralisés
 * Teste les fonctions validateEmployee, validateShift, validatePost
 */

// Import des utilitaires depuis le bon chemin
import { validateEmployee, validateShift, validatePost } from './src/utils/validation.ts';

console.log('🧪 [TEST-UTILITAIRES] Démarrage des tests d\'intégration...\n');

// Test 1: Validation d'employé valide
console.log('📋 Test 1: Employé valide');
const employeeValid = {
    id: 'emp-001',
    name: '<PERSON>',
    position: 'Chef d\'équipe',
    color: '#3B82F6'
};
const result1 = validateEmployee(employeeValid);
console.log('✅ Résultat:', result1.isValid ? 'VALID' : 'INVALID');
if (!result1.isValid) console.log('⚠️ Warnings:', result1.warnings);
console.log('');

// Test 2: Validation d'employé invalide
console.log('📋 Test 2: Employé invalide');
const employeeInvalid = {
    id: '',
    name: '',
    position: 'Chef d\'équipe',
    color: 'invalid-color'
};
const result2 = validateEmployee(employeeInvalid);
console.log('✅ Résultat:', result2.isValid ? 'VALID' : 'INVALID');
if (!result2.isValid) console.log('⚠️ Warnings:', result2.warnings);
console.log('');

// Test 3: Validation de shift valide
console.log('📋 Test 3: Shift valide');
const shiftValid = {
    id: 'shift-001',
    employeeId: 'emp-001',
    postId: 'post-001',
    dateKey: '2024-01-15',
    startTime: '08:00',
    endTime: '16:00',
    isRegular: true
};
const result3 = validateShift(shiftValid);
console.log('✅ Résultat:', result3.isValid ? 'VALID' : 'INVALID');
if (!result3.isValid) console.log('⚠️ Warnings:', result3.warnings);
console.log('');

// Test 4: Validation de shift invalide
console.log('📋 Test 4: Shift invalide');
const shiftInvalid = {
    id: '',
    employeeId: '',
    postId: '',
    dateKey: 'invalid-date',
    startTime: '25:00',
    endTime: 'invalid-time'
};
const result4 = validateShift(shiftInvalid);
console.log('✅ Résultat:', result4.isValid ? 'VALID' : 'INVALID');
if (!result4.isValid) console.log('⚠️ Warnings:', result4.warnings);
console.log('');

// Test 5: Validation de poste valide
console.log('📋 Test 5: Poste valide');
const postValid = {
    id: 'post-001',
    label: 'Chef d\'équipe',
    startTime: '08:00',
    endTime: '16:00',
    duration: 8,
    workingDays: [1, 2, 3, 4, 5]
};
const result5 = validatePost(postValid);
console.log('✅ Résultat:', result5.isValid ? 'VALID' : 'INVALID');
if (!result5.isValid) console.log('⚠️ Warnings:', result5.warnings);
console.log('');

// Test 6: Validation de poste invalide
console.log('📋 Test 6: Poste invalide');
const postInvalid = {
    id: '',
    label: '',
    startTime: 'invalid',
    endTime: 'invalid',
    duration: -1,
    workingDays: []
};
const result6 = validatePost(postInvalid);
console.log('✅ Résultat:', result6.isValid ? 'VALID' : 'INVALID');
if (!result6.isValid) console.log('⚠️ Warnings:', result6.warnings);
console.log('');

// Résumé final
console.log('📊 RÉSUMÉ DES TESTS:');
console.log(`✅ Tests réussis: ${[result1, result3, result5].filter(r => r.isValid).length}/3`);
console.log(`❌ Tests d'erreur: ${[result2, result4, result6].filter(r => !r.isValid).length}/3`);

const allTestsPassed = [result1, result3, result5].every(r => r.isValid) && 
                      [result2, result4, result6].every(r => !r.isValid);

if (allTestsPassed) {
    console.log('\n🎉 TOUS LES TESTS PASSÉS ! Les utilitaires centralisés fonctionnent correctement.');
} else {
    console.log('\n⚠️ Certains tests ont échoué. Vérifiez les utilitaires centralisés.');
}

console.log('\n✅ [TEST-UTILITAIRES] Tests terminés.'); 