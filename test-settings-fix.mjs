#!/usr/bin/env node

/**
 * Test de la correction du bouton paramètres
 * Vérifie que les corrections sont bien intégrées dans le code source
 */

import { promises as fs } from 'fs';

console.log('🧪 [TEST] Vérification des corrections du bouton paramètres...\n');

async function testSettingsFix() {
  let allChecks = true;
  
  try {
    // 1. Vérifier que l'import modalFunctionalities a été supprimé
    console.log('📋 [ÉTAPE 1] Vérification de la suppression des imports temporaires...');
    const agendaContent = await fs.readFile('src/Agenda.tsx', 'utf8');
    
    if (agendaContent.includes('./modalFunctionalities')) {
      console.log('❌ Import modalFunctionalities encore présent');
      allChecks = false;
    } else {
      console.log('✅ Import modalFunctionalities supprimé');
    }
    
    // 2. Vérifier que la correction est intégrée dans Agenda.tsx
    console.log('\n🔧 [ÉTAPE 2] Vérification de la correction intégrée...');
    if (agendaContent.includes('ensureSettingsWorks')) {
      console.log('✅ Correction intégrée dans Agenda.tsx');
    } else {
      console.log('❌ Correction manquante dans Agenda.tsx');
      allChecks = false;
    }
    
    if (agendaContent.includes('TeamCalendarApp.openSettingsModal()')) {
      console.log('✅ Appel à openSettingsModal présent');
    } else {
      console.log('❌ Appel à openSettingsModal manquant');
      allChecks = false;
    }
    
    // 3. Vérifier que TeamCalendarApp a bien openSettingsModal
    console.log('\n⚙️ [ÉTAPE 3] Vérification de TeamCalendarApp...');
    const teamCalendarContent = await fs.readFile('src/teamCalendarApp.ts', 'utf8');
    
    if (teamCalendarContent.includes('openSettingsModal: function()')) {
      console.log('✅ openSettingsModal présent dans TeamCalendarApp');
    } else {
      console.log('❌ openSettingsModal manquant dans TeamCalendarApp');
      allChecks = false;
    }
    
    if (teamCalendarContent.includes('window.TeamCalendarApp = TeamCalendarApp')) {
      console.log('✅ Exposition globale présente');
    } else {
      console.log('❌ Exposition globale manquante');
      allChecks = false;
    }
    
    // 4. Vérifier les styles fullscreen
    console.log('\n🎨 [ÉTAPE 4] Vérification des styles...');
    const cssContent = await fs.readFile('src/styles/fullscreen.css', 'utf8');
    
    if (cssContent.includes('.tab-btn')) {
      console.log('✅ Styles des onglets présents');
    } else {
      console.log('❌ Styles des onglets manquants');
      allChecks = false;
    }
    
    // 5. Rapport final
    console.log('\n' + '='.repeat(50));
    if (allChecks) {
      console.log('🎉 [SUCCÈS] TOUTES LES CORRECTIONS SONT EN PLACE !');
      console.log('\n✅ CORRECTIONS APPLIQUÉES :');
      console.log('   • Import temporaire supprimé');
      console.log('   • Correction intégrée dans le code source');
      console.log('   • Attachement d\'événements robuste');
      console.log('   • TeamCalendarApp.openSettingsModal disponible');
      console.log('   • Styles fullscreen appliqués');
      
      console.log('\n🚀 LE BOUTON PARAMÈTRES DEVRAIT MAINTENANT FONCTIONNER !');
      console.log('\n📍 INSTRUCTIONS DE TEST :');
      console.log('   1. Ouvrez votre navigateur sur le serveur local');
      console.log('   2. Cliquez sur l\'icône engrenage ⚙️');
      console.log('   3. Le modal paramètres devrait s\'ouvrir');
      console.log('   4. Vos fonctionnalités sont conservées');
      
    } else {
      console.log('❌ [PROBLÈME] CERTAINES CORRECTIONS SONT MANQUANTES');
      console.log('\n🔧 ACTION REQUISE : Vérifiez les éléments échoués ci-dessus');
    }
    
    return allChecks;
    
  } catch (error) {
    console.error('\n❌ [ERREUR] Échec du test:', error.message);
    return false;
  }
}

const success = await testSettingsFix();
process.exit(success ? 0 : 1); 