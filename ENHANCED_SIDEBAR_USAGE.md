# 🚀 **EnhancedSidebarMenu - Guide Complet**

## 📦 **Installation des Dépendances**

```bash
# Dépendances principales
npm install framer-motion lucide-react

# Dépendances optionnelles pour UI avancée
npm install @headlessui/react @tailwindcss/forms
```

## 🎯 **Utilisation du Composant**

```jsx
import React, { useState } from 'react';
import EnhancedSidebarMenu from './components/EnhancedSidebarMenu';
import EmployeesPage from './components/EmployeesPage';

function App() {
  const [employees, setEmployees] = useState([
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '01 23 45 67 89',
      status: 'Te<PERSON> Plein',
      position: 'Développeur',
      department: 'IT',
      avatar: '/api/placeholder/40/40'
    }
  ]);

  const [posts, setPosts] = useState([
    { id: 'morning', label: 'Poste Matin', hours: '08:00-16:00' },
    { id: 'evening', label: 'Poste Soir', hours: '16:00-24:00' }
  ]);

  const [showEmployeesPage, setShowEmployeesPage] = useState(false);

  const handleDeletePost = (postId) => {
    setPosts(prev => prev.filter(p => p.id !== postId));
  };

  const handleAddEmployee = (employee) => {
    setEmployees(prev => [...prev, employee]);
  };

  const handleUpdateEmployee = (updatedEmployee) => {
    setEmployees(prev => 
      prev.map(emp => emp.id === updatedEmployee.id ? updatedEmployee : emp)
    );
  };

  const handleDeleteEmployee = (employeeId) => {
    setEmployees(prev => prev.filter(emp => emp.id !== employeeId));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sidebar améliorée */}
      <EnhancedSidebarMenu
        employees={employees}
        posts={posts}
        onDeletePost={handleDeletePost}
        onNavigateToEmployees={() => setShowEmployeesPage(true)}
        teamCalendarApp={null} // Référence vers votre app principale
      />

      {/* Page Employés */}
      {showEmployeesPage && (
        <EmployeesPage
          employees={employees}
          onAddEmployee={handleAddEmployee}
          onUpdateEmployee={handleUpdateEmployee}
          onDeleteEmployee={handleDeleteEmployee}
          isVisible={showEmployeesPage}
        />
      )}

      {/* Votre contenu principal */}
      <main className="ml-0">
        <div className="p-8">
          <h1>Votre Application Calendrier</h1>
          <button 
            onClick={() => setShowEmployeesPage(false)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg"
          >
            Retour au Calendrier
          </button>
        </div>
      </main>
    </div>
  );
}

export default App;
```

## 🔧 **Fonctionnalités Incluses**

### 1. **Sidebar Hover-to-Expand (4px)**
- Zone de détection ultra-réduite (4 pixels)
- Animation fluide Framer Motion
- Expansion automatique au survol

### 2. **Suppression de Poste Améliorée**
- Dialog de confirmation interne (plus de popup navigateur)
- Animation d'apparition/disparition
- Validation avant suppression

### 3. **Panneau Paramètres Ancré**
- Position fixe en bas à droite
- Mini-calendrier interactif
- Sélection multi-employés pour filtrage
- Statistiques en temps réel

### 4. **Système de Logs Avancé**
- Hook `useLogs` avec persistance localStorage
- 4 niveaux : INFO, SUCCESS, WARNING, ERROR
- Visualisateur avec filtres
- Export JSON et purge
- Limite automatique (1000 logs max)

### 5. **Page Employés Complète**
- CRUD complet (Create, Read, Update, Delete)
- Validations robustes (email, téléphone français, etc.)
- Recherche et filtres en temps réel
- Statistiques visuelles

## 🎨 **Personnalisation CSS**

```css
/* Styles personnalisés pour la sidebar */
.enhanced-sidebar {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

/* Zone de trigger invisible */
.sidebar-trigger {
  pointer-events: auto;
  background: transparent;
  transition: none;
}

/* Animations personnalisées */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
```

## 🛠️ **API Extensions (TODO)**

```javascript
// Exemple d'extension API pour la synchronisation
class EmployeeAPI {
  static async syncEmployees() {
    try {
      const response = await fetch('/api/employees');
      return await response.json();
    } catch (error) {
      console.error('Sync failed:', error);
      return [];
    }
  }

  static async saveEmployee(employee) {
    const response = await fetch('/api/employees', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(employee)
    });
    return await response.json();
  }
}

// Utilisation dans le composant
const handleSyncEmployees = async () => {
  const synced = await EmployeeAPI.syncEmployees();
  setEmployees(synced);
  logSuccess(`${synced.length} employés synchronisés`);
};
```

## 🔍 **Validations Robustes**

### Validation Email
```javascript
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
```

### Validation Téléphone Français
```javascript
const phoneRegex = /^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/;
```

### Validation Personnalisée
```javascript
const customValidators = {
  required: (value) => value.trim() !== '',
  minLength: (min) => (value) => value.length >= min,
  maxLength: (max) => (value) => value.length <= max,
  pattern: (regex) => (value) => regex.test(value)
};
```

## 📱 **Responsive Design**

Le composant est entièrement responsive avec :
- Grille adaptative pour les statistiques
- Table responsive avec scroll horizontal
- Dialog adaptatif mobile/desktop
- Sidebar qui se transforme en overlay sur mobile

## 🚀 **Performance**

- **Lazy loading** des composants lourds
- **Debounce** sur la recherche (300ms)
- **Virtual scrolling** pour grandes listes (>100 employés)
- **Memoization** des composants coûteux

## 🎯 **Intégration TeamCalendar**

```javascript
// Dans votre teamCalendarApp.ts
import EnhancedSidebarMenu from './components/EnhancedSidebarMenu';

// Ajouter dans votre fonction render()
const sidebarContainer = document.createElement('div');
sidebarContainer.id = 'enhanced-sidebar';
document.body.appendChild(sidebarContainer);

// Render avec React
ReactDOM.render(
  <EnhancedSidebarMenu 
    employees={this.data.employees}
    posts={this.config.standardPosts}
    onDeletePost={(id) => this.handlePostDelete(id)}
    teamCalendarApp={this}
  />,
  sidebarContainer
);
```

## 🔧 **Variables d'Environnement**

```env
# Configuration logs
REACT_APP_MAX_LOGS=1000
REACT_APP_LOG_LEVEL=info
REACT_APP_ENABLE_LOG_EXPORT=true

# Configuration API
REACT_APP_API_BASE_URL=http://localhost:3001/api
REACT_APP_ENABLE_SYNC=true
```

---

## 🏆 **Fonctionnalités Complètes**

✅ **Sidebar hover-to-expand** (zone 4px)  
✅ **Dialog suppression interne**  
✅ **Panneau paramètres ancré**  
✅ **Mini-calendrier interactif**  
✅ **Filtrage multi-employés**  
✅ **Système logs complet**  
✅ **Page employés CRUD**  
✅ **Animations Framer Motion**  
✅ **Validations robustes**  
✅ **Responsive design**  
✅ **Performance optimisée**  

🎉 **Composant prêt pour la production !** 