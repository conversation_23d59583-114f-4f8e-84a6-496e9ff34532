// Script de vérification DOM pour le navigateur
console.log('🔍 [BROWSER-TEST] Vérification DOM...');

function checkDOMStructure() {
    const results = { passed: [], failed: [] };
    // Vérifier les conteneurs requis
    const containers = [
        { id: 'employee-list-container', name: 'Conteneur des employés' },
        { id: 'available-posts-container', name: 'Conteneur des postes' }
    ];
    containers.forEach(container => {
        const element = document.getElementById(container.id);
        if (element) {
            results.passed.push(`✅ ${container.name} (${container.id})`);
        } else {
            results.failed.push(`❌ ${container.name} (${container.id}) manquant`);
        }
    });
    // Vérifier les éléments dynamiques
    const dynamicElements = [
        { selector: '.employee-row', name: '<PERSON><PERSON><PERSON> demploy<PERSON>' },
        { selector: '.post-row-info', name: 'Informations des postes' }
    ];
    dynamicElements.forEach(item => {
        const elements = document.querySelectorAll(item.selector);
        if (elements.length > 0) {
            results.passed.push(`✅ ${item.name} (${elements.length} trouvés)`);
        } else {
            results.failed.push(`❌ ${item.name} (aucun trouvé)`);
        }
    });
    return results;
}

function checkTeamCalendarApp() {
    const results = { passed: [], failed: [] };
    if (window.teamCalendarApp) {
        results.passed.push('✅ Instance TeamCalendarApp disponible');
    } else {
        results.failed.push('❌ Instance TeamCalendarApp manquante');
    }
    if (window.teamCalendarApp && window.teamCalendarApp.verifyAndFixDom) {
        results.passed.push('✅ verifyAndFixDom disponible');
    } else {
        results.failed.push('❌ verifyAndFixDom manquant');
    }
    if (window.teamCalendarApp && window.teamCalendarApp.attachAllEventListeners) {
        results.passed.push('✅ attachAllEventListeners disponible');
    } else {
        results.failed.push('❌ attachAllEventListeners manquant');
    }
    if (window.teamCalendarApp && window.teamCalendarApp.destroy) {
        results.passed.push('✅ destroy disponible');
    } else {
        results.failed.push('❌ destroy manquant');
    }
    if (window.teamCalendarApp && window.teamCalendarApp.setupEmployeeDragDrop) {
        results.passed.push('✅ setupEmployeeDragDrop disponible');
    } else {
        results.failed.push('❌ setupEmployeeDragDrop manquant');
    }
    if (window.teamCalendarApp && window.teamCalendarApp.renderAvailablePosts) {
        results.passed.push('✅ renderAvailablePosts disponible');
    } else {
        results.failed.push('❌ renderAvailablePosts manquant');
    }
    return results;
}

// Exposer les fonctions pour utilisation manuelle
window.checkDOMStructure = checkDOMStructure;
window.checkTeamCalendarApp = checkTeamCalendarApp;

console.log('✅ Fonctions de test disponibles : checkDOMStructure(), checkTeamCalendarApp()');
console.log('🔍 [BROWSER-TEST] Script de test DOM chargé avec succès');
