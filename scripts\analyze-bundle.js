#!/usr/bin/env node
// 📊 Analyse des bundles pour optimisation des performances
// Utilise rollup-plugin-visualizer pour Vite

import { execSync } from 'child_process';
import { existsSync, mkdirSync, writeFileSync } from 'fs';
import { join } from 'path';

const ANALYSIS_DIR = 'bundle-analysis';
const DIST_DIR = 'dist';

// ✅ COULEURS POUR LA CONSOLE
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// ✅ CRÉER LE DOSSIER D'ANALYSE
function ensureAnalysisDir() {
  if (!existsSync(ANALYSIS_DIR)) {
    mkdirSync(ANALYSIS_DIR, { recursive: true });
    log(`📁 Dossier créé: ${ANALYSIS_DIR}`, 'green');
  }
}

// ✅ BUILD AVEC ANALYSE
function buildWithAnalysis() {
  log('🏗️ Build avec analyse des bundles...', 'blue');
  
  try {
    // Build de production avec stats
    execSync('npm run build -- --mode=production', { 
      stdio: 'inherit',
      env: { 
        ...process.env, 
        ANALYZE: 'true' 
      }
    });
    
    log('✅ Build terminé avec succès', 'green');
    return true;
  } catch (error) {
    log('❌ Erreur lors du build:', 'red');
    console.error(error.message);
    return false;
  }
}

// ✅ ANALYSER LA TAILLE DES FICHIERS
function analyzeBundleSize() {
  log('📊 Analyse de la taille des bundles...', 'cyan');
  
  if (!existsSync(DIST_DIR)) {
    log('❌ Dossier dist non trouvé. Lancez d\'abord le build.', 'red');
    return;
  }
  
  try {
    // Utiliser du pour analyser les tailles
    const output = execSync(`du -sh ${DIST_DIR}/* 2>/dev/null || ls -lah ${DIST_DIR}/`, { 
      encoding: 'utf8' 
    });
    
    const analysisFile = join(ANALYSIS_DIR, 'bundle-sizes.txt');
    const timestamp = new Date().toISOString();
    
    const report = `
# 📊 Analyse des Bundles - ${timestamp}

## Taille des fichiers:
${output}

## Recommandations:
- Fichiers JS > 500KB: Considérer le code splitting
- Fichiers CSS > 100KB: Optimiser les styles inutilisés
- Assets > 1MB: Compresser ou lazy load

## Commandes utiles:
- npm run build -- --analyze : Analyse détaillée
- npm run preview : Tester le build
- npm run lighthouse : Audit performance
`;
    
    writeFileSync(analysisFile, report);
    log(`📄 Rapport sauvé: ${analysisFile}`, 'green');
    
    // Afficher un résumé
    console.log('\n' + report);
    
  } catch (error) {
    log('❌ Erreur analyse taille:', 'red');
    console.error(error.message);
  }
}

// ✅ ANALYSER LES DÉPENDANCES
function analyzeDependencies() {
  log('📦 Analyse des dépendances...', 'magenta');
  
  try {
    // Analyser les dépendances avec npm ls
    const prodDeps = execSync('npm ls --prod --depth=0 --json', { encoding: 'utf8' });
    const devDeps = execSync('npm ls --dev --depth=0 --json', { encoding: 'utf8' });
    
    const prodData = JSON.parse(prodDeps);
    const devData = JSON.parse(devDeps);
    
    const prodCount = Object.keys(prodData.dependencies || {}).length;
    const devCount = Object.keys(devData.dependencies || {}).length;
    
    log(`📦 Dépendances production: ${prodCount}`, 'green');
    log(`🔧 Dépendances développement: ${devCount}`, 'yellow');
    
    // Identifier les grosses dépendances
    const heavyDeps = [
      'react', 'react-dom', '@tanstack/react-query', 
      'framer-motion', 'sortablejs'
    ];
    
    log('\n🎯 Dépendances lourdes détectées:', 'yellow');
    heavyDeps.forEach(dep => {
      if (prodData.dependencies?.[dep]) {
        log(`  - ${dep}: ${prodData.dependencies[dep].version}`, 'cyan');
      }
    });
    
    // Suggestions d'optimisation
    const suggestions = `
## 💡 Suggestions d'optimisation:

### Code Splitting:
- React.lazy() pour les composants lourds
- Dynamic imports pour les utilitaires
- Route-based splitting

### Tree Shaking:
- Imports nommés au lieu de default
- Vérifier les side effects
- Utiliser des alternatives plus légères

### Bundle Analysis:
- Utiliser rollup-plugin-visualizer
- Identifier les duplications
- Optimiser les polyfills
`;
    
    const depsFile = join(ANALYSIS_DIR, 'dependencies-analysis.md');
    writeFileSync(depsFile, suggestions);
    log(`📄 Suggestions sauvées: ${depsFile}`, 'green');
    
  } catch (error) {
    log('❌ Erreur analyse dépendances:', 'red');
    console.error(error.message);
  }
}

// ✅ GÉNÉRER RAPPORT COMPLET
function generateFullReport() {
  log('📋 Génération du rapport complet...', 'bright');
  
  const reportFile = join(ANALYSIS_DIR, 'full-report.md');
  const timestamp = new Date().toISOString();
  
  const report = `
# 📊 Rapport d'Analyse Bundle - ${timestamp}

## 🎯 Objectifs Performance
- First Contentful Paint < 2s
- Largest Contentful Paint < 2.5s
- Bundle JS principal < 500KB
- Bundle CSS < 100KB

## 🔍 Méthodes d'Analyse
1. **Build Analysis**: Taille des fichiers générés
2. **Dependencies**: Poids des dépendances
3. **Code Splitting**: Opportunités de division
4. **Tree Shaking**: Code mort détecté

## 📈 Métriques Actuelles
- Voir bundle-sizes.txt pour les détails
- Voir dependencies-analysis.md pour les suggestions

## 🚀 Actions Recommandées
1. **Immédiat**:
   - Lazy load des composants non-critiques
   - Optimiser les images (WebP, lazy loading)
   - Minifier et compresser les assets

2. **Court terme**:
   - Code splitting par routes
   - Tree shaking des dépendances
   - Service Worker pour cache

3. **Long terme**:
   - Micro-frontends si nécessaire
   - CDN pour assets statiques
   - Preloading intelligent

## 🔧 Outils Utilisés
- Vite build analyzer
- npm ls pour dépendances
- Bundle size analysis
- Performance recommendations

## 📊 Prochaines Étapes
1. Implémenter les suggestions prioritaires
2. Mesurer l'impact avec Lighthouse
3. Monitorer en continu
4. Itérer sur les optimisations
`;
  
  writeFileSync(reportFile, report);
  log(`📄 Rapport complet: ${reportFile}`, 'green');
  
  // Ouvrir le rapport si possible
  try {
    if (process.platform === 'win32') {
      execSync(`start ${reportFile}`);
    } else if (process.platform === 'darwin') {
      execSync(`open ${reportFile}`);
    } else {
      execSync(`xdg-open ${reportFile}`);
    }
    log('📖 Rapport ouvert dans l\'éditeur', 'cyan');
  } catch {
    log('💡 Ouvrez manuellement le rapport pour plus de détails', 'yellow');
  }
}

// ✅ FONCTION PRINCIPALE
async function main() {
  log('🚀 Démarrage de l\'analyse des bundles', 'bright');
  
  ensureAnalysisDir();
  
  const args = process.argv.slice(2);
  const shouldBuild = !args.includes('--no-build');
  
  if (shouldBuild) {
    const buildSuccess = buildWithAnalysis();
    if (!buildSuccess) {
      process.exit(1);
    }
  }
  
  analyzeBundleSize();
  analyzeDependencies();
  generateFullReport();
  
  log('\n✅ Analyse terminée! Consultez le dossier bundle-analysis/', 'green');
  log('💡 Utilisez npm run preview pour tester le build', 'cyan');
}

// ✅ GESTION DES ERREURS
process.on('uncaughtException', (error) => {
  log('❌ Erreur non gérée:', 'red');
  console.error(error);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  log('❌ Promise rejetée:', 'red');
  console.error(reason);
  process.exit(1);
});

// ✅ EXÉCUTION
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    log('❌ Erreur dans main():', 'red');
    console.error(error);
    process.exit(1);
  });
}
