import fs from 'fs';
import path from 'path';

console.log('🔧 Correction du placement de la fonction emergencyFixUndefinedPostIds...');

const teamCalendarAppPath = './src/teamCalendarApp.ts';

try {
  // Lire le fichier
  let content = fs.readFileSync(teamCalendarAppPath, 'utf8');
  
  // Trouver la fin de la fonction fixPostRefreshIssues
  const fixPostRefreshIssuesEnd = content.indexOf('        return fixedCount;');
  if (fixPostRefreshIssuesEnd === -1) {
    throw new Error('Impossible de trouver la fin de fixPostRefreshIssues');
  }
  
  // Trouver le début de emergencyFixUndefinedPostIds
  const emergencyStart = content.indexOf('    // ✅ NOUVELLE FONCTION : Correction d\'urgence pour les postId undefined');
  if (emergencyStart === -1) {
    throw new Error('Impossible de trouver le début de emergencyFixUndefinedPostIds');
  }
  
  // Trouver la fin de emergencyFixUndefinedPostIds (avant la prochaine fonction)
  const emergencyEnd = content.indexOf('    // ✅ NOUVELLE FONCTION : Sauvegarder les corrections en base de données');
  if (emergencyEnd === -1) {
    throw new Error('Impossible de trouver la fin de emergencyFixUndefinedPostIds');
  }
  
  // Extraire la fonction emergencyFixUndefinedPostIds
  const emergencyFunction = content.substring(emergencyStart, emergencyEnd);
  
  // Supprimer la fonction de son emplacement actuel
  content = content.substring(0, emergencyStart) + content.substring(emergencyEnd);
  
  // Insérer la fonction au bon endroit (après fixPostRefreshIssues)
  const insertPosition = fixPostRefreshIssuesEnd + '        return fixedCount;'.length;
  const beforeInsert = content.substring(0, insertPosition);
  const afterInsert = content.substring(insertPosition);
  
  // Corriger la fonction emergencyFixUndefinedPostIds pour qu'elle soit au bon niveau
  const correctedEmergencyFunction = emergencyFunction.replace('    // ✅ NOUVELLE FONCTION : Correction d\'urgence pour les postId undefined', '    },');
  
  content = beforeInsert + '\n' + correctedEmergencyFunction + afterInsert;
  
  // Écrire le fichier corrigé
  fs.writeFileSync(teamCalendarAppPath, content);
  
  console.log('✅ Placement de emergencyFixUndefinedPostIds corrigé');
  
  // Vérifier que la correction est valide
  const validationContent = fs.readFileSync(teamCalendarAppPath, 'utf8');
  if (validationContent.includes('emergencyFixUndefinedPostIds: function()') && 
      !validationContent.includes('return fixedCount;\n\n    // ✅ NOUVELLE FONCTION : Correction d\'urgence')) {
    console.log('✅ Structure de la fonction confirmée');
  } else {
    console.log('❌ Structure de la fonction encore problématique');
  }
  
} catch (error) {
  console.error('❌ Erreur lors de la correction:', error.message);
  process.exit(1);
}

console.log('🎯 Script terminé. Redémarrez le serveur pour appliquer les changements.'); 