# 🚀 GUIDE RAPIDE : Corriger la Persistance des Remplacements Ponctuels

## 🎯 PROBLÈME
Les remplacements ponctuels perdent leur forme orange après un refresh de la page.

## ⚡ SOLUTION IMMÉDIATE (2 minutes)

### 1. Chargez le script de correction
Copiez dans la console du navigateur :
```javascript
const script = document.createElement('script');
script.src = './fix-replacement-persistence-emergency.js';
document.head.appendChild(script);
```

### 2. Exécutez la correction
```javascript
fixReplacementPersistenceEmergency.runCompleteFixNow()
```

### 3. Testez
- Faites un refresh (F5)
- Vérifiez que les remplacements gardent leur forme orange

## 🗃️ SOLUTION PERMANENTE (Base de données)

### SQL à exécuter dans votre base de données :

```sql
-- Ajouter les colonnes de propriétés visuelles
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS shape VARCHAR(50) NULL;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS is_temporary BOOLEAN DEFAULT FALSE;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS original_assignment_id VARCHAR(36) NULL;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS replacement_date DATE NULL;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS replacement_reason TEXT NULL;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS visual_style VARCHAR(50) NULL;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS border_style VARCHAR(50) NULL;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS color_override VARCHAR(50) NULL;

-- Index pour les performances
CREATE INDEX IF NOT EXISTS idx_shifts_is_replacement ON shifts(is_replacement);
CREATE INDEX IF NOT EXISTS idx_shifts_is_temporary ON shifts(is_temporary);
CREATE INDEX IF NOT EXISTS idx_shifts_original_assignment_id ON shifts(original_assignment_id);
CREATE INDEX IF NOT EXISTS idx_shifts_replacement_date ON shifts(replacement_date);
```

## 🧪 VÉRIFIER LE SUCCÈS

### Chargez le script de test :
```javascript
const testScript = document.createElement('script');
testScript.src = './test-migration-status.js';
document.head.appendChild(testScript);
```

### Exécutez le test :
```javascript
testMigrationStatus.runCompleteTest()
```

## ✅ RÉSULTAT ATTENDU
- ✅ Remplacements ponctuels gardent leur forme orange après refresh
- ✅ Drag & drop de réintégration fonctionne
- ✅ Interface cohérente et fiable

## 📞 AIDE
Si le problème persiste, vérifiez les logs de la console pour des messages d'erreur spécifiques. 