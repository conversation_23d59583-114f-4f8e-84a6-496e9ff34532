-- Migration pour créer la table assignment_templates
-- Architecture: templates persistés + génération frontend

BEGIN;

-- 1. <PERSON><PERSON><PERSON> la table assignment_templates
CREATE TABLE IF NOT EXISTS assignment_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    post_id UUID NOT NULL REFERENCES standard_posts(id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL CHECK (type IN ('event', 'repeat', 'schedule')),
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NULL, -- NULL pour repeat (permanent), DATE pour schedule (temporaire)
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Contraintes métier
    CONSTRAINT valid_time_range CHECK (end_time > start_time),
    CONSTRAINT valid_date_range CHECK (end_date IS NULL OR end_date >= start_date),
    CONSTRAINT valid_type_end_date CHECK (
        (type = 'repeat' AND end_date IS NULL) OR 
        (type = 'schedule' AND end_date IS NOT NULL) OR
        (type = 'event')
    )
);

-- 2. Index pour les requêtes de performance
CREATE INDEX IF NOT EXISTS idx_assignment_templates_employee ON assignment_templates(employee_id);
CREATE INDEX IF NOT EXISTS idx_assignment_templates_post ON assignment_templates(post_id);
CREATE INDEX IF NOT EXISTS idx_assignment_templates_type ON assignment_templates(type);
CREATE INDEX IF NOT EXISTS idx_assignment_templates_day ON assignment_templates(day_of_week);
CREATE INDEX IF NOT EXISTS idx_assignment_templates_dates ON assignment_templates(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_assignment_templates_active ON assignment_templates(is_active) WHERE is_active = true;

-- 3. Fonction de mise à jour automatique du timestamp
CREATE OR REPLACE FUNCTION update_assignment_templates_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 4. Trigger pour la mise à jour automatique
DROP TRIGGER IF EXISTS trigger_assignment_templates_updated_at ON assignment_templates;
CREATE TRIGGER trigger_assignment_templates_updated_at
    BEFORE UPDATE ON assignment_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_assignment_templates_updated_at();

-- 5. Migrer les données existantes de regular_assignments vers assignment_templates
INSERT INTO assignment_templates (
    employee_id, post_id, type, day_of_week, start_time, end_time, start_date, end_date, is_active, created_at, updated_at
)
SELECT
    ra.employee_id,
    ra.post_id,
    'repeat' as type, -- Toutes les attributions existantes deviennent des repeat
    ra.day_of_week,
    COALESCE(ra.start_time, '08:00:00'::time) as start_time, -- Valeur par défaut si NULL
    COALESCE(ra.end_time, '16:00:00'::time) as end_time,     -- Valeur par défaut si NULL
    COALESCE(ra.start_date, CURRENT_DATE) as start_date,
    ra.end_date, -- NULL pour repeat permanent
    COALESCE(ra.is_active, true) as is_active,
    COALESCE(ra.created_at, NOW()) as created_at,
    COALESCE(ra.updated_at, NOW()) as updated_at
FROM regular_assignments ra
WHERE ra.start_time IS NOT NULL AND ra.end_time IS NOT NULL -- Seulement les données valides
AND NOT EXISTS (
    SELECT 1 FROM assignment_templates at
    WHERE at.employee_id = ra.employee_id
    AND at.post_id = ra.post_id
    AND at.day_of_week = ra.day_of_week
);

-- 6. Ajouter des exemples de templates pour test
INSERT INTO assignment_templates (employee_id, post_id, type, day_of_week, start_time, end_time, start_date, end_date)
SELECT 
    e.id,
    p.id,
    'repeat',
    1, -- Lundi
    '08:00:00',
    '16:00:00',
    '2025-06-01',
    NULL -- Permanent
FROM employees e, standard_posts p 
WHERE e.name = 'Jean Dupont' AND p.label LIKE '%Matin%'
AND NOT EXISTS (
    SELECT 1 FROM assignment_templates at 
    WHERE at.employee_id = e.id AND at.day_of_week = 1
)
LIMIT 1;

INSERT INTO assignment_templates (employee_id, post_id, type, day_of_week, start_time, end_time, start_date, end_date)
SELECT 
    e.id,
    p.id,
    'repeat',
    2, -- Mardi
    '08:00:00',
    '16:00:00',
    '2025-06-01',
    NULL -- Permanent
FROM employees e, standard_posts p 
WHERE e.name = 'Jean Dupont' AND p.label LIKE '%Matin%'
AND NOT EXISTS (
    SELECT 1 FROM assignment_templates at 
    WHERE at.employee_id = e.id AND at.day_of_week = 2
)
LIMIT 1;

INSERT INTO assignment_templates (employee_id, post_id, type, day_of_week, start_time, end_time, start_date, end_date)
SELECT 
    e.id,
    p.id,
    'schedule',
    5, -- Vendredi
    '14:00:00',
    '22:00:00',
    '2025-06-01',
    '2025-12-31' -- Temporaire jusqu'à fin d'année
FROM employees e, standard_posts p 
WHERE e.name = 'Marie Martin' AND p.label LIKE '%Soir%'
AND NOT EXISTS (
    SELECT 1 FROM assignment_templates at 
    WHERE at.employee_id = e.id AND at.day_of_week = 5
)
LIMIT 1;

-- 7. Vérification finale
SELECT 'Migration terminée - Templates créés:' as message;
SELECT 
    at.id,
    e.name as employee_name,
    p.label as post_label,
    at.type,
    CASE at.day_of_week 
        WHEN 0 THEN 'Dimanche'
        WHEN 1 THEN 'Lundi'
        WHEN 2 THEN 'Mardi'
        WHEN 3 THEN 'Mercredi'
        WHEN 4 THEN 'Jeudi'
        WHEN 5 THEN 'Vendredi'
        WHEN 6 THEN 'Samedi'
    END as day_name,
    at.start_time,
    at.end_time,
    at.start_date,
    at.end_date
FROM assignment_templates at
LEFT JOIN employees e ON at.employee_id = e.id
LEFT JOIN standard_posts p ON at.post_id = p.id
WHERE at.is_active = true
ORDER BY e.name, at.day_of_week, at.start_time;

COMMIT;

-- Affichage de la structure finale
SELECT 'Structure de la table assignment_templates:' as message;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'assignment_templates' 
ORDER BY ordinal_position;
