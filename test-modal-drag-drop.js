// ========================================
// SCRIPT DE TEST POUR LA MODALE DRAG & DROP
// ========================================

console.log('🧪 [TEST] Script de test de la modale drag & drop chargé');

// Fonction pour tester la modale d'attribution
function testAssignmentModal() {
    console.log('🧪 [TEST] Test de la modale d\'attribution...');
    
    // Vérifier que les éléments existent
    const modal = document.getElementById('assignment-context-modal');
    const closeBtn = document.getElementById('assignment-context-close');
    const cancelBtn = document.getElementById('assignment-context-cancel');
    const confirmBtn = document.getElementById('assignment-context-confirm');
    
    console.log('🔍 [TEST] Éléments trouvés:', {
        modal: !!modal,
        closeBtn: !!closeBtn,
        cancelBtn: !!cancelBtn,
        confirmBtn: !!confirmBtn
    });
    
    if (!modal || !closeBtn || !cancelBtn || !confirmBtn) {
        console.error('❌ [TEST] Éléments manquants dans le DOM');
        return false;
    }
    
    // Tester l'ouverture de la modale
    if (window.TeamCalendarApp && window.TeamCalendarApp.ModalManager) {
        try {
            // Simuler un drop avec des données de test
            const testPostId = 'e0332f5d-23e2-40fa-bf2d-d9271f03100a'; // Poste Matin
            const testEmployeeId = '59f5df3a-33ef-425c-bfa2-adca818cf94f'; // Sophie Leblanc
            
            console.log('🧪 [TEST] Ouverture de la modale avec données de test...');
            window.TeamCalendarApp.ModalManager.openAssignmentContextModal(testPostId, testEmployeeId);
            
            // Vérifier que la modale est visible
            const isVisible = !modal.classList.contains('hidden');
            console.log('🔍 [TEST] Modale visible:', isVisible);
            
            if (isVisible) {
                console.log('✅ [TEST] Modale ouverte avec succès');
                
                // Tester les boutons
                setTimeout(() => {
                    console.log('🧪 [TEST] Test des boutons...');
                    
                    // Simuler un clic sur le bouton confirmer
                    console.log('🧪 [TEST] Simulation clic bouton confirmer...');
                    confirmBtn.click();
                    
                }, 1000);
                
                return true;
            } else {
                console.error('❌ [TEST] Modale non visible après ouverture');
                return false;
            }
            
        } catch (error) {
            console.error('❌ [TEST] Erreur lors du test:', error);
            return false;
        }
    } else {
        console.error('❌ [TEST] TeamCalendarApp ou ModalManager non disponible');
        return false;
    }
}

// Fonction pour tester les listeners des boutons
function testButtonListeners() {
    console.log('🧪 [TEST] Test des listeners des boutons...');
    
    const closeBtn = document.getElementById('assignment-context-close');
    const cancelBtn = document.getElementById('assignment-context-cancel');
    const confirmBtn = document.getElementById('assignment-context-confirm');
    
    if (!closeBtn || !cancelBtn || !confirmBtn) {
        console.error('❌ [TEST] Boutons non trouvés');
        return false;
    }
    
    // Vérifier que les listeners sont attachés en inspectant les propriétés
    const hasCloseListener = closeBtn.onclick !== null || closeBtn.addEventListener !== undefined;
    const hasCancelListener = cancelBtn.onclick !== null || cancelBtn.addEventListener !== undefined;
    const hasConfirmListener = confirmBtn.onclick !== null || confirmBtn.addEventListener !== undefined;
    
    console.log('🔍 [TEST] Listeners détectés:', {
        close: hasCloseListener,
        cancel: hasCancelListener,
        confirm: hasConfirmListener
    });
    
    return hasCloseListener && hasCancelListener && hasConfirmListener;
}

// Fonction pour diagnostiquer les problèmes
function diagnoseModalIssues() {
    console.log('🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...');
    
    // 1. Vérifier l'existence des éléments DOM
    const elements = {
        modal: document.getElementById('assignment-context-modal'),
        closeBtn: document.getElementById('assignment-context-close'),
        cancelBtn: document.getElementById('assignment-context-cancel'),
        confirmBtn: document.getElementById('assignment-context-confirm'),
        radioButtons: document.querySelectorAll('input[name="assignment-type"]')
    };
    
    console.log('📋 [DIAGNOSTIC] Éléments DOM:', {
        modal: !!elements.modal,
        closeBtn: !!elements.closeBtn,
        cancelBtn: !!elements.cancelBtn,
        confirmBtn: !!elements.confirmBtn,
        radioButtons: elements.radioButtons.length
    });
    
    // 2. Vérifier TeamCalendarApp
    console.log('📋 [DIAGNOSTIC] TeamCalendarApp:', {
        exists: !!window.TeamCalendarApp,
        hasModalManager: !!(window.TeamCalendarApp && window.TeamCalendarApp.ModalManager),
        hasOpenFunction: !!(window.TeamCalendarApp && window.TeamCalendarApp.ModalManager && window.TeamCalendarApp.ModalManager.openAssignmentContextModal),
        hasHandleFunction: !!(window.TeamCalendarApp && window.TeamCalendarApp.handleAssignmentContextConfirm)
    });
    
    // 3. Vérifier l'état de la modale
    if (elements.modal) {
        console.log('📋 [DIAGNOSTIC] État de la modale:', {
            hidden: elements.modal.classList.contains('hidden'),
            classes: Array.from(elements.modal.classList),
            style: elements.modal.style.display
        });
    }
    
    // 4. Vérifier les données contextuelles
    if (window.TeamCalendarApp) {
        console.log('📋 [DIAGNOSTIC] Données contextuelles:', {
            currentContextAssignment: window.TeamCalendarApp.currentContextAssignment,
            employees: window.TeamCalendarApp.data?.employees?.length || 0,
            posts: window.TeamCalendarApp.config?.standardPosts?.length || 0
        });
    }
    
    return elements;
}

// Fonction pour forcer la reconfiguration des listeners
function forceReconfigureListeners() {
    console.log('🔧 [FIX] Reconfiguration forcée des listeners...');

    if (window.TeamCalendarApp && window.TeamCalendarApp.ModalManager && window.TeamCalendarApp.ModalManager.setupAssignmentContextModal) {
        try {
            window.TeamCalendarApp.ModalManager.setupAssignmentContextModal();
            console.log('✅ [FIX] Listeners reconfigurés avec succès');
            return true;
        } catch (error) {
            console.error('❌ [FIX] Erreur lors de la reconfiguration:', error);
            return false;
        }
    } else {
        console.error('❌ [FIX] setupAssignmentContextModal non disponible');
        console.log('🔍 [FIX] État de TeamCalendarApp:', {
            exists: !!window.TeamCalendarApp,
            hasModalManager: !!(window.TeamCalendarApp && window.TeamCalendarApp.ModalManager),
            hasSetupFunction: !!(window.TeamCalendarApp && window.TeamCalendarApp.ModalManager && window.TeamCalendarApp.ModalManager.setupAssignmentContextModal)
        });
        return false;
    }
}

// Fonction pour tester les clics sur les boutons
function testButtonClicks() {
    console.log('🧪 [TEST] Test des clics sur les boutons...');

    const confirmBtn = document.getElementById('assignment-context-confirm');
    const cancelBtn = document.getElementById('assignment-context-cancel');
    const closeBtn = document.getElementById('assignment-context-close');

    if (!confirmBtn || !cancelBtn || !closeBtn) {
        console.error('❌ [TEST] Boutons non trouvés');
        return false;
    }

    // Simuler des données de contexte
    if (window.TeamCalendarApp) {
        window.TeamCalendarApp.currentContextAssignment = {
            postId: 'e0332f5d-23e2-40fa-bf2d-d9271f03100a',
            employeeId: '59f5df3a-33ef-425c-bfa2-adca818cf94f',
            preselectedDay: null
        };
        console.log('✅ [TEST] Données de contexte simulées');
    }

    // Ouvrir la modale
    const modal = document.getElementById('assignment-context-modal');
    if (modal) {
        modal.classList.remove('hidden');
        console.log('✅ [TEST] Modale ouverte pour test');

        // Tester le clic sur confirmer après un délai
        setTimeout(() => {
            console.log('🧪 [TEST] Simulation clic bouton confirmer...');
            confirmBtn.click();
        }, 1000);

        return true;
    }

    return false;
}

// Exposer les fonctions globalement pour les tests manuels
window.testAssignmentModal = testAssignmentModal;
window.testButtonListeners = testButtonListeners;
window.diagnoseModalIssues = diagnoseModalIssues;
window.forceReconfigureListeners = forceReconfigureListeners;
window.testButtonClicks = testButtonClicks;

// Auto-diagnostic au chargement
setTimeout(() => {
    console.log('🚀 [TEST] Lancement du diagnostic automatique...');
    diagnoseModalIssues();
}, 2000);

console.log('✅ [TEST] Fonctions de test disponibles:');
console.log('- testAssignmentModal() : Tester l\'ouverture de la modale');
console.log('- testButtonListeners() : Tester les listeners des boutons');
console.log('- diagnoseModalIssues() : Diagnostiquer les problèmes');
console.log('- forceReconfigureListeners() : Reconfigurer les listeners');
console.log('- testButtonClicks() : Tester les clics sur les boutons');
