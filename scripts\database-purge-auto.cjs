const { purgeAuto } = require('./database-purge.cjs');

console.log('🤖 PURGE AUTOMATIQUE DE LA BASE DE DONNÉES');
console.log('⚡ Exécution sans confirmation...\n');

purgeAuto()
  .then(() => {
    console.log('\n🎉 Purge automatique terminée avec succès!');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n❌ Erreur lors de la purge automatique:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }); 