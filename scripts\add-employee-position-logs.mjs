import fs from 'fs/promises';
import path from 'path';

console.log('🔧 Ajout des logs de positions employés...');

async function run() {
  const filePath = path.join(process.cwd(), 'src', 'teamCalendarApp.ts');
  let content = await fs.readFile(filePath, 'utf-8');
  let modified = false;

  // 1. Ajouter la méthode logEmployeePositions si absente
  if (!content.includes('logEmployeePositions: function')) {
    const marker = "console.log('✅ Réparation rapide terminée');";
    const idxMarker = content.indexOf(marker);
    if (idxMarker !== -1) {
      const insertPos = content.indexOf('\n', idxMarker) + 1;
      const methodCode = `\n    // 🔎 UTILITAIRE DEBUG : journaliser l'ordre actuel des employés\n    logEmployeePositions: function(context) {\n        try {\n            const list = (this.data && Array.isArray(this.data.employees)) ? this.data.employees.map((e, idx) => \`\${idx}:\${e.name}\`) : [];\n            console.log(\`📊 [\${context}] Positions employés (\${list.length}):\`, list);\n        } catch (err) {\n            console.warn('⚠️ [logEmployeePositions] erreur', err);\n        }\n    },\n`;
      content = content.slice(0, insertPos) + methodCode + content.slice(insertPos);
      modified = true;
      console.log('✅ Méthode logEmployeePositions ajoutée');
    } else {
      console.warn('❌ Marqueur quickRepair non trouvé, insertion échouée');
    }
  } else {
    console.log('ℹ️ Méthode logEmployeePositions déjà présente');
  }

  // Helper pour insérer appels après un pattern unique
  function insertCall(patternStr, callCode) {
    const pattern = new RegExp(patternStr);
    const idx = content.search(pattern);
    if (idx !== -1) {
      // vérifier si call déjà présent
      if (content.indexOf(callCode.trim()) === -1) {
        const endIdx = idx + content.match(pattern)[0].length;
        content = content.slice(0, endIdx) + '\n' + callCode + content.slice(endIdx);
        modified = true;
        console.log(`✅ Appel ajouté après ${patternStr}`);
      }
    } else {
      console.warn(`❌ Pattern ${patternStr} non trouvé`);
    }
  }

  // 2. Ajouter appel dans init après chargement
  insertCall("const loaded = await this.loadState\(\);[\\s\\S]*?if \(!loaded\)", "            this.logEmployeePositions('INIT_AFTER_LOAD');");

  // 3. Ajouter appel dans reorderEmployees après log nouvel ordre
  insertCall("console\\.log\\(`✅ \\ [reorderEmployees\\] Nouvel ordre:`", "            this.logEmployeePositions('AFTER_REORDER');");

  // 4. Ajouter appel dans loadEmployeeOrder après réorganisation succès
  insertCall("console\\.log\\\('✅ \\[loadEmployeeOrder\\] Employés réorganisés", "                this.logEmployeePositions('AFTER_LOAD_EMPLOYEE_ORDER');");

  if (modified) {
    await fs.writeFile(filePath, content, 'utf-8');
    console.log('💾 Fichier teamCalendarApp.ts mis à jour');
  } else {
    console.log('ℹ️ Aucun changement nécessaire');
  }
}

run().catch(console.error); 