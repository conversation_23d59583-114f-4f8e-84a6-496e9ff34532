-- =====================================================
-- MIGRATION 008: AJOUT DES COLONNES POUR REMPLACEMENTS PONCTUELS
-- Date: 2025-01-21
-- Description: Ajout des colonnes critiques pour la persistance
--              des propriétés des remplacements ponctuels
-- =====================================================

-- ✅ AJOUT DES COLONNES MANQUANTES POUR LES REMPLACEMENTS PONCTUELS
ALTER TABLE shifts
  ADD COLUMN IF NOT EXISTS is_replacement      BOOLEAN   DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS original_assignment_id UUID   NULL,
  ADD COLUMN IF NOT EXISTS visual_style        VARCHAR(40) NULL,
  ADD COLUMN IF NOT EXISTS color_override      VARCHAR(20) NULL,
  ADD COLUMN IF NOT EXISTS replacement_date    DATE      NULL,
  ADD COLUMN IF NOT EXISTS replacement_reason  TEXT      NULL,
  ADD COLUMN IF NOT EXISTS is_temporary         BOOLEAN   DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS shape               VARCHAR(20) NULL,
  ADD COLUMN IF NOT EXISTS border_style        VARCHAR(40) NULL;

-- ✅ AJOUT D'INDEX POUR LES NOUVELLES COLONNES
CREATE INDEX IF NOT EXISTS idx_shifts_is_replacement ON shifts(is_replacement);
CREATE INDEX IF NOT EXISTS idx_shifts_original_assignment ON shifts(original_assignment_id);
CREATE INDEX IF NOT EXISTS idx_shifts_replacement_date ON shifts(replacement_date);
CREATE INDEX IF NOT EXISTS idx_shifts_is_temporary ON shifts(is_temporary);

-- ✅ AJOUT DE CONTRAINTE POUR LIER LES REMPLACEMENTS AUX ATTRIBUTIONS
ALTER TABLE shifts
  ADD CONSTRAINT fk_shifts_original_assignment 
  FOREIGN KEY (original_assignment_id) 
  REFERENCES regular_assignments(id) 
  ON DELETE SET NULL;

-- ✅ COMMENTAIRES SUR LES NOUVELLES COLONNES
COMMENT ON COLUMN shifts.is_replacement IS 'Indique si ce shift est un remplacement ponctuel';
COMMENT ON COLUMN shifts.original_assignment_id IS 'Référence vers l''attribution régulière remplacée';
COMMENT ON COLUMN shifts.visual_style IS 'Style visuel personnalisé (ex: orange-replacement)';
COMMENT ON COLUMN shifts.color_override IS 'Couleur de surcharge (ex: orange)';
COMMENT ON COLUMN shifts.replacement_date IS 'Date du remplacement (peut différer de date_key)';
COMMENT ON COLUMN shifts.replacement_reason IS 'Raison du remplacement';
COMMENT ON COLUMN shifts.is_temporary IS 'Indique si ce shift est temporaire';
COMMENT ON COLUMN shifts.shape IS 'Forme visuelle du shift (ex: full, top-half)';
COMMENT ON COLUMN shifts.border_style IS 'Style de bordure personnalisé';

-- =====================================================
-- FIN DE LA MIGRATION 008
-- ===================================================== 