const { Pool } = require('pg');

console.log('🔍 DIAGNOSTIC COMPLET DE LA BASE DE DONNÉES PostgreSQL');
console.log('='.repeat(60));

const pool = new Pool({
  host: '*************',
  port: 5432,
  database: 'glive_db',
  user: 'postgres',
  password: 'SebbZ12342323!!'
});

const EXPECTED_SCHEMA = {
  employees: ['id', 'name', 'status', 'avatar_url', 'extra_fields', 'created_at', 'updated_at'],
  shifts: ['id', 'employee_id', 'start_time', 'end_time', 'break_duration', 'post_id', 'notes', 'status'],
  standard_posts: ['id', 'label', 'color', 'created_at', 'updated_at'],
  app_settings: ['id', 'setting_key', 'setting_value', 'created_at', 'updated_at']
};

async function testConnection() {
  console.log('\n🔗 ÉTAPE 1: Test de connexion PostgreSQL DISTANT');
  console.log('-'.repeat(50));
  
  try {
    console.log('📡 Tentative de connexion...');
    console.log(`   📍 Host: ${pool.options.host}:${pool.options.port}`);
    console.log(`   🗄️  Database: ${pool.options.database}`);
    console.log(`   👤 User: ${pool.options.user}`);
    console.log(`   🔐 Password: ${'*'.repeat(pool.options.password.length)}`);
    
    const client = await pool.connect();
    console.log('✅ Connexion PostgreSQL DISTANTE réussie!');
    
    const versionResult = await client.query('SELECT version()');
    console.log(`📋 Version: ${versionResult.rows[0].version.split(' ')[0]} ${versionResult.rows[0].version.split(' ')[1]}`);
    
    // Tester la latence
    const startTime = Date.now();
    await client.query('SELECT 1');
    const latency = Date.now() - startTime;
    console.log(`⚡ Latence: ${latency}ms`);
    
    return client;
    
  } catch (error) {
    console.error('❌ ERREUR DE CONNEXION:');
    console.error(`   💥 Message: ${error.message}`);
    console.error(`   🔢 Code: ${error.code || 'N/A'}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('   🚨 Serveur PostgreSQL inaccessible!');
      console.error('   💡 Vérifiez la connectivité réseau vers *************');
    } else if (error.code === '3D000') {
      console.error('   🚨 La base de données glive_db n\'existe pas!');
    } else if (error.code === '28P01') {
      console.error('   🚨 Authentification échouée!');
      console.error('   💡 Vérifiez le mot de passe');
    } else if (error.code === 'ENOTFOUND') {
      console.error('   🚨 Nom d\'hôte introuvable!');
      console.error('   💡 Vérifiez l\'adresse IP *************');
    }
    
    throw error;
  }
}

async function checkTables(client) {
  console.log('\n📊 ÉTAPE 2: Vérification des tables');
  console.log('-'.repeat(50));
  
  try {
    const result = await client.query(`
      SELECT table_name FROM information_schema.tables 
      WHERE table_schema = 'public' ORDER BY table_name
    `);
    
    const existingTables = result.rows.map(row => row.table_name);
    console.log(`📋 ${existingTables.length} tables trouvées dans glive_db:`);
    existingTables.forEach((table, i) => console.log(`   ${i+1}. ${table}`));
    
    console.log('\n🔍 Vérification des tables requises:');
    const expectedTables = Object.keys(EXPECTED_SCHEMA);
    const missingTables = [];
    
    expectedTables.forEach(expectedTable => {
      if (existingTables.includes(expectedTable)) {
        console.log(`   ✅ ${expectedTable}`);
      } else {
        console.log(`   ❌ ${expectedTable} (MANQUANTE)`);
        missingTables.push(expectedTable);
      }
    });
    
    return { existingTables, missingTables };
    
  } catch (error) {
    console.error('❌ Erreur vérification tables:', error.message);
    throw error;
  }
}

async function checkColumns(client, existingTables) {
  console.log('\n🏗️  ÉTAPE 3: Vérification des colonnes');
  console.log('-'.repeat(50));
  
  for (const tableName of existingTables) {
    if (EXPECTED_SCHEMA[tableName]) {
      console.log(`\n📋 Table: ${tableName}`);
      
      try {
        const result = await client.query(`
          SELECT column_name, data_type, is_nullable
          FROM information_schema.columns 
          WHERE table_schema = 'public' AND table_name = $1
          ORDER BY ordinal_position
        `, [tableName]);
        
        const existingColumns = result.rows.map(col => col.column_name);
        const expectedColumns = EXPECTED_SCHEMA[tableName];
        
        console.log(`   📊 ${existingColumns.length} colonnes trouvées`);
        
        expectedColumns.forEach(expectedCol => {
          if (existingColumns.includes(expectedCol)) {
            console.log(`   ✅ ${expectedCol}`);
          } else {
            console.log(`   ❌ ${expectedCol} (MANQUANTE)`);
          }
        });
        
        // Afficher les colonnes supplémentaires
        const extraColumns = existingColumns.filter(col => !expectedColumns.includes(col));
        if (extraColumns.length > 0) {
          console.log('   🔍 Colonnes supplémentaires:');
          extraColumns.forEach(col => {
            console.log(`   ➕ ${col}`);
          });
        }
        
      } catch (error) {
        console.log(`   ❌ Erreur vérification colonnes: ${error.message}`);
      }
    }
  }
}

async function countData(client, existingTables) {
  console.log('\n📈 ÉTAPE 4: Comptage des données');
  console.log('-'.repeat(50));
  
  const dataCounts = {};
  
  for (const tableName of existingTables) {
    if (EXPECTED_SCHEMA[tableName]) {
      try {
        const result = await client.query(`SELECT COUNT(*) as total FROM ${tableName}`);
        const count = parseInt(result.rows[0].total);
        dataCounts[tableName] = count;
        
        const status = count === 0 ? '⚪' : count < 10 ? '🟡' : count < 100 ? '🟠' : '🔴';
        console.log(`   ${status} ${tableName}: ${count.toLocaleString()} enregistrements`);
        
      } catch (error) {
        console.log(`   ❌ ${tableName}: Erreur comptage - ${error.message}`);
      }
    }
  }
  
  return dataCounts;
}

async function runDiagnostic() {
  let client;
  
  try {
    client = await testConnection();
    const tablesInfo = await checkTables(client);
    await checkColumns(client, tablesInfo.existingTables);
    const dataCounts = await countData(client, tablesInfo.existingTables);
    
    console.log('\n📋 RÉSUMÉ FINAL');
    console.log('='.repeat(60));
    
    console.log(`🌐 Serveur distant: *************:5432`);
    console.log(`🗄️  Base de données: glive_db`);
    
    if (tablesInfo.missingTables.length === 0) {
      console.log('✅ Toutes les tables requises sont présentes!');
    } else {
      console.log(`❌ ${tablesInfo.missingTables.length} table(s) manquante(s): ${tablesInfo.missingTables.join(', ')}`);
    }
    
    const totalRecords = Object.values(dataCounts).reduce((sum, count) => sum + count, 0);
    console.log(`📊 Total des enregistrements: ${totalRecords.toLocaleString()}`);
    
    if (totalRecords > 5000) {
      console.log('⚠️  Volume de données élevé - considérez un nettoyage');
    }
    
    // Recommandations spécifiques
    console.log('\n💡 RECOMMANDATIONS:');
    if (tablesInfo.missingTables.length > 0) {
      console.log('   🔧 Exécutez les migrations pour créer les tables manquantes');
    }
    if (dataCounts.shifts > 1000) {
      console.log('   🧹 Beaucoup de quarts - surveillez les performances');
    }
    
    console.log('\n🎉 DIAGNOSTIC TERMINÉ!');
    
  } catch (error) {
    console.error('\n💥 DIAGNOSTIC ÉCHOUÉ:', error.message);
    process.exit(1);
  } finally {
    if (client) client.release();
    await pool.end();
    console.log('🔌 Connexion fermée');
  }
}

console.log('🚀 Lancement du diagnostic sur serveur distant...\n');
runDiagnostic(); 