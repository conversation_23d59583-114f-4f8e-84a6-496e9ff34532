import { readFile, writeFile } from 'fs/promises';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const TARGET_FILE = join(__dirname, 'server', 'app.js');
const LOG_PREFIX = '🔧 [fix-backend-import]';

async function applyBackendImportFix() {
    console.log(`${LOG_PREFIX} Démarrage du correctif d'import backend...`);
    try {
        let content = await readFile(TARGET_FILE, 'utf-8');

        const importBlock = `import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createRequire } from 'module';

const require = createRequire(import.meta.url);
const { safeWrite } = require('./utils/safeWrite.cjs');`;

        const oldImport = `const { safeWrite } = require('./utils/safeWrite.cjs');`;

        // Vérifier si la correction est déjà appliquée
        if (content.includes("const require = createRequire(import.meta.url);")) {
            console.log(`${LOG_PREFIX} ✅ Le correctif semble déjà appliqué. Aucune modification.`);
            return;
        }

        // Supprimer l'ancien import s'il existe
        content = content.replace(oldImport, '');
        
        // Ajouter le nouveau bloc d'import au début du fichier
        content = `${importBlock}\n\n${content}`;

        await writeFile(TARGET_FILE, content, 'utf-8');
        console.log(`${LOG_PREFIX} ✅ Correction appliquée avec succès à server/app.js.`);

    } catch (error) {
        console.error(`${LOG_PREFIX} ❌ Erreur lors de l'application du correctif:`, error);
        process.exit(1);
    }
}

applyBackendImportFix(); 