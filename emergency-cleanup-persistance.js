// 🚨 SCRIPT D'URGENCE : Nettoyage immédiat des conflits de persistance
console.log('🚨 [EMERGENCY] Nettoyage d\'urgence des conflits de persistance...');

try {
    // 1. Vérifier l'intégrité des données
    if (window.TeamCalendarApp) {
        const issues = window.TeamCalendarApp.checkDataIntegrity();
        console.log(`🔍 [INTEGRITY] ${issues} problèmes détectés`);
        
        if (issues > 0) {
            console.log('🔧 [REPAIR] Début réparation rapide...');
            window.TeamCalendarApp.quickRepair();
        }
        
        // 2. Nettoyer les données corrompues en mémoire
        console.log('🧹 [CLEANUP] Nettoyage des données corrompues...');
        
        // Nettoyer les shifts invalides
        let cleanedShifts = 0;
        Object.keys(window.TeamCalendarApp.data.schedule || {}).forEach(employeeId => {
            Object.keys(window.TeamCalendarApp.data.schedule[employeeId] || {}).forEach(dateKey => {
                const originalLength = window.TeamCalendarApp.data.schedule[employeeId][dateKey].length;
                
                // Filtrer les shifts valides
                window.TeamCalendarApp.data.schedule[employeeId][dateKey] = 
                    window.TeamCalendarApp.data.schedule[employeeId][dateKey].filter(shift => {
                        return shift && 
                               typeof shift === 'object' && 
                               (shift.id || shift.postId) &&
                               shift.text !== undefined;
                    });
                
                const newLength = window.TeamCalendarApp.data.schedule[employeeId][dateKey].length;
                cleanedShifts += (originalLength - newLength);
                
                // Supprimer les jours vides
                if (window.TeamCalendarApp.data.schedule[employeeId][dateKey].length === 0) {
                    delete window.TeamCalendarApp.data.schedule[employeeId][dateKey];
                }
            });
            
            // Supprimer les employés sans shifts
            if (Object.keys(window.TeamCalendarApp.data.schedule[employeeId] || {}).length === 0) {
                delete window.TeamCalendarApp.data.schedule[employeeId];
            }
        });
        
        console.log(`🧹 [CLEANUP] ${cleanedShifts} shifts corrompus nettoyés`);
        
        // 3. Forcer une sauvegarde sécurisée
        console.log('💾 [SAVE] Sauvegarde d\'urgence...');
        if (typeof window.TeamCalendarApp.saveState === 'function') {
            window.TeamCalendarApp.saveState();
        }
        
        // 4. Nettoyer le localStorage corrompu
        console.log('🗑️ [STORAGE] Nettoyage localStorage...');
        const keysToCheck = [
            'teamCalendarState_v1',
            'teamCalendarAppSettings',
            'teamCalendar_corrupted_backup'
        ];
        
        keysToCheck.forEach(key => {
            try {
                const data = localStorage.getItem(key);
                if (data) {
                    const parsed = JSON.parse(data);
                    // Vérifier si les données sont corrompues
                    if (!parsed || typeof parsed !== 'object') {
                        localStorage.removeItem(key);
                        console.log(`🗑️ [STORAGE] Clé corrompue supprimée: ${key}`);
                    }
                }
            } catch (error) {
                localStorage.removeItem(key);
                console.log(`🗑️ [STORAGE] Clé invalide supprimée: ${key}`);
            }
        });
        
        // 5. Forcer un rendu sécurisé
        console.log('🎨 [RENDER] Rendu sécurisé...');
        if (typeof window.TeamCalendarApp.renderSafe === 'function') {
            window.TeamCalendarApp.renderSafe();
        } else if (typeof window.TeamCalendarApp.render === 'function') {
            window.TeamCalendarApp.render();
        }
        
        // 6. Activer la protection anti-conflit
        console.log('🛡️ [PROTECTION] Activation protection anti-conflit...');
        window.TeamCalendarApp._emergencyMode = true;
        
        // Remplacer temporairement les fonctions critiques
        const originalSaveState = window.TeamCalendarApp.saveState;
        window.TeamCalendarApp.saveState = function() {
            if (this._savingState) {
                console.warn('⚠️ [PROTECTION] Sauvegarde déjà en cours, ignorée');
                return;
            }
            return originalSaveState.call(this);
        };
        
        const originalRender = window.TeamCalendarApp.render;
        window.TeamCalendarApp.render = function() {
            if (this._renderingInProgress) {
                console.warn('⚠️ [PROTECTION] Rendu déjà en cours, ignoré');
                return;
            }
            return originalRender.call(this);
        };
        
        console.log('✅ [EMERGENCY] Nettoyage d\'urgence terminé avec succès !');
        console.log('📋 [EMERGENCY] Résumé :');
        console.log(`   - ${issues} problèmes d'intégrité réparés`);
        console.log(`   - ${cleanedShifts} shifts corrompus nettoyés`);
        console.log('   - localStorage nettoyé');
        console.log('   - Protection anti-conflit activée');
        
        // Toast de succès si disponible
        if (window.toastSystem) {
            window.toastSystem.success('🚨 Nettoyage d\'urgence terminé ! Application stabilisée.');
        } else {
            alert('✅ Nettoyage d\'urgence terminé ! Application stabilisée.');
        }
        
    } else {
        console.error('❌ [EMERGENCY] TeamCalendarApp non disponible');
        
        // Nettoyage de base si l'app n'est pas disponible
        console.log('🗑️ [EMERGENCY] Nettoyage de base du localStorage...');
        localStorage.removeItem('teamCalendarState_v1');
        localStorage.removeItem('teamCalendarAppSettings');
        sessionStorage.clear();
        
        console.log('🔄 [EMERGENCY] Rechargement de la page recommandé');
        if (confirm('L\'application semble corrompue. Recharger la page ?')) {
            location.reload();
        }
    }
    
} catch (error) {
    console.error('❌ [EMERGENCY] Erreur critique lors du nettoyage:', error);
    
    // Dernier recours
    console.log('🚨 [EMERGENCY] Dernier recours - Nettoyage complet...');
    localStorage.clear();
    sessionStorage.clear();
    
    if (confirm('Erreur critique détectée. Rechargement complet nécessaire ?')) {
        location.reload();
    }
}

console.log('🎯 [EMERGENCY] Script d\'urgence terminé'); 