/**
 * ===== SYSTÈME DE TUTORIEL INTERACTIF AMÉLIORÉ V2 =====
 * Version avec positionnement intelligent, simulations en boucle et gestion des superpositions
 */

// ✅ CORRIGÉ : Styles CSS chargés via HTML au lieu d'import ES6
// import './styles/interactive-tutorial-v2.css';

class InteractiveTutorialV2 {
    constructor() {
        this.isActive = false;
        this.currentStep = 0;
        this.currentTutorial = null;
        this.overlays = [];
        this.simulationElements = [];
        this.animationTimeouts = [];
        this.simulationIntervals = [];
        this.currentSimulationLoop = null;
        
        // Définition des tutoriels avec simulations
        this.tutorials = {
            beginner: this.getBeginnerTutorial(),
            advanced: this.getAdvancedTutorial(),
            features: this.getFeaturesTutorial()
        };
        
        this.init();
    }

    init() {
        // Créer l'icône d'aide flottante
        this.createHelpIcon();
        
        // Écouter les événements
        this.setupEventListeners();
        
        console.log('🎓 [Tutorial] Système de tutoriel interactif V2 initialisé');
    }

    createHelpIcon() {
        const helpIcon = document.createElement('div');
        helpIcon.className = 'tutorial-help-icon';
        helpIcon.innerHTML = '🎓';
        helpIcon.title = '🎓 Besoin d\'aide ? Cliquez pour découvrir l\'application !';
        
        helpIcon.addEventListener('click', () => this.showMainMenu());
        
        document.body.appendChild(helpIcon);
        this.helpIcon = helpIcon;
    }

    setupEventListeners() {
        // Écouter les touches du clavier
        document.addEventListener('keydown', (e) => {
            if (!this.isActive) return;
            
            switch(e.key) {
                case 'Escape':
                    this.closeTutorial();
                    break;
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    this.nextStep();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.previousStep();
                    break;
            }
        });
        
        // Redimensionnement de la fenêtre
        window.addEventListener('resize', () => {
            if (this.isActive && this.currentBubble) {
                this.repositionBubble();
            }
        });
    }

    showMainMenu() {
        const overlay = document.createElement('div');
        overlay.className = 'tutorial-overlay';
        
        overlay.innerHTML = `
            <div class="tutorial-menu">
                <h2>🌟 Bienvenue dans ton Calendrier Magique !</h2>
                <p class="subtitle">Choisis ton aventure pour découvrir tous les secrets avec des démonstrations en direct !</p>
                
                <div class="tutorial-options">
                    <div class="tutorial-option beginner" data-tutorial="beginner">
                        <div class="icon">🌱</div>
                        <div class="content">
                            <h3>Je découvre tout !</h3>
                            <p>Parfait pour commencer ! Avec des simulations automatiques pour tout comprendre.</p>
                        </div>
                    </div>
                    
                    <div class="tutorial-option advanced" data-tutorial="advanced">
                        <div class="icon">🚀</div>
                        <div class="content">
                            <h3>Les super pouvoirs !</h3>
                            <p>Tu connais déjà ? Découvre les fonctions secrètes avec des démonstrations live !</p>
                        </div>
                    </div>
                    
                    <div class="tutorial-option features" data-tutorial="features">
                        <div class="icon">✨</div>
                        <div class="content">
                            <h3>Tour des fonctionnalités</h3>
                            <p>Une visite guidée avec simulations automatiques de toutes les fonctions !</p>
                        </div>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button class="tutorial-btn skip" onclick="window.interactiveTutorial.closeMainMenu()">
                        Peut-être plus tard 😊
                    </button>
                </div>
            </div>
        `;
        
        // Ajouter les listeners pour les options
        overlay.querySelectorAll('.tutorial-option').forEach(option => {
            option.addEventListener('click', () => {
                const tutorialType = option.dataset.tutorial;
                this.startTutorial(tutorialType);
            });
        });
        
        document.body.appendChild(overlay);
        this.mainMenuOverlay = overlay;
    }

    closeMainMenu() {
        if (this.mainMenuOverlay) {
            this.mainMenuOverlay.remove();
            this.mainMenuOverlay = null;
        }
    }

    startTutorial(type) {
        this.closeMainMenu();
        this.currentTutorial = this.tutorials[type];
        this.currentStep = 0;
        this.isActive = true;
        
        console.log(`🎓 [Tutorial] Démarrage du tutoriel: ${type}`);
        this.showStep();
    }

    showStep() {
        if (!this.currentTutorial || this.currentStep >= this.currentTutorial.length) {
            this.completeTutorial();
            return;
        }

        const step = this.currentTutorial[this.currentStep];
        
        // Nettoyer les overlays et animations précédents
        this.clearOverlays();
        this.clearAnimations();
        this.stopSimulationLoop();
        
        // Créer l'overlay de mise en évidence amélioré
        this.createEnhancedHighlightOverlay(step);
        
        // Créer la bulle d'explication avec positionnement intelligent
        this.createExplanationBubble(step);
        
        // Démarrer les simulations en boucle si définies
        if (step.simulation) {
            this.startSimulationLoop(step.simulation);
        }
        
        console.log(`🎓 [Tutorial] Étape ${this.currentStep + 1}/${this.currentTutorial.length}: ${step.title}`);
    }

    createEnhancedHighlightOverlay(step) {
        if (!step.target || step.target === "#root") return;
        
        try {
            // Overlay de fond semi-transparent
            const overlay = document.createElement('div');
            overlay.className = 'tutorial-highlight-overlay-enhanced';
            overlay.style.cssText = `
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background: rgba(0, 0, 0, 0.7) !important;
                z-index: 9998 !important;
                pointer-events: none !important;
                animation: tutorial-fade-in 0.5s ease !important;
            `;
            
            const target = document.querySelector(step.target);
            if (!target) {
                console.warn(`🎓 [Tutorial] Élément non trouvé: ${step.target}`);
                document.body.appendChild(overlay);
                this.overlays.push(overlay);
                return;
            }
            
            const rect = target.getBoundingClientRect();
            
            // Créer un trou dans l'overlay pour l'élément cible
            const spotlight = document.createElement('div');
            spotlight.className = 'tutorial-spotlight-enhanced';
            
            // Ajouter plus d'espace autour de l'élément pour éviter la superposition
            const padding = 20;
            spotlight.style.cssText = `
                position: fixed !important;
                left: ${rect.left - padding}px !important;
                top: ${rect.top - padding}px !important;
                width: ${rect.width + padding * 2}px !important;
                height: ${rect.height + padding * 2}px !important;
                border-radius: 12px !important;
                box-shadow: 
                    0 0 0 9999px rgba(0, 0, 0, 0.7),
                    0 0 50px rgba(79, 70, 229, 0.6),
                    inset 0 0 20px rgba(79, 70, 229, 0.2) !important;
                z-index: 9999 !important;
                pointer-events: none !important;
            `;
            
            // Bordure animée autour de l'élément
            const border = document.createElement('div');
            border.className = 'tutorial-highlight-border';
            border.style.cssText = `
                position: fixed !important;
                left: ${rect.left - padding - 4}px !important;
                top: ${rect.top - padding - 4}px !important;
                width: ${rect.width + padding * 2 + 8}px !important;
                height: ${rect.height + padding * 2 + 8}px !important;
                border: 3px solid #4f46e5 !important;
                border-radius: 16px !important;
                z-index: 9999 !important;
                pointer-events: none !important;
                animation: tutorial-border-pulse 2s infinite !important;
            `;
            
            document.body.appendChild(overlay);
            document.body.appendChild(spotlight);
            document.body.appendChild(border);
            
            this.overlays.push(overlay, spotlight, border);
            
            // Stocker la référence de l'élément cible pour le repositionnement
            this.currentTargetElement = target;
            
        } catch (error) {
            console.error('🎓 [Tutorial] Erreur lors de la création de l\'overlay:', error);
        }
    }

    createExplanationBubble(step) {
        try {
            const bubble = document.createElement('div');
            bubble.className = `tutorial-bubble-enhanced`;
            
            // Styles CSS inline pour garantir l'affichage
            bubble.style.cssText = `
                position: fixed !important;
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
                border-radius: 20px !important;
                padding: 25px !important;
                max-width: 400px !important;
                min-width: 320px !important;
                box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(79, 70, 229, 0.1) !important;
                z-index: 10001 !important;
                animation: tutorial-bubble-appear-enhanced 0.6s ease !important;
                font-family: Inter, system-ui, sans-serif !important;
                color: #374151 !important;
                border: 3px solid #4f46e5 !important;
            `;
            
            bubble.innerHTML = `
                <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 15px;">
                    <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #4f46e5, #7c3aed); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 20px;">
                        ${step.emoji || '🎯'}
                    </div>
                    <h3 style="color: #4f46e5 !important; font-size: 20px !important; font-weight: 700 !important; margin: 0 !important;">
                        ${step.title}
                    </h3>
                </div>
                <p style="color: #374151 !important; font-size: 16px !important; line-height: 1.6 !important; margin-bottom: 20px !important;">
                    ${step.explanation}
                </p>
                
                ${step.simulation ? `
                    <div style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 12px; padding: 12px; margin-bottom: 15px;">
                        <div style="display: flex; align-items: center; gap: 8px; color: #0369a1; font-size: 14px; font-weight: 600;">
                            <span class="tutorial-demo-indicator">🎬</span> 
                            <span>Démonstration en cours...</span>
                        </div>
                    </div>
                ` : ''}
                
                <div style="display: flex; justify-content: space-between; align-items: center; gap: 15px;">
                    <div style="font-size: 14px; color: #6b7280; background: #f3f4f6; padding: 6px 12px; border-radius: 20px; font-weight: 500;">
                        ${this.currentStep + 1} / ${this.currentTutorial.length}
                    </div>
                    
                    <div style="display: flex; gap: 10px;">
                        ${this.currentStep > 0 ? 
                            '<button class="tutorial-btn-prev" style="padding: 10px 20px; border-radius: 12px; border: none; cursor: pointer; font-size: 14px; font-weight: 600; background: #f3f4f6; color: #374151; transition: all 0.2s;">← Précédent</button>' : 
                            ''
                        }
                        <button class="tutorial-btn-next" style="padding: 10px 20px; border-radius: 12px; border: none; cursor: pointer; font-size: 14px; font-weight: 600; background: linear-gradient(135deg, #4f46e5, #7c3aed); color: white; transition: all 0.2s;">
                            ${this.currentStep < this.currentTutorial.length - 1 ? 'Suivant →' : 'Terminer ✨'}
                        </button>
                        <button class="tutorial-btn-close" style="padding: 10px 20px; border-radius: 12px; border: none; cursor: pointer; font-size: 14px; font-weight: 500; background: transparent; color: #6b7280; text-decoration: underline;">
                            Arrêter
                        </button>
                    </div>
                </div>
            `;
            
            // Ajouter les événements
            const prevBtn = bubble.querySelector('.tutorial-btn-prev');
            const nextBtn = bubble.querySelector('.tutorial-btn-next');
            const closeBtn = bubble.querySelector('.tutorial-btn-close');
            
            if (prevBtn) {
                prevBtn.addEventListener('click', () => this.previousStep());
                prevBtn.addEventListener('mouseenter', () => {
                    prevBtn.style.background = '#e5e7eb';
                    prevBtn.style.transform = 'translateY(-2px)';
                });
                prevBtn.addEventListener('mouseleave', () => {
                    prevBtn.style.background = '#f3f4f6';
                    prevBtn.style.transform = 'translateY(0)';
                });
            }
            
            nextBtn.addEventListener('click', () => this.nextStep());
            nextBtn.addEventListener('mouseenter', () => {
                nextBtn.style.transform = 'translateY(-2px)';
                nextBtn.style.boxShadow = '0 8px 25px rgba(79, 70, 229, 0.3)';
            });
            nextBtn.addEventListener('mouseleave', () => {
                nextBtn.style.transform = 'translateY(0)';
                nextBtn.style.boxShadow = 'none';
            });
            
            closeBtn.addEventListener('click', () => this.closeTutorial());
            closeBtn.addEventListener('mouseenter', () => {
                closeBtn.style.color = '#374151';
            });
            closeBtn.addEventListener('mouseleave', () => {
                closeBtn.style.color = '#6b7280';
            });
            
            document.body.appendChild(bubble);
            this.overlays.push(bubble);
            this.currentBubble = bubble;
            
            // Positionner la bulle intelligemment
            this.positionBubbleIntelligently(bubble, step);
            
            console.log('🎓 [Tutorial] Bulle créée avec positionnement intelligent');
        } catch (error) {
            console.error('🎓 [Tutorial] Erreur lors de la création de la bulle:', error);
        }
    }

    positionBubbleIntelligently(bubble, step) {
        try {
            // Attendre que la bulle soit rendue pour obtenir ses dimensions
            setTimeout(() => {
                const bubbleRect = bubble.getBoundingClientRect();
                const windowWidth = window.innerWidth;
                const windowHeight = window.innerHeight;
                const margin = 30; // Marge de sécurité
                
                let targetRect = null;
                let target = null;
                
                if (step.target && step.target !== "#root") {
                    target = document.querySelector(step.target);
                    if (target) {
                        targetRect = target.getBoundingClientRect();
                    }
                }
                
                // Si pas de cible ou cible au centre, positionner au centre
                if (!targetRect || step.target === "#root") {
                    bubble.style.top = '50%';
                    bubble.style.left = '50%';
                    bubble.style.transform = 'translate(-50%, -50%)';
                    return;
                }
                
                // Calculer les positions possibles autour de l'élément
                const positions = {
                    // Au-dessus
                    top: {
                        left: targetRect.left + (targetRect.width - bubbleRect.width) / 2,
                        top: targetRect.top - bubbleRect.height - margin,
                        feasible: targetRect.top - bubbleRect.height - margin > margin
                    },
                    // En dessous
                    bottom: {
                        left: targetRect.left + (targetRect.width - bubbleRect.width) / 2,
                        top: targetRect.bottom + margin,
                        feasible: targetRect.bottom + margin + bubbleRect.height < windowHeight - margin
                    },
                    // À gauche
                    left: {
                        left: targetRect.left - bubbleRect.width - margin,
                        top: targetRect.top + (targetRect.height - bubbleRect.height) / 2,
                        feasible: targetRect.left - bubbleRect.width - margin > margin
                    },
                    // À droite
                    right: {
                        left: targetRect.right + margin,
                        top: targetRect.top + (targetRect.height - bubbleRect.height) / 2,
                        feasible: targetRect.right + margin + bubbleRect.width < windowWidth - margin
                    },
                    // En haut à gauche
                    topLeft: {
                        left: targetRect.left - bubbleRect.width - margin,
                        top: targetRect.top - bubbleRect.height - margin,
                        feasible: targetRect.left - bubbleRect.width - margin > margin && 
                                 targetRect.top - bubbleRect.height - margin > margin
                    },
                    // En haut à droite
                    topRight: {
                        left: targetRect.right + margin,
                        top: targetRect.top - bubbleRect.height - margin,
                        feasible: targetRect.right + margin + bubbleRect.width < windowWidth - margin && 
                                 targetRect.top - bubbleRect.height - margin > margin
                    },
                    // En bas à gauche
                    bottomLeft: {
                        left: targetRect.left - bubbleRect.width - margin,
                        top: targetRect.bottom + margin,
                        feasible: targetRect.left - bubbleRect.width - margin > margin && 
                                 targetRect.bottom + margin + bubbleRect.height < windowHeight - margin
                    },
                    // En bas à droite
                    bottomRight: {
                        left: targetRect.right + margin,
                        top: targetRect.bottom + margin,
                        feasible: targetRect.right + margin + bubbleRect.width < windowWidth - margin && 
                                 targetRect.bottom + margin + bubbleRect.height < windowHeight - margin
                    }
                };
                
                // Ordre de préférence basé sur la position suggérée
                let preferredOrder = ['bottom', 'top', 'right', 'left', 'bottomRight', 'bottomLeft', 'topRight', 'topLeft'];
                
                if (step.bubblePosition) {
                    switch(step.bubblePosition) {
                        case 'top':
                            preferredOrder = ['top', 'topRight', 'topLeft', 'bottom', 'left', 'right'];
                            break;
                        case 'bottom':
                            preferredOrder = ['bottom', 'bottomRight', 'bottomLeft', 'top', 'left', 'right'];
                            break;
                        case 'left':
                            preferredOrder = ['left', 'topLeft', 'bottomLeft', 'right', 'top', 'bottom'];
                            break;
                        case 'right':
                            preferredOrder = ['right', 'topRight', 'bottomRight', 'left', 'top', 'bottom'];
                            break;
                    }
                }
                
                // Trouver la première position réalisable
                let chosenPosition = null;
                for (const posName of preferredOrder) {
                    if (positions[posName].feasible) {
                        chosenPosition = positions[posName];
                        break;
                    }
                }
                
                // Si aucune position n'est idéale, utiliser la meilleure disponible
                if (!chosenPosition) {
                    // Essayer de positionner où il y a le plus d'espace
                    const spaceTop = targetRect.top;
                    const spaceBottom = windowHeight - targetRect.bottom;
                    const spaceLeft = targetRect.left;
                    const spaceRight = windowWidth - targetRect.right;
                    
                    const maxSpace = Math.max(spaceTop, spaceBottom, spaceLeft, spaceRight);
                    
                    if (maxSpace === spaceBottom) {
                        chosenPosition = positions.bottom;
                    } else if (maxSpace === spaceTop) {
                        chosenPosition = positions.top;
                    } else if (maxSpace === spaceRight) {
                        chosenPosition = positions.right;
                    } else {
                        chosenPosition = positions.left;
                    }
                }
                
                // Appliquer la position avec ajustements pour rester dans la fenêtre
                let finalLeft = chosenPosition.left;
                let finalTop = chosenPosition.top;
                
                // Ajustements horizontaux
                if (finalLeft < margin) finalLeft = margin;
                if (finalLeft + bubbleRect.width > windowWidth - margin) {
                    finalLeft = windowWidth - bubbleRect.width - margin;
                }
                
                // Ajustements verticaux
                if (finalTop < margin) finalTop = margin;
                if (finalTop + bubbleRect.height > windowHeight - margin) {
                    finalTop = windowHeight - bubbleRect.height - margin;
                }
                
                bubble.style.left = `${finalLeft}px`;
                bubble.style.top = `${finalTop}px`;
                bubble.style.transform = 'none';
                
                console.log(`🎓 [Tutorial] Bulle positionnée intelligemment à ${finalLeft}, ${finalTop}`);
                
            }, 100);
            
        } catch (error) {
            console.error('🎓 [Tutorial] Erreur lors du positionnement:', error);
            bubble.style.top = '50%';
            bubble.style.left = '50%';
            bubble.style.transform = 'translate(-50%, -50%)';
        }
    }

    repositionBubble() {
        if (this.currentBubble && this.currentTutorial && this.currentStep < this.currentTutorial.length) {
            const step = this.currentTutorial[this.currentStep];
            this.positionBubbleIntelligently(this.currentBubble, step);
        }
    }

    // ===== SYSTÈME DE SIMULATION EN BOUCLE =====
    
    startSimulationLoop(simulation) {
        console.log('🎬 [Tutorial] Démarrage de la simulation en boucle:', simulation.type);
        
        // Exécuter la première fois immédiatement
        this.executeSimulation(simulation);
        
        // Puis répéter en boucle
        const loopDelay = simulation.loopDelay || 3000; // Délai par défaut de 3 secondes
        
        this.currentSimulationLoop = setInterval(() => {
            this.clearSimulationElements();
            this.executeSimulation(simulation);
        }, loopDelay);
    }
    
    stopSimulationLoop() {
        if (this.currentSimulationLoop) {
            clearInterval(this.currentSimulationLoop);
            this.currentSimulationLoop = null;
        }
        this.clearSimulationElements();
    }
    
    clearSimulationElements() {
        // Nettoyer uniquement les éléments de simulation, pas les overlays
        this.simulationElements.forEach(element => {
            if (element && element.parentNode) {
                element.remove();
            }
        });
        this.simulationElements = [];
    }

    executeSimulation(simulation) {
        try {
            switch(simulation.type) {
                case 'drag-drop':
                    this.simulateDragDrop(simulation);
                    break;
                case 'click':
                    this.simulateClick(simulation);
                    break;
                case 'hover':
                    this.simulateHover(simulation);
                    break;
                case 'type':
                    this.simulateTyping(simulation);
                    break;
                case 'highlight-sequence':
                    this.simulateHighlightSequence(simulation);
                    break;
                case 'cursor-movement':
                    this.simulateCursorMovement(simulation);
                    break;
            }
        } catch (error) {
            console.warn('🎬 [Tutorial] Erreur dans la simulation:', error);
        }
    }

    simulateDragDrop(simulation) {
        const sourceEl = document.querySelector(simulation.source);
        const targetEl = document.querySelector(simulation.target);
        
        if (!sourceEl || !targetEl) {
            console.warn('🎬 [Tutorial] Éléments non trouvés pour la simulation drag-drop');
            return;
        }
        
        const sourceRect = sourceEl.getBoundingClientRect();
        const targetRect = targetEl.getBoundingClientRect();
        
        // Créer un élément fantôme pour la simulation
        const ghost = sourceEl.cloneNode(true);
        ghost.style.cssText = `
            position: fixed !important;
            left: ${sourceRect.left}px !important;
            top: ${sourceRect.top}px !important;
            width: ${sourceRect.width}px !important;
            height: ${sourceRect.height}px !important;
            z-index: 10002 !important;
            opacity: 0.8 !important;
            transform: scale(1.05) !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
            transition: all 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
            pointer-events: none !important;
            border: 3px solid #4f46e5 !important;
            border-radius: 8px !important;
        `;
        
        document.body.appendChild(ghost);
        this.simulationElements.push(ghost);
        
        // Animer le déplacement
        setTimeout(() => {
            ghost.style.left = `${targetRect.left}px`;
            ghost.style.top = `${targetRect.top}px`;
            ghost.style.transform = 'scale(1.1)';
        }, 500);
        
        // Effet de drop
        setTimeout(() => {
            ghost.style.opacity = '0';
            ghost.style.transform = 'scale(0.8)';
            
            // Effet visuel sur la cible
            const originalBg = targetEl.style.background;
            const originalBorder = targetEl.style.border;
            const originalTransform = targetEl.style.transform;
            
            targetEl.style.background = 'rgba(79, 70, 229, 0.1)';
            targetEl.style.border = '2px dashed #4f46e5';
            targetEl.style.transform = 'scale(1.02)';
            
            setTimeout(() => {
                targetEl.style.background = originalBg;
                targetEl.style.border = originalBorder;
                targetEl.style.transform = originalTransform;
            }, 500);
        }, 2500);
    }

    simulateClick(simulation) {
        const element = document.querySelector(simulation.target);
        if (!element) return;
        
        const rect = element.getBoundingClientRect();
        
        // Créer un effet de clic visuel
        const clickEffect = document.createElement('div');
        clickEffect.style.cssText = `
            position: fixed !important;
            left: ${rect.left + rect.width/2 - 15}px !important;
            top: ${rect.top + rect.height/2 - 15}px !important;
            width: 30px !important;
            height: 30px !important;
            border: 3px solid #4f46e5 !important;
            border-radius: 50% !important;
            z-index: 10003 !important;
            pointer-events: none !important;
            opacity: 0 !important;
            animation: tutorial-click-ripple 1s ease-out forwards !important;
        `;
        
        document.body.appendChild(clickEffect);
        this.simulationElements.push(clickEffect);
        
        // Effet sur l'élément
        const originalTransform = element.style.transform;
        const originalBg = element.style.background;
        
        element.style.transform = 'scale(0.95)';
        element.style.background = 'rgba(79, 70, 229, 0.1)';
        
        setTimeout(() => {
            element.style.transform = originalTransform;
            element.style.background = originalBg;
        }, 300);
    }

    simulateHover(simulation) {
        const element = document.querySelector(simulation.target);
        if (!element) return;
        
        // Sauvegarder les styles originaux
        const originalTransform = element.style.transform;
        const originalShadow = element.style.boxShadow;
        const originalBg = element.style.background;
        
        // Simuler un survol
        element.style.transform = 'translateY(-2px)';
        element.style.boxShadow = '0 8px 25px rgba(79, 70, 229, 0.2)';
        element.style.background = 'rgba(79, 70, 229, 0.05)';
        
        // Restaurer après 2 secondes
        setTimeout(() => {
            element.style.transform = originalTransform;
            element.style.boxShadow = originalShadow;
            element.style.background = originalBg;
        }, 2000);
    }

    simulateTyping(simulation) {
        const element = document.querySelector(simulation.target);
        if (!element) return;
        
        const text = simulation.text || 'Exemple de texte...';
        let currentText = '';
        let index = 0;
        
        // Effacer le contenu existant
        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
            element.value = '';
        } else {
            element.textContent = '';
        }
        
        const typeInterval = setInterval(() => {
            if (index < text.length) {
                currentText += text[index];
                if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                    element.value = currentText;
                } else {
                    element.textContent = currentText;
                }
                index++;
            } else {
                clearInterval(typeInterval);
            }
        }, 100);
        
        this.simulationIntervals.push(typeInterval);
    }

    simulateHighlightSequence(simulation) {
        const elements = simulation.targets.map(target => document.querySelector(target)).filter(el => el);
        
        elements.forEach((element, index) => {
            setTimeout(() => {
                if (!element) return;
                
                // Sauvegarder les styles originaux
                const originalBg = element.style.background;
                const originalBorder = element.style.border;
                const originalRadius = element.style.borderRadius;
                const originalTransform = element.style.transform;
                
                element.style.background = 'rgba(79, 70, 229, 0.1)';
                element.style.border = '2px solid #4f46e5';
                element.style.borderRadius = '8px';
                element.style.transform = 'scale(1.02)';
                
                setTimeout(() => {
                    element.style.background = originalBg;
                    element.style.border = originalBorder;
                    element.style.borderRadius = originalRadius;
                    element.style.transform = originalTransform;
                }, 1500);
            }, index * 800);
        });
    }

    simulateCursorMovement(simulation) {
        // Créer un curseur simulé
        const cursor = document.createElement('div');
        cursor.className = 'tutorial-cursor';
        cursor.innerHTML = '👆';
        cursor.style.cssText = `
            position: fixed !important;
            width: 30px !important;
            height: 30px !important;
            font-size: 24px !important;
            z-index: 10004 !important;
            pointer-events: none !important;
            filter: drop-shadow(0 0 10px rgba(79, 70, 229, 0.5)) !important;
            transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
        `;
        
        document.body.appendChild(cursor);
        this.simulationElements.push(cursor);
        
        // Position initiale
        if (simulation.path && simulation.path.length > 0) {
            cursor.style.left = `${simulation.path[0].x}px`;
            cursor.style.top = `${simulation.path[0].y}px`;
        }
        
        // Déplacer le curseur selon le chemin défini
        simulation.path.forEach((point, index) => {
            setTimeout(() => {
                cursor.style.left = `${point.x}px`;
                cursor.style.top = `${point.y}px`;
                
                if (index === simulation.path.length - 1) {
                    // Dernier point - faire disparaître le curseur
                    setTimeout(() => {
                        cursor.style.opacity = '0';
                    }, 1000);
                }
            }, index * 1000);
        });
    }

    clearAnimations() {
        // Nettoyer tous les timeouts
        this.animationTimeouts.forEach(timeout => {
            clearTimeout(timeout);
        });
        this.animationTimeouts = [];
        
        // Nettoyer tous les intervals
        this.simulationIntervals.forEach(interval => {
            clearInterval(interval);
        });
        this.simulationIntervals = [];
        
        // Arrêter la boucle de simulation
        this.stopSimulationLoop();
        
        // Supprimer les éléments de simulation
        this.clearSimulationElements();
    }

    nextStep() {
        this.currentStep++;
        this.showStep();
    }

    previousStep() {
        if (this.currentStep > 0) {
            this.currentStep--;
            this.showStep();
        }
    }

    completeTutorial() {
        this.clearOverlays();
        this.clearAnimations();
        
        // Afficher un message de félicitations
        const congratsOverlay = document.createElement('div');
        congratsOverlay.className = 'tutorial-overlay';
        congratsOverlay.innerHTML = `
            <div class="tutorial-menu">
                <h2>🎉 Bravo, champion !</h2>
                <p class="subtitle">Tu as terminé le tutoriel avec toutes les démonstrations ! Tu es maintenant un expert du calendrier magique !</p>
                
                <div style="text-align: center; margin: 30px 0;">
                    <div style="font-size: 80px; margin-bottom: 20px;">🏆</div>
                    <p style="font-size: 20px; color: #4f46e5; font-weight: 700;">
                        Certificat d'Expert du Calendrier obtenu !
                    </p>
                    <p style="font-size: 14px; color: #6b7280; margin-top: 10px;">
                        Tu as vu toutes les simulations et maîtrises maintenant toutes les fonctionnalités !
                    </p>
                </div>
                
                <div style="text-align: center;">
                    <button class="tutorial-btn primary" onclick="window.interactiveTutorial.finishTutorial()" style="font-size: 18px; padding: 15px 30px;">
                        Commencer à utiliser le calendrier ! 🚀
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(congratsOverlay);
        this.congratsOverlay = congratsOverlay;
        
        // Marquer le tutoriel comme terminé
        localStorage.setItem('tutorial-completed', 'true');
    }

    finishTutorial() {
        if (this.congratsOverlay) {
            this.congratsOverlay.remove();
        }
        this.closeTutorial();
    }

    closeTutorial() {
        this.clearOverlays();
        this.clearAnimations();
        this.isActive = false;
        this.currentTutorial = null;
        this.currentStep = 0;
        this.currentBubble = null;
        this.currentTargetElement = null;
        
        console.log('🎓 [Tutorial] Tutoriel fermé');
    }

    clearOverlays() {
        this.overlays.forEach(overlay => {
            if (overlay && overlay.parentNode) {
                overlay.remove();
            }
        });
        this.overlays = [];
    }

    // ===== DÉFINITION DES TUTORIELS AVEC SIMULATIONS =====

    getBeginnerTutorial() {
        return [
            {
                title: "Bienvenue dans ton Calendrier Magique !",
                explanation: "Salut petit explorateur ! 👋 Ici, c'est ton calendrier magique où tu peux organiser le travail de toute ton équipe. Regarde comme c'est coloré et organisé !",
                emoji: "🌟",
                target: "#root",
                bubblePosition: "center"
            },
            {
                title: "Regarde, voici la liste de tes amis !",
                explanation: "À gauche, tu vois tous tes collègues ! 👥 Chaque personne a sa propre ligne, comme dans un carnet d'adresses. Je vais te montrer en surlignant chaque ligne !",
                emoji: "👥",
                target: "#employee-list-container",
                bubblePosition: "right",
                simulation: {
                    type: 'highlight-sequence',
                    targets: ['.employee-row:nth-child(1)', '.employee-row:nth-child(2)', '.employee-row:nth-child(3)'],
                    loopDelay: 4000
                }
            },
            {
                title: "Et voici les jours de la semaine !",
                explanation: "En haut, tu vois tous les jours de la semaine ! 📅 Dimanche, Lundi, Mardi... jusqu'à Samedi ! Regarde comme je les éclaire un par un !",
                emoji: "📅",
                target: ".calendar-header",
                bubblePosition: "bottom",
                simulation: {
                    type: 'highlight-sequence',
                    targets: ['.day-header:nth-child(1)', '.day-header:nth-child(2)', '.day-header:nth-child(3)', '.day-header:nth-child(4)', '.day-header:nth-child(5)', '.day-header:nth-child(6)', '.day-header:nth-child(7)'],
                    loopDelay: 6000
                }
            },
            {
                title: "Les petites boîtes colorées sont magiques !",
                explanation: "Tu vois ces petites boîtes colorées ? 🎨 Ce sont les 'quarts de travail' ! Chaque couleur représente un travail différent. Regarde comme elles brillent quand je les survole !",
                emoji: "🎨",
                target: ".shift-card",
                bubblePosition: "left",
                simulation: {
                    type: 'hover',
                    target: '.shift-card',
                    loopDelay: 3000
                }
            },
            {
                title: "Tu peux déplacer les boîtes comme des jouets !",
                explanation: "Le plus magique ? Tu peux prendre une boîte et la glisser vers un autre ami ! 🪄 Regarde bien cette démonstration : je vais déplacer une boîte d'un employé à un autre !",
                emoji: "🪄",
                target: ".employee-row:nth-child(2)",
                bubblePosition: "right",
                simulation: {
                    type: 'drag-drop',
                    source: '.shift-card:first-child',
                    target: '.employee-row:nth-child(2) .employee-shifts',
                    loopDelay: 4000
                }
            },
            {
                title: "Les postes disponibles t'attendent !",
                explanation: "À droite, tu vois les 'postes disponibles' ! 📋 Ce sont comme des missions qui attendent qu'on les assigne à quelqu'un. Je vais te montrer comment en déplacer un !",
                emoji: "📋",
                target: "#available-posts-container",
                bubblePosition: "left",
                simulation: {
                    type: 'drag-drop',
                    source: '.post-row-info:first-child',
                    target: '.employee-row:first-child .employee-shifts',
                    loopDelay: 4000
                }
            },
            {
                title: "Les boutons magiques en haut !",
                explanation: "En haut à droite, tu as plein de boutons magiques ! 🔧 Le bouton 'CORRIGER' répare tout, 'SEMAINE' change les dates, et d'autres surprises ! Regarde comme ils réagissent !",
                emoji: "🔧",
                target: ".control-buttons",
                bubblePosition: "bottom",
                simulation: {
                    type: 'click',
                    target: '.control-buttons button:first-child',
                    loopDelay: 2500
                }
            },
            {
                title: "Tu es maintenant prêt à explorer !",
                explanation: "Bravo ! 🎉 Tu connais maintenant tous les secrets de base ! Tu peux cliquer, glisser, déposer, et organiser ton équipe comme un vrai chef ! Amuse-toi bien !",
                emoji: "🎉",
                target: "#root",
                bubblePosition: "center"
            }
        ];
    }

    getAdvancedTutorial() {
        return [
            {
                title: "Bienvenue dans les super pouvoirs !",
                explanation: "Tu es déjà un expert ? Parfait ! 🚀 Je vais te montrer les fonctions secrètes et les astuces de ninja que seuls les vrais pros connaissent !",
                emoji: "🚀",
                target: "#root",
                bubblePosition: "center"
            },
            {
                title: "Le glisser-déposer des attributions régulières !",
                explanation: "Regarde sous les blocs récurrents... tu vois ces petites poignées (⋮⋮⋮) ? 🎯 Tu peux les saisir pour déplacer toute une attribution régulière ! C'est du niveau ninja !",
                emoji: "🎯",
                target: ".grip-regular",
                bubblePosition: "right",
                simulation: {
                    type: 'hover',
                    target: '.grip-regular',
                    loopDelay: 3000
                }
            },
            {
                title: "Les raccourcis clavier secrets !",
                explanation: "Pssst... 🤫 Tu peux naviguer avec les flèches ← → et Espace pour avancer, Échap pour fermer ! Essaie maintenant avec ce tutoriel !",
                emoji: "⌨️",
                target: "#root",
                bubblePosition: "center",
                simulation: {
                    type: 'cursor-movement',
                    path: [
                        {x: window.innerWidth/2 - 100, y: window.innerHeight/2},
                        {x: window.innerWidth/2 + 100, y: window.innerHeight/2},
                        {x: window.innerWidth/2, y: window.innerHeight/2 - 50},
                        {x: window.innerWidth/2, y: window.innerHeight/2 + 50}
                    ],
                    loopDelay: 5000
                }
            },
            {
                title: "Configuration avancée des postes !",
                explanation: "Dans les paramètres ⚙️, tu peux créer tes propres types de postes avec des couleurs personnalisées ! C'est là que tu deviens un vrai architecte du planning !",
                emoji: "⚙️",
                target: "#settings-btn",
                bubblePosition: "left",
                simulation: {
                    type: 'click',
                    target: '#settings-btn',
                    loopDelay: 3000
                }
            },
            {
                title: "Navigation temporelle ultra-rapide !",
                explanation: "Les flèches de navigation te permettent de voyager dans le temps ! ⏰ Tu peux même sauter directement à une semaine spécifique avec le sélecteur !",
                emoji: "⏰",
                target: ".week-navigation",
                bubblePosition: "bottom",
                simulation: {
                    type: 'click',
                    target: '.week-navigation button:last-child',
                    loopDelay: 3000
                }
            }
        ];
    }

    getFeaturesTutorial() {
        return [
            {
                title: "Tour complet des fonctionnalités !",
                explanation: "Prêt pour le grand tour ? ✨ Je vais te montrer TOUTES les fonctionnalités avec des démonstrations live ! Accroche-toi, c'est parti !",
                emoji: "✨",
                target: "#root",
                bubblePosition: "center"
            },
            {
                title: "Navigation entre les semaines",
                explanation: "Ces flèches te permettent de voyager dans le temps ! ⏰ Semaine précédente, suivante, ou revenir à aujourd'hui ! Regarde comme c'est fluide !",
                emoji: "⏰",
                target: ".week-navigation",
                bubblePosition: "bottom",
                simulation: {
                    type: 'click',
                    target: '.week-navigation button:nth-child(2)',
                    loopDelay: 3000
                }
            },
            {
                title: "Système de drag & drop intelligent",
                explanation: "Tu peux tout déplacer ! 🎮 Shifts entre employés, postes vers le planning, même les attributions régulières ! Regarde cette démonstration complète !",
                emoji: "🎮",
                target: ".shift-card",
                bubblePosition: "right",
                simulation: {
                    type: 'drag-drop',
                    source: '.shift-card:first-child',
                    target: '.employee-row:nth-child(3) .employee-shifts',
                    loopDelay: 4000
                }
            },
            {
                title: "Correction automatique des conflits",
                explanation: "Le bouton 'CORRIGER' est ton meilleur ami ! 🔧 Il répare automatiquement tous les problèmes d'assignation et optimise ton planning !",
                emoji: "🔧",
                target: ".control-buttons",
                bubblePosition: "bottom",
                simulation: {
                    type: 'click',
                    target: '.control-buttons button:first-child',
                    loopDelay: 3000
                }
            },
            {
                title: "Gestion des postes disponibles",
                explanation: "La colonne de droite, c'est ta réserve ! 📦 Tous les postes non assignés t'attendent ici. Tu peux les distribuer comme des cartes !",
                emoji: "📦",
                target: "#available-posts-container",
                bubblePosition: "left",
                simulation: {
                    type: 'highlight-sequence',
                    targets: ['.post-row-info:nth-child(1)', '.post-row-info:nth-child(2)', '.post-row-info:nth-child(3)'],
                    loopDelay: 3000
                }
            },
            {
                title: "Système de confirmation intelligent",
                explanation: "Quand tu déplaces une attribution régulière, un menu apparaît ! 💭 Changement permanent ou remplacement ponctuel ? Tu as le contrôle total !",
                emoji: "💭",
                target: ".grip-regular",
                bubblePosition: "right"
            },
            {
                title: "Félicitations, tu maîtrises tout !",
                explanation: "Incroyable ! 🏆 Tu as vu toutes les fonctionnalités en action ! Tu es maintenant un maître du calendrier magique ! Utilise tes nouveaux pouvoirs avec sagesse !",
                emoji: "🏆",
                target: "#root",
                bubblePosition: "center"
            }
        ];
    }
}

// ===== INITIALISATION AUTOMATIQUE =====

let interactiveTutorial;

// Initialiser dès que possible
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        interactiveTutorial = new InteractiveTutorialV2();
    });
} else {
    interactiveTutorial = new InteractiveTutorialV2();
}

// Exporter pour utilisation globale
window.interactiveTutorial = interactiveTutorial; 