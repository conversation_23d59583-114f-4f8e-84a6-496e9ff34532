'use strict';

/**
 * É<PERSON>rit de manière sécurisée dans un flux de réponse (res),
 * en ignorant les erreurs si la connexion a déjà été fermée par le client.
 * @param {import('express').Response} res - L'objet de réponse Express.
 * @param {string} chunk - Le morceau de données à écrire.
 * @returns {boolean} - True si l'écriture a réussi, false sinon.
 */
function safeWrite(res, chunk) {
  if (res.writableEnded) {
    // La connexion est déjà terminée, on ne fait rien.
    return false;
  }
  try {
    // Tente d'écrire dans le flux.
    return res.write(chunk);
  } catch (error) {
    // Une erreur s'est produite, probablement parce que le client s'est déconnecté.
    // On l'ignore pour éviter un crash du serveur.
    logger.warn(`[safeWrite] Erreur d'écriture supprimée (connexion probablement fermée): ${error.message}`);
    return false;
  }
}

module.exports = { safeWrite }; 