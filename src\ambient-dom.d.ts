// Lightweight augmentations to loosen DOM typings for legacy vanilla code.
// This prevents hundreds of TS2339  (property does not exist) errors without
// having to sprinkle casts everywhere.  Everything remains typed as `any`,
// so there is no impact on runtime – only type-checking becomes permissive.
// Feel free to delete / refine once full refactors are done.

declare global {
  interface HTMLElement {
    value?: any;
    checked?: boolean;
    files?: FileList | any;
  }
  interface Element {
    dataset: { [key: string]: any };
  }
  interface EventTarget {
    value?: any;
    checked?: boolean;
    files?: FileList | any;
    dataset?: { [key: string]: any };
  }
}

export {};
