#!/usr/bin/env node

import { query, closePool } from '../server/config/database.js';

// Couleurs pour les logs
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.magenta}🚀 ${msg}${colors.reset}`)
};

// Vérifications à effectuer
const verifications = [
  {
    name: 'Extension UUID',
    query: "SELECT installed_version FROM pg_available_extensions WHERE name = 'uuid-ossp' AND installed_version IS NOT NULL",
    check: (result) => result.rows.length > 0
  },
  {
    name: 'Table employee_templates',
    query: "SELECT to_regclass('employee_templates') as exists",
    check: (result) => result.rows[0].exists !== null
  },
  {
    name: 'Table employees',
    query: "SELECT to_regclass('employees') as exists",
    check: (result) => result.rows[0].exists !== null
  },
  {
    name: 'Table standard_posts',
    query: "SELECT to_regclass('standard_posts') as exists",
    check: (result) => result.rows[0].exists !== null
  },
  {
    name: 'Table shifts',
    query: "SELECT to_regclass('shifts') as exists",
    check: (result) => result.rows[0].exists !== null
  },
  {
    name: 'Table regular_assignments',
    query: "SELECT to_regclass('regular_assignments') as exists",
    check: (result) => result.rows[0].exists !== null
  },
  {
    name: 'Table vacation_periods',
    query: "SELECT to_regclass('vacation_periods') as exists",
    check: (result) => result.rows[0].exists !== null
  },
  {
    name: 'Table global_vacations',
    query: "SELECT to_regclass('global_vacations') as exists",
    check: (result) => result.rows[0].exists !== null
  },
  {
    name: 'Table app_settings',
    query: "SELECT to_regclass('app_settings') as exists",
    check: (result) => result.rows[0].exists !== null
  },
  {
    name: 'Table migrations',
    query: "SELECT to_regclass('migrations') as exists",
    check: (result) => result.rows[0].exists !== null
  },
  {
    name: 'Fonction update_updated_at_column',
    query: "SELECT proname FROM pg_proc WHERE proname = 'update_updated_at_column'",
    check: (result) => result.rows.length > 0
  }
];

// Vérifications des données
const dataVerifications = [
  {
    name: 'Modèle par défaut existe',
    query: "SELECT id, name FROM employee_templates WHERE is_default = true",
    check: (result) => result.rows.length > 0
  },
  {
    name: 'Postes standards par défaut',
    query: "SELECT COUNT(*) as count FROM standard_posts WHERE is_custom = false",
    check: (result) => parseInt(result.rows[0].count) >= 5
  },
  {
    name: 'Employés d\'exemple',
    query: "SELECT COUNT(*) as count FROM employees",
    check: (result) => parseInt(result.rows[0].count) >= 5
  },
  {
    name: 'Paramètres d\'application',
    query: "SELECT COUNT(*) as count FROM app_settings",
    check: (result) => parseInt(result.rows[0].count) >= 5
  },
  {
    name: 'Vacances globales',
    query: "SELECT COUNT(*) as count FROM global_vacations",
    check: (result) => parseInt(result.rows[0].count) >= 10
  }
];

// Vérifications des index
const indexVerifications = [
  {
    name: 'Index employees_name',
    query: "SELECT indexname FROM pg_indexes WHERE tablename = 'employees' AND indexname = 'idx_employees_name'",
    check: (result) => result.rows.length > 0
  },
  {
    name: 'Index shifts_employee_date',
    query: "SELECT indexname FROM pg_indexes WHERE tablename = 'shifts' AND indexname = 'idx_shifts_employee_date'",
    check: (result) => result.rows.length > 0
  },
  {
    name: 'Index app_settings_key',
    query: "SELECT indexname FROM pg_indexes WHERE tablename = 'app_settings' AND indexname = 'idx_app_settings_key'",
    check: (result) => result.rows.length > 0
  }
];

// Fonction pour exécuter une vérification
async function runVerification(verification) {
  try {
    const result = await query(verification.query);
    const passed = verification.check(result);
    
    if (passed) {
      log.success(`${verification.name}: OK`);
      return true;
    } else {
      log.error(`${verification.name}: ÉCHEC`);
      return false;
    }
  } catch (error) {
    log.error(`${verification.name}: ERREUR - ${error.message}`);
    return false;
  }
}

// Fonction pour afficher les statistiques de la base de données
async function showDatabaseStats() {
  try {
    log.title('STATISTIQUES DE LA BASE DE DONNÉES');
    
    const stats = [
      { table: 'employee_templates', name: 'Modèles de fiches' },
      { table: 'employees', name: 'Employés' },
      { table: 'standard_posts', name: 'Postes standards' },
      { table: 'shifts', name: 'Quarts' },
      { table: 'regular_assignments', name: 'Attributions régulières' },
      { table: 'vacation_periods', name: 'Congés individuels' },
      { table: 'global_vacations', name: 'Congés globaux' },
      { table: 'app_settings', name: 'Paramètres app' },
      { table: 'migrations', name: 'Migrations' }
    ];
    
    for (const stat of stats) {
      try {
        const result = await query(`SELECT COUNT(*) as count FROM ${stat.table}`);
        const count = parseInt(result.rows[0].count);
        log.info(`${stat.name}: ${count} enregistrement(s)`);
      } catch (error) {
        log.warning(`${stat.name}: Table non accessible`);
      }
    }
    
  } catch (error) {
    log.error(`Erreur lors de l'affichage des statistiques: ${error.message}`);
  }
}

// Fonction pour tester la connectivité
async function testConnection() {
  try {
    log.title('TEST DE CONNECTIVITÉ');
    
    const startTime = Date.now();
    const result = await query('SELECT NOW() as current_time, version() as version');
    const endTime = Date.now();
    
    const currentTime = result.rows[0].current_time;
    const version = result.rows[0].version;
    
    log.success(`Connexion établie en ${endTime - startTime}ms`);
    log.info(`Heure serveur: ${new Date(currentTime).toLocaleString()}`);
    log.info(`Version PostgreSQL: ${version.split(' ')[0]} ${version.split(' ')[1]}`);
    
    return true;
  } catch (error) {
    log.error(`Échec de la connexion: ${error.message}`);
    return false;
  }
}

// Fonction pour vérifier les permissions
async function checkPermissions() {
  try {
    log.title('VÉRIFICATION DES PERMISSIONS');
    
    const tests = [
      { action: 'SELECT', query: 'SELECT 1 FROM employees LIMIT 1' },
      { action: 'INSERT', query: "INSERT INTO app_settings (setting_key, setting_value) VALUES ('test_key', '\"test_value\"'::jsonb) RETURNING id" },
      { action: 'UPDATE', query: "UPDATE app_settings SET setting_value = '\"updated_test\"'::jsonb WHERE setting_key = 'test_key'" },
      { action: 'DELETE', query: "DELETE FROM app_settings WHERE setting_key = 'test_key'" }
    ];
    
    let allPassed = true;
    
    for (const test of tests) {
      try {
        await query(test.query);
        log.success(`Permission ${test.action}: OK`);
      } catch (error) {
        log.error(`Permission ${test.action}: ÉCHEC - ${error.message}`);
        allPassed = false;
      }
    }
    
    return allPassed;
  } catch (error) {
    log.error(`Erreur lors de la vérification des permissions: ${error.message}`);
    return false;
  }
}

// Fonction principale
async function main() {
  const command = process.argv[2] || 'all';
  
  try {
    console.log(`${colors.cyan}
╔══════════════════════════════════════════════════════════╗
║                 VÉRIFICATION DE LA BASE                  ║
║               Team Calendar App - PostgreSQL             ║
╚══════════════════════════════════════════════════════════╝
${colors.reset}`);
    
    let allPassed = true;
    
    // Test de connectivité
    const connectionOk = await testConnection();
    if (!connectionOk) {
      log.error('ÉCHEC: Impossible de se connecter à la base de données');
      process.exit(1);
    }
    
    if (command === 'all' || command === 'structure') {
      // Vérifications de structure
      log.title('VÉRIFICATION DE LA STRUCTURE');
      for (const verification of verifications) {
        const passed = await runVerification(verification);
        if (!passed) allPassed = false;
      }
      
      // Vérifications des index
      log.title('VÉRIFICATION DES INDEX');
      for (const verification of indexVerifications) {
        const passed = await runVerification(verification);
        if (!passed) allPassed = false;
      }
    }
    
    if (command === 'all' || command === 'data') {
      // Vérifications des données
      log.title('VÉRIFICATION DES DONNÉES');
      for (const verification of dataVerifications) {
        const passed = await runVerification(verification);
        if (!passed) allPassed = false;
      }
    }
    
    if (command === 'all' || command === 'permissions') {
      // Vérification des permissions
      const permissionsOk = await checkPermissions();
      if (!permissionsOk) allPassed = false;
    }
    
    if (command === 'all' || command === 'stats') {
      // Statistiques
      await showDatabaseStats();
    }
    
    // Résultat final
    console.log('\n' + '='.repeat(60));
    if (allPassed) {
      log.success('🎉 TOUTES LES VÉRIFICATIONS SONT PASSÉES AVEC SUCCÈS');
      log.success('La base de données est prête pour l\'application Team Calendar');
    } else {
      log.error('❌ CERTAINES VÉRIFICATIONS ONT ÉCHOUÉ');
      log.warning('Veuillez exécuter les migrations ou corriger les erreurs');
    }
    
  } catch (error) {
    log.error(`Erreur fatale: ${error.message}`);
    process.exit(1);
  } finally {
    await closePool();
  }
}

// Afficher l'aide
function showHelp() {
  console.log(`
${colors.cyan}📋 VÉRIFICATEUR DE BASE DE DONNÉES - TEAM CALENDAR APP${colors.reset}

Usage:
  node verify.js [command]

Commandes:
  all         Exécuter toutes les vérifications (par défaut)
  structure   Vérifier uniquement la structure (tables, index, fonctions)
  data        Vérifier uniquement les données
  permissions Vérifier uniquement les permissions
  stats       Afficher uniquement les statistiques
  help        Afficher cette aide

Exemples:
  node verify.js all
  node verify.js structure
  node verify.js data
  node verify.js stats
  `);
}

// Toujours exécuter le script principal
const command = process.argv[2];
if (command === 'help') {
  showHelp();
} else {
  main();
}

export {
  testConnection,
  checkPermissions,
  showDatabaseStats,
  runVerification
}; 