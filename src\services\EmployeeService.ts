/**
 * Service spécialisé pour la gestion des employés
 * Centralise toute la logique métier liée aux employés
 */

import { validateEmployee, validateArray, ValidationResult } from '../utils/validation';

export interface Employee {
  id: string;
  name: string;
  status?: string;
  avatar?: string;
  extraFields?: Record<string, any>;
}

export interface EmployeeOrder {
  id: string;
  order: number;
}

export interface EmployeeServiceConfig {
  apiService: any;
  logger?: any;
  onError?: (error: string) => void;
  onSuccess?: (message: string) => void;
}

export class EmployeeService {
  private config: EmployeeServiceConfig;
  private employees: Employee[] = [];
  private employeeOrder: EmployeeOrder[] = [];
  private _loadingEmployees = false;
  private _savingOrder = false;

  constructor(config: EmployeeServiceConfig) {
    this.config = config;
  }

  /**
   * Charge tous les employés depuis l'API
   */
  async loadEmployees(): Promise<Employee[]> {
    if (this._loadingEmployees) {
      console.warn('EmployeeService: loadEmployees already in progress');
      return this.employees;
    }

    this._loadingEmployees = true;

    try {
      if (!this.config.apiService || !this.config.apiService.getEmployees) {
        throw new Error('API service not available');
      }

      const result = await this.config.apiService.getEmployees();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to load employees');
      }

      const employees = result.data || [];
      
      // Validation des données
      const validation = validateArray(employees, validateEmployee);
      if (!validation.isValid) {
        console.warn('EmployeeService: Validation warnings:', validation.warnings);
      }

      this.employees = employees;
      
      if (this.config.onSuccess) {
        this.config.onSuccess(`Loaded ${employees.length} employees`);
      }

      return employees;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('EmployeeService: Failed to load employees:', errorMessage);
      
      if (this.config.onError) {
        this.config.onError(`Failed to load employees: ${errorMessage}`);
      }

      return [];
    } finally {
      this._loadingEmployees = false;
    }
  }

  /**
   * Sauvegarde l'ordre des employés
   */
  async saveEmployeeOrder(order: EmployeeOrder[]): Promise<boolean> {
    if (this._savingOrder) {
      console.warn('EmployeeService: saveEmployeeOrder already in progress');
      return false;
    }

    this._savingOrder = true;

    try {
      if (!this.config.apiService || !this.config.apiService.saveEmployeeOrder) {
        throw new Error('API service not available');
      }

      const result = await this.config.apiService.saveEmployeeOrder(order);
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to save employee order');
      }

      this.employeeOrder = order;
      
      if (this.config.onSuccess) {
        this.config.onSuccess('Employee order saved successfully');
      }

      return true;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('EmployeeService: Failed to save employee order:', errorMessage);
      
      if (this.config.onError) {
        this.config.onError(`Failed to save employee order: ${errorMessage}`);
      }

      return false;
    } finally {
      this._savingOrder = false;
    }
  }

  /**
   * Charge l'ordre des employés
   */
  async loadEmployeeOrder(): Promise<EmployeeOrder[]> {
    try {
      if (!this.config.apiService || !this.config.apiService.getEmployeeOrder) {
        throw new Error('API service not available');
      }

      const result = await this.config.apiService.getEmployeeOrder();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to load employee order');
      }

      this.employeeOrder = result.data || [];
      return this.employeeOrder;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('EmployeeService: Failed to load employee order:', errorMessage);
      
      if (this.config.onError) {
        this.config.onError(`Failed to load employee order: ${errorMessage}`);
      }

      return [];
    }
  }

  /**
   * Réorganise les employés
   */
  async reorderEmployees(oldIndex: number, newIndex: number): Promise<boolean> {
    if (oldIndex === newIndex) {
      return true; // Aucun changement
    }

    try {
      // Créer le nouvel ordre
      const newOrder = [...this.employeeOrder];
      const [movedItem] = newOrder.splice(oldIndex, 1);
      newOrder.splice(newIndex, 0, movedItem);

      // Sauvegarder le nouvel ordre
      const success = await this.saveEmployeeOrder(newOrder);
      
      if (success) {
        this.employeeOrder = newOrder;
      }

      return success;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('EmployeeService: Failed to reorder employees:', errorMessage);
      
      if (this.config.onError) {
        this.config.onError(`Failed to reorder employees: ${errorMessage}`);
      }

      return false;
    }
  }

  /**
   * Applique l'ordre des employés
   */
  applyEmployeeOrder(order: EmployeeOrder[]): Employee[] {
    if (!order || order.length === 0) {
      return this.employees;
    }

    // Créer un map pour un accès rapide
    const orderMap = new Map(order.map(item => [item.id, item.order]));
    
    // Trier les employés selon l'ordre
    return [...this.employees].sort((a, b) => {
      const orderA = orderMap.get(a.id) ?? Number.MAX_SAFE_INTEGER;
      const orderB = orderMap.get(b.id) ?? Number.MAX_SAFE_INTEGER;
      return orderA - orderB;
    });
  }

  /**
   * Obtient un employé par ID
   */
  getEmployeeById(id: string): Employee | undefined {
    return this.employees.find(emp => emp.id === id);
  }

  /**
   * Obtient le nom d'un employé
   */
  getEmployeeName(id: string): string {
    const employee = this.getEmployeeById(id);
    return employee?.name || 'Unknown Employee';
  }

  /**
   * Obtient tous les employés
   */
  getEmployees(): Employee[] {
    return this.employees;
  }

  /**
   * Obtient l'ordre des employés
   */
  getEmployeeOrder(): EmployeeOrder[] {
    return this.employeeOrder;
  }

  /**
   * Vérifie si un employé existe
   */
  employeeExists(id: string): boolean {
    return this.employees.some(emp => emp.id === id);
  }

  /**
   * Valide un employé
   */
  validateEmployee(employee: any): ValidationResult {
    return validateEmployee(employee);
  }

  /**
   * Nettoie les données d'employés
   */
  cleanupEmployees(): number {
    const initialCount = this.employees.length;
    
    // Supprimer les employés sans ID ou nom
    this.employees = this.employees.filter(emp => 
      emp && emp.id && emp.name && emp.name.trim() !== ''
    );

    const cleanedCount = initialCount - this.employees.length;
    
    if (cleanedCount > 0) {
      console.log(`EmployeeService: Cleaned ${cleanedCount} invalid employees`);
    }

    return cleanedCount;
  }

  /**
   * Met à jour un employé
   */
  updateEmployee(id: string, updates: Partial<Employee>): boolean {
    const index = this.employees.findIndex(emp => emp.id === id);
    
    if (index === -1) {
      return false;
    }

    this.employees[index] = { ...this.employees[index], ...updates };
    return true;
  }

  /**
   * Ajoute un employé
   */
  addEmployee(employee: Employee): boolean {
    if (!employee || !employee.id || !employee.name) {
      return false;
    }

    if (this.employeeExists(employee.id)) {
      return false;
    }

    this.employees.push(employee);
    return true;
  }

  /**
   * Supprime un employé
   */
  removeEmployee(id: string): boolean {
    const index = this.employees.findIndex(emp => emp.id === id);
    
    if (index === -1) {
      return false;
    }

    this.employees.splice(index, 1);
    
    // Supprimer aussi de l'ordre
    const orderIndex = this.employeeOrder.findIndex(item => item.id === id);
    if (orderIndex !== -1) {
      this.employeeOrder.splice(orderIndex, 1);
    }

    return true;
  }

  /**
   * Obtient le statut de chargement
   */
  isLoading(): boolean {
    return this._loadingEmployees;
  }

  /**
   * Obtient le statut de sauvegarde
   */
  isSaving(): boolean {
    return this._savingOrder;
  }
} 