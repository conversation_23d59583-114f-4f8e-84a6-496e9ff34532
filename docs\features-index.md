# Index des Fonctionnalités - Team Calendar

> **Documentation AI-Ready** - Index complet des fonctionnalités pour intervention rapide par IA

## 📋 Vue d'ensemble

Cette documentation fournit un mapping complet entre les fonctionnalités de l'application et leur implémentation dans le code source.

## 🏗️ Architecture Générale

| Composant | Fichier | Description |
|-----------|---------|-------------|
| **Application Principale** | `src/teamCalendarApp.ts` | Orchestration centrale, logique métier |
| **Gestion des Modales** | `src/modalFunctionalities.ts` | Architecture externe des modales |
| **Services API** | `src/api.ts` | Communication avec le backend |
| **Logging** | `src/logger.ts` | Système de logs unifié |
| **Composants** | `src/components/` | Modules réutilisables |

## 🎯 Fonctionnalités par Catégorie

### 👥 Gestion des Employés

| Fonctionnalité | Fichier Source | Fonction Principale | Description |
|----------------|----------------|-------------------|-------------|
| **Affichage des employés** | `teamCalendarApp.ts` | `renderEmployees()` | Rendu de la liste des employés |
| **Ajout d'employé** | `modalFunctionalities.ts` | `activateEmployeeManagement()` | Modal d'ajout via interface externe |
| **Édition d'employé** | `teamCalendarApp.ts` | `handleEditEmployee()` | Modification des données employé |
| **Suppression d'employé** | `teamCalendarApp.ts` | `deleteEmployee()` | Suppression avec vérifications |
| **Réorganisation** | `teamCalendarApp.ts` | `reorderEmployees()` | Drag & drop pour réorganiser |
| **Modèles d'employés** | `teamCalendarApp.ts` | `renderEmployeeTemplates()` | Gestion des modèles de fiches |
| **Swap d'employés** | `teamCalendarApp.ts` | `enableEmployeeSwapMode()` | Échange de postes entre employés |

### 📅 Gestion du Planning

| Fonctionnalité | Fichier Source | Fonction Principale | Description |
|----------------|----------------|-------------------|-------------|
| **Affichage calendrier** | `teamCalendarApp.ts` | `renderUnifiedCalendar()` | Grille unifiée du planning |
| **Navigation semaines** | `teamCalendarApp.ts` | `navigateWeek()` | Navigation prev/next/today |
| **Génération dates** | `components/utils/dateUtils.ts` | `generateWeekDates()` | Calcul des dates de semaine |
| **Modes d'affichage** | `teamCalendarApp.ts` | `setViewMode()` | Jour/Semaine/Mois |
| **Gestion fuseaux** | `teamCalendarApp.ts` | `setTimezone()` | Support multi-timezone |

### 🏢 Gestion des Postes

| Fonctionnalité | Fichier Source | Fonction Principale | Description |
|----------------|----------------|-------------------|-------------|
| **Affichage postes** | `teamCalendarApp.ts` | `renderPostsForConfig()` | Liste des postes configurés |
| **Création poste** | `modalFunctionalities.ts` | `activatePostsManagement()` | Modal de création externe |
| **Édition poste** | `teamCalendarApp.ts` | `handlePostEdit()` | Modification des postes |
| **Suppression poste** | `teamCalendarApp.ts` | `handlePostDelete()` | Suppression avec vérifications |
| **Drag & Drop postes** | `teamCalendarApp.ts` | `setupPostDragDrop()` | Glisser-déposer des postes |
| **Postes disponibles** | `teamCalendarApp.ts` | `createAvailableShiftElement()` | Affichage postes libres |

### 🔄 Attributions Régulières

| Fonctionnalité | Fichier Source | Fonction Principale | Description |
|----------------|----------------|-------------------|-------------|
| **Création attribution** | `teamCalendarApp.ts` | `createRegularAssignment()` | Nouvelle attribution régulière |
| **Application automatique** | `teamCalendarApp.ts` | `applyRegularAssignmentsForWeek()` | Application aux semaines |
| **Gestion conflits** | `teamCalendarApp.ts` | `checkConflictsBeforeDrop()` | Détection et résolution |
| **Drag régulier** | `teamCalendarApp.ts` | `handleRegularDragStart()` | Déplacement attributions |
| **Menu confirmation** | `teamCalendarApp.ts` | `showRegularAssignmentConfirmationMenu()` | Options de réassignation |

### 🏖️ Gestion des Congés

| Fonctionnalité | Fichier Source | Fonction Principale | Description |
|----------------|----------------|-------------------|-------------|
| **Ajout congés** | `modalFunctionalities.ts` | `activateVacationsManagement()` | Modal externe de congés |
| **Congés individuels** | `teamCalendarApp.ts` | `handleAddVacationPeriod()` | Périodes par employé |
| **Congés globaux** | `teamCalendarApp.ts` | `displayGlobalVacations()` | Jours fériés/fermetures |
| **Vérification congés** | `teamCalendarApp.ts` | `isEmployeeOnVacation()` | Contrôle disponibilité |

### ⚙️ Configuration et Paramètres

| Fonctionnalité | Fichier Source | Fonction Principale | Description |
|----------------|----------------|-------------------|-------------|
| **Modal paramètres** | `modalFunctionalities.ts` | `setupSettingsModal()` | Interface de configuration |
| **Paramètres généraux** | `teamCalendarApp.ts` | `renderGeneralSettings()` | Options globales |
| **Import/Export** | `modalFunctionalities.ts` | `activateDataManagement()` | Sauvegarde/restauration |
| **Réinitialisation** | `modalFunctionalities.ts` | `activateDataManagement()` | Reset complet des données |

### 🔧 Fonctionnalités Techniques

| Fonctionnalité | Fichier Source | Fonction Principale | Description |
|----------------|----------------|-------------------|-------------|
| **Cache intelligent** | `teamCalendarApp.ts` | `_weekCache` | Gestion mémoire optimisée |
| **Sauvegarde API** | `teamCalendarApp.ts` | `saveCurrentWeek()` | Persistance backend |
| **Validation données** | `components/utils/validationUtils.ts` | `validateEmployee()` | Contrôles d'intégrité |
| **Logging unifié** | `logger.ts` | `logger.info()` | Système de logs centralisé |
| **Corrections auto** | `teamCalendarApp.ts` | `emergencyFixUndefinedPostIds()` | Réparation automatique |

## 🎨 Interface Utilisateur

| Composant UI | Fichier Source | Description |
|--------------|----------------|-------------|
| **Grille principale** | `teamCalendarApp.ts` | Calendrier unifié avec employés et postes |
| **Barre de navigation** | `teamCalendarApp.ts` | Contrôles de navigation temporelle |
| **Modales externes** | `modalFunctionalities.ts` | Système de modales découplé |
| **Indicateurs visuels** | `teamCalendarApp.ts` | Feedback utilisateur (loading, erreurs) |
| **Drag & Drop** | `teamCalendarApp.ts` | Interactions glisser-déposer |

## 🔍 Points d'Extension

### Pour ajouter une nouvelle fonctionnalité :

1. **Logique métier** → `teamCalendarApp.ts`
2. **Interface utilisateur** → `modalFunctionalities.ts`
3. **Validation** → `components/utils/validationUtils.ts`
4. **Types** → `components/types/interfaces.ts`
5. **API** → `api.ts`

### Patterns d'architecture :

- **Séparation des responsabilités** : Logique métier vs Interface
- **Architecture externe** : Modales découplées
- **Validation centralisée** : Utilitaires réutilisables
- **Cache intelligent** : Performance optimisée
- **Logging unifié** : Debugging facilité

## 📊 Métriques de Code

| Métrique | Valeur | Fichier Principal |
|----------|--------|-------------------|
| **Lignes de code** | ~11,750 | `teamCalendarApp.ts` |
| **Fonctions publiques** | ~150 | Réparties dans les modules |
| **Interfaces TypeScript** | ~20 | `components/types/interfaces.ts` |
| **Composants modulaires** | ~10 | `components/` |

## 🚀 Prochaines Étapes

1. **Modularisation continue** : Extraction de plus de composants
2. **Tests automatisés** : Couverture des fonctionnalités critiques
3. **Documentation API** : JSDoc complet
4. **Performance** : Optimisations supplémentaires
5. **Accessibilité** : Amélioration de l'UX

---

> **Note pour l'IA** : Cette documentation est conçue pour permettre une intervention rapide et précise. Chaque fonctionnalité est mappée à son implémentation exacte dans le code source.
