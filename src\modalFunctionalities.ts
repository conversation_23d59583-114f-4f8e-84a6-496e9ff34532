/**
 * Module des fonctionnalités du modal paramètres
 * Intégré directement dans le code source - AUCUN PATCH TEMPORAIRE
 */

// Types pour les onglets du modal
type TabId = 'posts' | 'vacations' | 'assignments' | 'employee-templates' | 'employees' | 'general';

// ✅ DÉCLARATIONS GLOBALES POUR TYPESCRIPT
declare global {
  interface Window {
    teamCalendarApp: any;
    modalFunctionalities: any;
  }
}

interface ModalFunctionalities {
  activateTabSwitching(): void;
  activatePostsManagement(): void;
  activateVacationsManagement(): void;
  activateAssignmentsManagement(): void;
  activateEmployeeTemplatesManagement(): void;
  activateEmployeesManagement(): void;
  activateGeneralSettings(): void;
  activateModalClosing(): void;
  initializeAll(): void;
  openSettingsModal(): void;
  createSettingsModalIfNeeded(): void;
  renderSettingsContent(): void;
  reactivateWithTeamCalendarApp(): void;
  init(app: any): void;
  // ✅ NOUVELLES FONCTIONS POUR DRAG & DROP
  openAssignmentContextModal(modalData: any): void;
  createAssignmentContextModal(): HTMLElement;
  populateAssignmentModal(modal: HTMLElement, modalData: any): void;
  setupAssignmentModalEvents(modal: HTMLElement, modalData: any): void;
  handleAssignmentAction(actionType: string, modalData: any): void;
}

class ModalFunctionalitiesManager implements ModalFunctionalities {
  private activeTab: TabId = 'posts';
  private isInitialized = false;
  private app: any; // Référence à TeamCalendarApp
  private elements = {
    settingsModal: null as HTMLElement | null,
    settingsContent: null as HTMLElement | null,
    settingsOverlay: null as HTMLElement | null,
    settingsButton: null as HTMLElement | null
  };
  
  /**
   * Initialise le gestionnaire de modales avec une référence à l'application principale
   * @param app - Référence à l'application principale (TeamCalendarApp)
   */
  init(app: any): void {
    console.log('🔧 [ModalFunctionalitiesManager] Initialisation du gestionnaire de modales');
    this.app = app;
    // ✅ CORRECTION : Ne pas initialiser immédiatement, attendre l'ouverture du modal
    // this.initializeAll();
  }

  /**
   * Active la gestion des onglets dans le modal
   */
  activateTabSwitching(): void {
    try {
      // S'assurer qu'on cible le modal spécifique pour limiter la portée des sélecteurs
      const settingsModal = this.elements.settingsModal;
      if (!settingsModal) {
        console.error('❌ [MODAL] Modal introuvable pour l\'activation des onglets');
        return;
      }
      
      // Cibler spécifiquement les éléments dans ce modal
      const tabButtons = settingsModal.querySelectorAll('.tab-btn');
      const tabContents = settingsModal.querySelectorAll('.tab-content');
      
      console.log(`🔍 [MODAL] Activation des onglets: ${tabButtons.length} boutons, ${tabContents.length} contenus trouvés`);
      
      if (tabButtons.length === 0 || tabContents.length === 0) {
        // Essayons de trouver ces éléments par ID pour voir s'ils existent
        const postsTab = document.getElementById('tab-posts');
        const postsContent = document.getElementById('tab-content-posts');
        console.log(`🔍 [MODAL] Recherche alternative: tab-posts=${!!postsTab}, tab-content-posts=${!!postsContent}`);
      }
      
      // Détacher les anciens listeners pour éviter les doublons
      tabButtons.forEach(button => {
        // Clone pour retirer les listeners existants
        const newButton = button.cloneNode(true);
        if (button.parentNode) {
          button.parentNode.replaceChild(newButton, button);
        }
      });
      
      // Rattacher les listeners
      settingsModal.querySelectorAll('.tab-btn').forEach(button => {
        button.addEventListener('click', (e) => {
          e.preventDefault();
          const target = e.currentTarget as HTMLElement; // Utiliser currentTarget au lieu de target
          const tabId = target.id.replace('tab-', '') as TabId;
          
          console.log(`🔍 [MODAL] Clic sur onglet: ${target.id} -> tabId: ${tabId}`);
          
          // Désactiver tous les onglets
          settingsModal.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
          settingsModal.querySelectorAll('.tab-content').forEach(content => content.classList.add('hidden'));
          
          // Activer l'onglet sélectionné
          target.classList.add('active');
          const contentElement = settingsModal.querySelector(`#tab-content-${tabId}`);
          if (contentElement) {
            contentElement.classList.remove('hidden');
            console.log(`✅ [MODAL] Onglet activé et contenu affiché: ${tabId}`);
          } else {
            console.error(`❌ [MODAL] Contenu introuvable pour l'onglet: ${tabId}`);
          }
          
          this.activeTab = tabId;
        });
      });
      
      // Activer le premier onglet par défaut
      const firstTab = settingsModal.querySelector('.tab-btn');
      if (firstTab) {
        (firstTab as HTMLElement).click();
      }
      
      console.log('✅ [MODAL] Gestion des onglets activée');
    } catch (error) {
      console.error('❌ [MODAL] Erreur lors de l\'activation des onglets:', error);
    }
  }

  /**
   * Active la gestion des postes
   */
  activatePostsManagement(): void {
    const addPostBtn = document.getElementById('add-post-btn');
    if (addPostBtn && !addPostBtn.dataset.listenerAttached) {
      addPostBtn.addEventListener('click', () => {
        if (window.TeamCalendarApp && typeof window.TeamCalendarApp.handlePostEdit === 'function') {
          window.TeamCalendarApp.handlePostEdit();
          console.log('✅ [MODAL] Modal poste ouvert');
        } else {
          console.warn('⚠️ [MODAL] TeamCalendarApp.handlePostEdit non disponible');
        }
      });
      addPostBtn.dataset.listenerAttached = 'true';
    }

    // Rendre la liste des postes existants
    if (window.TeamCalendarApp && typeof window.TeamCalendarApp.renderPostsForConfig === 'function') {
      window.TeamCalendarApp.renderPostsForConfig();
      console.log('✅ [MODAL] Liste des postes rendue');
    }

    console.log('✅ [MODAL] Gestion des postes activée');
  }

  /**
   * Active la gestion des vacances
   */
  activateVacationsManagement(): void {
    const addVacationBtn = document.getElementById('add-vacation-btn');
    if (addVacationBtn && !addVacationBtn.dataset.listenerAttached) {
      addVacationBtn.addEventListener('click', () => {
        if (window.TeamCalendarApp && typeof window.TeamCalendarApp.handleAddVacationPeriod === 'function') {
          window.TeamCalendarApp.handleAddVacationPeriod();
          console.log('✅ [MODAL] Modal vacances ouvert');
        } else {
          console.warn('⚠️ [MODAL] TeamCalendarApp.handleAddVacationPeriod non disponible');
        }
      });
      addVacationBtn.dataset.listenerAttached = 'true';
    }

    // Rendre la liste des vacances existantes
    if (window.TeamCalendarApp && typeof window.TeamCalendarApp.renderVacationPeriods === 'function') {
      window.TeamCalendarApp.renderVacationPeriods();
      console.log('✅ [MODAL] Liste des vacances rendue');
    }

    console.log('✅ [MODAL] Gestion des vacances activée');
  }

  /**
   * Active la gestion des attributions
   */
  activateAssignmentsManagement(): void {
    const addAssignmentBtn = document.getElementById('add-assignment-btn');
    const removeAssignmentsBtn = document.getElementById('remove-assignments-btn');

    if (addAssignmentBtn && !addAssignmentBtn.dataset.listenerAttached) {
      addAssignmentBtn.addEventListener('click', () => {
        if (window.TeamCalendarApp && typeof window.TeamCalendarApp.handleAddAssignment === 'function') {
          window.TeamCalendarApp.handleAddAssignment();
          console.log('✅ [MODAL] Modal attribution ouvert');
        } else {
          console.warn('⚠️ [MODAL] TeamCalendarApp.handleAddAssignment non disponible');
        }
      });
      addAssignmentBtn.dataset.listenerAttached = 'true';
    }

    if (removeAssignmentsBtn && !removeAssignmentsBtn.dataset.listenerAttached) {
      removeAssignmentsBtn.addEventListener('click', () => {
        // TODO: Implémenter openRemoveAssignmentsModal dans ModalManager
        console.warn('⚠️ [MODAL] Fonctionnalité de suppression des attributions non encore implémentée');
        if (window.toastSystem) {
          window.toastSystem.info('Fonctionnalité en cours de développement');
        }
      });
      removeAssignmentsBtn.dataset.listenerAttached = 'true';
    }

    // Rendre la liste des attributions existantes
    if (window.TeamCalendarApp && typeof window.TeamCalendarApp.renderAssignments === 'function') {
      window.TeamCalendarApp.renderAssignments();
      console.log('✅ [MODAL] Liste des attributions rendue');
    }

    console.log('✅ [MODAL] Gestion des attributions activée');
  }

  /**
   * Active la gestion des modèles d'employés
   */
  activateEmployeeTemplatesManagement(): void {
    const addTemplateBtn = document.getElementById('add-template-btn');
    if (addTemplateBtn) {
      addTemplateBtn.addEventListener('click', () => {
        // TODO: Implémenter openEmployeeTemplateModal dans ModalManager
        console.warn('⚠️ [MODAL] Fonctionnalité de modèles d\'employés non encore implémentée');
        if (window.toastSystem) {
          window.toastSystem.info('Fonctionnalité en cours de développement');
        }
      });
    }
    
    console.log('✅ [MODAL] Gestion des modèles d\'employés activée');
  }

  /**
   * Active la gestion des employés
   */
  activateEmployeesManagement(): void {
    const addEmployeeBtn = document.getElementById('add-employee-btn');
    if (addEmployeeBtn && !addEmployeeBtn.dataset.listenerAttached) {
      addEmployeeBtn.addEventListener('click', () => {
        // TODO: Implémenter openEmployeeModal dans ModalManager
        console.warn('⚠️ [MODAL] Fonctionnalité de gestion des employés non encore implémentée');
        if (window.toastSystem) {
          window.toastSystem.info('Fonctionnalité en cours de développement');
        }
      });
      addEmployeeBtn.dataset.listenerAttached = 'true';
    }

    // Rendre la liste des employés existants
    if (window.TeamCalendarApp && typeof window.TeamCalendarApp.renderEmployeesManagement === 'function') {
      window.TeamCalendarApp.renderEmployeesManagement();
      console.log('✅ [MODAL] Liste des employés rendue');
    }

    console.log('✅ [MODAL] Gestion des employés activée');
  }

  // Méthode activateGeneralSettings déplacée plus bas dans le fichier pour éviter les duplications

  /**
   * Active les paramètres généraux
   */
  activateGeneralSettings(): void {
    // Gestion des boutons d'export/import/reset
    const exportBtn = document.getElementById('export-data-btn');
    const importBtn = document.getElementById('import-data-btn');
    const resetBtn = document.getElementById('reset-data-btn');
    
    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        // ✅ FIXED: Direct export functionality
        if (window.TeamCalendarApp && window.TeamCalendarApp.data) {
          const exportData = {
            appSettings: window.TeamCalendarApp.config.appSettings,
            employees: window.TeamCalendarApp.data.employees,
            schedule: window.TeamCalendarApp.data.schedule,
            vacations: window.TeamCalendarApp.data.vacations,
            globalVacations: window.TeamCalendarApp.data.globalVacations,
            customPosts: window.TeamCalendarApp.data.customPosts,
            exportDate: new Date().toISOString()
          };

          const dataStr = JSON.stringify(exportData, null, 2);
          const dataBlob = new Blob([dataStr], { type: 'application/json' });
          const url = URL.createObjectURL(dataBlob);

          const link = document.createElement('a');
          link.href = url;
          link.download = `team-calendar-backup-${new Date().toISOString().split('T')[0]}.json`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);

          console.log('✅ [MODAL] Export des données lancé');
        } else {
          console.warn('⚠️ [MODAL] TeamCalendarApp.data non disponible');
        }
      });
    }

    if (importBtn) {
      importBtn.addEventListener('click', () => {
        // ✅ FIXED: Direct import functionality
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = (e) => {
          const file = (e.target as HTMLInputElement).files?.[0];
          if (!file) return;

          const reader = new FileReader();
          reader.onload = (e) => {
            try {
              const result = e.target?.result;
              if (typeof result !== 'string') {
                alert('Erreur : format de fichier non supporté.');
                return;
              }
              const importedData = JSON.parse(result);

              // Validation basique
              if (window.TeamCalendarApp) {
                if (importedData.appSettings) window.TeamCalendarApp.config.appSettings = importedData.appSettings;
                if (importedData.employees) window.TeamCalendarApp.data.employees = importedData.employees;
                if (importedData.schedule) window.TeamCalendarApp.data.schedule = importedData.schedule;
                if (importedData.vacations) window.TeamCalendarApp.data.vacations = importedData.vacations;
                if (importedData.globalVacations) window.TeamCalendarApp.data.globalVacations = importedData.globalVacations;
                if (importedData.customPosts) window.TeamCalendarApp.data.customPosts = importedData.customPosts;

                if (typeof window.TeamCalendarApp.saveCurrentWeek === 'function') {
                  window.TeamCalendarApp.saveCurrentWeek({ showToast: false });
                }
                location.reload(); // Recharger pour appliquer tous les changements
              }
            } catch (error) {
              alert('Erreur lors de l\'importation : fichier JSON invalide.');
            }
          };
          reader.readAsText(file);
        };

        input.click();
        console.log('✅ [MODAL] Import des données lancé');
      });
    }

    if (resetBtn) {
      resetBtn.addEventListener('click', async () => {
        const confirmed = confirm('Êtes-vous sûr de vouloir réinitialiser toutes les données ? Cette action est irréversible.');
        if (confirmed && window.TeamCalendarApp) {
          try {
            // ✅ FIXED: Direct reset functionality
            const response = await fetch('http://localhost:3001/api/database/purge', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              }
            });

            const result = await response.json();

            if (result.success) {
              // Nettoyer le localStorage
              localStorage.removeItem(window.TeamCalendarApp.config.storageKey);
              localStorage.removeItem('teamCalendarAppSettings');
              localStorage.removeItem('workingDaysDebugLogs');
              localStorage.removeItem('modificationHistory');

              if (window.toastSystem) {
                window.toastSystem.success('✅ Réinitialisation complète terminée. Rechargement de la page...', { duration: 3000 });
              }

              setTimeout(() => {
                location.reload();
              }, 2000);
            } else {
              throw new Error(result.error || 'Erreur inconnue lors de la purge');
            }
          } catch (error) {
            console.error('❌ Erreur lors de la réinitialisation:', error);
            if (window.toastSystem) {
              window.toastSystem.error('❌ Erreur lors de la réinitialisation. Veuillez réessayer.', { duration: 8000 });
            }
          }
          console.log('✅ [MODAL] Reset des données lancé');
        }
      });
    }
    
    // Gestion des paramètres de semaine
    const weekStartInputs = document.querySelectorAll('input[name="week-start"]');
    weekStartInputs.forEach(input => {
      input.addEventListener('change', (e) => {
        const target = e.target as HTMLInputElement;
        if (window.TeamCalendarApp && typeof window.TeamCalendarApp.setWeekStartDay === 'function') {
          // Convertir la valeur string en nombre (0 = dimanche, 1 = lundi)
          const dayNumber = target.value === 'sunday' ? 0 : 1;
          window.TeamCalendarApp.setWeekStartDay(dayNumber);
          console.log(`✅ [MODAL] Début de semaine changé: ${target.value} (${dayNumber})`);
        }
      });
    });

    // -----------------------------
    // Gestion du fuseau horaire
    // -----------------------------
    const generalTab = document.getElementById('tab-content-general');
    if (generalTab && !document.getElementById('timezone-select')) {
      const tzBlock = document.createElement('div');
      tzBlock.className = 'mb-6';

      // Tenter d'obtenir la liste des fuseaux via Intl.supportedValuesOf (Chrome 103+, Node 18+)
      let timezones: string[] = [];
      try {
        // @ts-ignore - API stage-4 mais pas encore dans TS lib
        if (typeof Intl.supportedValuesOf === 'function') {
          // @ts-ignore
          timezones = Intl.supportedValuesOf('timeZone');
        }
      } catch (err) {
        console.warn('⚠️ [MODAL] Intl.supportedValuesOf non disponible', err);
      }

      // Fallback minimal si l'API n'est pas dispo avec informations UTC
      if (timezones.length === 0) {
        timezones = [
          'UTC',
          'Europe/Paris',
          'America/New_York',
          'Asia/Tokyo',
          'Europe/London',
          'America/Los_Angeles',
          'Australia/Sydney',
          'Asia/Shanghai',
          'Europe/Berlin',
          'America/Chicago'
        ];
      }

      // ✅ AMÉLIORATION : Fonction pour obtenir l'offset UTC d'un timezone
      const getTimezoneOffset = (timezone: string): string => {
        try {
          const now = new Date();
          const utc = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));
          const targetTime = new Date(utc.toLocaleString('en-US', { timeZone: timezone }));
          const offsetMinutes = (targetTime.getTime() - utc.getTime()) / (1000 * 60);
          const offsetHours = Math.floor(Math.abs(offsetMinutes) / 60);
          const offsetMins = Math.abs(offsetMinutes) % 60;
          const sign = offsetMinutes >= 0 ? '+' : '-';
          return `UTC${sign}${offsetHours.toString().padStart(2, '0')}:${offsetMins.toString().padStart(2, '0')}`;
        } catch (error) {
          return 'UTC+00:00';
        }
      };

      tzBlock.innerHTML =
        '<label class="block text-sm font-medium text-gray-700 mb-2">Fuseau horaire</label>' +
        '<select id="timezone-select" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-indigo-500 focus:border-indigo-500 w-full max-w-xs overflow-y-auto">' +
        timezones.map(tz => {
          const offset = getTimezoneOffset(tz);
          const displayName = tz.replace(/_/g, ' ');
          return `<option value="${tz}">${displayName} (${offset})</option>`;
        }).join('') +
        '</select>';

      // Insérer le bloc au début du conteneur, avant d'autres éléments si possible
      generalTab.insertBefore(tzBlock, generalTab.firstChild);
    }

    // Initialisation et écouteur du select timezone
    const timezoneSelect = document.getElementById('timezone-select') as HTMLSelectElement | null;
    if (timezoneSelect) {
      // Définir la valeur actuelle selon l'application
      if (
        window.TeamCalendarApp &&
        typeof window.TeamCalendarApp.getTimezone === 'function'
      ) {
        const currentTz = window.TeamCalendarApp.getTimezone();
        const hasOption = Array.from(timezoneSelect.options).some(
          opt => opt.value === currentTz,
        );
        timezoneSelect.value = hasOption ? currentTz : 'UTC';
      }

      if (timezoneSelect.dataset.listenerAttached !== 'true') {
        timezoneSelect.addEventListener('change', e => {
          const target = e.target as HTMLSelectElement;
          const tz = target.value;
          if (
            window.TeamCalendarApp &&
            typeof window.TeamCalendarApp.setTimezone === 'function'
          ) {
            window.TeamCalendarApp.setTimezone(tz);
            console.log(`✅ [MODAL] Fuseau horaire changé: ${tz}`);
          }
        });
        timezoneSelect.dataset.listenerAttached = 'true';
      }
    }
    
    console.log('✅ [MODAL] Paramètres généraux activés');
  }

  /**
   * Active la fermeture du modal
   */
  activateModalClosing(): void {
    // ✅ CORRECTION : Chercher le modal existant ou le modal externe
    const modal = document.getElementById('settings-modal') || document.getElementById('settings-modal-external');
    const closeBtn = document.getElementById('settings-modal-close') || document.getElementById('settings-modal-external-close');

    if (closeBtn && modal) {
      closeBtn.addEventListener('click', () => {
        modal.classList.add('hidden');
        modal.style.display = 'none';
        console.log('✅ [MODAL] Modal fermé via bouton');
      });
    } else {
      console.warn('⚠️ [MODAL] Bouton de fermeture ou modal non trouvé pour l\'activation de la fermeture');
    }
    
    // Fermeture en cliquant en dehors
    if (modal) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.classList.add('hidden');
          console.log('✅ [MODAL] Modal fermé en cliquant dehors');
        }
      });
    }
    
    // Fermeture avec Escape
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && modal && !modal.classList.contains('hidden')) {
        modal.classList.add('hidden');
        console.log('✅ [MODAL] Modal fermé avec Escape');
      }
    });
    
    console.log('✅ [MODAL] Fermeture du modal activée');
  }

  // L'initializeAll() est implémenté plus loin dans le fichier
  
  /**
   * Ouvre la modale des paramètres
   */
  openSettingsModal(): void {
    console.log('🔍 [MODAL] Ouverture de la modale paramètres');

    // Créer le modal si nécessaire
    this.createSettingsModalIfNeeded();

    // ✅ CORRECTION : Initialiser les fonctionnalités après création du modal
    if (!this.isInitialized && this.elements.settingsModal) {
      this.initializeAll();
    }

    // S'assurer que l'overlay et la modale sont bien visibles
    if (this.elements.settingsModal) {
      // ✅ CORRECTION : Utiliser la méthode d'affichage appropriée pour le modal React
      this.elements.settingsModal.style.display = 'flex';
      this.elements.settingsModal.classList.remove('hidden');
      console.log('✅ [MODAL] Modal visible');
    } else {
      console.error('❌ [MODAL] Modal non créé, impossible de l\'afficher');
      return;
    }

    // Rendre le contenu du modal après s'être assuré que le modal est visible
    this.renderSettingsContent();

    console.log('✅ [MODAL] Modal paramètres ouvert avec succès');
  }
  
  /**
   * Crée la modale des paramètres si elle n'existe pas déjà
   */
  createSettingsModalIfNeeded(): void {
    if (this.elements.settingsModal) {
      console.log('✅ [ModalManager] Modal paramètres déjà créé');
      return;
    }

    console.log('🔧 [ModalManager] Création du modal paramètres');

    // ✅ CORRECTION : Utiliser le modal existant de l'interface React au lieu d'en créer un nouveau
    const existingModal = document.getElementById('settings-modal');
    if (existingModal) {
      console.log('✅ [ModalManager] Utilisation du modal existant de l\'interface React');
      this.elements.settingsModal = existingModal;
      this.elements.settingsOverlay = existingModal; // Le modal React inclut déjà l'overlay

      // ✅ CORRECTION : Ne pas chercher un conteneur de contenu global,
      // mais utiliser les conteneurs d'onglets existants
      console.log('✅ [ModalManager] Modal React trouvé, utilisation des conteneurs d\'onglets existants');

      // Vérifier que les onglets existent et définir settingsContent
      const tabsExist = existingModal.querySelector('#tab-content-posts');
      if (tabsExist) {
        console.log('✅ [ModalManager] Conteneurs d\'onglets détectés dans le modal React');
        // ✅ CORRECTION : Définir settingsContent pour le modal React
        this.elements.settingsContent = existingModal; // Le modal React lui-même contient les onglets
      } else {
        console.warn('⚠️ [ModalManager] Conteneurs d\'onglets non trouvés dans le modal React');
      }
    } else {
      // Fallback : créer un nouveau modal si l'existant n'est pas trouvé
      console.log('⚠️ [ModalManager] Modal React non trouvé, création d\'un nouveau modal');

      // Créer l'overlay du modal
      const overlay = document.createElement('div');
      overlay.id = 'settings-overlay-external';
      overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-50';
      document.body.appendChild(overlay);

      // Créer le contenu du modal
      const modal = document.createElement('div');
      modal.id = 'settings-modal-external';
      modal.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-lg p-6 z-50 w-11/12 max-w-4xl max-h-[90vh] overflow-y-auto';
      document.body.appendChild(modal);

      this.elements.settingsModal = modal;
      this.elements.settingsOverlay = overlay;

      // ✅ CORRECTION : Ajouter l'en-tête du modal seulement pour le modal externe
      const header = document.createElement('div');
      header.className = 'flex justify-between items-center mb-4';
      header.innerHTML = `
        <h2 class="text-xl font-bold">Paramètres</h2>
        <button id="close-settings-btn" class="text-gray-500 hover:text-gray-700">
          <span class="material-icons">close</span>
        </button>
      `;
      modal.appendChild(header);

      // Ajouter la navigation par onglets
      const tabsNav = document.createElement('div');
      tabsNav.className = 'border-b mb-4';
      tabsNav.innerHTML = `
        <nav class="flex space-x-4" aria-label="Tabs">
          <button id="tab-posts" class="tab-btn px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap active">Postes</button>
          <button id="tab-vacations" class="tab-btn px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap">Vacances</button>
          <button id="tab-assignments" class="tab-btn px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap">Attributions</button>
          <button id="tab-employee-templates" class="tab-btn px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap">Templates employés</button>
          <button id="tab-employees" class="tab-btn px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap">Employés</button>
          <button id="tab-general" class="tab-btn px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap">Général</button>
        </nav>
      `;
      modal.appendChild(tabsNav);

      // Ajouter le contenu des onglets
      const content = document.createElement('div');
      content.id = 'settings-content';
      modal.appendChild(content);

      // Stocker la référence au contenu
      this.elements.settingsContent = content;
    }
    
    // Ajouter l'événement de fermeture
    const closeBtn = document.getElementById('close-settings-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.closeSettingsModal();
      });
    }
    
    console.log('✅ [ModalManager] Modal paramètres créé avec succès');
  }
  
  /**
   * Ferme la modale des paramètres
   */
  closeSettingsModal(): void {
    console.log('🔍 [MODAL] Fermeture de la modale paramètres');
    if (this.elements.settingsModal) {
      this.elements.settingsModal.classList.add('hidden');
      this.elements.settingsModal.style.display = 'none';
    }
    
    if (this.elements.settingsOverlay) {
      this.elements.settingsOverlay.classList.add('hidden');
      this.elements.settingsOverlay.style.display = 'none';
    }
    console.log('✅ [MODAL] Modal paramètres fermé');
  }

  /**
   * Rend le contenu des onglets du modal paramètres
   */
  renderSettingsContent(): void {
    try {
      console.log('🔍 [ModalManager] Début renderSettingsContent, vérification des éléments');
      console.log('🔍 [ModalManager] this.elements.settingsModal =', this.elements.settingsModal ? 'existe' : 'null');
      console.log('🔍 [ModalManager] this.elements.settingsContent =', this.elements.settingsContent ? 'existe' : 'null');
      
      if (!this.elements.settingsContent) {
        console.error('❌ [ModalManager] Contenu du modal paramètres non trouvé, création du modal forcée');
        this.createSettingsModalIfNeeded();
        
        if (!this.elements.settingsContent) {
          console.error('❌ [ModalManager] Impossible de créer le contenu du modal, abandon');
          return;
        }
      }
      
      console.log('🔧 [ModalManager] Rendu du contenu du modal paramètres');
      console.log('🔍 [ModalManager] Nombre d\'enfants du contenu:', this.elements.settingsContent.children.length);
      
      // ✅ CORRECTION MAJEURE : Utiliser les conteneurs d'onglets existants du modal React
      // au lieu de recréer tout le contenu
      console.log('🔧 [ModalManager] Utilisation des conteneurs d\'onglets existants du modal React');

      const tabs: TabId[] = ['posts', 'vacations', 'assignments', 'employee-templates', 'employees', 'general'];

      tabs.forEach(tab => {
        const existingTabContent = document.getElementById(`tab-content-${tab}`);
        if (existingTabContent) {
          console.log(`✅ [ModalManager] Conteneur onglet ${tab} trouvé, peuplement du contenu`);

          // Vider le contenu existant de cet onglet seulement si nécessaire
          if (existingTabContent.children.length === 0) {
            this.populateTabContent(tab, existingTabContent);
          } else {
            console.log(`✅ [ModalManager] Onglet ${tab} déjà peuplé, activation des fonctionnalités`);
            this.activateTabFunctionalities(tab);
          }
        } else {
          console.warn(`⚠️ [ModalManager] Conteneur onglet ${tab} non trouvé dans le modal React`);
        }
      });

      console.log('✅ [ModalManager] Rendu du contenu terminé');
    } catch (error) {
      console.error('❌ [ModalManager] Erreur lors du rendu du contenu:', error);
    }
  }

  /**
   * Peuple le contenu d'un onglet spécifique dans le conteneur React existant
   */
  private populateTabContent(tab: TabId, container: HTMLElement): void {
    console.log(`🔧 [ModalManager] Peuplement du contenu de l'onglet ${tab}`);

    // ✅ CORRECTION : Utiliser le conteneur passé en paramètre au lieu de tabContent
    switch (tab) {
      case 'posts':
        container.innerHTML = `
          <div class="mb-4">
            <button id="add-post-btn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Ajouter un poste</button>
          </div>
          <div id="posts-list" class="space-y-2"></div>
        `;
        break;
      case 'vacations':
        container.innerHTML = `
          <div class="mb-4">
            <button id="add-vacation-btn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Ajouter une vacance</button>
          </div>
          <div id="vacations-list" class="space-y-2"></div>
        `;
        break;
      case 'assignments':
        container.innerHTML = `
          <div class="mb-4">
            <button id="add-assignment-btn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Ajouter une attribution</button>
            <button id="remove-assignments-btn" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 ml-2">Supprimer des attributions</button>
          </div>
          <div id="assignments-list" class="space-y-2"></div>
        `;
        break;
      case 'employee-templates':
        container.innerHTML = `
          <div class="mb-4">
            <button id="add-template-btn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Ajouter un modèle d'employé</button>
          </div>
          <div id="templates-list" class="space-y-2"></div>
        `;
        break;
      case 'employees':
        container.innerHTML = `
          <div class="mb-4">
            <button id="add-employee-btn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Ajouter un employé</button>
          </div>
          <div id="employees-list" class="space-y-2"></div>
        `;
        break;
      case 'general':
        container.innerHTML = `
          <div class="space-y-6">
            <div class="mb-6">
              <h3 class="font-medium mb-2">Début de semaine</h3>
              <div class="flex space-x-4">
                <label class="inline-flex items-center">
                  <input type="radio" name="week-start" value="monday" class="form-radio" checked>
                  <span class="ml-2">Lundi</span>
                </label>
                <label class="inline-flex items-center">
                  <input type="radio" name="week-start" value="sunday" class="form-radio">
                  <span class="ml-2">Dimanche</span>
                </label>
              </div>
            </div>
            <div id="timezone-container" class="mb-6">
              <h3 class="font-medium mb-2">Fuseau horaire</h3>
            </div>
            <div class="mb-6">
              <h3 class="font-medium mb-2">Données</h3>
              <div class="flex space-x-2">
                <button id="export-data-btn" class="bg-gray-200 hover:bg-gray-300 px-3 py-1 rounded text-sm">Exporter</button>
                <button id="import-data-btn" class="bg-gray-200 hover:bg-gray-300 px-3 py-1 rounded text-sm">Importer</button>
                <button id="reset-data-btn" class="bg-red-500 hover:bg-red-600 px-3 py-1 rounded text-sm text-white">Réinitialiser</button>
              </div>
            </div>
          </div>
        `;
        break;
    }

    // Activer les fonctionnalités spécifiques à cet onglet
    this.activateTabFunctionalities(tab);
    console.log(`✅ [ModalManager] Contenu et fonctionnalités pour l'onglet ${tab} activés`);
  }

  /**
   * Active les fonctionnalités spécifiques à un onglet
   */
  private activateTabFunctionalities(tab: TabId): void {
    console.log(`🔧 [ModalManager] Activation des fonctionnalités pour l'onglet ${tab}`);

    switch (tab) {
      case 'posts':
        this.activatePostsManagement();
        break;
      case 'vacations':
        this.activateVacationsManagement();
        break;
      case 'assignments':
        this.activateAssignmentsManagement();
        break;
      case 'employee-templates':
        this.activateEmployeeTemplatesManagement();
        break;
      case 'employees':
        this.activateEmployeesManagement();
        break;
      case 'general':
        this.activateGeneralSettings();
        break;
    }

    console.log(`✅ [ModalManager] Fonctionnalités activées pour l'onglet ${tab}`);
  }

  /**
   * Cette méthode est commentée car elle est déjà définie plus haut dans la classe
   * Cette duplication causait des erreurs de compilation TypeScript
   */
  /* activateGeneralSettings_DEPRECATED(): void {
    console.log('🔍 [MODAL] Activation des paramètres généraux');
    try {
      // Gérer le fuseau horaire
      const timezoneContainer = document.getElementById('timezone-container');
      if (timezoneContainer) {
        // Création du sélecteur de fuseau horaire s'il n'existe pas
        if (!document.getElementById('timezone-select')) {
          const timezoneSelect = document.createElement('select');
          timezoneSelect.id = 'timezone-select';
          timezoneSelect.className = 'form-select w-full rounded p-1 border border-gray-300';
          
          // Ajouter les options de fuseau horaire
          const timezones = [
            'Europe/Paris',
            'Europe/London',
            'America/New_York',
            'America/Los_Angeles',
            'Asia/Tokyo',
            'Australia/Sydney'
          ];
          
          timezones.forEach(tz => {
            const option = document.createElement('option');
            option.value = tz;
            option.text = tz;
            timezoneSelect.appendChild(option);
          });
          
          // Définir le fuseau horaire actuel si disponible
          if (window.TeamCalendarApp && window.TeamCalendarApp.getSettings) {
            const settings = window.TeamCalendarApp.getSettings();
            if (settings && settings.timezone) {
              timezoneSelect.value = settings.timezone;
            }
          }
          
          // Ajouter l'écouteur de changement
          timezoneSelect.addEventListener('change', (e) => {
            const target = e.target as HTMLSelectElement;
            if (window.TeamCalendarApp && window.TeamCalendarApp.setTimezone) {
              window.TeamCalendarApp.setTimezone(target.value);
              console.log(`✅ [MODAL] Fuseau horaire changé: ${target.value}`);
            }
          });
          
          timezoneContainer.appendChild(timezoneSelect);
          console.log('✅ [MODAL] Sélecteur de fuseau horaire créé');
        }
      }
      
      // Gérer les boutons d'export, import et reset
      const exportBtn = document.getElementById('export-data-btn');
      const importBtn = document.getElementById('import-data-btn');
      const resetBtn = document.getElementById('reset-data-btn');
      
      if (exportBtn) {
        exportBtn.addEventListener('click', () => {
          if (window.TeamCalendarApp && window.TeamCalendarApp.exportData) {
            window.TeamCalendarApp.exportData();
            console.log('✅ [MODAL] Export des données déclenché');
          } else {
            console.warn('⚠️ [MODAL] TeamCalendarApp.exportData non disponible');
          }
        });
      }
      
      if (importBtn) {
        importBtn.addEventListener('click', () => {
          if (window.TeamCalendarApp && window.TeamCalendarApp.importData) {
            window.TeamCalendarApp.importData();
            console.log('✅ [MODAL] Import des données déclenché');
          } else {
            console.warn('⚠️ [MODAL] TeamCalendarApp.importData non disponible');
          }
        });
      }
      
      if (resetBtn) {
        resetBtn.addEventListener('click', () => {
          if (confirm('Êtes-vous sûr de vouloir réinitialiser toutes les données? Cette action est irréversible.')) {
            if (window.TeamCalendarApp && window.TeamCalendarApp.resetData) {
              window.TeamCalendarApp.resetData();
              console.log('✅ [MODAL] Réinitialisation des données déclenchée');
            } else {
              console.warn('⚠️ [MODAL] TeamCalendarApp.resetData non disponible');
            }
          }
        });
      }
      
      // Gérer les options de début de semaine
      const weekStartOptions = document.querySelectorAll('input[name="week-start"]');
      weekStartOptions.forEach(option => {
        option.addEventListener('change', (e) => {
          const target = e.target as HTMLInputElement;
          if (target.checked && window.TeamCalendarApp && window.TeamCalendarApp.setWeekStart) {
            window.TeamCalendarApp.setWeekStart(target.value as 'monday' | 'sunday');
            console.log(`✅ [MODAL] Début de semaine changé: ${target.value}`);
          }
        });
      });
      
      // Initialiser les valeurs selon les paramètres actuels
      if (window.TeamCalendarApp && window.TeamCalendarApp.getSettings) {
        const settings = window.TeamCalendarApp.getSettings();
        if (settings) {
          // Début de semaine
          if (settings.weekStart) {
            const weekStartOption = document.querySelector(`input[name="week-start"][value="${settings.weekStart}"]`) as HTMLInputElement;
            if (weekStartOption) {
              weekStartOption.checked = true;
            }
          }
        }
      }
      
      console.log('✅ [MODAL] Paramètres généraux activés');
    } catch (error) {
      console.error('❌ [MODAL] Erreur lors de l\'activation des paramètres généraux:', error);
    }
  } */

  /**
   * Méthode pour réactiver les fonctionnalités si TeamCalendarApp devient disponible
   */
  reactivateWithTeamCalendarApp(): void {
    if (window.TeamCalendarApp) {
      console.log('🔄 [MODAL] TeamCalendarApp détecté - réactivation des fonctionnalités...');
      this.activatePostsManagement();
      this.activateVacationsManagement();
      this.activateAssignmentsManagement();
      this.activateEmployeeTemplatesManagement();
      this.activateEmployeesManagement();
      this.activateGeneralSettings();
      console.log('✅ [MODAL] Fonctionnalités réactivées avec TeamCalendarApp');
    }
  }

  /**
   * Initialise toutes les fonctionnalités
   */
  initializeAll(): void {
    // Vérifier si déjà initialisé pour éviter la duplication des gestionnaires d'événements
    if (this.isInitialized) {
      console.log('⚠️ [MODAL] Fonctionnalités déjà initialisées');
      return;
    }
    
    console.log('🔍 [MODAL] Initialisation des fonctionnalités du modal');
    
    try {
      if (!this.elements.settingsModal) {
        console.error('❌ [MODAL] Modal introuvable pour initialisation');
        return;
      }

      // Vérifier la présence des éléments du DOM en ciblant spécifiquement dans notre modal
      const tabButtons = this.elements.settingsModal.querySelectorAll('.tab-btn');
      const tabContents = this.elements.settingsModal.querySelectorAll('.tab-content');
      
      console.log(`🔍 [MODAL] Éléments trouvés: ${tabButtons.length} boutons d'onglets, ${tabContents.length} contenus d'onglets`);
      
      // Si aucun élément trouvé, essayer avec des sélecteurs plus précis
      if (tabButtons.length === 0 || tabContents.length === 0) {
        console.warn('⚠️ [MODAL] Éléments non trouvés avec sélecteurs de classe, vérification par ID...');
        const postsTab = this.elements.settingsModal.querySelector('#tab-posts');
        const postsContent = this.elements.settingsModal.querySelector('#tab-content-posts');
        console.log(`🔍 [MODAL] Vérification par ID: tab-posts=${!!postsTab}, tab-content-posts=${!!postsContent}`);
        
        // Si toujours rien, attendons un peu et réessayons
        if (!postsTab || !postsContent) {
          console.warn('⚠️ [MODAL] Éléments introuvables même par ID, forçage du délai');
          setTimeout(() => this.initializeAll(), 100);
          return;
        }
      }
      
      // ✅ NOUVELLE ARCHITECTURE : Activer seulement les fonctionnalités de base
      this.activateTabSwitching(); // Navigation entre onglets
      this.activateModalClosing(); // Fermeture du modal

      // ✅ Les fonctionnalités spécifiques aux onglets seront activées individuellement
      // lors du peuplement de chaque onglet dans renderSettingsContent()
      console.log('✅ [MODAL] Fonctionnalités de base activées, les onglets seront peuplés à la demande');
      
      // Marquer comme initialisé pour éviter de réinitialiser
      this.isInitialized = true;
      console.log('✅ [MODAL] Toutes les fonctionnalités du modal ont été initialisées');
    } catch (error) {
      console.error('❌ [MODAL] Erreur lors de l\'initialisation des fonctionnalités:', error);
    }
  }

  // ✅ NOUVELLES MÉTHODES POUR DRAG & DROP
  openAssignmentContextModal(modalData: any): void {
    // Implémentation sera ajoutée via l'instance globale
    console.log('🎯 [openAssignmentContextModal] Méthode de classe appelée');
  }

  createAssignmentContextModal(): HTMLElement {
    // Implémentation sera ajoutée via l'instance globale
    return document.createElement('div');
  }

  populateAssignmentModal(modal: HTMLElement, modalData: any): void {
    // Implémentation sera ajoutée via l'instance globale
    console.log('📝 [populateAssignmentModal] Méthode de classe appelée');
  }

  setupAssignmentModalEvents(modal: HTMLElement, modalData: any): void {
    // Implémentation sera ajoutée via l'instance globale
    console.log('🔧 [setupAssignmentModalEvents] Méthode de classe appelée');
  }

  handleAssignmentAction(actionType: string, modalData: any): void {
    // Implémentation sera ajoutée via l'instance globale
    console.log('🎯 [handleAssignmentAction] Méthode de classe appelée');
  }
}

// Instance globale
const modalFunctionalities = new ModalFunctionalitiesManager();

// Initialisation automatique - DÉSACTIVÉE pour éviter les conflits
// L'initialisation se fait maintenant uniquement lors de l'ouverture du modal
console.log('🔧 [MODAL] Initialisation automatique désactivée - activation à la demande');

// ✅ CORRECTION CRITIQUE : Fonction manquante pour le drag & drop
modalFunctionalities.openAssignmentContextModal = function(modalData: any): void {
  console.log('🎯 [openAssignmentContextModal] Ouverture du modal d\'attribution', modalData);

  try {
    // Vérifier si le modal existe
    let modal = document.getElementById('assignment-context-modal');

    if (!modal) {
      console.log('🔧 [openAssignmentContextModal] Création du modal d\'attribution');
      modal = this.createAssignmentContextModal();
    }

    // Remplir les données du modal
    this.populateAssignmentModal(modal, modalData);

    // Afficher le modal
    modal.classList.remove('hidden');
    modal.style.display = 'block';

    // Configurer les événements
    this.setupAssignmentModalEvents(modal, modalData);

    console.log('✅ [openAssignmentContextModal] Modal d\'attribution ouvert');

  } catch (error) {
    console.error('❌ [openAssignmentContextModal] Erreur:', error);

    // Fallback : utiliser la fonction existante de TeamCalendarApp
    if (window.teamCalendarApp && window.teamCalendarApp.showAssignmentContextModal) {
      window.teamCalendarApp.showAssignmentContextModal(
        modalData.postData,
        modalData.employeeId,
        modalData.dateKey
      );
    }
  }
};

modalFunctionalities.createAssignmentContextModal = function(): HTMLElement {
  console.log('🔧 [createAssignmentContextModal] Création du modal d\'attribution');

  const modal = document.createElement('div');
  modal.id = 'assignment-context-modal';
  modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden';

  modal.innerHTML = `
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
      <div class="p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Attribution de poste</h3>
          <button data-action="close" class="text-gray-400 hover:text-gray-600">
            <span class="material-icons-outlined">close</span>
          </button>
        </div>

        <div class="mb-4">
          <p class="text-sm text-gray-600 mb-2">Poste :</p>
          <p id="modal-post-name" class="font-medium text-gray-900"></p>
        </div>

        <div class="mb-4">
          <p class="text-sm text-gray-600 mb-2">Employé :</p>
          <p id="modal-employee-name" class="font-medium text-gray-900"></p>
        </div>

        <div class="mb-6">
          <p class="text-sm text-gray-600 mb-2">Date :</p>
          <p id="modal-date" class="font-medium text-gray-900"></p>
        </div>

        <div class="mb-6">
          <p class="text-sm text-gray-600 mb-3">Type d'attribution :</p>
          <div class="space-y-2">
            <button data-action="regular" class="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors">
              <div class="font-medium text-gray-900">Attribution régulière</div>
              <div class="text-sm text-gray-600">Répéter chaque semaine</div>
            </button>
            <button data-action="temporary" class="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-green-50 hover:border-green-300 transition-colors">
              <div class="font-medium text-gray-900">Attribution temporaire</div>
              <div class="text-sm text-gray-600">Une seule fois</div>
            </button>
            <button data-action="replacement" class="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-orange-50 hover:border-orange-300 transition-colors">
              <div class="font-medium text-gray-900">Remplacement</div>
              <div class="text-sm text-gray-600">Remplacer un autre employé</div>
            </button>
          </div>
        </div>

        <div class="flex gap-3">
          <button data-action="cancel" class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
            Annuler
          </button>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);
  return modal;
};

modalFunctionalities.populateAssignmentModal = function(modal: HTMLElement, modalData: any): void {
  console.log('📝 [populateAssignmentModal] Remplissage des données du modal');

  try {
    // Remplir les informations
    const postNameEl = modal.querySelector('#modal-post-name');
    const employeeNameEl = modal.querySelector('#modal-employee-name');
    const dateEl = modal.querySelector('#modal-date');

    if (postNameEl) postNameEl.textContent = modalData.postData.label || 'Poste inconnu';
    if (employeeNameEl) employeeNameEl.textContent = modalData.employeeName || 'Employé inconnu';
    if (dateEl) {
      const date = new Date(modalData.dateKey);
      dateEl.textContent = date.toLocaleDateString('fr-FR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }

  } catch (error) {
    console.error('❌ [populateAssignmentModal] Erreur:', error);
  }
};

modalFunctionalities.setupAssignmentModalEvents = function(modal: HTMLElement, modalData: any): void {
  console.log('🔧 [setupAssignmentModalEvents] Configuration des événements du modal');

  // Bouton fermer
  const closeBtn = modal.querySelector('[data-action="close"]');
  if (closeBtn) {
    closeBtn.addEventListener('click', () => {
      modal.classList.add('hidden');
      modal.style.display = 'none';
    });
  }

  // Bouton annuler
  const cancelBtn = modal.querySelector('[data-action="cancel"]');
  if (cancelBtn) {
    cancelBtn.addEventListener('click', () => {
      modal.classList.add('hidden');
      modal.style.display = 'none';
    });
  }

  // Boutons d'action
  const regularBtn = modal.querySelector('[data-action="regular"]');
  const temporaryBtn = modal.querySelector('[data-action="temporary"]');
  const replacementBtn = modal.querySelector('[data-action="replacement"]');

  if (regularBtn) {
    regularBtn.addEventListener('click', () => {
      console.log('🎯 [setupAssignmentModalEvents] Attribution régulière sélectionnée');
      this.handleAssignmentAction('regular', modalData);
      modal.classList.add('hidden');
      modal.style.display = 'none';
    });
  }

  if (temporaryBtn) {
    temporaryBtn.addEventListener('click', () => {
      console.log('🎯 [setupAssignmentModalEvents] Attribution temporaire sélectionnée');
      this.handleAssignmentAction('temporary', modalData);
      modal.classList.add('hidden');
      modal.style.display = 'none';
    });
  }

  if (replacementBtn) {
    replacementBtn.addEventListener('click', () => {
      console.log('🎯 [setupAssignmentModalEvents] Remplacement sélectionné');
      this.handleAssignmentAction('replacement', modalData);
      modal.classList.add('hidden');
      modal.style.display = 'none';
    });
  }

  // Fermer en cliquant sur l'overlay
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.classList.add('hidden');
      modal.style.display = 'none';
    }
  });
};

modalFunctionalities.handleAssignmentAction = function(actionType: string, modalData: any): void {
  console.log(`🎯 [handleAssignmentAction] Action: ${actionType}`, modalData);

  try {
    const app = window.teamCalendarApp;
    if (!app) {
      console.error('❌ [handleAssignmentAction] TeamCalendarApp non disponible');
      return;
    }

    const { postData, employeeId, dateKey } = modalData;

    switch (actionType) {
      case 'regular':
        // Créer une attribution régulière
        app.createRegularAssignment(postData.id, employeeId, false);
        break;

      case 'temporary':
        // Attribution temporaire (une seule fois)
        app.assignSingleCell(postData.id, employeeId, dateKey);
        break;

      case 'replacement':
        // Remplacement (logique plus complexe)
        app.assignFullPostOnce(postData.id, employeeId);
        break;

      default:
        console.warn(`⚠️ [handleAssignmentAction] Action inconnue: ${actionType}`);
    }

    // Rafraîchir l'affichage
    app.render();

  } catch (error) {
    console.error('❌ [handleAssignmentAction] Erreur:', error);
  }
};

// Surveillance de TeamCalendarApp - DÉSACTIVÉE pour éviter les conflits
// La réactivation se fait maintenant uniquement lors de l'ouverture du modal
if (typeof window !== 'undefined') {
  // Surveillance désactivée - les fonctionnalités sont activées à la demande
  console.log('🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande');

  // Optionnel : vérification unique au démarrage
  if (window.TeamCalendarApp && typeof window.TeamCalendarApp.init === 'function') {
    console.log('✅ [MODAL] TeamCalendarApp détecté au démarrage');
  }
}

export default modalFunctionalities;