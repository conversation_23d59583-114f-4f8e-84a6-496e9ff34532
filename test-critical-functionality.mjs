#!/usr/bin/env node

/**
 * Test des fonctionnalités critiques après corrections
 */

import fs from 'fs';

console.log('🧪 [TEST-CRITICAL-FUNCTIONALITY] Test des fonctionnalités critiques...\n');

const filePath = './src/teamCalendarApp.ts';
const content = fs.readFileSync(filePath, 'utf8');

// Tests des corrections appliquées
const tests = [
    {
        name: 'setupEmployeeDragDrop appelé',
        pattern: /this\.setupEmployeeDragDrop\(\)/,
        shouldExist: true,
        description: 'La fonction setupEmployeeDragDrop est appelée'
    },
    {
        name: 'addPostButton initialisé',
        pattern: /addPostButton:\s*document\.getElementById/,
        shouldExist: true,
        description: 'addPostButton est initialisé dans elements'
    },
    {
        name: 'createAvailablePostsContainer appelé',
        pattern: /this\.createAvailablePostsContainer\(\)/,
        shouldExist: true,
        description: 'createAvailablePostsContainer est appelé'
    },
    {
        name: 'ModalManager initialisé',
        pattern: /this\.ModalManager\.init\(this\)/,
        shouldExist: true,
        description: 'ModalManager.init() est appelé'
    },
    {
        name: 'Drag & drop configuration',
        pattern: /setupEmployeeDragDrop:\s*function/,
        shouldExist: true,
        description: 'La fonction setupEmployeeDragDrop existe'
    },
    {
        name: 'Postes disponibles',
        pattern: /createAvailablePostsContainer:\s*function/,
        shouldExist: true,
        description: 'La fonction createAvailablePostsContainer existe'
    },
    {
        name: 'ModalManager complet',
        pattern: /ModalManager:\s*\{/,
        shouldExist: true,
        description: 'L\'objet ModalManager est défini'
    }
];

let passedTests = 0;
let totalTests = tests.length;

console.log('📋 Tests des fonctionnalités critiques :');
tests.forEach((test, index) => {
    const testPassed = test.shouldExist ? test.pattern.test(content) : !test.pattern.test(content);
    
    if (testPassed) {
        console.log(`✅ ${index + 1}. ${test.name}`);
        console.log(`   ${test.description}`);
        passedTests++;
    } else {
        console.log(`❌ ${index + 1}. ${test.name}`);
        console.log(`   ${test.description} - ÉCHEC`);
    }
});

console.log(`\n📊 RÉSULTATS : ${passedTests}/${totalTests} tests passés`);

if (passedTests === totalTests) {
    console.log('🎉 TOUS LES TESTS PASSÉS !');
    console.log('\n✅ Les corrections ont été appliquées avec succès :');
    console.log('✅ 1. setupEmployeeDragDrop() ajouté dans attachAllEventListeners');
    console.log('✅ 2. addPostButton initialisé dans elements');
    console.log('✅ 3. createAvailablePostsContainer() ajouté dans renderEmployees');
    console.log('✅ 4. ModalManager.init() ajouté dans attachAllEventListeners');
    console.log('✅ 5. Toutes les fonctions de drag & drop existent');
    console.log('✅ 6. Toutes les fonctions de postes disponibles existent');
    console.log('✅ 7. ModalManager complet et fonctionnel');
    
    console.log('\n🚀 FONCTIONNALITÉS CRITIQUES RÉTABLIES !');
    console.log('\n📋 Instructions pour tester dans le navigateur :');
    console.log('1. Ouvrir http://localhost:5173');
    console.log('2. Vérifier le drag & drop des employés');
    console.log('3. Cliquer sur les boutons de paramètres');
    console.log('4. Vérifier que les postes disponibles sont visibles');
    console.log('5. Tester les modales d\'édition');
    
    console.log('\n🔧 Si problèmes persistants, exécuter dans la console :');
    console.log('checkDOMStructure()');
    console.log('checkTeamCalendarApp()');
    console.log('teamCalendarApp.setupEmployeeDragDrop()');
    
} else {
    console.log('⚠️ Certains tests ont échoué. Vérifiez les corrections.');
}

console.log('\n✅ [TEST-CRITICAL-FUNCTIONALITY] Test terminé !'); 