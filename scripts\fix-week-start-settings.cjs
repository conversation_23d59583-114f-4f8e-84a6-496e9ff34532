#!/usr/bin/env node

/**
 * Script de correction des paramètres weekStartsOn
 * Corrige le décalage des jours de la semaine en changeant de 'sunday' à 'monday'
 */

const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  host: process.env.DB_HOST || '*************',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'team_calendar',
  user: process.env.DB_USER || 'team_calendar_user',
  password: process.env.DB_PASSWORD || 'SecurePassword123!',
  ssl: false,
  connectionTimeoutMillis: 10000,
  idleTimeoutMillis: 30000,
  max: 10
});

async function query(text, params) {
  const client = await pool.connect();
  try {
    const result = await client.query(text, params);
    return result;
  } finally {
    client.release();
  }
}

async function fixWeekStartSettings() {
  console.log('🔧 [FIX] Correction des paramètres de début de semaine...');
  
  try {
    // 1. Vérifier les paramètres actuels
    console.log('📋 Vérification des paramètres actuels...');
    const currentSettings = await query('SELECT * FROM app_settings WHERE setting_key IN ($1, $2)', 
      ['weekStartsOn', 'weekStartDay']);
    
    console.log('Paramètres actuels:');
    currentSettings.rows.forEach(row => {
      console.log(`   ${row.setting_key}: ${row.setting_value}`);
    });
    
    // 2. Mettre à jour weekStartsOn vers 'monday'
    console.log('\n🔄 Mise à jour de weekStartsOn vers "monday"...');
    await query(`
      INSERT INTO app_settings (setting_key, setting_value, description)
      VALUES ($1, $2, $3)
      ON CONFLICT (setting_key) 
      DO UPDATE SET 
        setting_value = $2,
        updated_at = CURRENT_TIMESTAMP
    `, ['weekStartsOn', '"monday"', 'Premier jour de la semaine']);
    
    // 3. Ajouter/mettre à jour weekStartDay vers 1
    console.log('🔄 Mise à jour de weekStartDay vers 1...');
    await query(`
      INSERT INTO app_settings (setting_key, setting_value, description)
      VALUES ($1, $2, $3)
      ON CONFLICT (setting_key) 
      DO UPDATE SET 
        setting_value = $2,
        updated_at = CURRENT_TIMESTAMP
    `, ['weekStartDay', '1', 'Premier jour de la semaine (numérique)']);
    
    // 4. Vérifier les nouveaux paramètres
    console.log('\n✅ Vérification des nouveaux paramètres...');
    const newSettings = await query('SELECT * FROM app_settings WHERE setting_key IN ($1, $2)', 
      ['weekStartsOn', 'weekStartDay']);
    
    console.log('Nouveaux paramètres:');
    newSettings.rows.forEach(row => {
      console.log(`   ${row.setting_key}: ${row.setting_value}`);
    });
    
    console.log('\n✅ [FIX] Correction terminée avec succès !');
    console.log('📝 Les attributions régulières devraient maintenant s\'appliquer correctement du lundi au vendredi.');
    
  } catch (error) {
    console.error('❌ [FIX] Erreur lors de la correction:', error);
    throw error;
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  fixWeekStartSettings()
    .then(() => {
      console.log('\n🎉 Script terminé avec succès');
      pool.end();
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Erreur fatale:', error);
      pool.end();
      process.exit(1);
    });
}

module.exports = { fixWeekStartSettings };
