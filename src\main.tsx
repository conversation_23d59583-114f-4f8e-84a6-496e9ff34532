import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import './styles/loading-animation.css'
import './styles/employee-names-fix.css'
import './toastSystem.js'
// quick-fix.js supprimé - hotfixes intégrés dans le code principal
import App from './App'

// 🟣 LOGS FRONTEND - Intégration avec système unifié (types corrigés)
if (typeof window !== 'undefined') {
  // Attendre que le système unifié soit prêt
  setTimeout(() => {
    const unifiedLogger = (window as any).unifiedLogger;
    if (unifiedLogger) {
      unifiedLogger.frontend.info('Application React démarrée', {
        version: '1.0.0',
        timestamp: new Date().toISOString()
      });
      
      // Capturer les erreurs React spécifiques
      const originalError = console.error;
      console.error = (...args) => {
        originalError.apply(console, args);
        const message = args.join(' ');
        if (message.includes('React') || message.includes('Warning:')) {
          unifiedLogger.frontend.error(`React: ${message}`, { args });
        }
      };
      
      console.log('🟣 [Frontend] Logger React intégré au système unifié');
    }
  }, 1000);
}

// ✅ INITIALISATION DU SYSTÈME DE LOGS OPTIMISÉ
import '../scripts/cleanup-logs-system.js'
import '../scripts/log-level-control.js'


// ✅ ENREGISTREMENT DU SERVICE WORKER
if ('serviceWorker' in navigator && window.location.hostname !== 'localhost') {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('✅ [SW] Service Worker enregistré:', registration.scope);

        // Vérifier les mises à jour
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                console.log('🔄 [SW] Nouvelle version disponible');
                // Optionnel : notifier l'utilisateur
                if (window.toastSystem) {
                  window.toastSystem.info('Nouvelle version disponible. Rechargez la page.');
                }
              }
            });
          }
        });
      })
      .catch((error) => {
        console.error('❌ [SW] Erreur enregistrement Service Worker:', error);
      });
  });
}

const rootElement = document.getElementById('root')!;

createRoot(rootElement).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
