import { pool, closePool } from '../server/config/database.js';
import { readFileSync } from 'fs';
import { join } from 'path';

// Couleurs pour les logs
const colors = {
    red: '\x1b[31m', green: '\x1b[32m', yellow: '\x1b[33m',
    blue: '\x1b[34m', magenta: '\x1b[35m', cyan: '\x1b[36m', reset: '\x1b[0m'
};
const log = {
    info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
    success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
    error: (msg) => console.error(`${colors.red}❌ ${msg}${colors.reset}`),
    title: (msg) => console.log(`${colors.magenta}🚀 ${msg}${colors.reset}`)
};

async function applyLogMigration() {
    log.title('Application de la migration pour la table `logs`...');
    const client = await pool.connect();
    
    try {
        const migrationFile = '010_create_logs_table.sql';
        const sql = readFileSync(join(process.cwd(), 'database', 'migrations', migrationFile), 'utf-8');
        
        await client.query(sql);
        log.success(`Migration ${migrationFile} appliquée avec succès ! La table 'logs' est prête.`);
        
    } catch (err) {
        log.error('Erreur durant l\'application de la migration de la table `logs`.');
        log.error(err.message);
        process.exit(1);
    } finally {
        client.release();
    }
}

async function main() {
    try {
        await applyLogMigration();
    } finally {
        await closePool();
        log.title('Script terminé.');
    }
}

main(); 