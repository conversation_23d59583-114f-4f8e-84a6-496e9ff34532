#!/usr/bin/env node

/**
 * 🔄 INTÉGRATION COMPLÈTE - LOGS BACKEND DANS SYSTÈME UNIFIÉ
 * 
 * Objectifs :
 * 1. Capturer TOUS les logs de la console du serveur backend
 * 2. Les intégrer dans le système de logs unifié
 * 3. Assurer l'ordre chronologique avec les autres logs
 * 4. Activer le mode temps réel pour tous les logs
 * 
 * Date: 2025-07-02
 * Contexte: Demande utilisateur pour intégrer tous les logs backend
 */

import { readFileSync, writeFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🔄 [INTEGRATION] Démarrage intégration logs backend dans système unifié...');

// =====================================================
// ÉTAPE 1: CORRECTION DU PATCH CONSOLE BACKEND
// =====================================================

console.log('\n1️⃣ Correction du patch console backend...');

const serverAppPath = join(__dirname, 'server/app.js');
let serverContent = readFileSync(serverAppPath, 'utf8');

// Rechercher et corriger le patch console existant
const patchConsolePattern = /function patchConsole\(sessionId\) \{[\s\S]*?\}/;
const existingPatch = serverContent.match(patchConsolePattern);

if (existingPatch) {
  console.log('✅ Patch console existant trouvé, amélioration...');
  
  // Nouveau patch console amélioré avec intégration unifiée
  const improvedPatch = `function patchConsole(sessionId) {
    let isSending = false;
    let logQueue = [];
    let batchTimer = null;
    
    const shouldSkip = (args) => {
        const txt = args.filter(a => typeof a === 'string').join(' ');
        return txt.includes('[query]') || 
               txt.includes('INSERT INTO logs') || 
               txt.includes('/api/logs') ||
               txt.includes('[LOGS]') ||
               txt.includes('[CAPTURE]');
    };

    const processBatch = async () => {
        if (logQueue.length === 0) return;
        
        const batch = logQueue.splice(0);
        isSending = true;
        
        try {
            for (const logEntry of batch) {
                await fetch('http://localhost:3001/api/logs', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sessionId,
                        source: 'backend',
                        level: logEntry.level,
                        message: logEntry.message,
                        data: logEntry.data || {},
                        priority: logEntry.level === 'error' ? 3 : (logEntry.level === 'warn' ? 2 : 1),
                        timestamp: new Date().toISOString()
                    })
                });
            }
        } catch (err) {
            originalConsoleError('[LOGS] Envoi batch échoué:', err);
        } finally {
            isSending = false;
        }
    };

    const queueLog = (level, args) => {
        if (shouldSkip(args)) return;
        
        const message = args.map(arg => {
            if (typeof arg === 'object') {
                try { 
                    return JSON.stringify(arg); 
                } catch { 
                    return '[Objet non sérialisable]'; 
                }
            }
            return String(arg);
        }).join(' ');
        
        logQueue.push({
            level,
            message: \`[BACKEND] \${message}\`,
            data: { 
                originalArgs: args,
                source: 'console-backend',
                timestamp: new Date().toISOString()
            }
        });
        
        // Traitement par batch toutes les 500ms
        if (!batchTimer) {
            batchTimer = setTimeout(() => {
                batchTimer = null;
                processBatch();
            }, 500);
        }
    };

    // Patch des méthodes console
    console.log = (...args) => { 
        originalConsoleLog(...args); 
        queueLog('info', args); 
    };
    console.error = (...args) => { 
        originalConsoleError(...args); 
        queueLog('error', args); 
    };
    console.warn = (...args) => { 
        originalConsoleWarn(...args); 
        queueLog('warn', args); 
    };
    console.info = (...args) => { 
        originalConsoleLog(...args); 
        queueLog('info', args); 
    };
    console.debug = (...args) => { 
        originalConsoleLog(...args); 
        queueLog('debug', args); 
    };

    originalConsoleLog('[LOGS] Console backend patchée avec intégration unifiée (batch processing).');
}`;

  // Remplacer le patch existant
  serverContent = serverContent.replace(patchConsolePattern, improvedPatch);
  console.log('✅ Patch console amélioré avec batch processing');
  
} else {
  console.log('⚠️ Aucun patch console trouvé, création...');
  
  // Ajouter le patch après les imports
  const importEndIndex = serverContent.lastIndexOf('import');
  const insertIndex = serverContent.indexOf('\n', importEndIndex) + 1;
  
  const newPatch = `

// =====================================================
// PATCH CONSOLE BACKEND - INTÉGRATION SYSTÈME UNIFIÉ
// =====================================================
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

function patchConsole(sessionId) {
    let isSending = false;
    let logQueue = [];
    let batchTimer = null;
    
    const shouldSkip = (args) => {
        const txt = args.filter(a => typeof a === 'string').join(' ');
        return txt.includes('[query]') || 
               txt.includes('INSERT INTO logs') || 
               txt.includes('/api/logs') ||
               txt.includes('[LOGS]') ||
               txt.includes('[CAPTURE]');
    };

    const processBatch = async () => {
        if (logQueue.length === 0) return;
        
        const batch = logQueue.splice(0);
        isSending = true;
        
        try {
            for (const logEntry of batch) {
                await fetch('http://localhost:3001/api/logs', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sessionId,
                        source: 'backend',
                        level: logEntry.level,
                        message: logEntry.message,
                        data: logEntry.data || {},
                        priority: logEntry.level === 'error' ? 3 : (logEntry.level === 'warn' ? 2 : 1),
                        timestamp: new Date().toISOString()
                    })
                });
            }
        } catch (err) {
            originalConsoleError('[LOGS] Envoi batch échoué:', err);
        } finally {
            isSending = false;
        }
    };

    const queueLog = (level, args) => {
        if (shouldSkip(args)) return;
        
        const message = args.map(arg => {
            if (typeof arg === 'object') {
                try { 
                    return JSON.stringify(arg); 
                } catch { 
                    return '[Objet non sérialisable]'; 
                }
            }
            return String(arg);
        }).join(' ');
        
        logQueue.push({
            level,
            message: \`[BACKEND] \${message}\`,
            data: { 
                originalArgs: args,
                source: 'console-backend',
                timestamp: new Date().toISOString()
            }
        });
        
        // Traitement par batch toutes les 500ms
        if (!batchTimer) {
            batchTimer = setTimeout(() => {
                batchTimer = null;
                processBatch();
            }, 500);
        }
    };

    // Patch des méthodes console
    console.log = (...args) => { 
        originalConsoleLog(...args); 
        queueLog('info', args); 
    };
    console.error = (...args) => { 
        originalConsoleError(...args); 
        queueLog('error', args); 
    };
    console.warn = (...args) => { 
        originalConsoleWarn(...args); 
        queueLog('warn', args); 
    };
    console.info = (...args) => { 
        originalConsoleLog(...args); 
        queueLog('info', args); 
    };
    console.debug = (...args) => { 
        originalConsoleLog(...args); 
        queueLog('debug', args); 
    };

    originalConsoleLog('[LOGS] Console backend patchée avec intégration unifiée (batch processing).');
}

// Initialiser le patch avec session serveur
const serverSessionId = \`server-\${new Date().toISOString()}\`;
patchConsole(serverSessionId);

`;

  serverContent = serverContent.slice(0, insertIndex) + newPatch + serverContent.slice(insertIndex);
  console.log('✅ Patch console créé et intégré');
}

// =====================================================
// ÉTAPE 2: AMÉLIORATION DU LOGGER BACKEND
// =====================================================

console.log('\n2️⃣ Amélioration du logger backend...');

const loggerPath = join(__dirname, 'server/config/logger.js');
let loggerContent = readFileSync(loggerPath, 'utf8');

// Activer le patch console dans le logger
const patchConsolePattern2 = /patchConsole\(\) \{[\s\S]*?\}/;
const existingPatch2 = loggerContent.match(patchConsolePattern2);

if (existingPatch2) {
  console.log('✅ Patch console dans logger trouvé, activation...');
  
  const activatedPatch = `patchConsole() {
    // ✅ ACTIVÉ : Patch automatique de la console pour intégration unifiée
    if (this.isConsolePatched) {
      console.log('⚠️ [Logger] Console déjà patchée');
      return;
    }
    
    const originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info,
      debug: console.debug
    };
    
    const shouldSkip = (args) => {
      const txt = args.filter(a => typeof a === 'string').join(' ');
      return txt.includes('[LOGS]') || 
             txt.includes('INSERT INTO logs') || 
             txt.includes('/api/logs') ||
             txt.includes('[CAPTURE]');
    };
    
    const sendToUnified = async (level, args) => {
      if (shouldSkip(args)) return;
      
      const message = args.map(arg => {
        if (typeof arg === 'object') {
          try { return JSON.stringify(arg); } catch { return '[Objet non sérialisable]'; }
        }
        return String(arg);
      }).join(' ');
      
      // Envoyer vers le système unifié
      await this.captureToDb(level, \`[LOGGER] \${message}\`, {
        originalArgs: args,
        source: 'logger-backend',
        timestamp: new Date().toISOString()
      }, 'backend');
    };
    
    // Patch des méthodes console
    console.log = (...args) => { 
      originalConsole.log(...args); 
      sendToUnified('info', args); 
    };
    console.error = (...args) => { 
      originalConsole.error(...args); 
      sendToUnified('error', args); 
    };
    console.warn = (...args) => { 
      originalConsole.warn(...args); 
      sendToUnified('warn', args); 
    };
    console.info = (...args) => { 
      originalConsole.info(...args); 
      sendToUnified('info', args); 
    };
    console.debug = (...args) => { 
      originalConsole.debug(...args); 
      sendToUnified('debug', args); 
    };
    
    this.isConsolePatched = true;
    console.log('✅ [Logger] Console patchée pour intégration unifiée');
  }`;
  
  loggerContent = loggerContent.replace(patchConsolePattern2, activatedPatch);
  console.log('✅ Patch console activé dans logger');
}

// =====================================================
// ÉTAPE 3: AMÉLIORATION DE LA ROUTE SSE POUR ORDRE CHRONOLOGIQUE
// =====================================================

console.log('\n3️⃣ Amélioration route SSE pour ordre chronologique...');

// Rechercher la route SSE dans server/app.js
const sseRoutePattern = /app\.get\(['"`]\/api\/debug\/stream\/:sessionId['"`], \(req, res\) => \{/;
const sseMatch = serverContent.match(sseRoutePattern);

if (sseMatch) {
  console.log('✅ Route SSE trouvée, amélioration ordre chronologique...');
  
  // Améliorer la requête SQL pour un ordre chronologique strict
  const improvedQuery = `
        WITH ordered_logs AS (
          SELECT id, ts, level, source, message, 
                 data,
                 CASE 
                   WHEN level = 'error' THEN 100
                   WHEN level = 'warn' THEN 85
                   WHEN message ILIKE '%DRAG-SYSTEM%' OR message ILIKE '%AI-DRAG-LOG%' THEN 90
                   WHEN source = 'backend' THEN 70
                   WHEN source = 'frontend' THEN 65
                   WHEN source = 'drag-system' THEN 80
                   ELSE 50
                 END as score
          FROM logs 
          WHERE session_id = $1 
        )
        SELECT id, ts, level, source, message, data, score 
        FROM ordered_logs 
        ORDER BY ts ASC, score DESC, id ASC
        LIMIT $2`;
  
  // Remplacer la requête existante
  const queryPattern = /SELECT id, ts, level, source, message, [\s\S]*?ORDER BY [\s\S]*?LIMIT \$2/;
  serverContent = serverContent.replace(queryPattern, improvedQuery);
  
  console.log('✅ Requête SSE améliorée pour ordre chronologique strict');
}

// =====================================================
// ÉTAPE 4: AJOUT D'UNE ROUTE DE TEST BACKEND
// =====================================================

console.log('\n4️⃣ Ajout route de test backend...');

// Ajouter une route de test pour vérifier l'intégration
const testRoute = `

// =====================================================
// ROUTE DE TEST - INTÉGRATION LOGS BACKEND
// =====================================================
app.get('/api/debug/test-backend-logs', (req, res) => {
  console.log('[TEST] Log info depuis backend');
  console.warn('[TEST] Log warning depuis backend');
  console.error('[TEST] Log error depuis backend');
  
  // Test avec des données complexes
  console.log('[TEST] Objet complexe:', { 
    timestamp: new Date().toISOString(),
    test: true,
    nested: { data: 'test' }
  });
  
  res.json({ 
    message: 'Logs de test envoyés depuis backend',
    timestamp: new Date().toISOString(),
    sessionId: serverSessionId
  });
});

`;

// Insérer la route de test avant les routes existantes
const routesIndex = serverContent.indexOf('// =====================================================');
if (routesIndex !== -1) {
  serverContent = serverContent.slice(0, routesIndex) + testRoute + serverContent.slice(routesIndex);
  console.log('✅ Route de test backend ajoutée');
}

// =====================================================
// ÉTAPE 5: ÉCRITURE DES FICHIERS MODIFIÉS
// =====================================================

console.log('\n5️⃣ Écriture des fichiers modifiés...');

try {
  writeFileSync(serverAppPath, serverContent);
  console.log('✅ server/app.js mis à jour');
  
  writeFileSync(loggerPath, loggerContent);
  console.log('✅ server/config/logger.js mis à jour');
  
} catch (error) {
  console.error('❌ Erreur écriture fichiers:', error);
  process.exit(1);
}

// =====================================================
// ÉTAPE 6: SCRIPT DE TEST D'INTÉGRATION
// =====================================================

console.log('\n6️⃣ Création script de test...');

const testScript = `#!/usr/bin/env node

/**
 * 🧪 TEST INTÉGRATION LOGS BACKEND
 * Vérifier que tous les logs backend sont capturés
 */

import fetch from 'node-fetch';

const API_BASE = 'http://localhost:3001';

async function testBackendLogsIntegration() {
  console.log('🧪 [TEST] Test intégration logs backend...');
  
  try {
    // 1. Test connexion serveur
    console.log('1️⃣ Test connexion serveur...');
    const healthResponse = await fetch(\`\${API_BASE}/api/health\`);
    if (!healthResponse.ok) {
      throw new Error('Serveur non accessible');
    }
    console.log('✅ Serveur accessible');
    
    // 2. Test route de test backend
    console.log('2️⃣ Test route logs backend...');
    const testResponse = await fetch(\`\${API_BASE}/api/debug/test-backend-logs\`);
    if (!testResponse.ok) {
      throw new Error('Route test backend non accessible');
    }
    const testData = await testResponse.json();
    console.log('✅ Logs de test envoyés:', testData.message);
    
    // 3. Attendre un peu pour que les logs soient traités
    console.log('3️⃣ Attente traitement logs...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 4. Récupérer les logs récents
    console.log('4️⃣ Récupération logs récents...');
    const sessionResponse = await fetch(\`\${API_BASE}/api/debug/current-session\`);
    const sessionData = await sessionResponse.json();
    
    const logsResponse = await fetch(\`\${API_BASE}/api/debug/sessions/\${sessionData.sessionId}?mode=chronological&max=50\`);
    const logs = await logsResponse.json();
    
    // 5. Analyser les logs backend
    const backendLogs = logs.filter(log => 
      log.source === 'backend' || 
      log.message.includes('[BACKEND]') ||
      log.message.includes('[LOGGER]')
    );
    
    console.log(\`✅ \${backendLogs.length} logs backend trouvés\`);
    
    // Afficher les logs backend récents
    const recentBackendLogs = backendLogs.slice(-5);
    console.log('📋 Logs backend récents:');
    recentBackendLogs.forEach(log => {
      console.log(\`   [\${log.ts}] [\${log.level}] \${log.message}\`);
    });
    
    // 6. Vérifier l'ordre chronologique
    const timestamps = logs.map(log => new Date(log.ts).getTime());
    const isChronological = timestamps.every((ts, i) => i === 0 || ts >= timestamps[i - 1]);
    
    console.log(\`✅ Ordre chronologique: \${isChronological ? 'CORRECT' : 'INCORRECT'}\`);
    
    console.log('\\n🎉 TEST RÉUSSI - Intégration logs backend opérationnelle');
    
  } catch (error) {
    console.error('❌ Test échoué:', error.message);
    process.exit(1);
  }
}

testBackendLogsIntegration();
`;

writeFileSync('test-backend-logs-integration.mjs', testScript);
console.log('✅ Script de test créé: test-backend-logs-integration.mjs');

// =====================================================
// RÉSUMÉ FINAL
// =====================================================

console.log('\n🎉 INTÉGRATION TERMINÉE');
console.log('=====================================');
console.log('✅ 1. Patch console backend amélioré avec batch processing');
console.log('✅ 2. Logger backend activé pour intégration unifiée');
console.log('✅ 3. Route SSE optimisée pour ordre chronologique strict');
console.log('✅ 4. Route de test backend ajoutée');
console.log('✅ 5. Script de test créé');
console.log('');
console.log('🔄 PROCHAINES ÉTAPES:');
console.log('1. Redémarrer le serveur: npm run start:server');
console.log('2. Tester l\'intégration: node test-backend-logs-integration.mjs');
console.log('3. Vérifier la page /logs pour voir tous les logs backend');
console.log('');
console.log('📋 FONCTIONNALITÉS AJOUTÉES:');
console.log('- TOUS les console.log/error/warn du backend sont capturés');
console.log('- Ordre chronologique strict avec les autres logs');
console.log('- Batch processing pour éviter les surcharges');
console.log('- Intégration transparente dans le système unifié');
console.log('- Route de test pour validation'); 