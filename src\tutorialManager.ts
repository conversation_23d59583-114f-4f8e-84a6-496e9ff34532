/*
 * Tutorial Manager – Gestionnaire centralisé des tutoriels interactifs
 * ---------------------------------------------------------------
 * 1. Enregistrement facile de « flows » (parcours) décrits sous forme d’objets JSON/TS
 * 2. Overlay + bulles d’aide ancrées automatiquement au DOM cible
 * 3. API publique : start / next / prev / skip / resume
 * 4. Persistance locale de la progression (localStorage)
 * 5. Compatible avec n’importe quelle appli vanilla/React (zéro dépendance externe)
 */

export interface TutorialStep {
  id: string;
  title: string;
  description: string;
  /** Sélecteur CSS ou fonction retournant un élément */
  target: string | (() => HTMLElement | null);
  /** Callback synchrone ou asynchrone qui doit retourner true pour valider la step */
  validate?: () => boolean | Promise<boolean>;
  /** Callback exécuté quand la step devient active */
  onEnter?: () => void;
  /** Callback exécuté quand on quitte la step */
  onExit?: () => void;
}

export interface TutorialFlow {
  id: string;
  title: string;
  steps: TutorialStep[];
}

interface StoredProgress {
  flowId: string;
  stepIndex: number;
  updatedAt: string; // ISO
}

export class TutorialManager {
  private flows: Map<string, TutorialFlow> = new Map();
  private currentFlow: TutorialFlow | null = null;
  private currentStepIndex = 0;
  private overlay: HTMLElement | null = null;
  private bubble: HTMLElement | null = null;

  // ------------------------------------------------------------
  // API publique
  // ------------------------------------------------------------
  registerFlow(flow: TutorialFlow) {
    this.flows.set(flow.id, flow);
  }

  listFlows(): TutorialFlow[] {
    return Array.from(this.flows.values());
  }

  start(flowId: string) {
    const flow = this.flows.get(flowId);
    if (!flow) {
      console.error(`[tutorial] Flow not found: ${flowId}`);
      return;
    }
    this.currentFlow = flow;
    this.currentStepIndex = 0;
    this.createOverlay();
    void this.gotoStep(0);
  }

  next() {
    if (!this.currentFlow) return;
    if (this.currentStepIndex < this.currentFlow.steps.length - 1) {
      void this.gotoStep(this.currentStepIndex + 1);
    } else {
      this.completeFlow();
    }
  }

  prev() {
    if (!this.currentFlow) return;
    if (this.currentStepIndex > 0) {
      void this.gotoStep(this.currentStepIndex - 1);
    }
  }

  skip() {
    this.destroyOverlay();
    this.currentFlow = null;
  }

  resume() {
    const progress = this.loadProgress();
    if (progress && this.flows.has(progress.flowId)) {
      this.currentFlow = this.flows.get(progress.flowId)!;
      this.currentStepIndex = progress.stepIndex;
      this.createOverlay();
      void this.gotoStep(this.currentStepIndex);
    }
  }

  // ------------------------------------------------------------
  // Step navigation internal
  // ------------------------------------------------------------
  private async gotoStep(index: number) {
    if (!this.currentFlow) return;
    const step = this.currentFlow.steps[index];
    if (!step) return;

    // OnExit précédente
    if (this.currentStepIndex !== index) {
      const prev = this.currentFlow.steps[this.currentStepIndex];
      prev?.onExit?.();
    }

    this.currentStepIndex = index;
    this.saveProgress();

    // OnEnter
    step.onEnter?.();

    // Afficher la bulle
    this.showBubble(step);

    // Attendre la validation le cas échéant
    if (step.validate) {
      const ok = await this.waitForValidation(step);
      if (ok) {
        this.next();
      }
    }
  }

  private waitForValidation(step: TutorialStep): Promise<boolean> {
    return new Promise(resolve => {
      const check = async () => {
        const res = await step.validate!();
        if (res) {
          resolve(true);
        } else {
          // re-essayer dans 600 ms
          setTimeout(check, 600);
        }
      };
      check();
    });
  }

  private completeFlow() {
    console.log('[tutorial] Flow completed');
    this.destroyOverlay();
    this.clearProgress();
  }

  // ------------------------------------------------------------
  // Overlay & bubble UI
  // ------------------------------------------------------------
  private createOverlay() {
    if (this.overlay) return;
    this.overlay = document.createElement('div');
    this.overlay.className = 'tutorial-overlay fixed inset-0 bg-black bg-opacity-40 z-[9998]';
    document.body.appendChild(this.overlay);

    // Fermer via ESC
    const esc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') this.skip();
    };
    document.addEventListener('keydown', esc, { once: true });
  }

  private destroyOverlay() {
    this.overlay?.remove();
    this.overlay = null;
    this.bubble?.remove();
    this.bubble = null;
  }

  private showBubble(step: TutorialStep) {
    // Nettoyer bulle précédente
    this.bubble?.remove();

    const targetEl = typeof step.target === 'string' ? document.querySelector(step.target as string) : step.target();
    const rect = targetEl?.getBoundingClientRect();

    this.bubble = document.createElement('div');
    this.bubble.className = 'tutorial-bubble bg-white rounded-lg shadow-xl p-4 max-w-xs text-sm z-[9999] fixed';
    this.bubble.innerHTML = `
      <h3 class="text-base font-semibold mb-1">${step.title}</h3>
      <p class="mb-3 text-gray-700">${step.description}</p>
      <div class="flex gap-2 justify-between">
        <button id="tut-prev" class="px-3 py-1 text-sm bg-gray-200 rounded">◀</button>
        <button id="tut-next" class="px-3 py-1 text-sm bg-indigo-600 text-white rounded">Suivant ▶</button>
        <button id="tut-skip" class="px-3 py-1 text-sm text-gray-500">Passer</button>
      </div>`;

    document.body.appendChild(this.bubble);

    // Positionner la bulle : au-dessus par défaut
    if (rect) {
      const top = rect.top - this.bubble.offsetHeight - 12;
      const left = rect.left + rect.width / 2 - this.bubble.offsetWidth / 2;
      this.bubble.style.top = `${Math.max(8, top)}px`;
      this.bubble.style.left = `${Math.max(8, left)}px`;
    } else {
      // Centré si pas de cible
      this.bubble.style.top = '40%';
      this.bubble.style.left = '50%';
      this.bubble.style.transform = 'translate(-50%, -50%)';
    }

    // Boutons
    this.bubble.querySelector('#tut-next')?.addEventListener('click', () => this.next());
    this.bubble.querySelector('#tut-prev')?.addEventListener('click', () => this.prev());
    this.bubble.querySelector('#tut-skip')?.addEventListener('click', () => this.skip());
  }

  // ------------------------------------------------------------
  // Persistance locale
  // ------------------------------------------------------------
  private saveProgress() {
    if (!this.currentFlow) return;
    const progress: StoredProgress = {
      flowId: this.currentFlow.id,
      stepIndex: this.currentStepIndex,
      updatedAt: new Date().toISOString()
    };
    localStorage.setItem('tutorial-progress', JSON.stringify(progress));
  }

  private loadProgress(): StoredProgress | null {
    try {
      return JSON.parse(localStorage.getItem('tutorial-progress') || 'null');
    } catch {
      return null;
    }
  }

  private clearProgress() {
    localStorage.removeItem('tutorial-progress');
  }
}

// --------------------------------------------------------------
// Export global instance (pour debug et accès depuis l'app)
// --------------------------------------------------------------
export const tutorialManager = new TutorialManager();
if (typeof window !== 'undefined') {
  (window as any).tutorialManager = tutorialManager;
} 