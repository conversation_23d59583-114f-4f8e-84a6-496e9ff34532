#!/usr/bin/env node

/**
 * 🧪 SCRIPT DE TEST DE L'API DE PURGE
 * 
 * Ce script teste l'API /api/database/purge pour s'assurer qu'elle fonctionne correctement
 */

import { createRequire } from 'module';
const require = createRequire(import.meta.url);

async function testPurgeAPI() {
    console.log('🧪 [TEST] Test de l\'API de purge de la base de données...\n');
    
    const API_URL = 'http://localhost:3001/api/database/purge';
    
    try {
        console.log(`🌐 [TEST] Appel API: POST ${API_URL}`);
        
        // Utiliser node-fetch ou la méthode HTTP native
        let response, result;
        
        try {
            // Essayer avec fetch global (Node 18+)
            response = await fetch(API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            result = await response.json();
        } catch (fetchError) {
            // Fallback avec http natif
            const https = require('https');
            const http = require('http');
            const url = require('url');
            
            const parsedUrl = url.parse(API_URL);
            const client = parsedUrl.protocol === 'https:' ? https : http;
            
            const options = {
                hostname: parsedUrl.hostname,
                port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
                path: parsedUrl.path,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            result = await new Promise((resolve, reject) => {
                const req = client.request(options, (res) => {
                    let data = '';
                    res.on('data', chunk => data += chunk);
                    res.on('end', () => {
                        try {
                            resolve({
                                status: res.statusCode,
                                statusText: res.statusMessage,
                                json: () => Promise.resolve(JSON.parse(data))
                            });
                        } catch (e) {
                            reject(e);
                        }
                    });
                });
                req.on('error', reject);
                req.end();
            });
            
            response = { status: result.status, statusText: result.statusText };
            result = await result.json();
        }
        
        console.log(`📡 [TEST] Status: ${response.status} ${response.statusText}`);
        
        if (result.success) {
            console.log('✅ [TEST] API de purge fonctionnelle !');
            console.log('\n📊 [RÉSUMÉ] Résultat de la purge:');
            console.log(`   🗑️ Shifts supprimés: ${result.summary?.deleted?.shifts || 'N/A'}`);
            console.log(`   👥 Employés supprimés: ${result.summary?.deleted?.employees || 'N/A'}`);
            console.log(`   ✅ Employés conservés: ${result.summary?.final?.employees || 'N/A'}`);
            console.log(`   🏢 Postes standards: ${result.summary?.final?.posts || 'N/A'}`);
            console.log(`   ⚙️ Paramètres: ${result.summary?.final?.settings || 'N/A'}`);
            console.log(`   📋 Assignements: ${result.summary?.final?.assignments || 'N/A'}`);
            console.log(`\n⏰ Timestamp: ${result.timestamp}`);
        } else {
            console.error('❌ [TEST] Échec de l\'API de purge');
            console.error(`   Erreur: ${result.error}`);
            console.error(`   Message: ${result.message}`);
        }
        
    } catch (error) {
        console.error('❌ [TEST] Erreur lors du test:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.error('\n💡 [CONSEIL] Le serveur n\'est probablement pas démarré.');
            console.error('   Lancez: npm run server');
        }
    }
}

// Exécution si appelé directement
if (import.meta.url === `file://${process.argv[1]}`) {
    testPurgeAPI().catch(error => {
        console.error('❌ Erreur fatale:', error.message);
        process.exit(1);
    });
}

export { testPurgeAPI }; 