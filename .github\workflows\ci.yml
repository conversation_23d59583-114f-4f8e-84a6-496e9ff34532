name: 🚀 CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  # ✅ JOB 1 : LINT & FORMAT
  lint:
    name: 🔍 Lint & Format
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci
        
      - name: 🔍 Run ESLint
        run: npm run lint
        
      - name: 🎨 Check Prettier formatting
        run: npm run format:check
        
      - name: 📊 Upload lint results
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: lint-results
          path: eslint-report.json

  # ✅ JOB 2 : TESTS UNITAIRES
  test:
    name: 🧪 Unit Tests
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci
        
      - name: 🧪 Run tests with coverage
        run: npm run test:coverage
        
      - name: 📊 Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          
      - name: 📊 Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results
          path: |
            coverage/
            test-results.xml

  # ✅ JOB 3 : BUILD & TYPE CHECK
  build:
    name: 🏗️ Build & Type Check
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci
        
      - name: 🔍 TypeScript type check
        run: npm run type-check
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 📊 Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files
          path: dist/

  # ✅ JOB 4 : TESTS E2E (PLAYWRIGHT)
  e2e:
    name: 🎭 E2E Tests
    runs-on: ubuntu-latest
    needs: [build]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci
        
      - name: 📥 Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files
          path: dist/
          
      - name: 🎭 Install Playwright browsers
        run: npx playwright install --with-deps
        
      - name: 🎭 Run Playwright tests
        run: npm run test:e2e
        
      - name: 📊 Upload E2E test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: playwright-report/

  # ✅ JOB 5 : SECURITY AUDIT
  security:
    name: 🔒 Security Audit
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci
        
      - name: 🔒 Run npm audit
        run: npm audit --audit-level=moderate
        
      - name: 🔍 Run CodeQL analysis
        uses: github/codeql-action/init@v2
        with:
          languages: javascript
          
      - name: 🔍 Perform CodeQL analysis
        uses: github/codeql-action/analyze@v2

  # ✅ JOB 6 : PERFORMANCE LIGHTHOUSE
  lighthouse:
    name: 🚦 Lighthouse Performance
    runs-on: ubuntu-latest
    needs: [build]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 📥 Install dependencies
        run: npm ci
        
      - name: 📥 Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files
          path: dist/
          
      - name: 🚦 Run Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.12.x
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  # ✅ JOB 7 : DEPLOY PREVIEW (si PR)
  deploy-preview:
    name: 🚀 Deploy Preview
    runs-on: ubuntu-latest
    needs: [lint, test, build]
    if: github.event_name == 'pull_request'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📥 Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files
          path: dist/
          
      - name: 🚀 Deploy to Netlify Preview
        uses: nwtgck/actions-netlify@v2.0
        with:
          publish-dir: './dist'
          github-token: ${{ secrets.GITHUB_TOKEN }}
          deploy-message: "Deploy from GitHub Actions"
          enable-pull-request-comment: true
          enable-commit-comment: false
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}

  # ✅ JOB 8 : DEPLOY PRODUCTION (si main)
  deploy-production:
    name: 🚀 Deploy Production
    runs-on: ubuntu-latest
    needs: [lint, test, build, e2e, security]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📥 Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files
          path: dist/
          
      - name: 🚀 Deploy to production
        uses: nwtgck/actions-netlify@v2.0
        with:
          publish-dir: './dist'
          production-branch: main
          github-token: ${{ secrets.GITHUB_TOKEN }}
          deploy-message: "Production deployment"
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}

  # ✅ JOB 9 : NOTIFICATION SLACK
  notify:
    name: 📢 Notifications
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: always()
    
    steps:
      - name: 📢 Notify Slack on success
        if: needs.deploy-production.result == 'success'
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: '✅ Déploiement production réussi!'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          
      - name: 📢 Notify Slack on failure
        if: needs.deploy-production.result == 'failure'
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: '❌ Échec du déploiement production'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
