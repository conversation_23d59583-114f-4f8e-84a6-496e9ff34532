-- Migration : Ajout des colonnes de propriétés visuelles pour les remplacements ponctuels
-- Fichier : 007_add_visual_properties_columns.sql

-- Ajouter les colonnes de propriétés visuelles à la table shifts
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS shape VARCHAR(50) NULL;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS is_temporary BOOLEAN DEFAULT FALSE;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS is_replacement BOOLEAN DEFAULT FALSE;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS original_assignment_id VARCHAR(36) NULL;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS replacement_date DATE NULL;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS replacement_reason TEXT NULL;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS visual_style VARCHAR(50) NULL;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS border_style VARCHAR(50) NULL;
<PERSON>TE<PERSON> TABLE shifts ADD COLUMN IF NOT EXISTS color_override VARCHAR(50) NULL;

-- Ajouter des index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_shifts_is_replacement ON shifts(is_replacement);
CREATE INDEX IF NOT EXISTS idx_shifts_is_temporary ON shifts(is_temporary);
CREATE INDEX IF NOT EXISTS idx_shifts_original_assignment_id ON shifts(original_assignment_id);
CREATE INDEX IF NOT EXISTS idx_shifts_replacement_date ON shifts(replacement_date);

-- Ajouter une contrainte de clé étrangère pour original_assignment_id (optionnel)
-- ALTER TABLE shifts ADD CONSTRAINT fk_shifts_original_assignment 
--   FOREIGN KEY (original_assignment_id) REFERENCES regular_assignments(id) ON DELETE SET NULL;

-- Log de la migration
INSERT INTO schema_migrations (version, description, applied_at) 
VALUES ('007', 'Ajout des colonnes de propriétés visuelles pour remplacements ponctuels', NOW())
ON CONFLICT (version) DO NOTHING; 