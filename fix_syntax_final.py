import re

file_path = "src/teamCalendarApp.ts"

with open(file_path, "r", encoding="utf-8") as f:
    content = f.read()

# Correction des apostrophes non échappées
# La chaîne originale en JS: '❌ [init] Erreur lors de l'initialisation:', error);
# Pour que Python lise cette chaîne, il faut l'échapper en Python: '❌ [init] Erreur lors de l\'initialisation:', error);
# Pour que Python écrive cela dans le fichier JS avec un échappement pour le JS, il faut un double échappement: '❌ [init] Erreur lors de l\\\'initialisation:', error);

# Remarque: Je vais utiliser des littéraux de chaîne bruts (raw strings) en Python (r"...")
# pour simplifier l'échappement des backslashes dans les chaînes que je cherche à remplacer ou à insérer.

# Correction pour la ligne console.error
old_error_str = r"            console.error('❌ [init] Erreur lors de l'initialisation:', error);"
new_error_str = r"            console.error('❌ [init] Erreur lors de l'initialisation:', error);"

# Correction pour la ligne window.toastSystem?.error
old_toast_str = r"            window.toastSystem?.error('Erreur lors de l'initialisation de l'application');"
new_toast_str = r"            window.toastSystem?.error('Erreur lors de l'initialisation de l'application');"

# Suppression de la ligne de syntaxe incorrecte "}, 1000);"
# La ligne complète, incluant l'indentation, est "        }, 1000);"
old_stray_line = r"        }, 1000);"
new_stray_line = r"" # Remplacer par une chaîne vide pour la supprimer

# Effectuer les remplacements
modified_content = content.replace(old_error_str, new_error_str)
modified_content = modified_content.replace(old_toast_str, new_toast_str)
modified_content = modified_content.replace(old_stray_line, new_stray_line)

with open(file_path, "w", encoding="utf-8") as f:
    f.write(modified_content)

print("✅ Corrections appliquées dans teamCalendarApp.ts") 