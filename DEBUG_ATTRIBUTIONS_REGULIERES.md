# 🔍 Debug Attributions Régulières

## 🔍 **Problème Identifié**

Les attributions régulières sont **traitées** mais **aucun shift n'est créé** :
- ✅ Attribution chargée : 1 attribution régulière
- ✅ Attribution traitée : Logs "Traitement attribution: Object"
- ❌ Aucun shift créé : Seulement 1 shift dans la plage (ancien)
- ❌ Pas de logs de création : Aucun "Shift créé" ou "appliedCount"

## 🔧 **Logs de Débogage Ajoutés**

### **1. Debug shouldApplyAssignmentToDay**

```typescript
shouldApplyAssignmentToDay: function(assignment, post, day) {
    console.log(`🔍 [shouldApplyAssignmentToDay] Vérification pour:`, {
        assignmentId: assignment.id,
        postId: post.id,
        postLabel: post.label,
        postCategory: post.category,
        dayDate: day.date,
        dayDateKey: day.dateKey,
        dayIsWeekend: day.isWeekend,
        dayOfWeek: day.date.getDay(),
        selectedDays: assignment.selectedDays,
        postWorkingDays: post.workingDays
    });

    // Vérifications avec logs détaillés
    if (!assignment.selectedDays.includes(day.date.getDay())) {
        console.log(`❌ Jour ${day.date.getDay()} non sélectionné dans:`, assignment.selectedDays);
        return false;
    }

    if (post.workingDays && !post.workingDays.includes(day.date.getDay())) {
        console.log(`❌ Jour ${day.date.getDay()} non autorisé dans workingDays:`, post.workingDays);
        return false;
    }

    if (post.category === 'weekend' && !day.isWeekend) {
        console.log(`❌ Poste week-end mais jour non week-end`);
        return false;
    }

    if (day.isWeekend && post.category !== 'weekend' && !post.workingDays) {
        console.log(`❌ Jour week-end mais poste non week-end sans workingDays`);
        return false;
    }

    // Vérifications de dates...

    console.log(`✅ Attribution autorisée pour ${day.dateKey}`);
    return true;
}
```

### **2. Debug Boucle des Jours**

```typescript
// Avant la boucle
console.log(`🔄 Traitement des jours pour attribution ${assignment.id}:`, 
    this.config.days.map(d => ({ 
        dateKey: d.dateKey, 
        isWeekend: d.isWeekend, 
        dayOfWeek: d.date.getDay() 
    }))
);

// Dans la boucle
this.config.days.forEach((day, dayIndex) => {
    console.log(`📅 Vérification jour ${dayIndex}: ${day.dateKey} (${day.date.getDay()})`);
    
    if (!this.shouldApplyAssignmentToDay(assignment, post, day)) {
        console.log(`⏭️ Jour ${day.dateKey} ignoré pour attribution ${assignment.id}`);
        return;
    }

    console.log(`📋 Jour ${dateKey} validé, shifts existants:`, existingShifts.length);
    
    // ... vérifications doublons ...
    
    console.log(`✅ Shift créé: ${post.label} → ${dateKey} pour ${this.getEmployeeName(assignment.employeeId)}`);
    appliedCount++;
});
```

### **3. Debug Compteur Final**

```typescript
console.log(`📊 ${appliedCount} attributions appliquées pour la semaine ${weekKey}`);

if (appliedCount > 0) {
    console.log(`🔄 Rendu demandé car ${appliedCount} shifts créés`);
    this.render();
} else {
    console.log(`⚠️ Aucun shift créé, pas de rendu`);
}
```

## 🎯 **Hypothèses à Vérifier**

### **Hypothèse 1 : selectedDays Incorrect**
- **Vérifier** : `assignment.selectedDays` contient les bons jours
- **Format attendu** : `[1, 2, 3, 4, 5]` pour semaine, `[0, 6]` pour week-end
- **Problème possible** : Jours mal configurés ou format incorrect

### **Hypothèse 2 : workingDays Conflit**
- **Vérifier** : `post.workingDays` vs `assignment.selectedDays`
- **Problème possible** : workingDays restrictif qui bloque les jours sélectionnés

### **Hypothèse 3 : Catégorie Week-end**
- **Vérifier** : `post.category` vs `day.isWeekend`
- **Problème possible** : Postes week-end mal catégorisés ou jours mal détectés

### **Hypothèse 4 : Dates de Début/Fin**
- **Vérifier** : `assignment.startDate` et `assignment.endDate`
- **Problème possible** : Dates qui excluent la semaine courante

### **Hypothèse 5 : Cache Invalide**
- **Vérifier** : `_appliedWeeksCache` et `_lastAppliedWeek`
- **Problème possible** : Cache qui empêche l'application

## 🧪 **Tests de Diagnostic**

### **Test 1 : Logs shouldApplyAssignmentToDay**
1. Lancer l'application
2. Naviguer entre semaines
3. Vérifier logs → Voir pourquoi `shouldApplyAssignmentToDay` retourne `false`

### **Test 2 : Logs Boucle des Jours**
1. Vérifier logs → Voir si la boucle `config.days.forEach` s'exécute
2. Compter les jours traités vs jours ignorés

### **Test 3 : Logs Compteur Final**
1. Vérifier logs → Voir `appliedCount` final
2. Si 0, identifier à quelle étape les shifts sont bloqués

### **Test 4 : Données Attribution**
1. Vérifier logs → Structure complète de l'attribution
2. Vérifier `selectedDays`, `postId`, `employeeId`

## 🔍 **Logs Attendus**

### **Si Tout Fonctionne :**
```
🔄 Traitement des jours pour attribution xxx: [{dateKey: "2025-06-16", isWeekend: false, dayOfWeek: 1}, ...]
📅 Vérification jour 0: 2025-06-16 (1)
🔍 [shouldApplyAssignmentToDay] Vérification pour: {assignmentId: "xxx", postId: "yyy", ...}
✅ Attribution autorisée pour 2025-06-16
📋 Jour 2025-06-16 validé, shifts existants: 0
✅ Shift créé: Poste WE2 → 2025-06-16 pour Sophie Leblanc
📊 1 attributions appliquées pour la semaine 2025-W25
🔄 Rendu demandé car 1 shifts créés
```

### **Si Problème selectedDays :**
```
🔍 [shouldApplyAssignmentToDay] Vérification pour: {selectedDays: [0, 6], dayOfWeek: 1, ...}
❌ Jour 1 non sélectionné dans: [0, 6]
⏭️ Jour 2025-06-16 ignoré pour attribution xxx
📊 0 attributions appliquées pour la semaine 2025-W25
⚠️ Aucun shift créé, pas de rendu
```

### **Si Problème workingDays :**
```
🔍 [shouldApplyAssignmentToDay] Vérification pour: {postWorkingDays: [1,2,3,4,5], dayOfWeek: 6, ...}
❌ Jour 6 non autorisé dans workingDays: [1,2,3,4,5]
⏭️ Jour 2025-06-21 ignoré pour attribution xxx
```

### **Si Problème Catégorie :**
```
🔍 [shouldApplyAssignmentToDay] Vérification pour: {postCategory: "weekend", dayIsWeekend: false, ...}
❌ Poste week-end mais jour non week-end
⏭️ Jour 2025-06-16 ignoré pour attribution xxx
```

## 🎯 **Actions Selon Diagnostic**

### **Si selectedDays Incorrect :**
- Corriger la logique de génération des `selectedDays`
- Vérifier la correspondance avec les jours de la semaine

### **Si workingDays Conflit :**
- Ajuster la logique de `workingDays` vs `selectedDays`
- Permettre override par `selectedDays`

### **Si Catégorie Week-end :**
- Corriger la détection `day.isWeekend`
- Vérifier la catégorisation des postes

### **Si Dates Début/Fin :**
- Corriger les dates d'attribution
- Vérifier le format des dates

### **Si Cache Invalide :**
- Nettoyer le cache au démarrage
- Revoir la logique de cache

**Les logs de débogage vont révéler exactement pourquoi aucun shift n'est créé !** 🔍

## 📋 **Checklist de Diagnostic**

- [ ] Lancer application avec logs de débogage
- [ ] Naviguer entre semaines → Voir logs shouldApplyAssignmentToDay
- [ ] Identifier la cause du rejet (selectedDays, workingDays, catégorie, dates)
- [ ] Vérifier structure attribution dans logs
- [ ] Vérifier structure jours dans logs
- [ ] Compter appliedCount final
- [ ] Corriger la cause identifiée
- [ ] Retester sans logs de débogage
