// SCRIPT DE CORRECTION SIMPLE - CONFLITS DRAG & DROP EMPLOYÉS

import { readFileSync, writeFileSync } from 'fs';

console.log('🔧 [SIMPLE-FIX] Début correction conflits drag & drop...');

try {
    let content = readFileSync('src/teamCalendarApp.ts', 'utf8');
    
    // 1. Supprimer l'appel à reorderEmployeeRowsOnlyOptimized dans reorderEmployees
    console.log('🧹 [FIX-1] Suppression appel reorderEmployeeRowsOnlyOptimized...');
    content = content.replace(/this\.reorderEmployeeRowsOnlyOptimized\(\);/g, '// Supprimé pour éviter les conflits');
    
    // 2. Supprimer l'appel à addSimplifiedEmployeeDragHandles
    console.log('🧹 [FIX-2] Suppression appel addSimplifiedEmployeeDragHandles...');
    content = content.replace(/this\.addSimplifiedEmployeeDragHandles\(\);/g, '// Supprimé pour éviter les conflits');
    
    // 3. Supprimer la protection anti-écrasement dans loadEmployeeOrder
    console.log('🧹 [FIX-3] Suppression protection anti-écrasement...');
    content = content.replace(/if \(this\._lastDragDropTime && \(now - this\._lastDragDropTime\) < gracePeriod\) \{[\s\S]*?\}/g, '// Protection supprimée');
    content = content.replace(/this\._lastDragDropTime && \(now - this\._lastDragDropTime\) < gracePeriod/g, 'false');
    
    // 4. Simplifier reorderEmployees - supprimer les timeouts et re-renders
    console.log('🧹 [FIX-4] Simplification reorderEmployees...');
    const simpleReorderEmployees = `    reorderEmployees: function(oldIndex, newIndex) {
        if (oldIndex === newIndex) return;
        
        console.log(\`🔄 [reorderEmployees] Déplacement \${oldIndex} → \${newIndex}\`);
        
        // Simple réorganisation des données
        const employees = [...this.data.employees];
        const movedEmployee = employees.splice(oldIndex, 1)[0];
        employees.splice(newIndex, 0, movedEmployee);
        this.data.employees = employees;
        
        console.log('✅ [reorderEmployees] Ordre mis à jour:', employees.map(e => e.name));
        
        // Sauvegarder immédiatement
        this.saveEmployeeOrder();
    },`;
    
    // Remplacer la fonction reorderEmployees complexe par la version simple
    content = content.replace(/reorderEmployees:\s*function\([^)]*\)\s*\{[\s\S]*?(?=\n\s{4}[a-zA-Z_])/g, simpleReorderEmployees + '\n\n    ');
    
    // 5. Supprimer les flags de protection qui causent des problèmes
    console.log('🧹 [FIX-5] Suppression flags de protection...');
    content = content.replace(/this\._reorderInProgress = true;/g, '// Protection supprimée');
    content = content.replace(/this\._lastDragDropTime = Date\.now\(\);/g, '// Timestamp supprimé');
    
    writeFileSync('src/teamCalendarApp.ts', content, 'utf8');
    
    console.log('✅ [SIMPLE-FIX] Corrections appliquées avec succès !');
    console.log('');
    console.log('📋 [RÉSUMÉ] Modifications:');
    console.log('   1. ✅ Appels conflictuels supprimés');
    console.log('   2. ✅ Protection anti-écrasement désactivée');  
    console.log('   3. ✅ reorderEmployees simplifié');
    console.log('   4. ✅ Flags de protection supprimés');
    console.log('');
    console.log('🚀 [NEXT] Redémarrez l\'application pour tester');
    
} catch (error) {
    console.error('❌ [SIMPLE-FIX] Erreur:', error);
} 