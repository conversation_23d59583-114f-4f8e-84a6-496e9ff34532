import { query } from './config/database.js';

async function quickFixShifts() {
    console.log('🔧 [QuickFix] Création manuelle des shifts pour la semaine W24...');
    
    try {
        // Créer les shifts manuellement pour <PERSON>
        const shifts = [
            { date: '2025-06-10', day: 'Lund<PERSON>' },    // Jour 1
            { date: '2025-06-11', day: 'Mar<PERSON>' },    // Jour 2  
            { date: '2025-06-12', day: 'Mercredi' }  // Jour 3
        ];
        
        const employeeId = '64ed8433-4931-454a-84ae-e7e9af5217ba'; // <PERSON>
        const postId = '9199ff1c-7014-4f62-a8b3-19adeb010e34';     // Poste Matin
        const assignmentId = '8ee05320-7a37-46fe-8d01-1d54e086ff34'; // Attribution Lundi
        
        for (const shift of shifts) {
            // Vérifier si le shift existe déjà
            const existing = await query(`
                SELECT id FROM shifts 
                WHERE employee_id = $1 AND date_key = $2
            `, [employeeId, shift.date]);
            
            if (existing.rows.length === 0) {
                // C<PERSON>er le shift
                const { v4: uuidv4 } = await import('uuid');
                const shiftId = uuidv4();
                
                await query(`
                    INSERT INTO shifts (
                        id, employee_id, post_id, date_key, shift_data, is_regular, is_punctual, assignment_id, created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, true, false, $6, NOW(), NOW())
                `, [
                    shiftId, 
                    employeeId, 
                    postId, 
                    shift.date, 
                    JSON.stringify({
                        id: shiftId,
                        postId: postId,
                        text: '08:00-16:00',
                        type: 'emerald',
                        dateKey: shift.date,
                        isRegular: true,
                        isPunctual: false,
                        assignmentId: assignmentId
                    }),
                    assignmentId
                ]);
                
                console.log(`✅ [QuickFix] Shift créé: Jean Dupont → ${shift.day} ${shift.date} → 08:00-16:00`);
            } else {
                console.log(`⏭️ [QuickFix] Shift existant: ${shift.day} ${shift.date}`);
            }
        }
        
        // Vérifier le résultat
        console.log('\n📊 [QuickFix] Vérification des shifts créés:');
        const result = await query(`
            SELECT s.*, e.name as employee_name, sp.label as post_label,
                   COALESCE((s.shift_data->>'text')::text, sp.hours) as text,
                   COALESCE((s.shift_data->>'type')::text, 'standard') as type
            FROM shifts s
            LEFT JOIN employees e ON s.employee_id = e.id
            LEFT JOIN standard_posts sp ON s.post_id = sp.id
            WHERE s.date_key >= '2025-06-09' AND s.date_key <= '2025-06-15'
            ORDER BY s.date_key
        `);
        
        console.table(result.rows);
        console.log(`📋 Total: ${result.rows.length} shifts pour la semaine W24`);
        
    } catch (error) {
        console.error('❌ [QuickFix] Erreur:', error);
        throw error;
    }
}

// Exécuter le fix
quickFixShifts()
    .then(() => {
        console.log('\n🎉 [QuickFix] Shifts créés avec succès !');
        console.log('🔄 Rechargez la page http://localhost:3001/simple pour voir les shifts');
        process.exit(0);
    })
    .catch((error) => {
        console.error('💥 [QuickFix] Échec:', error);
        process.exit(1);
    });
