/**
 * @fileoverview Interfaces et types TypeScript
 * @description Définitions des types pour l'application
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

/**
 * @description Interface pour les données d'un créneau horaire
 */
export interface ShiftData {
  /** ID unique du shift */
  id: string;
  /** Texte affiché */
  text: string;
  /** Type/couleur du shift */
  type: string;
  /** ID du poste standard associé */
  postId?: string;
  /** Heures formatées (ex: '08:00-16:00') */
  hours?: string;
  /** Durée en heures */
  duration?: number;
  /** Forme visuelle */
  shape?: string;
  /** Catégorie spécifique */
  category?: string;
  /** Si le poste continue de la veille */
  isContinuation?: boolean;
  /** Si le poste continue le lendemain */
  isContinuationFromNext?: boolean;
  /** ID original pour les postes splittés */
  originalId?: string;
  /** ID du poste qu'il remplace temporairement */
  replacementFor?: string;
  /** Si c'est un congé */
  leave?: boolean;
  /** Si c'est une attribution régulière */
  isRegular?: boolean;
  /** Si c'est ponctuel */
  isPunctual?: boolean;
  /** ID de l'attribution régulière */
  assignmentId?: string;
  /** Décalage de semaine */
  weekOffset?: number;
  /** Clé de date YYYY-MM-DD */
  dateKey?: string;
  /** Propriétés dynamiques */
  [key: string]: any;
}

/**
 * @description Interface pour les données d'un jour dans la grille
 */
export interface DayData {
  /** Objet Date complet */
  date: Date;
  /** Nom court du jour (ex: LUN) */
  short: string;
  /** Nom long du jour (ex: lundi) */
  long: string;
  /** Numéro du jour dans le mois */
  number: number;
  /** Si c'est aujourd'hui */
  isToday: boolean;
  /** Si c'est un week-end */
  isWeekend: boolean;
  /** Clé unique YYYY-MM-DD */
  dateKey: string;
}

/**
 * @description Interface pour les périodes de congés
 */
export interface VacationPeriod {
  /** ID unique pour la période de congé */
  id: string;
  /** ID de l'employé */
  employeeId: string;
  /** Date de début (YYYY-MM-DD) */
  startDate: string;
  /** Date de fin (YYYY-MM-DD) */
  endDate: string;
  /** Type de congé */
  type: string;
  /** Description optionnelle */
  description?: string;
}

/**
 * @description Interface pour les attributions régulières
 */
export interface RegularAssignment {
  /** ID unique */
  id: string;
  /** ID de l'employé */
  employeeId: string;
  /** ID du poste */
  postId: string;
  /** Si l'attribution est limitée dans le temps */
  isLimited: boolean;
  /** Date de début (YYYY-MM-DD) */
  startDate: string;
  /** Date de fin (YYYY-MM-DD) ou null */
  endDate: string | null;
  /** Jours de la semaine (0=dimanche, 1=lundi, etc.) */
  selectedDays: number[];
  /** Alias pour compatibilité backend */
  daysOfWeek?: number[];
}

/**
 * @description Interface pour les postes standards
 */
export interface StandardPost {
  /** ID unique */
  id: string;
  /** Nom du poste */
  label: string;
  /** Heures formatées */
  hours: string;
  /** Durée en heures */
  duration: number;
  /** Type/couleur */
  type: string;
  /** Catégorie optionnelle */
  category?: string;
  /** Jours de travail autorisés (0-6) */
  workingDays?: number[];
  /** Si le poste est uniquement pour le week-end */
  isWeekendOnly?: boolean;
}

/**
 * @description Interface pour les champs du modèle d'employé
 */
export interface EmployeeFieldTemplate {
  /** ID unique */
  id: string;
  /** Label du champ */
  label: string;
  /** Type de champ */
  type: 'text' | 'select' | 'email' | 'tel' | 'textarea' | 'date' | 'number';
  /** Si le champ est requis */
  required: boolean;
  /** Options pour les champs select */
  options?: string[];
  /** Placeholder */
  placeholder?: string;
  /** Valeur par défaut */
  defaultValue?: string;
  /** Ordre d'affichage */
  order: number;
}

/**
 * @description Interface pour le modèle de fiche employé
 */
export interface EmployeeTemplate {
  /** ID unique */
  id: string;
  /** Nom du modèle */
  name: string;
  /** Description optionnelle */
  description?: string;
  /** Champs du modèle */
  fields: EmployeeFieldTemplate[];
  /** Si c'est le modèle par défaut */
  isDefault: boolean;
  /** Date de création */
  createdAt: string;
  /** Date de mise à jour */
  updatedAt: string;
}

/**
 * @description Interface pour les employés
 */
export interface Employee {
  /** ID unique */
  id: string;
  /** Nom complet */
  name: string;
  /** Statut */
  status: string;
  /** Avatar (base64 ou URL) */
  avatar: string;
  /** ID du modèle utilisé */
  templateId?: string;
  /** Champs supplémentaires */
  extraFields?: Record<string, string>;
}

/**
 * @description Interface pour les paramètres de l'application
 */
export interface AppSettings {
  /** Fuseau horaire IANA */
  timezone?: string;
  /** Jour de début de semaine */
  weekStartsOn: 'monday' | 'sunday';
  /** Jour de début de semaine (format numérique) */
  weekStartDay?: number;
}

/**
 * @description Interface pour les périodes de congés globaux
 */
export interface GlobalVacation {
  /** Nom du congé */
  name: string;
  /** Date de début */
  startDate: string;
  /** Date de fin */
  endDate: string;
}

/**
 * @description Interface pour les données de TeamCalendarApp
 */
export interface TeamCalendarData {
  /** Liste des employés */
  employees: Employee[];
  /** Planning (clé: employeeId_dateKey) */
  schedule: Record<string, ShiftData[]>;
  /** Périodes de congés */
  vacations: VacationPeriod[];
  /** Congés globaux */
  globalVacations: GlobalVacation[];
  /** Attributions régulières */
  regularAssignments: RegularAssignment[];
  /** Postes personnalisés */
  customPosts: StandardPost[];
  /** Modèles d'employés */
  employeeTemplates: EmployeeTemplate[];
  /** Suivi des fragmentations */
  fragmentationTracking: Record<string, any>;
  /** Historique des maintenances */
  maintenanceRecords: any[];
}

/**
 * @description Interface pour la configuration de TeamCalendarApp
 */
export interface TeamCalendarConfig {
  /** Postes standards */
  standardPosts: StandardPost[];
  /** Jours de la semaine */
  days: DayData[];
  /** Format d'affichage de la semaine */
  currentWeekDisplayFormat: string;
  /** Clé de stockage */
  storageKey: string;
  /** Paramètres de l'application */
  appSettings: AppSettings;
  /** Si les listeners de navigation sont attachés */
  _navigationListenersAttached: boolean;
  /** Décalage de semaine actuel */
  _currentWeekOffset: number;
  /** Clé de la semaine actuelle */
  _currentWeekKey?: string;
  /** Si l'application est initialisée */
  _isInitialized?: boolean;
  /** Mode d'affichage */
  viewMode: 'day' | 'week' | 'month';
  /** Date actuelle pour navigation */
  currentDate: Date;
}

/**
 * @description Interface pour les éléments DOM
 */
export interface TeamCalendarElements {
  employeeListContainer: HTMLElement | null;
  scheduleGridContent: HTMLElement | null;
  currentWeekDisplay: HTMLElement | null;
  scheduleContainer: HTMLElement | null;
  scheduleGridScrollableContent: HTMLElement | null;
  addShiftButton: HTMLElement | null;
  statsOutput: HTMLElement | null;
  settingsButton: HTMLElement | null;
  settingsModal: HTMLElement | null;
  settingsModalClose: HTMLElement | null;
  postsConfigContainer: HTMLElement | null;
  addPostButton: HTMLElement | null;
  availablePostsContainer: HTMLElement | null;
}

/**
 * @description Interface pour l'objet draggedItem
 */
export interface DraggedItemData {
  type: string | null;
  data: any | null;
  originalEmployeeId: string | null;
  originalDayIndex: number | null;
  originalShiftIndex: number | null;
  originalEmployeeIndex: number | null;
}

/**
 * @description Interface pour les résultats de validation
 */
export interface ValidationResult {
  /** Si la validation est réussie */
  isValid: boolean;
  /** Liste des erreurs */
  errors: string[];
  /** Avertissements optionnels */
  warnings?: string[];
}

/**
 * @description Interface pour les options de modal
 */
export interface ModalOptions {
  /** Titre de la modal */
  title: string;
  /** Message de la modal */
  message: string;
  /** Type de modal */
  type: 'success' | 'error' | 'warning' | 'info' | 'confirm';
  /** Boutons de la modal */
  buttons?: Array<{
    text: string;
    action: () => void;
    primary: boolean;
  }>;
  /** Si la modal peut être fermée en cliquant à l'extérieur */
  closable?: boolean;
  /** Durée d'affichage automatique (ms) */
  duration?: number;
}
