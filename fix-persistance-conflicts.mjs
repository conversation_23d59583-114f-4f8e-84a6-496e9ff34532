#!/usr/bin/env node

/**
 * 🚨 CORRECTION CRITIQUE : Conflits de Persistance Désastreux
 * 
 * Problèmes identifiés :
 * 1. Modifications directes de this.data.schedule partout
 * 2. Appels render() en boucle
 * 3. Conflits localStorage vs API
 * 4. Données corrompues en mémoire
 * 5. Pas de verrous de transaction
 */

import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

const TEAM_CALENDAR_FILE = 'src/teamCalendarApp.ts';

console.log('🚨 [PERSISTANCE-FIX] Démarrage correction conflits de persistance...');

try {
    let content = readFileSync(TEAM_CALENDAR_FILE, 'utf8');
    let corrections = 0;
    
    // ✅ 1. AJOUTER UN SYSTÈME DE VERROUILLAGE TRANSACTIONNEL
    const transactionSystem = `
    // ✅ SYSTÈME DE VERROUILLAGE TRANSACTIONNEL
    _transactionLock: false,
    _pendingTransactions: [],
    _savingState: false,
    _loadingState: false,
    _renderingInProgress: false,
    
    // Démarrer une transaction
    startTransaction: function(operation: string) {
        if (this._transactionLock) {
            console.warn(\`⚠️ [TRANSACTION] Opération \${operation} en attente - verrou actif\`);
            return new Promise((resolve) => {
                this._pendingTransactions.push({ operation, resolve });
            });
        }
        
        this._transactionLock = true;
        console.log(\`🔒 [TRANSACTION] Début: \${operation}\`);
        return Promise.resolve();
    },
    
    // Terminer une transaction
    endTransaction: function(operation: string) {
        this._transactionLock = false;
        console.log(\`🔓 [TRANSACTION] Fin: \${operation}\`);
        
        // Traiter la prochaine transaction en attente
        if (this._pendingTransactions.length > 0) {
            const next = this._pendingTransactions.shift();
            setTimeout(() => {
                this.startTransaction(next.operation).then(next.resolve);
            }, 10);
        }
    },
    
    // Modifier le schedule de manière sécurisée
    safeScheduleUpdate: async function(employeeId: string, dateKey: string, updateFn: Function, operation: string = 'schedule-update') {
        await this.startTransaction(operation);
        
        try {
            // Initialiser si nécessaire
            if (!this.data.schedule[employeeId]) {
                this.data.schedule[employeeId] = {};
            }
            if (!this.data.schedule[employeeId][dateKey]) {
                this.data.schedule[employeeId][dateKey] = [];
            }
            
            // Appliquer la modification
            const result = updateFn(this.data.schedule[employeeId][dateKey]);
            
            // Sauvegarder de manière asynchrone
            setTimeout(() => this.saveState(), 100);
            
            console.log(\`✅ [SAFE-UPDATE] \${operation} réussie pour \${employeeId} le \${dateKey}\`);
            return result;
            
        } catch (error) {
            console.error(\`❌ [SAFE-UPDATE] Erreur \${operation}:\`, error);
            throw error;
        } finally {
            this.endTransaction(operation);
        }
    },`;
    
    // Insérer le système de transaction après la déclaration de l'objet
    const insertPoint = content.indexOf('const TeamCalendarApp: TeamCalendarAppType = {');
    if (insertPoint !== -1) {
        const nextBrace = content.indexOf('\n', insertPoint);
        content = content.slice(0, nextBrace) + transactionSystem + content.slice(nextBrace);
        corrections++;
        console.log('✅ [PERSISTANCE-FIX] Système de verrouillage transactionnel ajouté');
    }
    
    // ✅ 2. CORRIGER LA FONCTION SAVESTATE POUR ÉVITER LES CONFLITS
    const saveStateRegex = /saveState:\s*async?\s*function\s*\([^)]*\)\s*{[^{}]*(?:{[^{}]*}[^{}]*)*}/gs;
    
    const newSaveState = `saveState: async function() {
        // Éviter les sauvegardes en cascade
        if (this._savingState) {
            console.log('⚠️ [saveState] Sauvegarde déjà en cours, ignorée');
            return;
        }
        
        this._savingState = true;
        
        try {
            console.log('💾 [saveState] Début sauvegarde sécurisée...');
            
            // Nettoyer les données avant sauvegarde
            this.cleanDataBeforeSave();
            
            // Sauvegarder via API si disponible
            if (window.apiService && window.apiService.isAvailable()) {
                await this.saveToAPI();
            }
            
            // Toujours sauvegarder en localStorage comme fallback
            this.saveToLocalStorage();
            
            console.log('✅ [saveState] Sauvegarde sécurisée terminée');
            
        } catch (error) {
            console.error('❌ [saveState] Erreur sauvegarde:', error);
        } finally {
            this._savingState = false;
        }
    }`;
    
    // Remplacer l'ancienne fonction saveState
    if (saveStateRegex.test(content)) {
        content = content.replace(saveStateRegex, newSaveState);
        corrections++;
        console.log('✅ [PERSISTANCE-FIX] Fonction saveState sécurisée');
    }
    
    // ✅ 3. AJOUTER LES FONCTIONS DE NETTOYAGE ET SAUVEGARDE
    const cleaningFunctions = `
    
    // Nettoyer les données avant sauvegarde
    cleanDataBeforeSave: function() {
        let cleaned = 0;
        
        // Nettoyer les shifts corrompus
        Object.keys(this.data.schedule).forEach(employeeId => {
            Object.keys(this.data.schedule[employeeId]).forEach(dateKey => {
                const originalLength = this.data.schedule[employeeId][dateKey].length;
                
                // Supprimer les shifts invalides
                this.data.schedule[employeeId][dateKey] = this.data.schedule[employeeId][dateKey].filter(shift => {
                    return shift && 
                           typeof shift === 'object' && 
                           (shift.id || shift.postId) &&
                           shift.text !== undefined;
                });
                
                // Supprimer les doublons
                const uniqueShifts = [];
                const seenIds = new Set();
                
                this.data.schedule[employeeId][dateKey].forEach(shift => {
                    const shiftId = shift.id || \`\${shift.postId}-\${shift.text}\`;
                    if (!seenIds.has(shiftId)) {
                        seenIds.add(shiftId);
                        uniqueShifts.push(shift);
                    }
                });
                
                this.data.schedule[employeeId][dateKey] = uniqueShifts;
                
                const newLength = this.data.schedule[employeeId][dateKey].length;
                if (originalLength !== newLength) {
                    cleaned += (originalLength - newLength);
                }
                
                // Supprimer les jours vides
                if (this.data.schedule[employeeId][dateKey].length === 0) {
                    delete this.data.schedule[employeeId][dateKey];
                }
            });
            
            // Supprimer les employés sans shifts
            if (Object.keys(this.data.schedule[employeeId]).length === 0) {
                delete this.data.schedule[employeeId];
            }
        });
        
        if (cleaned > 0) {
            console.log(\`🧹 [cleanDataBeforeSave] \${cleaned} éléments corrompus nettoyés\`);
        }
    },
    
    // Sauvegarder vers API
    saveToAPI: async function() {
        try {
            // Sauvegarder les shifts
            const allShifts = [];
            Object.keys(this.data.schedule).forEach(employeeId => {
                Object.keys(this.data.schedule[employeeId]).forEach(dateKey => {
                    this.data.schedule[employeeId][dateKey].forEach(shift => {
                        allShifts.push({
                            ...shift,
                            employee_id: employeeId,
                            date: dateKey,
                            shift_data: shift
                        });
                    });
                });
            });
            
            if (allShifts.length > 0) {
                const result = await window.apiService.saveShifts(allShifts);
                if (!result.success) {
                    throw new Error('Échec sauvegarde API shifts');
                }
            }
            
            console.log('✅ [saveToAPI] Sauvegarde API réussie');
        } catch (error) {
            console.warn('⚠️ [saveToAPI] Erreur API, fallback localStorage:', error);
        }
    },
    
    // Sauvegarder vers localStorage
    saveToLocalStorage: function() {
        try {
            const stateToSave = {
                employees: this.data.employees,
                schedule: this.data.schedule,
                vacations: this.data.vacations,
                globalVacations: this.data.globalVacations,
                regularAssignments: this.data.regularAssignments,
                customPosts: this.data.customPosts,
                timestamp: Date.now()
            };
            
            localStorage.setItem(this.config.storageKey, JSON.stringify(stateToSave));
            localStorage.setItem('teamCalendarAppSettings', JSON.stringify(this.config.appSettings));
            
            console.log('✅ [saveToLocalStorage] Sauvegarde localStorage réussie');
        } catch (error) {
            console.error('❌ [saveToLocalStorage] Erreur:', error);
        }
    },`;
    
    // Insérer les fonctions de nettoyage
    const insertCleaningPoint = content.lastIndexOf('};');
    if (insertCleaningPoint !== -1) {
        content = content.slice(0, insertCleaningPoint) + cleaningFunctions + '\n' + content.slice(insertCleaningPoint);
        corrections++;
        console.log('✅ [PERSISTANCE-FIX] Fonctions de nettoyage ajoutées');
    }
    
    // ✅ 4. LIMITER LES APPELS RENDER() EN BOUCLE
    const renderThrottling = `
    _lastRender: 0,
    _renderThrottle: 100, // 100ms minimum entre renders
    
    renderSafe: function() {
        const now = Date.now();
        if (now - this._lastRender < this._renderThrottle) {
            console.log('⚠️ [renderSafe] Throttled - trop fréquent');
            return;
        }
        
        if (this._renderingInProgress) {
            console.log('⚠️ [renderSafe] Rendu déjà en cours');
            return;
        }
        
        this._renderingInProgress = true;
        this._lastRender = now;
        
        try {
            this.actualRender();
        } catch (error) {
            console.error('❌ [renderSafe] Erreur rendu:', error);
        } finally {
            this._renderingInProgress = false;
        }
    },`;
    
    // Insérer le throttling avant la fonction render existante
    const renderFunction = content.indexOf('render: function()');
    if (renderFunction !== -1) {
        content = content.slice(0, renderFunction) + renderThrottling + '\n    ' + content.slice(renderFunction);
        corrections++;
        console.log('✅ [PERSISTANCE-FIX] Throttling de render ajouté');
    }
    
    // ✅ 5. CORRIGER LES MODIFICATIONS DIRECTES DE SCHEDULE
    const directModifications = [
        // Remplacer les modifications directes les plus critiques
        {
            pattern: /this\.data\.schedule\[([^\]]+)\]\[([^\]]+)\]\s*=\s*\[\];?/g,
            replacement: 'this.safeScheduleUpdate($1, $2, (shifts) => { shifts.length = 0; return shifts; }, "clear-day");'
        },
        {
            pattern: /this\.data\.schedule\[([^\]]+)\]\[([^\]]+)\]\.push\(([^)]+)\);?/g,
            replacement: 'this.safeScheduleUpdate($1, $2, (shifts) => { shifts.push($3); return shifts; }, "add-shift");'
        }
    ];
    
    directModifications.forEach(({ pattern, replacement }) => {
        const matches = content.match(pattern);
        if (matches) {
            content = content.replace(pattern, replacement);
            corrections += matches.length;
            console.log(\`✅ [PERSISTANCE-FIX] \${matches.length} modifications directes corrigées\`);
        }
    });
    
    // ✅ 6. AJOUTER UN SYSTÈME DE RÉCUPÉRATION D'URGENCE
    const emergencyRecovery = `
    
    // Récupération d'urgence en cas de corruption
    emergencyRecovery: function() {
        console.log('🚨 [EMERGENCY] Début récupération d\\'urgence...');
        
        try {
            // 1. Sauvegarder l'état actuel
            const corruptedState = JSON.stringify(this.data);
            localStorage.setItem('teamCalendar_corrupted_backup', corruptedState);
            
            // 2. Nettoyer les données corrompues
            this.data.schedule = {};
            
            // 3. Recharger depuis l'API
            this.loadState().then(() => {
                this.renderSafe();
                console.log('✅ [EMERGENCY] Récupération réussie');
                if (window.toastSystem) {
                    window.toastSystem.success('🚨 Récupération d\\'urgence terminée');
                }
            });
            
        } catch (error) {
            console.error('❌ [EMERGENCY] Échec récupération:', error);
            // Dernier recours : rechargement complet
            localStorage.clear();
            location.reload();
        }
    },
    
    // Vérifier l'intégrité des données
    checkDataIntegrity: function() {
        let issues = 0;
        
        // Vérifier la structure de base
        if (!this.data || typeof this.data !== 'object') {
            issues++;
        }
        
        if (!this.data.schedule || typeof this.data.schedule !== 'object') {
            issues++;
        }
        
        // Vérifier les employés
        if (!Array.isArray(this.data.employees)) {
            issues++;
        }
        
        // Vérifier les shifts
        try {
            Object.keys(this.data.schedule).forEach(employeeId => {
                if (typeof this.data.schedule[employeeId] !== 'object') {
                    issues++;
                    return;
                }
                
                Object.keys(this.data.schedule[employeeId]).forEach(dateKey => {
                    if (!Array.isArray(this.data.schedule[employeeId][dateKey])) {
                        issues++;
                    }
                });
            });
        } catch (error) {
            issues += 10; // Erreur critique
        }
        
        if (issues > 0) {
            console.warn(\`⚠️ [INTEGRITY] \${issues} problèmes détectés\`);
            if (issues >= 5) {
                console.error('🚨 [INTEGRITY] Corruption critique détectée');
                this.emergencyRecovery();
            }
        }
        
        return issues;
    },
    
    // Fonction de réparation rapide
    quickRepair: function() {
        console.log('🔧 [QUICK-REPAIR] Démarrage réparation rapide...');
        
        let repaired = 0;
        
        // Réparer les structures manquantes
        if (!this.data.schedule) {
            this.data.schedule = {};
            repaired++;
        }
        
        if (!Array.isArray(this.data.employees)) {
            this.data.employees = [];
            repaired++;
        }
        
        // Réparer les employés sans schedule
        this.data.employees.forEach(employee => {
            if (!this.data.schedule[employee.id]) {
                this.data.schedule[employee.id] = {};
                repaired++;
            }
        });
        
        // Nettoyer les données corrompues
        this.cleanDataBeforeSave();
        
        console.log(\`✅ [QUICK-REPAIR] \${repaired} éléments réparés\`);
        
        // Sauvegarder les réparations
        this.saveState();
        
        return repaired;
    }`;
    
    // Ajouter le système de récupération
    const insertRecovery = content.lastIndexOf('};');
    if (insertRecovery !== -1) {
        content = content.slice(0, insertRecovery) + emergencyRecovery + '\n' + content.slice(insertRecovery);
        corrections++;
        console.log('✅ [PERSISTANCE-FIX] Système de récupération d\\'urgence ajouté');
    }
    
    // Sauvegarder le fichier corrigé
    writeFileSync(TEAM_CALENDAR_FILE, content);
    
    console.log(\`✅ [PERSISTANCE-FIX] \${corrections} corrections appliquées avec succès\`);
    console.log('🎯 [PERSISTANCE-FIX] Corrections appliquées :');
    console.log('   - Système de verrouillage transactionnel');
    console.log('   - Fonction saveState sécurisée');
    console.log('   - Fonctions de nettoyage et sauvegarde');
    console.log('   - Throttling des renders');
    console.log('   - Correction des modifications directes');
    console.log('   - Système de récupération d\\'urgence');
    
    // Créer un script de test
    const testScript = `
// Test du système de persistance corrigé
console.log('🧪 [TEST] Démarrage test persistance...');

// Vérifier l'intégrité
const integrity = window.TeamCalendarApp.checkDataIntegrity();
console.log(\`🔍 [TEST] Intégrité: \${integrity} problèmes\`);

// Réparation rapide si nécessaire
if (integrity > 0) {
    const repaired = window.TeamCalendarApp.quickRepair();
    console.log(\`🔧 [TEST] Réparation: \${repaired} éléments corrigés\`);
}

// Tester une transaction
window.TeamCalendarApp.safeScheduleUpdate('test-emp', '2025-01-20', (shifts) => {
    shifts.push({ id: 'test-shift', text: 'Test', postId: 'test' });
    return shifts;
}, 'test-transaction').then(() => {
    console.log('✅ [TEST] Transaction réussie');
}).catch(error => {
    console.error('❌ [TEST] Transaction échouée:', error);
});

console.log('✅ [TEST] Test persistance terminé');
`;
    
    writeFileSync('test-persistance-fix.js', testScript);
    console.log('📝 [PERSISTANCE-FIX] Script de test créé : test-persistance-fix.js');
    
    // Créer un script de nettoyage d'urgence
    const emergencyScript = `
// Script de nettoyage d'urgence
console.log('🚨 [EMERGENCY] Nettoyage d\\'urgence...');

// Vider le localStorage corrompu
localStorage.removeItem('teamCalendarState_v1');
localStorage.removeItem('teamCalendarAppSettings');

// Nettoyer les données de session
sessionStorage.clear();

// Forcer le rechargement
window.TeamCalendarApp.emergencyRecovery();

console.log('✅ [EMERGENCY] Nettoyage terminé');
`;
    
    writeFileSync('emergency-cleanup.js', emergencyScript);
    console.log('🚨 [PERSISTANCE-FIX] Script d\\'urgence créé : emergency-cleanup.js');
    
} catch (error) {
    console.error('❌ [PERSISTANCE-FIX] Erreur critique:', error);
    process.exit(1);
}

console.log('🎉 [PERSISTANCE-FIX] Correction complète terminée !');
console.log('📋 [PERSISTANCE-FIX] Prochaines étapes :');
console.log('   1. Tester avec: node test-persistance-fix.js');
console.log('   2. En cas de problème: node emergency-cleanup.js');
console.log('   3. Recharger l\\'application'); 