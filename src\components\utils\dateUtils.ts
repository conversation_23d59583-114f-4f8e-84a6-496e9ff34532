/**
 * @fileoverview Utilitaires de gestion des dates
 * @description Fonctions utilitaires pour la manipulation des dates dans l'application
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

/**
 * @description Interface pour les données d'un jour
 */
export interface DayData {
  date: Date;
  short: string;
  long: string;
  number: number;
  isToday: boolean;
  isWeekend: boolean;
  dateKey: string;
}

/**
 * @description Génère une clé de date au format YYYY-MM-DD
 * @param date - Date à convertir
 * @returns Clé de date formatée
 * @example
 * ```typescript
 * const key = formatDateToKey(new Date()); // "2025-07-18"
 * ```
 */
export function formatDateToKey(date: Date | string): string {
  if (typeof date === 'string') {
    date = new Date(date);
  }
  
  if (isNaN(date.getTime())) {
    console.error('❌ [formatDateToKey] Date invalide:', date);
    return new Date().toISOString().split('T')[0];
  }
  
  return date.toISOString().split('T')[0];
}

/**
 * @description Vérifie si deux dates sont le même jour
 * @param date1 - Première date
 * @param date2 - Deuxième date
 * @returns true si les dates sont le même jour
 */
export function isSameDay(date1: Date, date2: Date): boolean {
  return formatDateToKey(date1) === formatDateToKey(date2);
}

/**
 * @description Obtient la clé de date d'aujourd'hui
 * @returns Clé de date d'aujourd'hui
 */
export function getTodayKey(): string {
  return formatDateToKey(new Date());
}

/**
 * @description Génère les dates d'une semaine
 * @param weekOffset - Décalage de semaine (0 = semaine actuelle)
 * @param weekStartsOn - Jour de début de semaine ('monday' | 'sunday' | number)
 * @returns Tableau des données des jours de la semaine
 */
export function generateWeekDates(
  weekOffset: number = 0, 
  weekStartsOn: 'monday' | 'sunday' | number = 'monday'
): DayData[] {
  const today = new Date();
  const currentDay = today.getDay(); // 0 = dimanche, 1 = lundi, etc.
  
  // Convertir weekStartsOn en nombre
  let startDayNumber: number;
  if (typeof weekStartsOn === 'number') {
    startDayNumber = weekStartsOn;
  } else {
    startDayNumber = weekStartsOn === 'monday' ? 1 : 0;
  }
  
  // Calculer le début de la semaine
  const daysFromStart = (currentDay - startDayNumber + 7) % 7;
  const weekStart = new Date(today);
  weekStart.setDate(today.getDate() - daysFromStart + (weekOffset * 7));
  
  const days: DayData[] = [];
  const dayNames = ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'];
  const shortDayNames = ['DIM', 'LUN', 'MAR', 'MER', 'JEU', 'VEN', 'SAM'];
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(weekStart);
    date.setDate(weekStart.getDate() + i);
    
    const dayOfWeek = date.getDay();
    const isToday = isSameDay(date, today);
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    
    days.push({
      date: new Date(date),
      short: shortDayNames[dayOfWeek],
      long: dayNames[dayOfWeek],
      number: date.getDate(),
      isToday,
      isWeekend,
      dateKey: formatDateToKey(date)
    });
  }
  
  return days;
}

/**
 * @description Obtient le numéro de semaine ISO
 * @param date - Date pour laquelle obtenir le numéro de semaine
 * @returns Numéro de semaine (1-53)
 */
export function getWeekNumber(date: Date): number {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  const dayNum = d.getUTCDay() || 7;
  d.setUTCDate(d.getUTCDate() + 4 - dayNum);
  const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
  return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
}

/**
 * @description Génère une clé de semaine au format YYYY-W##
 * @param date - Date pour laquelle générer la clé (par défaut: aujourd'hui)
 * @returns Clé de semaine
 * @example
 * ```typescript
 * const weekKey = generateWeekKey(); // "2025-W03"
 * ```
 */
export function generateWeekKey(date: Date = new Date()): string {
  const year = date.getFullYear();
  const week = getWeekNumber(date);
  return `${year}-W${week.toString().padStart(2, '0')}`;
}

/**
 * @description Parse une clé de semaine
 * @param weekKey - Clé de semaine au format YYYY-W##
 * @returns Informations sur la semaine ou null si invalide
 */
export function parseWeekKey(weekKey: string): {
  year: number;
  week: number;
  startDate: Date;
  endDate: Date;
  weekKey: string;
} | null {
  const match = weekKey.match(/^(\d{4})-W(\d{2})$/);
  if (!match) {
    return null;
  }
  
  const year = parseInt(match[1]);
  const week = parseInt(match[2]);
  
  // Calculer la date de début de semaine
  const jan4 = new Date(year, 0, 4);
  const startDate = new Date(jan4);
  startDate.setDate(jan4.getDate() - jan4.getDay() + 1 + (week - 1) * 7);
  
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 6);
  
  return {
    year,
    week,
    startDate,
    endDate,
    weekKey
  };
}

/**
 * @description Trie les dates par ordre de semaine
 * @param dates - Tableau de dates à trier
 * @returns Dates triées
 */
export function sortDatesByWeekOrder(dates: any[]): any[] {
  return dates.sort((a, b) => {
    const dateA = new Date(a.date || a.dateKey || a);
    const dateB = new Date(b.date || b.dateKey || b);
    return dateA.getTime() - dateB.getTime();
  });
}

/**
 * @description Obtient une date relative (il y a X temps)
 * @param timestamp - Timestamp ou date
 * @returns Texte relatif
 */
export function getTimeAgo(timestamp: Date | string | number): string {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);
  
  if (diffMins < 1) return 'À l\'instant';
  if (diffMins < 60) return `Il y a ${diffMins} min`;
  if (diffHours < 24) return `Il y a ${diffHours}h`;
  if (diffDays < 7) return `Il y a ${diffDays} jour${diffDays > 1 ? 's' : ''}`;
  if (diffDays < 30) return `Il y a ${Math.floor(diffDays / 7)} semaine${Math.floor(diffDays / 7) > 1 ? 's' : ''}`;
  if (diffDays < 365) return `Il y a ${Math.floor(diffDays / 30)} mois`;
  return `Il y a ${Math.floor(diffDays / 365)} an${Math.floor(diffDays / 365) > 1 ? 's' : ''}`;
}
