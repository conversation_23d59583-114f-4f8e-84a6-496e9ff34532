import { query } from './database.js';
import { v4 as uuid } from 'uuid';
import { EventEmitter } from 'events';

// Session unique générée au démarrage du serveur
export const currentSession = uuid();

// Configuration du logger avec capture PostgreSQL complète
export class Logger {
  constructor() {
    this.sessionId = currentSession;
    this.dbAvailable = null; // Cache du statut de la DB
    this.lastDbCheck = 0;
    this.dbCheckInterval = 30000; // Vérifier la DB toutes les 30s
    this.isConsolePatched = false;
    
    console.log(`📋 [Logger] Session démarrée: ${this.sessionId}`);
    
    // Test initial de la connexion DB (async)
    this.testDbConnection();
    
    // ✅ NOUVEAU : Patch automatique de la console
    this.patchConsole();
  }

  async testDbConnection() {
    try {
      // Test simple pour voir si la table logs existe
      await query('SELECT 1 FROM logs LIMIT 1');
      this.dbAvailable = true;
      console.log('✅ [Logger] Connexion PostgreSQL (distant) validée');
    } catch (error) {
      this.dbAvailable = false;
      if (error.code === '42P01') {
        console.log('⚠️  [Logger] Table logs non trouvée - Migration requise');
        console.log('💡 [Logger] Exécutez: node migrate-logs-remote.mjs');
      } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
        console.log('⚠️  [Logger] PostgreSQL distant non accessible - Mode dégradé');
      } else {
        console.log('⚠️  [Logger] Erreur DB:', error.message);
      }
    }
  }

  // ✅ DÉSACTIVÉ : Patch console (causait des fuites mémoire)
  patchConsole() {
    // ✅ ACTIVÉ : Patch automatique de la console pour intégration unifiée
    if (this.isConsolePatched) {
      console.log('⚠️ [Logger] Console déjà patchée');
      return;
    }
    
    const originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info,
      debug: console.debug
    };
    
    const shouldSkip = (args) => {
      const txt = args.filter(a => typeof a === 'string').join(' ');
      return txt.includes('[LOGS]') || 
             txt.includes('INSERT INTO logs') || 
             txt.includes('/api/logs') ||
             txt.includes('[CAPTURE]');
    };
    
    const sendToUnified = async (level, args) => {
      if (shouldSkip(args)) return;
      
      const message = args.map(arg => {
        if (typeof arg === 'object') {
          try { return JSON.stringify(arg); } catch { return '[Objet non sérialisable]'; }
        }
        return String(arg);
      }).join(' ');
      
      // Envoyer vers le système unifié
      await this.captureToDb(level, `[LOGGER] ${message}`, {
        originalArgs: args,
        source: 'logger-backend',
        timestamp: new Date().toISOString()
      }, 'backend');
    };
    
    // Patch des méthodes console
    console.log = (...args) => { 
      originalConsole.log(...args); 
      sendToUnified('info', args); 
    };
    console.error = (...args) => { 
      originalConsole.error(...args); 
      sendToUnified('error', args); 
    };
    console.warn = (...args) => { 
      originalConsole.warn(...args); 
      sendToUnified('warn', args); 
    };
    console.info = (...args) => { 
      originalConsole.info(...args); 
      sendToUnified('info', args); 
    };
    console.debug = (...args) => { 
      originalConsole.debug(...args); 
      sendToUnified('debug', args); 
    };
    
    this.isConsolePatched = true;
    console.log('✅ [Logger] Console patchée pour intégration unifiée');
  }

  // ✅ NOUVEAU : Méthode de capture séparée pour éviter les boucles
  async captureToDb(level, message, data = {}, source = 'backend') {
    try {
      // Vérifier périodiquement la disponibilité de la DB
      const now = Date.now();
      if (this.dbAvailable === false && (now - this.lastDbCheck) > this.dbCheckInterval) {
        this.lastDbCheck = now;
        await this.testDbConnection();
      }
      
      // Si la DB n'est pas disponible, ignorer silencieusement
      if (this.dbAvailable === false) {
        return;
      }
      
      await query(`
        INSERT INTO logs(session_id, source, level, message, data, capture_mode)
        VALUES($1, $2, $3, $4, $5, $6)
      `, [
        this.sessionId,
        source,
        level,
        message,
        JSON.stringify(data),
        'full'
      ]);
      
      // ⚡ Diffusion immédiate (SSE)
      logEmitter.emit('log', {
        id: uuid(),
        session_id: this.sessionId,
        ts: new Date().toISOString(),
        source,
        level,
        message,
        data
      });
      
      // Marquer comme disponible si le log réussit
      if (this.dbAvailable === null) {
        this.dbAvailable = true;
      }
      
    } catch (dbError) {
      // Marquer comme non disponible et programmer un nouveau test
      if (this.dbAvailable !== false) {
        this.dbAvailable = false;
        this.lastDbCheck = Date.now();
      }
    }
  }

  async log(level, message, data = {}, source = 'backend') {
    try {
      // Log vers la console (comportement normal)
      const timestamp = new Date().toISOString();
      const logMessage = `[${timestamp}] [${level.toUpperCase()}] [${source}] ${message}`;
      
      switch (level) {
        case 'error':
        case 'fatal':
          console.error(logMessage, data);
          break;
        case 'warn':
          console.warn(logMessage, data);
          break;
        case 'debug':
          console.debug(logMessage, data);
          break;
        default:
          console.log(logMessage, data);
      }

      // Capture vers PostgreSQL (async, non-bloquant)
      setImmediate(() => {
        this.captureToDb(level, message, data, source);
      });

    } catch (error) {
      console.error('[Logger] Erreur critique:', error);
    }
  }

  // ✅ NOUVEAU : Méthode spéciale pour les logs de déplacements
  logDragDrop(level, message, data = {}) {
    return this.log(level, `[DRAG-DROP] ${message}`, data, 'drag-system');
  }

  debug(message, data) { return this.log('debug', message, data); }
  info(message, data) { return this.log('info', message, data); }
  warn(message, data) { return this.log('warn', message, data); }
  error(message, data) { return this.log('error', message, data); }
  fatal(message, data) { return this.log('fatal', message, data); }
  
  // Méthode pour forcer un test de reconnexion
  async reconnectDb() {
    console.log('🔄 [Logger] Test de reconnexion PostgreSQL...');
    await this.testDbConnection();
    return this.dbAvailable;
  }
}

// Instance globale
export const logger = new Logger();

// Fonction utilitaire pour les logs système
export function logSystem(message, data = {}) {
  logger.info(`[SYSTEM] ${message}`, data);
}

export function logError(message, error, data = {}) {
  logger.error(`[ERROR] ${message}`, { 
    error: error.message, 
    stack: error.stack, 
    ...data 
  });
}

// ✅ NOUVEAU : Fonction spéciale pour les logs de déplacements 
export function logDragDrop(level, message, data = {}) {
  logger.logDragDrop(level, message, data);
}

// Exposer la reconnexion pour usage externe
export function reconnectLogger() {
  return logger.reconnectDb();
}

// ✅ Émetteur global pour diffusion des logs en temps réel
export const logEmitter = new EventEmitter(); 