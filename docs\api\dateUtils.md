# Documentation - dateUtils.ts

> **Fichier**: `src\components\utils\dateUtils.ts`

## 📚 Fonctions Documentées

### Fonction 1

**Description**: Fonctions utilitaires pour la manipulation des dates dans l'application
 *

---

### Fonction 2

**Description**: Interface pour les données d'un jour

---

### Fonction 3

**Description**: Génère une clé de date au format YYYY-MM-DD
 *

**Exemple**:
```typescript
* ```typescript
 * const key = formatDateToKey(new Date()); // "2025-07-18"
 * ```
```

---

### Fonction 4

**Description**: Vérifie si deux dates sont le même jour
 *

---

### Fonction 5

**Description**: Obtient la clé de date d'aujourd'hui
 *

---

### Fonction 6

**Description**: Génère les dates d'une semaine
 *

---

### Fonction 7

**Description**: Obtient le numéro de semaine ISO
 *

---

### Fonction 8

**Description**: <PERSON><PERSON><PERSON> une clé de semaine au format YYYY-W##
 *

**Exemple**:
```typescript
* ```typescript
 * const weekKey = generateWeekKey(); // "2025-W03"
 * ```
```

---

### Fonction 9

**Description**: Parse une clé de semaine
 *

---

### Fonction 10

**Description**: Trie les dates par ordre de semaine
 *

---

### Fonction 11

**Description**: Obtient une date relative (il y a X temps)
 *

---

## 🔧 Toutes les Fonctions

| Fonction | Ligne |
|----------|-------|
| `formatDateToKey` | 30 |
| `isSameDay` | 49 |
| `getTodayKey` | 57 |
| `generateWeekDates` | 67 |
| `getWeekNumber` | 118 |
| `generateWeekKey` | 135 |
| `parseWeekKey` | 146 |
| `sortDatesByWeekOrder` | 183 |
| `getTimeAgo` | 196 |

