/**
 * 🚨 ACTIVATEUR MODE DEBUG ULTRA-INTENSIF
 * Script à exécuter dans la console du navigateur pour diagnostics poussés
 */

// ✅ ACTIVATION RAPIDE
console.log(`
🚨 ACTIVATEUR MODE DEBUG ULTRA-INTENSIF
=====================================

Commandes disponibles:
- activerDebugVerbose()     → Mode verbeux (20k logs avec détails)
- activerDebugInsane()      → Mode INSANE (100k logs ultra-détaillés)
- desactiverDebug()         → Désactiver le mode debug
- statusDebug()             → Afficher le statut actuel
- forceCaptureLogs()        → Forcer la capture immédiate des logs
- testPerformanceDebug()    → Tester les performances en mode debug

⚠️  ATTENTION: Le mode INSANE peut ralentir significativement l'interface !
`);

// ✅ FONCTIONS D'ACTIVATION
window.activerDebugVerbose = () => {
  localStorage.setItem('ULTRA_DEBUG_MODE', 'true');
  localStorage.setItem('DEBUG_LEVEL', 'verbose');
  localStorage.setItem('CAPTURE_ALL', 'true');
  
  console.log('🚨 MODE DEBUG VERBOSE ACTIVÉ');
  console.log('📊 Capacités: 20k logs, fetch tracking, métadonnées enrichies');
  console.log('🔧 Redémarrage recommandé pour activation complète');
  
  if (confirm('Redémarrer la page pour activer le mode debug verbose ?')) {
    location.reload();
  }
};

window.activerDebugInsane = () => {
  console.warn('⚠️  ATTENTION: Mode INSANE - Performances très réduites !');
  
  if (!confirm('CONFIRMER: Activer le mode DEBUG INSANE (100k logs ultra-détaillés) ?\n\n⚠️ Cela peut considérablement ralentir l\'interface !')) {
    console.log('❌ Activation annulée');
    return;
  }
  
  localStorage.setItem('ULTRA_DEBUG_MODE', 'true');
  localStorage.setItem('DEBUG_LEVEL', 'insane');
  localStorage.setItem('CAPTURE_ALL', 'true');
  
  console.log('🚨 MODE DEBUG INSANE ACTIVÉ');
  console.log('🔥 Capacités: 100k logs, DOM mutations, stack traces, performance tracking');
  console.log('⚡ Capture: Fetch, clics, focus, mutations DOM complètes');
  console.log('🔧 Redémarrage requis pour activation complète');
  
  if (confirm('Redémarrer la page pour activer le mode debug INSANE ?')) {
    location.reload();
  }
};

window.desactiverDebug = () => {
  localStorage.removeItem('ULTRA_DEBUG_MODE');
  localStorage.removeItem('DEBUG_LEVEL');
  localStorage.removeItem('CAPTURE_ALL');
  
  console.log('✅ Mode debug désactivé');
  console.log('🔧 Redémarrage recommandé pour désactivation complète');
  
  if (confirm('Redémarrer la page pour désactiver le mode debug ?')) {
    location.reload();
  }
};

window.statusDebug = () => {
  const debugMode = localStorage.getItem('ULTRA_DEBUG_MODE') === 'true';
  const debugLevel = localStorage.getItem('DEBUG_LEVEL') || 'normal';
  const captureAll = localStorage.getItem('CAPTURE_ALL') === 'true';
  
  const unifiedLogger = window.unifiedLogger;
  const currentDebugMode = unifiedLogger?.getDebugMode?.() || { enabled: false, level: 'normal' };
  
  console.log(`
🔍 STATUT MODE DEBUG
==================
LocalStorage:
  - Debug activé: ${debugMode ? '🚨 OUI' : '❌ NON'}
  - Niveau: ${debugLevel.toUpperCase()}
  - Capture all: ${captureAll ? '✅ OUI' : '❌ NON'}

Runtime:
  - Logger unifié: ${unifiedLogger ? '✅ Disponible' : '❌ Non disponible'}
  - Mode actuel: ${currentDebugMode.enabled ? `🚨 ${currentDebugMode.level.toUpperCase()}` : '❌ Désactivé'}
  - Capture niveau: ${currentDebugMode.captureAll ? '🔥 COMPLETE' : '📊 STANDARD'}
  `);
  
  if (debugMode && !currentDebugMode.enabled) {
    console.warn('⚠️  ATTENTION: Debug configuré mais non actif - redémarrage requis');
  }
};

window.forceCaptureLogs = () => {
  const unifiedLogger = window.unifiedLogger;
  if (!unifiedLogger) {
    console.error('❌ Logger unifié non disponible');
    return;
  }
  
  // Forcer le flush de la queue
  unifiedLogger.flushQueue();
  
  // Générer des logs de test
  console.log('🧪 [TEST DEBUG] Log de test niveau INFO');
  console.warn('🧪 [TEST DEBUG] Log de test niveau WARN');
  console.error('🧪 [TEST DEBUG] Log de test niveau ERROR');
  
  unifiedLogger.frontend.info('🧪 [TEST FRONTEND] Log frontend direct');
  unifiedLogger.browser.debug('🧪 [TEST BROWSER] Log browser debug direct');
  
  console.log('✅ Logs de test générés et queue forcée');
};

window.testPerformanceDebug = async () => {
  console.log('🚀 DÉBUT TEST PERFORMANCE DEBUG');
  
  const startTime = performance.now();
  const startMemory = performance.memory?.usedJSHeapSize || 0;
  
  // Générer beaucoup de logs rapidement
  for (let i = 0; i < 1000; i++) {
    console.log(`🧪 [PERF TEST ${i}] Test performance debug avec données`, {
      iteration: i,
      timestamp: Date.now(),
      randomData: Math.random(),
      testArray: new Array(100).fill(i)
    });
    
    // Pause toutes les 100 itérations
    if (i % 100 === 0) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }
  
  const endTime = performance.now();
  const endMemory = performance.memory?.usedJSHeapSize || 0;
  
  console.log(`
📊 RÉSULTATS TEST PERFORMANCE
=============================
Durée: ${Math.round(endTime - startTime)}ms
Mémoire utilisée: ${Math.round((endMemory - startMemory) / 1024 / 1024)}MB
Logs/seconde: ${Math.round(1000 / ((endTime - startTime) / 1000))}
  `);
};

// ✅ DÉTECTION AUTOMATIQUE ET SUGGESTIONS
window.detectProblems = () => {
  const problems = [];
  
  // Vérifier si on est sur la page de logs
  if (window.location.pathname.includes('/logs')) {
    problems.push('📋 Vous êtes sur la page de logs - parfait pour le debug');
  }
  
  // Vérifier la session actuelle
  const sessionId = localStorage.getItem('logs_session_id');
  if (sessionId) {
    problems.push(`📝 Session active: ${sessionId.substring(0, 8)}...`);
  }
  
  // Vérifier les erreurs dans la console
  const errorCount = window.console._errorCount || 0;
  if (errorCount > 0) {
    problems.push(`❌ ${errorCount} erreurs détectées dans la console`);
  }
  
  // Suggestions basées sur l'état
  console.log(`
🔍 DIAGNOSTIC AUTOMATIQUE
========================
${problems.join('\n')}

💡 SUGGESTIONS:
- Si vous avez des problèmes de logs manquants → activerDebugVerbose()
- Si vous diagnostiquez des bugs complexes → activerDebugInsane()
- Si l'interface est lente → desactiverDebug()
- Pour tester la capture → forceCaptureLogs()
  `);
};

// ✅ RACCOURCIS CLAVIER (si supportés)
if (typeof document !== 'undefined') {
  document.addEventListener('keydown', (e) => {
    // Ctrl+Shift+D → Status debug
    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
      e.preventDefault();
      window.statusDebug();
    }
    
    // Ctrl+Shift+V → Debug verbose
    if (e.ctrlKey && e.shiftKey && e.key === 'V') {
      e.preventDefault();
      window.activerDebugVerbose();
    }
    
    // Ctrl+Shift+I → Debug insane (attention !)
    if (e.ctrlKey && e.shiftKey && e.key === 'I') {
      e.preventDefault();
      if (confirm('Activer le mode DEBUG INSANE ?')) {
        window.activerDebugInsane();
      }
    }
  });
  
  console.log(`
⌨️  RACCOURCIS CLAVIER:
- Ctrl+Shift+D → Statut debug
- Ctrl+Shift+V → Activer verbose
- Ctrl+Shift+I → Activer insane (confirmation requise)
  `);
}

// ✅ AUTO-DÉTECTION DU CONTEXTE
setTimeout(() => {
  window.detectProblems();
  window.statusDebug();
}, 1000);

console.log('✅ Activateur mode debug ultra-intensif chargé');
console.log('💡 Tapez activerDebugInsane() pour commencer !'); 