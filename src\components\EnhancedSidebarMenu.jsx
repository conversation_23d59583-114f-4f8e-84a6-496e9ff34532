import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Menu, 
  Users, 
  Settings, 
  Calendar, 
  Activity, 
  Trash2, 
  ChevronRight,
  Filter,
  Download,
  RefreshCw,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react';
import { useLogs } from '../hooks/useLogs';
import { Dialog, ConfirmDialog } from './ui/Dialog';

/**
 * Composant EnhancedSidebarMenu avec fonctionnalités avancées
 * CORRECTION: Ouverture uniquement au survol d'icône spécifique
 */
const EnhancedSidebarMenu = ({ 
  employees = [], 
  posts = [], 
  onDeletePost, 
  onNavigateToEmployees,
  teamCalendarApp // Référence à l'app principal
}) => {
  // États principaux
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedEmployees, setSelectedEmployees] = useState([]);
  const [showSettings, setShowSettings] = useState(false);
  const [showLogs, setShowLogs] = useState(false);
  const [showEmployees, setShowEmployees] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState({ isOpen: false, postId: null });
  
  // Refs pour la zone de détection
  const sidebarRef = useRef(null);
  const triggerIconRef = useRef(null);

  // Hook logs personnalisé
  const { 
    logs, 
    logInfo, 
    logWarning, 
    logError, 
    logSuccess, 
    clearLogs, 
    getLogStats,
    exportLogs 
  } = useLogs();

  // ✅ CORRECTION: Zone de détection précise - uniquement sur l'icône
  useEffect(() => {
    const handleMouseEnter = () => {
      if (!isExpanded) {
        setIsExpanded(true);
        logInfo('Sidebar étendue via icône');
      }
    };

    const handleMouseLeave = () => {
      // Délai avant fermeture pour permettre la transition vers la sidebar
      setTimeout(() => {
        const isOverSidebar = sidebarRef.current?.matches(':hover');
        const isOverIcon = triggerIconRef.current?.matches(':hover');
        
        if (!isOverSidebar && !isOverIcon && isExpanded) {
          setIsExpanded(false);
          logInfo('Sidebar réduite');
        }
      }, 100);
    };

    // Attacher les événements uniquement à l'icône
    const iconElement = triggerIconRef.current;
    if (iconElement) {
      iconElement.addEventListener('mouseenter', handleMouseEnter);
      iconElement.addEventListener('mouseleave', handleMouseLeave);
    }
    
    return () => {
      if (iconElement) {
        iconElement.removeEventListener('mouseenter', handleMouseEnter);
        iconElement.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, [isExpanded, logInfo]);

  // Gestionnaire de suppression de poste
  const handleDeletePost = (postId) => {
    setDeleteDialog({ isOpen: true, postId });
  };

  const confirmDeletePost = () => {
    if (deleteDialog.postId) {
      onDeletePost?.(deleteDialog.postId);
      logWarning(`Poste supprimé: ${deleteDialog.postId}`);
      setDeleteDialog({ isOpen: false, postId: null });
    }
  };

  // Gestionnaire de filtrage par employés
  const handleEmployeeFilter = (employeeId) => {
    setSelectedEmployees(prev => {
      const isSelected = prev.includes(employeeId);
      const newSelection = isSelected 
        ? prev.filter(id => id !== employeeId)
        : [...prev, employeeId];
      
      logInfo(`Filtre employés modifié: ${newSelection.length} sélectionnés`);
      return newSelection;
    });
  };

  // Statistiques des logs
  const logStats = getLogStats();

  return (
    <>
      {/* ✅ ICÔNE DE DÉCLENCHEMENT FIXE - Zone de détection précise */}
      <div 
        ref={triggerIconRef}
        className="fixed left-2 top-4 w-10 h-10 bg-blue-600 hover:bg-blue-700 rounded-lg shadow-lg z-50 flex items-center justify-center cursor-pointer transition-all duration-200 hover:scale-110"
        title="Ouvrir le menu"
      >
        <Menu className="w-5 h-5 text-white" />
      </div>

      {/* Sidebar principale */}
      <motion.div
        ref={sidebarRef}
        initial={{ x: -240 }}
        animate={{ x: isExpanded ? 0 : -240 }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
        className="fixed left-0 top-0 h-full w-64 bg-white shadow-2xl z-40 border-r border-gray-200"
        onMouseLeave={() => {
          // Fermer après un délai si on quitte la sidebar
          setTimeout(() => {
            if (!triggerIconRef.current?.matches(':hover')) {
              setIsExpanded(false);
              logInfo('Sidebar fermée - délai de sortie');
            }
          }, 500);
        }}
      >
        {/* En-tête */}
        <div className="p-4 border-b border-gray-200 mt-16">
          <div className="flex items-center gap-3">
            <Menu className="w-6 h-6 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900">
              Menu Principal
            </h2>
          </div>
        </div>

        {/* Navigation principale */}
        <nav className="p-4 space-y-2">
          <button
            onClick={() => {
              setShowEmployees(!showEmployees);
              onNavigateToEmployees?.();
              logInfo('Navigation vers page Employés');
            }}
            className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-blue-50 transition-colors group"
          >
            <Users className="w-5 h-5 text-gray-600 group-hover:text-blue-600" />
            <span className="text-gray-700 group-hover:text-blue-700">
              Employés
            </span>
            <ChevronRight className="w-4 h-4 text-gray-400 ml-auto group-hover:text-blue-600" />
          </button>

          <button
            onClick={() => {
              setShowSettings(!showSettings);
              logInfo('Panneau paramètres basculé');
            }}
            className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-green-50 transition-colors group"
          >
            <Settings className="w-5 h-5 text-gray-600 group-hover:text-green-600" />
            <span className="text-gray-700 group-hover:text-green-700">
              Paramètres
            </span>
            <ChevronRight className="w-4 h-4 text-gray-400 ml-auto group-hover:text-green-600" />
          </button>

          <button
            onClick={() => {
              setShowLogs(!showLogs);
              logInfo('Visualisateur de logs ouvert');
            }}
            className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-purple-50 transition-colors group"
          >
            <Activity className="w-5 h-5 text-gray-600 group-hover:text-purple-600" />
            <span className="text-gray-700 group-hover:text-purple-700">
              Logs Système
            </span>
            <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full ml-auto">
              {logStats.total}
            </span>
          </button>

          {/* ✅ NOUVEAU: Bouton pour récupérer les logs IA */}
          <button
            onClick={() => {
              if (typeof window !== 'undefined' && window.exportLogsForAI) {
                const aiLogs = window.exportLogsForAI();
                navigator.clipboard.writeText(aiLogs).then(() => {
                  logInfo('Logs IA copiés dans le presse-papiers');
                }).catch(() => {
                  logError('Erreur lors de la copie des logs IA');
                });
              }
            }}
            className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-orange-50 transition-colors group"
          >
            <Download className="w-5 h-5 text-gray-600 group-hover:text-orange-600" />
            <span className="text-gray-700 group-hover:text-orange-700">
              Export IA
            </span>
            <span className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded-full ml-auto">
              Debug
            </span>
          </button>
        </nav>

        {/* Section Postes */}
        <div className="p-4 border-t border-gray-200">
          <h3 className="text-sm font-medium text-gray-500 mb-3">
            Gestion des Postes
          </h3>
          <div className="space-y-1 max-h-40 overflow-y-auto">
            {posts.map(post => (
              <div 
                key={post.id}
                className="flex items-center justify-between p-2 rounded-lg hover:bg-red-50 group"
              >
                <span className="text-sm text-gray-700">{post.label}</span>
                <button
                  onClick={() => handleDeletePost(post.id)}
                  className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-red-100 transition-all"
                >
                  <Trash2 className="w-4 h-4 text-red-500" />
                </button>
              </div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Panneau Paramètres ancré en bas à droite */}
      <AnimatePresence>
        {showSettings && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.95 }}
            className="fixed bottom-4 right-4 w-96 bg-white rounded-2xl shadow-2xl border border-gray-200 z-40"
          >
            {/* En-tête du panneau */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Paramètres Avancés
                </h3>
                <button
                  onClick={() => setShowSettings(false)}
                  className="p-2 rounded-lg hover:bg-gray-100"
                >
                  <XCircle className="w-5 h-5 text-gray-500" />
                </button>
              </div>
            </div>

            <div className="p-4 space-y-4 max-h-96 overflow-y-auto">
              {/* Mini-calendrier */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  Navigation Rapide
                </h4>
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="grid grid-cols-7 gap-1 text-xs">
                    {['D', 'L', 'M', 'M', 'J', 'V', 'S'].map(day => (
                      <div key={day} className="text-center text-gray-500 p-1">
                        {day}
                      </div>
                    ))}
                    {Array.from({ length: 30 }, (_, i) => (
                      <button
                        key={i}
                        className="text-center p-1 rounded hover:bg-blue-100 hover:text-blue-700 transition-colors"
                        onClick={() => {
                          logInfo(`Navigation vers le ${i + 1}`);
                        }}
                      >
                        {i + 1}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Filtrage multi-employés */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <Filter className="w-4 h-4" />
                  Filtrage Employés ({selectedEmployees.length})
                </h4>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {employees.map(employee => (
                    <label 
                      key={employee.id}
                      className="flex items-center gap-2 p-2 rounded-lg hover:bg-gray-50 cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={selectedEmployees.includes(employee.id)}
                        onChange={() => handleEmployeeFilter(employee.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{employee.name}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Statistiques rapides */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  Statistiques Système
                </h4>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="bg-blue-50 p-2 rounded">
                    <div className="text-blue-600 font-medium">Employés</div>
                    <div className="text-blue-800">{employees.length}</div>
                  </div>
                  <div className="bg-green-50 p-2 rounded">
                    <div className="text-green-600 font-medium">Postes</div>
                    <div className="text-green-800">{posts.length}</div>
                  </div>
                  <div className="bg-purple-50 p-2 rounded">
                    <div className="text-purple-600 font-medium">Logs</div>
                    <div className="text-purple-800">{logStats.total}</div>
                  </div>
                  <div className="bg-yellow-50 p-2 rounded">
                    <div className="text-yellow-600 font-medium">Filtrés</div>
                    <div className="text-yellow-800">{selectedEmployees.length}</div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Visualisateur de logs */}
      <Dialog 
        isOpen={showLogs} 
        onClose={() => setShowLogs(false)} 
        title="Visualisateur de Logs Système"
        className="max-w-4xl"
      >
        <div className="space-y-4">
          {/* Actions logs */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">
                Total: {logStats.total} logs
              </span>
              {logStats.ERROR && (
                <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">
                  {logStats.ERROR} erreurs
                </span>
              )}
            </div>
            <div className="flex gap-2">
              <button
                onClick={exportLogs}
                className="flex items-center gap-2 px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200"
              >
                <Download className="w-4 h-4" />
                Exporter
              </button>
              <button
                onClick={clearLogs}
                className="flex items-center gap-2 px-3 py-1 text-sm bg-red-100 text-red-700 rounded-lg hover:bg-red-200"
              >
                <RefreshCw className="w-4 h-4" />
                Purger
              </button>
            </div>
          </div>

          {/* Liste des logs */}
          <div className="max-h-96 overflow-y-auto space-y-2">
            {logs.slice(-50).reverse().map(log => {
              const icons = {
                INFO: <Info className="w-4 h-4 text-blue-500" />,
                SUCCESS: <CheckCircle className="w-4 h-4 text-green-500" />,
                WARNING: <AlertCircle className="w-4 h-4 text-yellow-500" />,
                ERROR: <XCircle className="w-4 h-4 text-red-500" />
              };

              return (
                <div key={log.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0 mt-0.5">
                    {icons[log.level]}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-900">
                        {log.message}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(log.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    {log.data && (
                      <pre className="text-xs text-gray-600 mt-1 bg-white p-2 rounded border overflow-x-auto">
                        {log.data}
                      </pre>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </Dialog>

      {/* Dialog de confirmation de suppression */}
      <ConfirmDialog
        isOpen={deleteDialog.isOpen}
        onClose={() => setDeleteDialog({ isOpen: false, postId: null })}
        onConfirm={confirmDeletePost}
        title="Supprimer le poste"
        message="Êtes-vous sûr de vouloir supprimer ce poste ? Cette action est irréversible."
        confirmText="Supprimer"
        cancelText="Annuler"
        variant="danger"
      />
    </>
  );
};

export default EnhancedSidebarMenu; 