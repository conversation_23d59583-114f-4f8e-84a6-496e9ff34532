import { query } from './config/database.js';

async function generateShiftsFromAssignments() {
    console.log('🔄 [GenerateShifts] Génération des shifts depuis les attributions régulières...');
    
    try {
        // 1. Charger toutes les attributions régulières actives
        const assignments = await query(`
            SELECT ra.*, e.name as employee_name, sp.label as post_label
            FROM regular_assignments ra
            LEFT JOIN employees e ON ra.employee_id = e.id
            LEFT JOIN standard_posts sp ON ra.post_id = sp.id
            WHERE ra.is_active = true
            ORDER BY ra.day_of_week, ra.start_time, e.name
        `);
        
        console.log(`📋 [GenerateShifts] ${assignments.rows.length} attributions régulières trouvées`);
        
        if (assignments.rows.length === 0) {
            console.log('⚠️ [GenerateShifts] Aucune attribution régulière active trouvée');
            return;
        }
        
        // 2. Définir la période de génération (semaine courante + quelques semaines)
        const startDate = new Date('2025-06-09'); // Début semaine courante
        const endDate = new Date('2025-06-29');   // 3 semaines
        
        console.log(`📅 [GenerateShifts] Génération pour la période: ${startDate.toISOString().split('T')[0]} → ${endDate.toISOString().split('T')[0]}`);
        
        let shiftsCreated = 0;
        let shiftsSkipped = 0;
        
        // 3. Générer les shifts pour chaque jour de la période
        const currentDate = new Date(startDate);
        while (currentDate <= endDate) {
            const dateKey = currentDate.toISOString().split('T')[0];
            const dayOfWeek = currentDate.getDay();
            
            console.log(`\n📅 [GenerateShifts] Traitement ${dateKey} (${getDayName(dayOfWeek)})`);
            
            // Trouver les attributions pour ce jour de la semaine
            const dayAssignments = assignments.rows.filter(a => a.day_of_week === dayOfWeek);
            
            if (dayAssignments.length > 0) {
                console.log(`  📌 ${dayAssignments.length} attribution(s) pour ce jour`);
                
                for (const assignment of dayAssignments) {
                    // Vérifier si un shift existe déjà
                    const existingShift = await query(`
                        SELECT id FROM shifts 
                        WHERE employee_id = $1 AND post_id = $2 AND date_key = $3
                    `, [assignment.employee_id, assignment.post_id, dateKey]);
                    
                    if (existingShift.rows.length === 0) {
                        // Créer le shift
                        const { v4: uuidv4 } = await import('uuid');
                        const shiftId = uuidv4();
                        
                        const shiftText = `${assignment.start_time.substring(0, 5)}-${assignment.end_time.substring(0, 5)}`;
                        
                        await query(`
                            INSERT INTO shifts (
                                id, employee_id, post_id, date_key, shift_data, is_regular, is_punctual, assignment_id, created_at, updated_at
                            )
                            VALUES ($1, $2, $3, $4, $5, true, false, $6, NOW(), NOW())
                        `, [
                            shiftId, 
                            assignment.employee_id, 
                            assignment.post_id, 
                            dateKey, 
                            JSON.stringify({
                                id: shiftId,
                                postId: assignment.post_id,
                                text: shiftText,
                                type: 'emerald',
                                dateKey: dateKey,
                                isRegular: true,
                                isPunctual: false,
                                assignmentId: assignment.id
                            }),
                            assignment.id
                        ]);
                        
                        console.log(`    ✅ Shift créé: ${assignment.employee_name} → ${assignment.post_label} → ${shiftText}`);
                        shiftsCreated++;
                    } else {
                        console.log(`    ⏭️ Shift existant: ${assignment.employee_name} → ${assignment.post_label}`);
                        shiftsSkipped++;
                    }
                }
            } else {
                console.log(`  ⚪ Aucune attribution pour ce jour`);
            }
            
            // Passer au jour suivant
            currentDate.setDate(currentDate.getDate() + 1);
        }
        
        console.log(`\n🎉 [GenerateShifts] Génération terminée !`);
        console.log(`✅ ${shiftsCreated} shifts créés`);
        console.log(`⏭️ ${shiftsSkipped} shifts existants ignorés`);
        
        // 4. Vérifier le résultat pour la semaine courante
        console.log(`\n📊 [GenerateShifts] Vérification semaine courante (2025-06-09 → 2025-06-15):`);
        const weekShifts = await query(`
            SELECT s.*, e.name as employee_name, sp.label as post_label
            FROM shifts s
            LEFT JOIN employees e ON s.employee_id = e.id
            LEFT JOIN standard_posts sp ON s.post_id = sp.id
            WHERE s.date_key >= '2025-06-09' AND s.date_key <= '2025-06-15'
            ORDER BY s.date_key, e.name
        `);
        
        console.table(weekShifts.rows);
        console.log(`📋 Total: ${weekShifts.rows.length} shifts pour la semaine courante`);
        
    } catch (error) {
        console.error('❌ [GenerateShifts] Erreur:', error);
        throw error;
    }
}

function getDayName(dayNumber) {
    const days = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
    return days[dayNumber] || 'Inconnu';
}

// Exécuter la génération
generateShiftsFromAssignments()
    .then(() => {
        console.log('\n🎉 [GenerateShifts] Génération complète !');
        process.exit(0);
    })
    .catch((error) => {
        console.error('💥 [GenerateShifts] Échec:', error);
        process.exit(1);
    });
