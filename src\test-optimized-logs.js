/**
 * Script de test pour le système de logs optimisé IA
 * À exécuter dans la console du navigateur pour tester les fonctionnalités
 */

console.log('🧪 [TEST-LOGS] Démarrage des tests du système de logs optimisé...');

// Test 1: Vérifier que le logger est disponible
if (typeof window !== 'undefined' && window.logger) {
  console.log('✅ [TEST-LOGS] Logger global disponible');
  
  // Test 2: Logs basiques avec différents niveaux
  console.log('\n📝 [TEST-LOGS] Test des niveaux de logs...');
  window.logger.debug('TestSystem', 'Message de debug');
  window.logger.info('TestSystem', 'Message d\'information');
  window.logger.warn('TestSystem', 'Message d\'avertissement');
  window.logger.error('TestSystem', 'Message d\'erreur');
  
  // Test 3: Compression des logs répétés
  console.log('\n🔄 [TEST-LOGS] Test de compression des logs répétés...');
  for (let i = 0; i < 10; i++) {
    window.logger.info('TestCompression', 'Message répété pour test de compression', { iteration: i });
  }
  
  // Test 4: Logs avec données complexes
  console.log('\n📊 [TEST-LOGS] Test avec données complexes...');
  window.logger.info('TestData', 'Log avec données', {
    user: 'TestUser',
    action: 'test_logs',
    timestamp: new Date().toISOString(),
    metadata: {
      browser: navigator.userAgent,
      url: window.location.href
    }
  });
  
  // Test 5: Package IA
  setTimeout(() => {
    console.log('\n🤖 [TEST-LOGS] Test du package IA...');
    if (window.getAIDebugPackage) {
      const aiPackage = window.getAIDebugPackage();
      console.log('📦 Package IA généré:', aiPackage);
      console.log('📊 Résumé:', aiPackage.summary);
      console.log('🔍 Analyse des patterns:', aiPackage.patternAnalysis);
      console.log('📈 Estimation tokens:', aiPackage.tokenEstimate);
      
      // Test 6: Export pour IA
      if (window.exportLogsForAI) {
        console.log('\n📤 [TEST-LOGS] Test d\'export pour IA...');
        const exportedLogs = window.exportLogsForAI();
        console.log('✅ Logs exportés (longueur):', exportedLogs.length, 'caractères');
        
        // Afficher un aperçu
        console.log('👁️ Aperçu des logs exportés:');
        console.log(exportedLogs.substring(0, 500) + '...');
      }
    } else {
      console.log('❌ [TEST-LOGS] getAIDebugPackage non disponible');
    }
    
    // Test 7: Historique
    console.log('\n📚 [TEST-LOGS] Test de l\'historique...');
    const history = window.logger.getHistory();
    console.log('📜 Historique total:', history.length, 'entrées');
    
    const errors = window.logger.getHistory(3); // LogLevel.ERROR
    console.log('❌ Erreurs dans l\'historique:', errors.length);
    
    // Test 8: Nettoyage
    console.log('\n🧹 [TEST-LOGS] Test de nettoyage...');
    const beforeCount = window.logger.getHistory().length;
    window.logger.clearHistory();
    const afterCount = window.logger.getHistory().length;
    console.log('📊 Avant nettoyage:', beforeCount, '| Après nettoyage:', afterCount);
    
    console.log('\n✅ [TEST-LOGS] Tous les tests terminés avec succès !');
    console.log('💡 [TEST-LOGS] Utilisez window.exportLogsForAI() pour récupérer les logs pour l\'IA');
    
  }, 2000);
  
} else {
  console.log('❌ [TEST-LOGS] Logger non disponible. Vérifiez que logger.ts est bien chargé.');
}

// Test des fonctions utilitaires
console.log('\n🔧 [TEST-LOGS] Test des fonctions utilitaires...');

// Test du timer de performance
if (window.logger) {
  const timer = window.logger.time('TestOperation');
  setTimeout(() => {
    const duration = timer.end();
    console.log('⏱️ Durée de l\'opération test:', duration, 'ms');
  }, 100);
}

// Test des loggers spécialisés
if (window.apiLogger) {
  console.log('✅ [TEST-LOGS] API Logger disponible');
  window.apiLogger.info('Test du logger API spécialisé');
}

if (window.uiLogger) {
  console.log('✅ [TEST-LOGS] UI Logger disponible');
  window.uiLogger.info('Test du logger UI spécialisé');
}

// Instructions pour l'utilisateur
console.log('\n📋 [TEST-LOGS] Instructions pour récupérer les logs pour l\'IA:');
console.log('1. Attendez que l\'application génère quelques logs');
console.log('2. Utilisez: window.exportLogsForAI()');
console.log('3. Copiez le résultat pour l\'analyse IA');
console.log('4. Ou utilisez le bouton "Export IA" dans la barre latérale');

export {}; 