// SCRIPT DE SAUVEGARDE DES DONNÉES
// Sauvegarde complète de l'état de l'application

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('📦 [BACKUP] Début de la sauvegarde des données...');

// Configuration
const backupDir = path.join(__dirname, '..', 'backups');
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
const backupName = `backup-${timestamp}`;
const backupPath = path.join(backupDir, backupName);

try {
    // Créer le répertoire de sauvegarde
    if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
        console.log('📁 [BACKUP] Répertoire de sauvegarde créé');
    }

    if (!fs.existsSync(backupPath)) {
        fs.mkdirSync(backupPath, { recursive: true });
        console.log(`📁 [BACKUP] Dossier de sauvegarde créé: ${backupName}`);
    }

    // 1. Sauvegarder les fichiers de configuration
    console.log('🔧 [BACKUP] Sauvegarde des fichiers de configuration...');
    const configFiles = [
        'package.json',
        'package-lock.json',
        'vite.config.ts',
        'tsconfig.json',
        'tailwind.config.cjs',
        'postcss.config.cjs'
    ];

    configFiles.forEach(file => {
        const sourcePath = path.join(__dirname, '..', file);
        if (fs.existsSync(sourcePath)) {
            const destPath = path.join(backupPath, file);
            fs.copyFileSync(sourcePath, destPath);
            console.log(`  ✅ ${file}`);
        }
    });

    // 2. Sauvegarder le code source principal
    console.log('💾 [BACKUP] Sauvegarde du code source...');
    const sourceFiles = [
        'src/teamCalendarApp.ts',
        'src/Agenda.tsx',
        'src/main.tsx',
        'src/api.ts',
        'index.html'
    ];

    sourceFiles.forEach(file => {
        const sourcePath = path.join(__dirname, '..', file);
        if (fs.existsSync(sourcePath)) {
            const destPath = path.join(backupPath, file);
            const destDir = path.dirname(destPath);
            if (!fs.existsSync(destDir)) {
                fs.mkdirSync(destDir, { recursive: true });
            }
            fs.copyFileSync(sourcePath, destPath);
            console.log(`  ✅ ${file}`);
        }
    });

    // 3. Sauvegarder les styles
    console.log('🎨 [BACKUP] Sauvegarde des styles...');
    const stylesDir = path.join(__dirname, '..', 'src', 'styles');
    if (fs.existsSync(stylesDir)) {
        const backupStylesDir = path.join(backupPath, 'src', 'styles');
        fs.mkdirSync(backupStylesDir, { recursive: true });
        
        const styleFiles = fs.readdirSync(stylesDir);
        styleFiles.forEach(file => {
            const sourcePath = path.join(stylesDir, file);
            const destPath = path.join(backupStylesDir, file);
            fs.copyFileSync(sourcePath, destPath);
            console.log(`  ✅ styles/${file}`);
        });
    }

    // 4. Sauvegarder les fichiers serveur
    console.log('🖥️ [BACKUP] Sauvegarde des fichiers serveur...');
    const serverFiles = [
        'server/app.js',
        'server/config/database.js',
        'server/config/logger.js'
    ];

    serverFiles.forEach(file => {
        const sourcePath = path.join(__dirname, '..', file);
        if (fs.existsSync(sourcePath)) {
            const destPath = path.join(backupPath, file);
            const destDir = path.dirname(destPath);
            if (!fs.existsSync(destDir)) {
                fs.mkdirSync(destDir, { recursive: true });
            }
            fs.copyFileSync(sourcePath, destPath);
            console.log(`  ✅ ${file}`);
        }
    });

    // 5. Créer un manifeste de sauvegarde
    console.log('📋 [BACKUP] Création du manifeste...');
    const manifest = {
        timestamp: new Date().toISOString(),
        backupName: backupName,
        description: 'Sauvegarde automatique avant corrections drag & drop',
        files: [],
        gitInfo: null
    };

    // Ajouter les informations Git si disponible
    try {
        const gitBranch = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
        const gitCommit = execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
        const gitStatus = execSync('git status --porcelain', { encoding: 'utf8' }).trim();
        
        manifest.gitInfo = {
            branch: gitBranch,
            commit: gitCommit,
            hasUncommittedChanges: gitStatus.length > 0,
            status: gitStatus
        };
        
        console.log(`  ✅ Git - Branche: ${gitBranch}`);
        console.log(`  ✅ Git - Commit: ${gitCommit.substring(0, 8)}`);
    } catch (error) {
        console.log('  ⚠️ Git non disponible');
    }

    // Lister tous les fichiers sauvegardés
    function listFiles(dir, relativePath = '') {
        const files = fs.readdirSync(dir);
        files.forEach(file => {
            const fullPath = path.join(dir, file);
            const relativeFile = path.join(relativePath, file);
            
            if (fs.statSync(fullPath).isDirectory()) {
                listFiles(fullPath, relativeFile);
            } else {
                manifest.files.push({
                    path: relativeFile,
                    size: fs.statSync(fullPath).size,
                    modified: fs.statSync(fullPath).mtime.toISOString()
                });
            }
        });
    }

    listFiles(backupPath);

    // Sauvegarder le manifeste
    const manifestPath = path.join(backupPath, 'manifest.json');
    fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));

    // 6. Créer un fichier de restauration
    console.log('🔄 [BACKUP] Création du script de restauration...');
    const restoreScript = `#!/bin/bash
# SCRIPT DE RESTAURATION AUTOMATIQUE
# Généré le ${new Date().toISOString()}

echo "🔄 Restauration de la sauvegarde ${backupName}..."

# Copier les fichiers de configuration
${configFiles.map(file => `cp "${file}" "../${file}"`).join('\n')}

# Copier le code source
${sourceFiles.map(file => `cp "${file}" "../${file}"`).join('\n')}

# Copier les styles
if [ -d "src/styles" ]; then
    cp -r "src/styles" "../src/"
fi

# Copier les fichiers serveur
${serverFiles.map(file => `cp "${file}" "../${file}"`).join('\n')}

echo "✅ Restauration terminée !"
echo "💡 N'oubliez pas de redémarrer l'application avec 'npm run dev'"
`;

    const restoreScriptPath = path.join(backupPath, 'restore.sh');
    fs.writeFileSync(restoreScriptPath, restoreScript);

    // Résumé final
    console.log('\n🎉 [BACKUP] Sauvegarde terminée avec succès !');
    console.log(`📁 Emplacement: ${backupPath}`);
    console.log(`📊 Fichiers sauvegardés: ${manifest.files.length}`);
    console.log(`💾 Taille totale: ${(manifest.files.reduce((sum, f) => sum + f.size, 0) / 1024).toFixed(2)} KB`);
    console.log('\n🔄 Pour restaurer cette sauvegarde:');
    console.log(`   cd "${backupPath}"`);
    console.log(`   bash restore.sh`);
    console.log('\n✅ Vous pouvez maintenant tester les corrections en toute sécurité !');

} catch (error) {
    console.error('❌ [BACKUP] Erreur lors de la sauvegarde:', error);
    process.exit(1);
} 