#!/usr/bin/env node

import { spawn, exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * 🚀 SCRIPT DE DÉMARRAGE SÉCURISÉ
 * 
 * Ferme automatiquement tous les anciens serveurs Node.js
 * et démarre le serveur stable avec protection UUID
 */

async function killOldServers() {
  console.log('🔄 Fermeture des anciens serveurs...');
  
  try {
    if (process.platform === 'win32') {
      // Windows - Fermer tous les processus Node.js
      await execAsync('taskkill /f /im node.exe 2>nul || echo "Aucun processus Node.js actif"');
      console.log('✅ Anciens serveurs Windows fermés');
    } else {
      // Unix/Linux/Mac - Fermer les serveurs spécifiquement
      try {
        await execAsync('pkill -f "node.*server" || echo "Aucun serveur Node.js actif"');
        console.log('✅ Anciens serveurs Unix fermés');
      } catch (error) {
        console.log('ℹ️  Aucun serveur à fermer');
      }
    }
    
    // Petit délai pour s'assurer que les ports sont libérés
    await new Promise(resolve => setTimeout(resolve, 1000));
    
  } catch (error) {
    console.warn('⚠️  Erreur lors de la fermeture:', error.message);
  }
}

async function startStableServer() {
  console.log('🚀 Démarrage du serveur stable...');
  
  const serverProcess = spawn('node', ['server/app-simple.js'], {
    stdio: 'inherit',
    cwd: process.cwd()
  });

  serverProcess.on('error', (error) => {
    console.error('❌ Erreur serveur:', error);
    process.exit(1);
  });

  serverProcess.on('close', (code) => {
    console.log(`🔴 Serveur fermé avec le code ${code}`);
    process.exit(code);
  });

  // Gestion des signaux pour arrêt propre
  process.on('SIGINT', () => {
    console.log('🛑 Arrêt du serveur...');
    serverProcess.kill('SIGTERM');
  });

  process.on('SIGTERM', () => {
    console.log('🛑 Arrêt du serveur...');
    serverProcess.kill('SIGTERM');
  });
}

async function main() {
  console.log(`
═══════════════════════════════════════════════════════════
🛡️  DÉMARRAGE SÉCURISÉ DU SERVEUR
═══════════════════════════════════════════════════════════
`);

  try {
    // 1. Fermer les anciens serveurs
    await killOldServers();
    
    // 2. Démarrer le serveur stable
    await startStableServer();
    
  } catch (error) {
    console.error('❌ Erreur fatale:', error);
    process.exit(1);
  }
}

// Lancer le script principal
main().catch(console.error); 