-- 🗃️ Optimisation des index pour améliorer les performances des requêtes
-- Version: 1.0.0
-- Date: 2025-06-16

-- ✅ ANALYSE DES REQUÊTES LENTES
-- Activer le logging des requêtes lentes (si pas déjà fait)
-- SET log_min_duration_statement = 1000; -- Log requêtes > 1s

-- ✅ INDEX POUR LA TABLE EMPLOYEES
-- Index sur les colonnes fréquemment utilisées
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_status 
ON employees(status) 
WHERE status IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_name_search 
ON employees USING gin(to_tsvector('french', name))
WHERE name IS NOT NULL;

-- Index composite pour les requêtes de filtrage
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_status_name 
ON employees(status, name) 
WHERE status IS NOT NULL AND name IS NOT NULL;

-- ✅ INDEX POUR LA TABLE SHIFTS
-- Index sur date_key (très fréquemment utilisé)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_shifts_date_key 
ON shifts(date_key);

-- Index sur employee_id (jointures fréquentes)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_shifts_employee_id 
ON shifts(employee_id);

-- Index sur post_id (jointures fréquentes)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_shifts_post_id 
ON shifts(post_id);

-- Index composite pour les requêtes de planning
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_shifts_employee_date 
ON shifts(employee_id, date_key);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_shifts_post_date 
ON shifts(post_id, date_key);

-- Index pour les requêtes de plage de dates
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_shifts_date_range 
ON shifts(date_key, employee_id, post_id);

-- Index partiel pour les shifts réguliers
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_shifts_regular 
ON shifts(employee_id, post_id, date_key) 
WHERE is_regular = true;

-- ✅ INDEX POUR LA TABLE REGULAR_ASSIGNMENTS
-- Index sur employee_id (très utilisé)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_regular_assignments_employee 
ON regular_assignments(employee_id);

-- Index sur post_id (très utilisé)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_regular_assignments_post 
ON regular_assignments(post_id);

-- Index composite pour éviter les doublons
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_regular_assignments_unique 
ON regular_assignments(employee_id, post_id, start_date, end_date)
WHERE is_active = true;

-- Index partiel pour les assignations actives
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_regular_assignments_active 
ON regular_assignments(employee_id, post_id) 
WHERE is_active = true;

-- Index pour les requêtes de période
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_regular_assignments_dates 
ON regular_assignments(start_date, end_date) 
WHERE start_date IS NOT NULL AND end_date IS NOT NULL;

-- ✅ INDEX POUR LA TABLE STANDARD_POSTS
-- Index sur le label pour les recherches
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_standard_posts_label 
ON standard_posts(label);

-- Index sur working_days (nouveau champ)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_standard_posts_working_days 
ON standard_posts USING gin(working_days)
WHERE working_days IS NOT NULL;

-- Index composite pour les requêtes de disponibilité
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_standard_posts_schedule 
ON standard_posts(label, start_time, end_time)
WHERE start_time IS NOT NULL AND end_time IS NOT NULL;

-- ✅ INDEX POUR OPTIMISER LES JOINTURES COMPLEXES
-- Index pour la requête "shifts avec employés et postes"
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_shifts_full_join 
ON shifts(date_key, employee_id, post_id, start_time, end_time);

-- ✅ INDEX POUR LES STATISTIQUES
-- Index pour compter les shifts par employé
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_shifts_stats_employee 
ON shifts(employee_id, date_key) 
WHERE date_key >= CURRENT_DATE - INTERVAL '30 days';

-- Index pour compter les shifts par poste
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_shifts_stats_post 
ON shifts(post_id, date_key) 
WHERE date_key >= CURRENT_DATE - INTERVAL '30 days';

-- ✅ VUES MATÉRIALISÉES POUR LES REQUÊTES COMPLEXES
-- Vue pour les statistiques d'employés (mise à jour quotidienne)
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_employee_stats AS
SELECT 
    e.id,
    e.name,
    e.status,
    COUNT(s.id) as total_shifts_30d,
    COUNT(DISTINCT s.date_key) as days_worked_30d,
    COUNT(DISTINCT s.post_id) as different_posts_30d,
    MAX(s.date_key) as last_shift_date
FROM employees e
LEFT JOIN shifts s ON e.id = s.employee_id 
    AND s.date_key >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY e.id, e.name, e.status;

-- Index sur la vue matérialisée
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_employee_stats_id 
ON mv_employee_stats(id);

CREATE INDEX IF NOT EXISTS idx_mv_employee_stats_performance 
ON mv_employee_stats(total_shifts_30d DESC, days_worked_30d DESC);

-- Vue pour les statistiques de postes
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_post_stats AS
SELECT 
    p.id,
    p.label,
    p.start_time,
    p.end_time,
    COUNT(s.id) as total_assignments_30d,
    COUNT(DISTINCT s.employee_id) as different_employees_30d,
    COUNT(DISTINCT s.date_key) as days_covered_30d
FROM standard_posts p
LEFT JOIN shifts s ON p.id = s.post_id 
    AND s.date_key >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY p.id, p.label, p.start_time, p.end_time;

-- Index sur la vue matérialisée
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_post_stats_id 
ON mv_post_stats(id);

-- ✅ PROCÉDURE DE MAINTENANCE DES INDEX
CREATE OR REPLACE FUNCTION maintain_indexes() 
RETURNS void AS $$
BEGIN
    -- Rafraîchir les vues matérialisées
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_employee_stats;
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_post_stats;
    
    -- Analyser les tables pour mettre à jour les statistiques
    ANALYZE employees;
    ANALYZE shifts;
    ANALYZE regular_assignments;
    ANALYZE standard_posts;
    
    -- Log de maintenance
    RAISE NOTICE 'Index maintenance completed at %', NOW();
END;
$$ LANGUAGE plpgsql;

-- ✅ TÂCHE CRON POUR MAINTENANCE AUTOMATIQUE
-- À ajouter dans crontab ou scheduler:
-- 0 2 * * * psql -d team_calendar -c "SELECT maintain_indexes();"

-- ✅ REQUÊTES D'ANALYSE DES PERFORMANCES
-- Vérifier l'utilisation des index
/*
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
ORDER BY idx_scan DESC;
*/

-- Identifier les requêtes lentes
/*
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE mean_time > 100 
ORDER BY mean_time DESC 
LIMIT 10;
*/

-- Vérifier la taille des index
/*
SELECT 
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) as size
FROM pg_stat_user_indexes 
ORDER BY pg_relation_size(indexrelid) DESC;
*/

-- ✅ COMMENTAIRES POUR DOCUMENTATION
COMMENT ON INDEX idx_shifts_date_key IS 'Index principal pour les requêtes par date';
COMMENT ON INDEX idx_shifts_employee_date IS 'Index composite pour planning employé';
COMMENT ON INDEX idx_regular_assignments_unique IS 'Évite les doublons d''assignations';
COMMENT ON MATERIALIZED VIEW mv_employee_stats IS 'Statistiques employés (refresh quotidien)';

-- ✅ PERMISSIONS
-- Accorder les permissions nécessaires
GRANT SELECT ON mv_employee_stats TO team_calendar_app;
GRANT SELECT ON mv_post_stats TO team_calendar_app;

-- ✅ MONITORING
-- Créer une table pour suivre les performances
CREATE TABLE IF NOT EXISTS performance_monitoring (
    id SERIAL PRIMARY KEY,
    query_type VARCHAR(100),
    execution_time_ms INTEGER,
    rows_affected INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_performance_monitoring_type_date 
ON performance_monitoring(query_type, created_at);

-- ✅ FINALISATION
-- Mettre à jour les statistiques après création des index
ANALYZE;

-- Log de fin
SELECT 'Index optimization completed successfully' as status;
