name: backup-on-checkpoint
on:
  workflow_dispatch:          # déclenché manuellement
  workflow_call:              # appelé par un autre workflow «curseur»
  push:
    branches: [ Nouvel-algorithm ]   # quand ta branche d’IA bouge

jobs:
  backup:
    runs-on: windows-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Exécuter le checkpoint IA (ou autre étape)
        run: npm run generate-ai-checkpoint      # ← ton étape « curseur »

      - name: Commit & push snapshot
        shell: pwsh
        run: |
          $repo   = "${{ github.workspace }}"
          $branch = "Nouvelle-version-après-perte"
          git -C $repo add -A
          if (-not (git -C $repo status --porcelain)) { exit 0 }   # rien à commiter ?
          git -C $repo commit -m "Checkpoint $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
          git -C $repo push origin $branch