<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Reset Direct</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #dc2626;
            transform: translateY(-2px);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 600;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .status.loading {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        .hidden {
            display: none;
        }
        pre {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid #e2e8f0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Test Reset Direct</h1>
        <p>Test de la fonctionnalité de réinitialisation avec timeout étendu.</p>
        
        <button class="btn" onclick="testReset()">
            🗑️ Tester la Réinitialisation (60s timeout)
        </button>
        
        <div id="status" class="status hidden"></div>
        <pre id="result" class="hidden"></pre>
    </div>

    <script>
        async function testReset() {
            const status = document.getElementById('status');
            const result = document.getElementById('result');
            
            status.className = 'status loading';
            status.textContent = '🧹 Réinitialisation en cours... (peut prendre jusqu\'à 60 secondes)';
            status.classList.remove('hidden');
            result.classList.add('hidden');

            try {
                console.log('🌐 Début de la requête de purge...');
                
                // Timeout étendu à 60 secondes
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 60000);
                
                const response = await fetch('http://localhost:3001/api/database/purge', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                
                console.log('📡 Réponse reçue:', response.status);
                
                const data = await response.json();
                
                console.log('📊 Données:', data);

                if (response.ok && data.success) {
                    status.className = 'status success';
                    status.textContent = '✅ Réinitialisation réussie !';
                    result.textContent = JSON.stringify(data, null, 2);
                    result.classList.remove('hidden');
                    
                    // Afficher un message de succès détaillé
                    setTimeout(() => {
                        alert('🎉 Réinitialisation terminée avec succès !\n\n' +
                              'La base de données a été purgée et réinitialisée.\n' +
                              'Vous pouvez maintenant retourner à l\'application principale.');
                    }, 1000);
                    
                } else {
                    status.className = 'status error';
                    status.textContent = `❌ Erreur: ${data.error || data.message}`;
                    result.textContent = JSON.stringify(data, null, 2);
                    result.classList.remove('hidden');
                }

            } catch (error) {
                console.error('❌ Erreur:', error);
                
                if (error.name === 'AbortError') {
                    status.className = 'status error';
                    status.textContent = '⏰ Timeout - La requête a pris plus de 60 secondes';
                } else {
                    status.className = 'status error';
                    status.textContent = `❌ Erreur de connexion: ${error.message}`;
                }
                
                result.textContent = `Erreur: ${error.message}\nStack: ${error.stack}`;
                result.classList.remove('hidden');
            }
        }
        
        // Test automatique au chargement (optionnel)
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Page de test chargée');
            console.log('💡 Cliquez sur le bouton pour tester la réinitialisation');
        });
    </script>
</body>
</html> 