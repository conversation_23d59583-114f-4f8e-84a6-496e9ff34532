<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fonctionnalité Reset Backend</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        .btn:hover {
            background: #dc2626;
        }
        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        .error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }
        .loading {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }
        .hidden {
            display: none;
        }
        pre {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid #e2e8f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Test Fonctionnalité Reset Backend</h1>
        <p>Cette page teste la nouvelle fonctionnalité de reset qui exécute la purge complète de la base de données via l'API backend.</p>
        
        <h2>🎯 Test Direct de l'API</h2>
        <button id="test-api-btn" class="btn">
            <span>🌐</span>
            Tester l'API de Purge
        </button>
        
        <div id="status" class="status hidden"></div>
        
        <h2>📊 Résultat du Test</h2>
        <pre id="result" class="hidden"></pre>
        
        <h2>ℹ️ Informations</h2>
        <ul>
            <li><strong>URL API :</strong> http://localhost:3001/api/database/purge</li>
            <li><strong>Méthode :</strong> POST</li>
            <li><strong>Configuration :</strong> weekStartsOn: "sunday" (Dimanche comme premier jour)</li>
            <li><strong>Données conservées :</strong> 5 employés de base + 5 postes standards</li>
        </ul>
    </div>

    <script>
        const testBtn = document.getElementById('test-api-btn');
        const status = document.getElementById('status');
        const result = document.getElementById('result');

        function showStatus(message, type) {
            status.className = `status ${type}`;
            status.textContent = message;
            status.classList.remove('hidden');
        }

        function showResult(data) {
            result.textContent = JSON.stringify(data, null, 2);
            result.classList.remove('hidden');
        }

        testBtn.addEventListener('click', async () => {
            testBtn.disabled = true;
            testBtn.innerHTML = '<span>⏳</span> Test en cours...';
            
            showStatus('🌐 Appel de l\'API de purge en cours...', 'loading');
            result.classList.add('hidden');

            try {
                const response = await fetch('http://localhost:3001/api/database/purge', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    showStatus('✅ Test réussi ! La purge backend fonctionne parfaitement.', 'success');
                    showResult(data);
                } else {
                    showStatus(`❌ Erreur API: ${data.error || data.message}`, 'error');
                    showResult(data);
                }

            } catch (error) {
                showStatus(`❌ Erreur de connexion: ${error.message}`, 'error');
                showResult({ error: error.message, stack: error.stack });
            } finally {
                testBtn.disabled = false;
                testBtn.innerHTML = '<span>🌐</span> Tester l\'API de Purge';
            }
        });

        // Test de connectivité au chargement
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('http://localhost:3001/health');
                if (response.ok) {
                    showStatus('🟢 Serveur backend accessible', 'info');
                } else {
                    showStatus('🟡 Serveur backend répond mais avec erreur', 'error');
                }
            } catch (error) {
                showStatus('🔴 Serveur backend non accessible - Vérifiez que npm run server est lancé', 'error');
            }
        });
    </script>
</body>
</html> 