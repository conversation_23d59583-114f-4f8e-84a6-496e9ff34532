import { readFile, writeFile } from 'fs/promises';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const TARGET_FILE = join(__dirname, 'src', 'teamCalendarApp.ts');
const LOG_PREFIX = '🔧 [fix-drop-handler]';

async function applyDropHandlerFix() {
    console.log(`${LOG_PREFIX} Démarrage du correctif du gestionnaire de drop...`);
    try {
        let content = await readFile(TARGET_FILE, 'utf-8');

        const simpleSearch = `this.data.schedule[targetEmployeeId][targetDateKey].push(movedShift);`;
        const simpleReplacement = `this.data.schedule[targetEmployeeId][targetDateKey].push(movedShift);

        // Mise à jour du shift via l'API
        const apiModule = await import('./api.js');
        const apiService = apiModule.default || apiModule.apiService;
        await apiService.updateShift(movedShift.id, {
            employee_id: targetEmployeeId,
            date_key: targetDateKey,
            is_regular: false,
            assignment_id: null
        });`;

        content = content.replace(simpleSearch, simpleReplacement);
        
        await writeFile(TARGET_FILE, content, 'utf-8');
        console.log(`${LOG_PREFIX} ✅ Correctif appliqué avec succès à handleDrop.`);

    } catch (error) {
        console.error(`${LOG_PREFIX} ❌ Erreur lors de la modification de teamCalendarApp.ts:`, error);
        process.exit(1);
    }
}

applyDropHandlerFix(); 