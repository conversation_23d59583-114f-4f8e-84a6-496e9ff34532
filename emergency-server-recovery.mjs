#!/usr/bin/env node

/**
 * SCRIPT D'URGENCE - RÉCUPÉRATION SERVEUR
 * 
 * Problème: Fuite mémoire massive causée par le système de logs
 * Solution: Redémarrer le serveur et désactiver les fonctionnalités problématiques
 * 
 * Date: 2025-07-02
 */

import { spawn } from 'child_process';
import { readFileSync, writeFileSync } from 'fs';

console.log('🚨 [URGENCE] Script de récupération serveur...');

// Étape 1: Tuer tous les processus Node.js
function killAllNodeProcesses() {
  console.log('🔪 [STEP-1] Arrêt de tous les processus Node.js...');
  
  try {
    if (process.platform === 'win32') {
      spawn('taskkill', ['/F', '/IM', 'node.exe'], { stdio: 'inherit' });
    } else {
      spawn('pkill', ['-f', 'node'], { stdio: 'inherit' });
    }
    console.log('✅ [STEP-1] Processus Node.js arrêtés');
  } catch (error) {
    console.log('⚠️ [STEP-1] Erreur arrêt processus:', error.message);
  }
}

// Étape 2: Nettoyer les logs localStorage
function cleanupClientLogs() {
  console.log('🧹 [STEP-2] Instructions pour nettoyer les logs client...');
  console.log('');
  console.log('📋 ACTIONS MANUELLES REQUISES:');
  console.log('1. Ouvrir la console navigateur (F12)');
  console.log('2. Exécuter: localStorage.clear()');
  console.log('3. Exécuter: sessionStorage.clear()');
  console.log('4. Recharger la page (Ctrl+F5)');
  console.log('');
}

// Étape 3: Afficher les instructions de redémarrage
function showRestartInstructions() {
  console.log('🚀 [STEP-3] Instructions de redémarrage sécurisé...');
  console.log('');
  console.log('📋 COMMANDES À EXÉCUTER:');
  console.log('');
  console.log('# Terminal 1 - Redémarrer le serveur backend:');
  console.log('node --max-old-space-size=8192 server/app.js');
  console.log('');
  console.log('# Terminal 2 - Redémarrer le frontend:');
  console.log('npm run dev');
  console.log('');
  console.log('⚠️ IMPORTANT: Le serveur redémarre avec plus de mémoire (8GB)');
  console.log('⚠️ Le système de logs backend est désactivé pour éviter les fuites');
  console.log('');
}

// Étape 4: Vérifier que les corrections sont appliquées
function verifyFixes() {
  console.log('✅ [STEP-4] Vérification des corrections...');
  
  try {
    const loggerContent = readFileSync('server/config/logger.js', 'utf8');
    if (loggerContent.includes('DÉSACTIVÉ pour éviter les boucles infinies')) {
      console.log('✅ Patch console backend: DÉSACTIVÉ ✓');
    } else {
      console.log('❌ Patch console backend: ENCORE ACTIF');
    }
    
    const captureContent = readFileSync('public/capture-logs-unified.js', 'utf8');
    if (captureContent.includes('MESSAGE_LIMIT_PER_SECOND = 2')) {
      console.log('✅ Throttling frontend: RENFORCÉ ✓');
    } else {
      console.log('❌ Throttling frontend: NON CORRIGÉ');
    }
    
  } catch (error) {
    console.log('⚠️ Erreur vérification:', error.message);
  }
}

// Fonction principale
async function emergencyRecovery() {
  console.log('\n=== RÉCUPÉRATION D\'URGENCE ===');
  console.log('Problème: Fuite mémoire serveur Node.js');
  console.log('Cause: Système de logs avec boucles infinies');
  console.log('Solution: Redémarrage avec corrections\n');
  
  // Exécuter les étapes
  killAllNodeProcesses();
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  cleanupClientLogs();
  showRestartInstructions();
  verifyFixes();
  
  console.log('\n=== RÉSULTAT ATTENDU ===');
  console.log('✅ Application fonctionnelle sans fuites mémoire');
  console.log('✅ Logs basiques uniquement (pas de spam)');
  console.log('✅ Interface /logs accessible');
  console.log('✅ Fonctionnalités principales restaurées');
  console.log('\n🎯 REDÉMARREZ LE SERVEUR MAINTENANT !');
}

// Exécuter le script
emergencyRecovery().catch(console.error); 