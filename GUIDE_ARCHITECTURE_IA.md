# 🤖 Guide pour IA - TeamCalendarApp

## 🎯 Mission

Tu vas prendre le relais sur un projet de gestion d'équipe complexe. Ce guide te permettra de comprendre rapidement l'architecture et d'éviter les pièges courants.

---

## 📋 Checklist de démarrage

### **1. Comprendre le contexte**
- ✅ Lire `DOCUMENTATION_COMPLETE_PROJET.md`
- ✅ Examiner `refactor-plan.md`
- ✅ Consulter `integration-plan.md`
- ✅ Identifier les fichiers utilitaires créés

### **2. Analyser l'état actuel**
- ✅ Vérifier que l'application fonctionne
- ✅ Identifier les erreurs TypeScript
- ✅ Comprendre les fonctionnalités critiques
- ✅ Noter les métriques de performance

### **3. Planifier les modifications**
- ✅ Choisir une étape du plan d'intégration
- ✅ Préparer un backup
- ✅ Définir les tests de validation

---

## 🏗️ Architecture en 5 minutes

### **Fichier principal** : `src/teamCalendarApp.ts`
- **15,444 lignes** de code TypeScript
- **400+ fonctions** différentes
- **Classe principale** : `TeamCalendarApp`

### **Structure des données**
```typescript
interface TeamCalendarData {
  employees: Employee[];                    // Employés
  schedule: Record<string, ShiftData[]>;   // Planning
  regularAssignments: RegularAssignment[]; // Attributions régulières
  // ... autres données
}
```

### **Flux principal**
```
init() → loadState() → render() → setupEventListeners()
```

---

## 🚨 Points critiques à connaître

### **1. Gestion d'état complexe**
- `_isInitialized` : Protection anti-double init
- `_renderCache` : Cache pour éviter re-rendus
- `_weekCache` : Cache des données de semaine
- `_saveStateTimer` : Debouncing des sauvegardes

### **2. Debouncing critique**
```typescript
// Ces fonctions sont debounced pour éviter les appels multiples
private _debouncedRender = debounce(this.render.bind(this), 100);
private _debouncedSave = debounce(this.saveCurrentWeek.bind(this), 1000);
```

### **3. Validation obligatoire**
- Toujours valider les données avant traitement
- Utiliser les nouveaux utilitaires de validation
- Gérer les erreurs de manière cohérente

---

## 🛠️ Utilitaires disponibles

### **Validation** (`src/utils/validation.ts`)
```typescript
import { validateEmployee, validateShift, validatePost } from './utils/validation';

// Utilisation
const validation = validateEmployee(employee);
if (!validation.isValid) {
  console.error('Validation failed:', validation.errors);
  return;
}
```

### **Dates** (`src/utils/dateHelpers.ts`)
```typescript
import { formatDateToKey, getWeekKey, isSameDay } from './utils/dateHelpers';

// Utilisation
const dateKey = formatDateToKey(date);
const weekKey = getWeekKey(date);
```

### **Service Employés** (`src/services/EmployeeService.ts`)
```typescript
import { EmployeeService } from './services/EmployeeService';

// Initialisation
this.employeeService = new EmployeeService({
  apiService: this.ApiService,
  onError: (error) => window.toastSystem?.error(error),
  onSuccess: (message) => window.toastSystem?.success(message)
});
```

---

## 🔄 Plan d'intégration à suivre

### **Étape 1 : Imports (Sécurisé)**
```typescript
// Ajouter en haut de teamCalendarApp.ts
import { validateEmployee, validateShift, validatePost } from './utils/validation';
import { normalizeDateKey, formatDateToKey, getWeekKey } from './utils/dateHelpers';
import { EmployeeService } from './services/EmployeeService';
```

### **Étape 2 : Service Employé (Sécurisé)**
```typescript
// Dans le constructeur de TeamCalendarApp
this.employeeService = new EmployeeService({
  apiService: this.ApiService,
  onError: (error) => {
    if (window.toastSystem) {
      window.toastSystem.error(error);
    }
  },
  onSuccess: (message) => {
    if (window.toastSystem) {
      window.toastSystem.success(message);
    }
  }
});
```

### **Étape 3 : Remplacer les validations**
```typescript
// AVANT
if (!employee || !employee.id) {
  console.error('Invalid employee');
  return;
}

// APRÈS
const validation = validateEmployee(employee);
if (!validation.isValid) {
  console.error('Employee validation failed:', validation.errors);
  return;
}
```

### **Étape 4 : Utiliser les helpers de dates**
```typescript
// AVANT
const dateKey = date.toISOString().split('T')[0];

// APRÈS
const dateKey = formatDateToKey(date);
```

---

## 🧪 Tests de validation obligatoires

### **Avant chaque modification**
- [ ] L'application se charge sans erreur
- [ ] Les employés s'affichent correctement
- [ ] Le drag & drop fonctionne
- [ ] La sauvegarde fonctionne
- [ ] La navigation fonctionne

### **Après chaque modification**
- [ ] Vérifier les logs d'erreur
- [ ] Tester les fonctionnalités critiques
- [ ] Mesurer les performances
- [ ] Valider l'interface utilisateur

---

## 🚨 Erreurs courantes à éviter

### **1. Double initialisation**
```typescript
// ❌ MAUVAIS
if (this.config._isInitialized) {
  this.init(); // Peut causer des boucles infinies
}

// ✅ BON
if (!this.config._isInitialized) {
  this.init();
}
```

### **2. Re-rendus excessifs**
```typescript
// ❌ MAUVAIS
this.render(); // Appel direct sans debouncing

// ✅ BON
this._debouncedRender(); // Utilise le debouncing
```

### **3. Sauvegardes multiples**
```typescript
// ❌ MAUVAIS
this.saveCurrentWeek(); // Peut causer des conflits

// ✅ BON
this._debouncedSave(); // Utilise le debouncing
```

### **4. Validation manquante**
```typescript
// ❌ MAUVAIS
const employee = data.employees[0];
employee.name = employee.name.toUpperCase(); // Pas de validation

// ✅ BON
const validation = validateEmployee(employee);
if (validation.isValid) {
  employee.name = employee.name.toUpperCase();
}
```

---

## 📊 Métriques à surveiller

### **Performance**
- **Temps de chargement** : < 3 secondes
- **Temps de rendu** : < 100ms
- **Mémoire** : Pas de fuites
- **CPU** : < 50% en idle

### **Qualité**
- **Erreurs TypeScript** : 0
- **Erreurs console** : Réduire de 50%
- **Code dupliqué** : Réduire de 30%
- **Complexité** : Réduire de 50%

---

## 🔧 Commandes utiles

### **Tester l'application**
```bash
npm run dev
# Ouvrir http://localhost:5173
```

### **Vérifier TypeScript**
```bash
npx tsc --noEmit
```

### **Analyser le bundle**
```bash
npm run build
npx vite-bundle-analyzer
```

### **Lancer les tests**
```bash
npm test
```

---

## 📞 Support et ressources

### **Fichiers de référence**
- `DOCUMENTATION_COMPLETE_PROJET.md` : Documentation complète
- `refactor-plan.md` : Plan de refactorisation
- `integration-plan.md` : Plan d'intégration
- `src/utils/validation.ts` : Utilitaires de validation
- `src/utils/dateHelpers.ts` : Utilitaires de dates
- `src/services/EmployeeService.ts` : Service employés

### **Fonctions critiques à comprendre**
- `init()` : Initialisation de l'application
- `loadState()` : Chargement des données
- `render()` : Rendu de l'interface
- `saveCurrentWeek()` : Sauvegarde des données
- `setupEmployeeDragDrop()` : Configuration drag & drop

### **Événements importants**
- `DOMContentLoaded` : Démarrage de l'application
- `dragstart` / `dragend` : Gestion du drag & drop
- `click` : Interactions utilisateur
- `beforeunload` : Sauvegarde avant fermeture

---

## 🎯 Objectifs de la refactorisation

### **Phase 1 : Sécurisation (✅ TERMINÉE)**
- ✅ Création des utilitaires
- ✅ Documentation complète
- ✅ Plan d'intégration

### **Phase 2 : Intégration (🔄 EN COURS)**
- 🔄 Ajout des imports
- 🔄 Initialisation du service employé
- 🔄 Remplacement des validations
- 🔄 Utilisation des helpers de dates

### **Phase 3 : Optimisation (📋 PLANIFIÉE)**
- 📋 Mémoisation des calculs coûteux
- 📋 Amélioration du debouncing
- 📋 Ajout de tests unitaires
- 📋 Documentation complète

---

**⚠️ RÈGLE D'OR** : Toujours tester après chaque modification, même minime !

**📝 CONVENTION** : Documenter chaque changement pour les prochaines IA

**🔄 APPROCHE** : Modifications incrémentales, un changement à la fois 