import re

file_path = "src/teamCalendarApp.ts"

with open(file_path, "r", encoding="utf-8") as f:
    content = f.read()

# Remplacer 'Erreur lors de l'initialisation' par 'Erreur lors de l\'initialisation'
content = content.replace("console.error('❌ [init] Erreur lors de l'initialisation:', error);", "console.error('❌ [init] Erreur lors de l\\'initialisation:', error);")
content = content.replace("window.toastSystem?.error('Erreur lors de l'initialisation de l'application');", "window.toastSystem?.error('Erreur lors de l\\'initialisation de l\\'application');")

with open(file_path, "w", encoding="utf-8") as f:
    f.write(content)

print("✅ Apostrophes corrigées dans teamCalendarApp.ts") 