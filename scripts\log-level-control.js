/**
 * Script de contrôle des niveaux de logs
 * Permet de changer dynamiquement la verbosité depuis la console
 */

// Niveaux disponibles
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  CRITICAL: 4
};

// Fonctions globales pour contrôler les logs
window.setLogLevel = function(level) {
  if (typeof level === 'string') {
    level = LOG_LEVELS[level.toUpperCase()];
  }
  
  if (level === undefined || level < 0 || level > 4) {
    console.error('❌ Niveau invalide. Utilisez: DEBUG, INFO, WARN, ERROR, CRITICAL ou 0-4');
    return;
  }
  
  if (window.logger) {
    window.logger.setLevel(level);
    const levelName = Object.keys(LOG_LEVELS)[level];
    console.log(`✅ Niveau de logs défini à: ${levelName} (${level})`);
  } else {
    console.error('❌ Logger non disponible');
  }
};

window.getLogLevel = function() {
  if (window.logger) {
    const level = window.logger.getLevel();
    const levelName = Object.keys(LOG_LEVELS)[level];
    console.log(`📊 Niveau actuel: ${levelName} (${level})`);
    return level;
  } else {
    console.error('❌ Logger non disponible');
    return null;
  }
};

window.showLogHelp = function() {
  console.log(`
🔧 CONTRÔLE DES LOGS - Aide

📋 Commandes disponibles:
  setLogLevel('DEBUG')   - Afficher tous les logs (très verbeux)
  setLogLevel('INFO')    - Afficher infos, warnings et erreurs
  setLogLevel('WARN')    - Afficher seulement warnings et erreurs
  setLogLevel('ERROR')   - Afficher seulement les erreurs
  setLogLevel('CRITICAL')- Afficher seulement les erreurs critiques
  
  getLogLevel()          - Voir le niveau actuel
  showLogHelp()          - Afficher cette aide

📊 Niveaux numériques:
  0 = DEBUG, 1 = INFO, 2 = WARN, 3 = ERROR, 4 = CRITICAL

💡 Exemples:
  setLogLevel('WARN')    // Mode silencieux (par défaut)
  setLogLevel('INFO')    // Mode normal
  setLogLevel('DEBUG')   // Mode debug complet

🚀 Raccourcis rapides:
  quiet()                // Équivalent à setLogLevel('WARN')
  verbose()              // Équivalent à setLogLevel('INFO')
  debug()                // Équivalent à setLogLevel('DEBUG')
  `);
};

// Raccourcis rapides
window.quiet = function() {
  window.setLogLevel('WARN');
  console.log('🔇 Mode silencieux activé');
};

window.verbose = function() {
  window.setLogLevel('INFO');
  console.log('📢 Mode verbeux activé');
};

window.debug = function() {
  window.setLogLevel('DEBUG');
  console.log('🐛 Mode debug activé');
};

// Message d'aide au chargement
console.log('🔧 Contrôle des logs chargé. Niveau par défaut: WARN');
console.log('💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")');
console.log('📖 Aide complète: showLogHelp()');

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    LOG_LEVELS,
    setLogLevel: window.setLogLevel,
    getLogLevel: window.getLogLevel,
    showLogHelp: window.showLogHelp
  };
} 