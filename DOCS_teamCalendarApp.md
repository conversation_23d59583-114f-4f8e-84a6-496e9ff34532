# Documentation extraite de src/teamCalendarApp.ts

### `safeScheduleUpdate(employeeId, dateKey, updateFn, operation)`

### `if(this._transactionLock)`

### `renderSafe()`

### `if(typeof this.actualRender === 'function')`

### `checkDataIntegrity()`

### `logEmployeePositions(context: string)`

### `quickRepair()`

### `clearRenderCache()`

### `if(this._renderCache)`

### `fixPostRefreshIssues()`

### `if(!shift.postId)`

### `if(shift.text && shift.text !== 'Shift' && shift.text !== 'undefined')`

### `if(matchingPost)`

### `if(!shift.postId && this.config.standardPosts.length > 0)`

### `if(!shift.text || shift.text === 'undefined' || shift.text === undefined)`

### `if(shift.postId)`

### `if(post)`

### `emergencyFixUndefinedPostIds()`

### `if(!this.config.standardPosts || this.config.standardPosts.length === 0)`

### `if(!shift.postId)`

### `if(shift.text && shift.text !== 'Shift' && shift.text !== 'undefined')`

### `if(exactMatch)`

### `if(labelMatch)`

### `if(!correctedPostId && shift.id && typeof shift.id === 'string')`

### `for(const post of this.config.standardPosts)`

### `if(!correctedPostId)`

### `if(correctedPostId)`

### `if(correctedPost)`

### `if(fixedCount > 0)`

### `saveFixedShifts()`

### `if(shiftsToSave.length === 0)`

### `if(this.ApiService && apiService.saveWeekShifts)`

### `if(result.success)`

### `fixUndefinedPostIdsOnLoad()`

### `if(!shift.postId)`

### `if(undefinedCount > 0)`

### `if(fixedCount > 0)`

### `getEmployeeName(id: string)`

### `normalizeAssignment(raw: any)`

### `if(!raw.employee_id || !raw.post_id)`

### `if(selectedDays.length === 0)`

### `getPostById(id: string)`

### `if(standardPost)`

### `if(standardPost.workingDays == null)`

### `if(labelMapping[id])`

### `if(mappedPost)`

### `if(mappedPost.workingDays == null)`

### `isSameDay(date1: Date, date2: Date)`

### `getEmployeeRowHeightClass()`

### `getEmployeeRowHeightValue()`

### `isEmployeeOnVacation(employeeId, dayIndex)`

### `getAvailablePostsPerDay()`

### `for(const empId in this.data.schedule)`

### `for(const dateKeyOrIndex in this.data.schedule[empId])`

### `if(shift.postId)`

### `if(post.category === 'weekend' && !day.isWeekend)`

### `if(day.isWeekend && post.category !== 'weekend' && !post.workingDays)`

### `if(assignedPostsCount[key] === 0)`

### `if(!availablePosts[post.id])`

### `generateWeekDates(weekOffset = 0)`

### `for(let i = 0; i < 7; i++)`

### `getWeekNumber(date)`

### `navigateWeek(direction)`

### `if(this.data.regularAssignments && this.data.regularAssignments.length > 0)`

### `if(weekDistance > 52)`

### `getCachedWeek(weekId)`

### `setCachedWeek(weekId, weekData)`

### `if(this._weekCache.size >= 10)`

### `clearWeekCache()`

### `saveCurrentWeek(options = {})`

### `if(this._isSavingWeek)`

### `if(!force)`

### `if(this._saveStateTimer)`

### `if(shiftsToSave.length === 0)`

### `if(window.toastSystem && !silent)`

### `if(window.toastSystem && !silent)`

### `if(this._pendingSaveWeek)`

### `loadState()`

### `if(isConnected)`

### `if(employeesResult.success && employeesResult.data)`

### `if(settingsResult.success && settingsResult.data && settingsResult.data.employee_order)`

### `if(shiftsResult.success && shiftsResult.data)`

### `if(shift.date_key instanceof Date)`

### `if(!this.data.schedule[shift.employee_id])`

### `if(!this.data.schedule[shift.employee_id][normalizedDateKey])`

### `if(shift.shift_data)`

### `if(!shift.shift_data.postId)`

### `if(shift.post_id)`

### `if(!shift.shift_data.text && shift.shift_data.postId)`

### `if(post)`

### `if(!shift.shift_data.id)`

### `if(!shift.shift_data.type)`

### `if(shift.shift_data.isReplacement && shift.shift_data.originalAssignmentId)`

### `if(duplicatesCleaned > 0)`

### `if(invalidIdsCleaned > 0)`

### `loadStateFromLocalStorage()`

### `if(savedState)`

### `if(savedAppSettings)`

/**
* Vérifie et corrige le DOM (containers clés) avant de poursuivre l'initialisation
*/
### `verifyAndFixDom(maxWaitMs = 2000)`

### `if(!this.elements.employeeListContainer)`

### `if(this.elements.employeeListContainer)`

### `init()`

### `if(savedDragTime)`

### `if(timeSinceDrag > 30000)`

### `if(this.config._isInitialized)`

### `if(!loaded)`

### `attachAllEventListeners()`

### `if(!this.elements.employeeListContainer)`

### `if(this.elements.addPostButton)`

### `if(removeAssignmentsBtn)`

### `if(employeeId)`

### `if(employeeId)`

### `if(this.data.regularAssignments && this.data.regularAssignments.length > 0)`

### `setupViewNavigationListeners()`

### `if(todayBtn)`

### `if(monthBtn)`

### `if(weekBtn)`

### `if(dayBtn)`

### `goToToday()`

### `if(this.data.regularAssignments && this.data.regularAssignments.length > 0)`

### `setViewMode(mode: 'day' | 'week' | 'month')`

### `switch(mode)`

### `generateDayView()`

### `generateMonthView()`

### `updateDisplayText()`

### `switch(this.config.viewMode)`

### `if(displayText.length > maxLength)`

### `if(this.elements.currentWeekDisplay)`

### `updateNavigationButtons()`

### `if(activeButton)`

### `checkConflictsBeforeDrop(targetEmployeeId: string, targetDateKey: string, postData: any)`

### `if(targetDateKey < today)`

### `if(existingShifts.length > 0)`

### `if(duplicatePost)`

### `handleStrictSinglePostPolicy(employeeId: string, dateKey: string, existingShifts: any[], newPostData: any, resolve: Function)`

### `if(existingShifts.length > 1)`

### `if(!window.toastSystem)`

### `showReplacementOptions(employeeId: string, dateKey: string, existingShifts: any[], newPostData: any, resolve: Function)`

### `handleChoiceSelection(choice: string | null, employeeId: string, dateKey: string, existingShifts: any[], newPostData: any, resolve: Function)`

### `switch(choice)`

### `handleStrictReplacement(employeeId: string, dateKey: string, existingShifts: any[], newPostData: any, resolve: Function)`

### `handleTemporaryReplacement(employeeId: string, dateKey: string, existingShifts: any[], newPostData: any, resolve: Function)`

### `if(!temporaryPost.postId)`

### `if(this.config.standardPosts.length > 0)`

### `handleAdvancedConflictResolution(employeeId: string, dateKey: string, existingShifts: any[], newPostData: any, resolve: Function)`

### `switch(choice)`

### `handleCompleteReplacement(employeeId: string, dateKey: string, resolve: Function)`

### `handleSimpleAddition(employeeId: string, dateKey: string, newPostData: any, existingShifts: any[], resolve: Function)`

### `if(finalTotal > 12)`

### `handleSelectiveManagement(employeeId: string, dateKey: string, existingShifts: any[], resolve: Function)`

### `if(toRemove === null)`

### `if(indices.length > 0)`

### `handlePartialTimeManagement(employeeId: string, dateKey: string, existingShifts: any[], newPostData: any, resolve: Function)`

### `switch(timeChoice)`

### `adjustTimeScheduling(employeeId: string, dateKey: string, existingShifts: any[], newPostData: any)`

### `if(post && newPost)`

### `removeRegularAssignmentsFromDate()`

### `if(dateInput)`

### `setupRemoveAssignmentsModal()`

### `confirmRemoveAssignments()`

### `if(!startDateStr)`

### `if(selectedEmployees.length === 0)`

### `executeRemoveAssignments(startDateStr, selectedEmployees = [])`

### `if(dateKey >= startDateStr)`

### `if(regularShifts.length > 0)`

### `if(this.data.schedule[employeeId][dateKey].length === 0)`

### `renderEmployeesForRemoval()`

### `if(employeesWithAssignments.length === 0)`

### `setupEmployeeCheckboxEvents()`

### `if(countDisplay)`

### `openSingleAssignmentRemovalModal(employeeId, assignmentId)`

### `if(!assignment)`

### `setupSingleRemovalModal()`

### `confirmSingleRemoval()`

### `if(!startDateStr)`

### `if(!this.currentSingleRemovalData)`

### `executeSingleRemoval(employeeId, assignmentId, startDateStr)`

### `if(this.data.schedule[employeeId])`

### `if(dateKey >= startDateStr)`

### `if(filteredShifts.length !== shifts.length)`

### `if(filteredShifts.length === 0)`

### `renderEmployeeTemplates()`

### `openTemplateModal(templateId = null)`

### `if(templateId)`

### `if(template)`

### `if(nameInput)`

### `if(descInput)`

### `renderTemplateFields(fields = [])`

### `addTemplateField()`

### `removeTemplateField(fieldId)`

### `updateTemplatePreview()`

### `if(previewName)`

### `if(previewDesc)`

### `if(previewFieldCount)`

### `if(previewFieldsList)`

### `if(fieldCount === 0)`

### `if(previewEmployeeStatus && fieldCount > 0)`

### `saveEmployeeTemplate()`

### `if(label)`

### `if(type === 'select' && optionsTextarea)`

### `if(this.currentEditingTemplateId)`

### `if(index !== -1)`

### `deleteEmployeeTemplate(templateId)`

### `if(window.toastSystem?.confirm)`

### `if(confirmed)`

### `renderEmployeesManagement()`

### `if(selector)`

### `openEmployeeAdvancedModal(employeeId = null)`

### `if(employeeId)`

### `if(employee)`

### `if(avatarPreview)`

### `if(avatarPreview)`

### `renderEmployeeFormFields(template, employee = null)`

### `switch(field.type)`

### `handleAvatarUpload(file)`

### `removeEmployeeAvatar(employeeId)`

### `if(employee)`

### `saveEmployeeAdvanced()`

### `if(missingFields.length > 0)`

### `if(fieldId)`

### `if(key !== 'name' && key !== 'status')`

### `if(this.currentEditingEmployeeAdvancedId)`

### `if(index !== -1)`

### `if(createResult.success && createResult.data)`

### `deleteEmployee(employeeId)`

### `if(window.toastSystem?.confirm)`

### `if(confirmed)`

### `setupGlobalEscapeHandler()`

### `if(e.key === 'Escape')`

### `if(modalClosed)`

### `if(modalClosed)`

### `setupNavigationListeners()`

### `if(this.config._navigationListenersAttached)`

### `if(prevBtn && nextBtn)`

### `render()`

### `if(this._debouncedRender)`

### `actualRender()`

### `if(now - window._lastRenderTime < 1000)`

### `if(window._renderCount > 5)`

### `if(!this.elements.scheduleContainer)`

### `if(!this.elements.scheduleContainer)`

### `renderEmployees()`

### `if(!this.elements.employeeListContainer)`

### `if(!this.elements.employeeListContainer)`

### `if(!this.elements.availablePostsContainer)`

### `if(!this.elements.availablePostsContainer)`

### `if(allPosts.length === 0)`

### `if(!isAvailable)`

### `renderScheduleGrid()`

### `if(!this.elements.scheduleGridContent)`

### `if(!this.elements.scheduleGridContent)`

### `if(day.isToday)`

### `if(day.isToday)`

### `if(isAvailableToday)`

### `renderUnifiedCalendar()`

### `if(!employeeRowsContainer)`

### `if(!availablePostsContainer)`

### `if(dayHeader)`

### `if(!postsListContainer)`

### `if(allPosts.length === 0)`

### `if(shift.postId === post.id)`

### `if(assignedEmployeesList.length > 0)`

### `if(assignedEmployeesList.length <= 3)`

### `if(postShouldWorkThisDay)`

### `if(shift.postId === post.id)`

### `if(shiftsForPostAndDay.length > 0)`

### `diagnoseRegularAssignmentGrips()`

### `if(shift.isRegular && shift.assignmentId)`

### `if(regularShifts.length === 0)`

### `ensureRegularAssignmentGrips()`

### `if(shiftPayload.isRegular && shiftPayload.assignmentId)`

### `if(!existingGrip)`

### `if(gripsAdded > 0)`

### `attachGripEvents(gripDiv, shiftElement, shiftData)`

### `addSwapIconsToEmployees()`

### `if(!actionsContainer)`

### `startEmployeeSwap(initiatorEmployeeId)`

### `if(employeeId === initiatorEmployeeId)`

### `attachSwapTargetEvents()`

### `if(employeeId)`

### `if(this._swapModeActive && this._swapSelectedEmployeeId)`

### `cleanupSwapMode()`

### `if(htmlRow._swapClickHandler)`

### `setupEmployeeDragDrop()`

### `if(!this._employeeDragLogger)`

### `if(!employeeContainer)`

### `if(typeof Sortable !== 'undefined')`

### `if(this.employeeSortable)`

### `if(this.employeeSortable)`

### `if(employee)`

### `if(evt.oldIndex !== evt.newIndex)`

### `if(!isValidTarget)`

### `isValidEmployeeName(name)`

### `handleEmployeeReorder(draggedEmployeeName, targetEmployeeId)`

### `if(!draggedEmployee)`

### `if(!targetEmployee)`

### `if(draggedIndex === -1 || targetIndex === -1)`

### `if(draggedIndex === targetIndex)`

### `addSimplifiedEmployeeDragHandles()`

### `if(employeeId && employeeImg)`

### `if(this._currentEmployeeDrag)`

### `if(allDraggableImages.length === 0)`

### `diagnoseEmployeeDragState()`

### `if(draggableImages.length > 0)`

### `addDragHandlesToEmployees()`

### `if(employeeId && employeeImg)`

### `if(finalImg)`

### `if(isEmployeeReorder)`

### `if(draggedIndex !== -1 && draggedIndex !== index)`

### `reorderEmployees(oldIndex, newIndex)`

### `if(this._reorderInProgress)`

### `if(this._saveOrderTimeout)`

### `reorderEmployeeRowsOnly()`

### `if(!employeeRowsContainer)`

### `if(employeeId)`

### `if(row)`

### `reorderEmployeeRowsOnlyOptimized()`

### `if(!employeeRowsContainer)`

### `if(!this._renderFallbackDone)`

### `for(let i = 0; i < this.data.employees.length; i++)`

### `if(!row)`

### `if(!this._renderFallbackDone)`

### `if(!this._dragDropConfigured)`

### `saveEmployeeOrder()`

### `if(this._saveOrderInProgress)`

### `if(!this.data.employees || this.data.employees.length === 0)`

### `if(!emp || !emp.id)`

### `if(result.success)`

### `if(window.toastSystem)`

### `if(window.toastSystem)`

### `if(window.toastSystem)`

### `if(this._pendingSave)`

### `applyEmployeeOrder(orderArr: {id: string; order: number}[])`

### `if(!orderArr?.length)`

### `loadEmployeeOrder()`

### `if(this._loadingEmployeeOrder)`

### `if(result.success && result.data)`

### `if(orderData && orderData.length > 0)`

### `if(savedOrderStr)`

### `if(cacheAge < maxCacheAge && cacheData.employeeOrder)`

### `if(employee)`

### `createShiftElement(shiftData, employeeId, dayIndex, shiftIndex, isTodayCell)`

### `if(shiftData.isTemporary)`

### `if(shiftData.type === 'amber')`

### `if(shiftData.isTemporary)`

### `if(shiftData.postId)`

### `if(post)`

### `if(displayText === 'undefined' || displayText === undefined || displayText === null)`

### `if(shiftData.isPunctual && !shiftData.isReplacement && !shiftData.isRegular)`

### `if(shiftData.isPunctual && shiftData.isReplacement && shiftData.originalAssignmentId)`

### `if(convertBtn)`

### `if(shiftData.isRegular && shiftData.assignmentId)`

### `if(e.key === ' ' || e.key === 'Enter')`

### `createPostShiftElement(shiftData, dayIndex, shiftIndex, isTodayCell)`

### `if(shiftData.isTemporary)`

### `if(shiftData.type === 'amber')`

### `if(shiftData.isTemporary)`

### `if(displayText === 'undefined' || displayText === undefined || displayText === null)`

### `createAvailablePostShiftHTML(post, dayIndex, isTodayCell)`

### `enableEmployeeSwapMode()`

### `if(employeeId)`

### `disableEmployeeSwapMode()`

### `showSwapInstructions()`

### `if(!instructionsModal)`

### `hideSwapInstructions()`

### `if(instructionsModal)`

### `attachSwapClickEvents()`

### `if(employeeId)`

### `detachSwapClickEvents()`

### `handleSwapEmployeeClick(e)`

### `if(!this._swapSelectedEmployeeId)`

### `executeEmployeeSwap(employeeId1, employeeId2)`

### `if(shifts1.length > 0 || shifts2.length > 0)`

### `handleAvailablePostShiftClick(postId, dayIndex)`

### `createPostShiftElementHTML(shiftData, dayIndex, shiftIndex, isTodayCell, status = 'assigned')`

### `if(shiftData.isTemporary)`

### `if(shiftData.type === 'amber')`

### `if(shiftData.isTemporary)`

### `if(shiftData.postId)`

### `if(displayText === 'undefined' || displayText === undefined || displayText === null)`

### `handleDeleteShiftFromPost(employeeId, dayIndex, shiftData)`

### `if(shiftIndex !== -1)`

### `createAvailableShiftElement(post, dayIndex, isTodayCell)`

### `handleAvailableShiftClick(postId, dayIndex)`

### `getShiftState(dayIndex)`

### `checkReplacementReintegration(replacementShift: any, targetEmployeeId: string, targetDateKey: string)`

### `if(!replacementShift.isReplacement || !replacementShift.originalAssignmentId)`

### `if(!originalAssignment)`

### `if(originalAssignment.employeeId !== targetEmployeeId)`

### `if(targetDateKey < originalAssignment.startDate)`

### `if(originalAssignment.endDate && targetDateKey > originalAssignment.endDate)`

### `handleReplacementReintegration(replacementShift: any, sourceEmployeeId: string, sourceDateKey: string, targetEmployeeId: string, targetDateKey: string)`

### `if(!originalAssignment)`

### `if(!this.data.schedule[sourceEmployeeId])`

### `if(!this.data.schedule[sourceEmployeeId][sourceDateKey])`

### `executeReplacementReintegration(replacementShift: any, sourceEmployeeId: string, sourceDateKey: string, targetEmployeeId: string, targetDateKey: string, originalAssignment: any)`

### `if(originalAssignment.excludedDates)`

### `if(excludeIndex !== -1)`

### `if(!updateResult.success)`

### `showSuccessModal(title: string, message: string)`

### `showErrorModal(title: string, message: string)`

### `showConfirmModal(title: string, message: string, onConfirm: ()`

### `showCustomModal(title: string, message: string, type: 'success' | 'error' | 'confirm', buttons: Array<{text: string, action: ()`

### `if(button.primary)`

### `if(e.key === 'Escape')`

### `if(e.target === overlay)`

### `closeCustomModal()`

### `if(current)`

### `if(current.overlay.parentNode)`

### `setupDragAndDrop()`

### `if(!targetEmployeeId)`

### `if(shiftData.isPlaceholder)`

### `if(targetDayIndex >= 0 && this.config.days[targetDayIndex])`

### `if(!sourceEmployeeId)`

### `if(!this.data.schedule[sourceEmployeeId]?.[sourceDateKey])`

### `if(evt.oldIndex >= sourceShifts.length)`

### `if(movedShift.isRegular && movedShift.assignmentId)`

### `if(sourceEmployee && targetEmployee && assignment)`

### `if(sourceShifts.length === 0)`

### `if(window._currentDragData)`

### `if(evt.oldIndex !== evt.newIndex)`

### `setupPostDragDrop()`

### `if(!this.elements.availablePostsContainer)`

### `if(!postId)`

### `isValidUUID(str)`

### `enableAgendaPreviewMode(postId)`

### `if(!post)`

### `addGhostPreviewsToCompatibleCells(post)`

### `if(isCompatibleDay)`

### `if(cell)`

### `isPostCompatibleWithDay(post, dayIndex)`

### `addGhostPreviewToCell(cell, post, employee, day)`

### `enableCellHoverTracking()`

### `if(employeeId && dayIndex !== undefined && this._currentDraggedPost)`

### `if(cell)`

### `highlightTargetCell(cell, employeeId, dayIndex)`

### `if(ghost)`

### `unhighlightTargetCell(cell)`

### `if(ghost)`

### `disableAgendaPreviewMode()`

### `if(ghost.parentNode)`

### `disableCellHoverTracking()`

### `if(this._cellMouseEnterHandler && this._cellMouseLeaveHandler)`

### `setupCentralizedDropZone()`

### `if(this._centralizedDragOverHandler)`

### `if(this._centralizedDropHandler)`

### `if(employeeRowsContainer)`

### `if(!employeeRows || employeeRows.length === 0)`

### `if(employeeListContainer)`

### `if(!employeeRows || employeeRows.length === 0)`

### `if(!employeeRows || employeeRows.length === 0)`

### `if(employeeCards.length > 0)`

### `if(oldListeners)`

### `if(hasRegularAssignment)`

### `for(const format of formats)`

### `if(regularAssignmentId)`

### `if(missingElements.length > 0)`

### `if(this._currentEmployeeDrag && this._currentEmployeeDrag.employeeName)`

### `if(this._currentEmployeeDrag && !employeeName)`

### `if(draggablePosts.length > 0 && dropZones.length > 0)`

### `if(dragOverEvent.defaultPrevented)`

### `ensureDropZonesExist()`

### `if(existingZones.length > 0)`

### `if(typeof this.renderUnifiedCalendar === 'function')`

### `if(newZones.length === 0)`

### `createTemporaryDropZones()`

### `for(let i = 0; i < 3; i++)`

### `findEmployeeAtPoint(x, y)`

### `for(let empEl of employeeElements)`

### `if(x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom)`

### `findClosestEmployee(x, y)`

### `assignFullPostAndSwap(postId, targetEmployeeId)`

### `if(!postToAssign || !postData)`

### `for(const dayIndex of postToAssign.days)`

### `if(shouldProceed)`

### `if(proceed)`

### `if(conflictChecks.length > 0)`

### `handleAddShiftPrompt()`

### `if(!selectedPost)`

### `if(replIdx)`

### `if(!shiftText)`

### `if(replIdx)`

### `addShift(employeeId, dayIndex, shiftData, shouldRender = true)`

### `if(!day)`

### `addShiftByDateKey(employeeId, dateKey, shiftData, shouldRender = true, skipConflictCheck = false)`

### `if(!this.data.schedule[employeeId])`

### `if(!this.data.schedule[employeeId][dateKey])`

### `if(!skipConflictCheck && targetShifts.length > 0 && !shiftData.leave)`

### `if(isReplacementShift)`

### `if(isAutomaticRegular)`

### `if(shouldRender)`

### `if(shouldRender)`

### `if(targetShifts.length >= 2)`

### `if(shouldRender)`

### `addStandardShift(employeeId, dayIndex, postId, shouldRender = true)`

### `if(!post)`

### `handleShiftClick(employeeId, dayIndex, shiftIndex)`

### `if(postIdxStr)`

### `if(selected)`

### `if(replIdxStr)`

### `handleDeleteShift(employeeId, dayIndex, shiftIndex)`

### `if(this.data.schedule[employeeId]?.[dateKey]?.[shiftIndex])`

### `if(this.data.schedule[employeeId][dateKey].length === 0)`

### `if(deleteResult.success)`

### `updateStats()`

### `for(const empId in this.data.schedule)`

### `for(const dateKeyOrIndex in this.data.schedule[empId])`

### `if(shift.postId)`

### `if(post)`

### `if(missingPostsForDay.length > 0)`

### `initScrollIndicator(retryDelay: number = 100, remainingRetries: number = 20)`

### `if(!this.elements.scheduleContainer)`

### `if(!this.elements.scheduleGridScrollableContent)`

### `if(!this.elements.scheduleGridScrollableContent)`

### `if(!scheduleContainer || !scheduleGrid)`

### `if(remainingRetries <= 0)`

### `if(isScrollable && !isAtBottom)`

### `handleManageVacation(e: Event)`

### `openSettingsModal()`

### `if(!this.elements.settingsModal)`

### `closeSettingsModal()`

### `if(this.elements.settingsModal)`

### `createSettingsModal()`

### `if(existingModal)`

### `if(this.elements.settingsModalClose)`

### `if(this.ModalManager && !this.ModalManager._modalHandlersSetup)`

### `setupTabNavigation()`

### `if(targetContent)`

### `attachSettingsButtonIfMissing()`

### `if(this.elements.settingsButton && this.elements.settingsButton.onclick)`

### `if(settingsBtn && !this.elements.settingsButton)`

### `renderSettingsContent()`

### `renderPostsForConfig()`

### `handleColorChange(postId, newColor)`

### `if(!post)`

### `if(updateResult.success)`

### `renderVacationPeriods()`

### `if(globalVacations.length === 0)`

### `renderAssignments()`

### `if(!this.data.regularAssignments || this.data.regularAssignments.length === 0)`

### `if(selectedDays.length > 0)`

### `for(let i = 1; i < sortedDays.length; i++)`

### `if(sortedDays[i] === end + 1)`

### `if(start === end)`

### `if(start === end)`

### `handleAddVacationPeriod()`

### `handleAddAssignment()`

### `handlePostEdit(index = null)`

### `handlePostDelete(index)`

### `if(!post)`

### `if(!response.ok)`

### `if(window.toastSystem)`

### `if(window.toastSystem)`

### `handleDeleteVacation(index)`

### `handleEditAssignment(index)`

### `if(!assignment)`

### `handleDeleteAssignment(index)`

### `if(result.success)`

### `handleMovePost(index, direction)`

### `handleDuplicatePost(index)`

### `handlePurgeShifts(employeeId)`

### `if(this.currentPurgeEmployeeId === employeeId)`

### `if(!employee)`

### `confirmPurgeShifts()`

### `if(!purgeType)`

### `switch(purgeType)`

### `if(!startDate || !endDate)`

### `if(!confirmed)`

### `executePurge(employeeId, purgeType)`

### `switch(purgeType)`

### `if(this.data.schedule[employeeId][dayIndex])`

### `if(dayDate && dayDate >= startDate && dayDate <= endDate)`

### `handleEditEmployee(employeeId)`

### `renderExtraFields(extraFields)`

### `addExtraField()`

### `addExtraFieldElement(key = '', value = '')`

### `saveEmployee()`

### `if(!name)`

### `if(key && value)`

### `openAssignmentContextModal(postId, employeeId, context = null)`

### `if(postRow)`

### `if(!post || !employee)`

### `if(singleCellOption)`

### `if(isCellSpecificDrop)`

### `if(startDateInput)`

### `handleAssignmentContextConfirm()`

### `switch(assignmentType)`

### `if(!startDate)`

### `if(!startDate)`

### `if(!endDate)`

### `assignFullPostOnce(postId, employeeId)`

### `if(!post)`

### `if(!shiftData.postId)`

### `if(this.config.standardPosts.length > 0)`

### `assignSingleCell(postId, employeeId)`

### `if(!context || !context.cellDate)`

### `if(!post)`

### `if(!this.data.schedule[employeeId])`

### `if(!this.data.schedule[employeeId][dateKey])`

### `if(existingShifts.length > 0)`

### `if(!confirmReplace)`

### `if(!shiftData.postId)`

### `if(this.config.standardPosts.length > 0)`

### `createRegularAssignment(postId, employeeId, isLimited, startDate = null, endDate = null)`

### `if(selectedDays.length === 0)`

### `if(post?.workingDays && post.workingDays.length > 0)`

### `if(!startDate)`

### `if(appliedCount === 0)`

### `if(!result.success)`

### `if(result.data && result.data.assignment)`

### `testRegularAssignment(assignment)`

### `if(!post)`

### `if(assignment.startDate)`

### `if(assignment.isLimited && assignment.endDate)`

### `if(existingShifts.length > 0)`

### `applyRegularAssignment(assignment)`

### `if(!post)`

### `if(existingShifts.length > 0)`

### `updateAssignmentIdInShifts(oldAssignmentId, newAssignmentId)`

### `for(const employeeId in this.data.schedule)`

### `for(const dateKey in this.data.schedule[employeeId])`

### `if(shift.assignmentId === oldAssignmentId)`

### `shouldApplyAssignmentToDay(assignment, post, day)`

### `if(isExcluded)`

### `if(assignment.startDate)`

### `if(normalizedStartDate && dayDateStr < normalizedStartDate)`

### `if(assignment.endDate)`

### `if(normalizedEndDate && dayDateStr > normalizedEndDate)`

### `applyRegularAssignmentsForCurrentWeek()`

### `if(this._isApplyingRegularAssignments)`

### `if(!this.data.regularAssignments || this.data.regularAssignments.length === 0)`

### `if(!this.config.days || this.config.days.length === 0)`

### `for(const employeeId in this.data.schedule)`

### `if(this.data.schedule[employeeId]?.[dateKey])`

### `if(isRegularShift)`

### `if(this.data.schedule[employeeId][dateKey].length === 0)`

### `if(this._appliedWeeksCache)`

### `if(!this._appliedWeeksCache)`

### `for(const assignment of this.data.regularAssignments)`

### `for(const employeeId in this.data.schedule)`

### `if(employeeId === assignment.employeeId)`

### `for(const dateKey in this.data.schedule[employeeId])`

### `if(hasAnyAssignment)`

### `if(hasExistingRegularShifts && this._lastAppliedWeek === weekKey)`

### `if(assignment.isActive === false)`

### `if(assignment.startDate)`

### `if(normalizedStartDate && normalizedStartDate > lastDayKey)`

### `if(assignment.endDate)`

### `if(normalizedEndDate && normalizedEndDate < firstDayKey)`

### `if(!assignment.postId || !assignment.employeeId)`

### `if(hasThisRegularShift)`

### `if(hasSamePostShift)`

### `if(!shiftData.postId)`

### `if(this.config.standardPosts.length > 0)`

### `if(appliedCount > 0)`

### `cleanupRegularAssignments()`

### `if(post)`

### `if(!assignment.id)`

### `if(!employee)`

### `if(!post)`

### `if(cleanedCount > 0 || removedCount > 0)`

### `init(app)`

### `setupTabNavigation()`

### `if(targetContent)`

### `setupModalHandlers()`

### `if(this._modalHandlersSetup)`

### `if(missingElements.length > 0)`

### `if(postSaveBtn)`

### `if(e.target.checked)`

### `if(e.target.checked)`

### `if(e.target.checked)`

### `if(selectedTemplate)`

### `if(file)`

### `if(preview)`

### `if(preview)`

### `setupGeneralSettings()`

### `setupWeekStartSelector()`

### `if(!container)`

### `if(container.dataset.listenerAttached === 'true')`

### `if(currentRadio)`

### `if(typeof dayNumber === 'number')`

### `setupAssignmentContextModal()`

### `if(value === 'regular-indefinite' || value === 'regular-limited')`

### `if(value === 'regular-limited')`

### `openAssignmentContextModal(postId, employeeId)`

### `openAssignmentModal()`

### `saveAssignment()`

### `if(!employeeSelect || !postSelect || !startDateInput)`

### `if(!employeeId)`

### `if(!postId)`

### `if(!startDate)`

### `if(!post)`

### `exportData()`

### `importData()`

### `if(typeof result !== 'string')`

### `if(resetBtn)`

### `if(result.success)`

### `if(window.toastSystem)`

### `if(resetBtn)`

### `if(window.toastSystem)`

### `openPostModal(post: any = null, index: any = null)`

### `if(!modal)`

### `if(post && typeof post === 'object')`

### `initializeDaySelector(post: any = null)`

### `if(!container)`

### `renderFallbackDaySelector(container: HTMLElement, post: any = null)`

### `attachDaySelectorEvents(container: HTMLElement)`

### `if(daysStr)`

### `updateDaySelection(container: HTMLElement)`

### `if(checkbox.checked)`

### `if(countEl)`

### `updateDaySelectorUI(container: HTMLElement, days: number[])`

### `setPostWorkingDays(days: number[])`

### `if(container)`

### `if(validDays.length === 0)`

### `if(!labelInput || !startTimeInput || !endTimeInput)`

### `if(startTime && endTime)`

### `if(endMinutes < startMinutes)`

### `if(!postData.label)`

### `if(!postData.startTime || !postData.endTime)`

### `if(postData.workingDays.length === 0)`

### `if(editIndex !== '' && editIndex !== undefined)`

### `if(index >= 0 && index < this.app.config.standardPosts.length)`

### `if(canonicalPost && canonicalPost.id)`

### `if(this.app && typeof this.app.renderPostsForConfig === 'function')`

### `if(this.app && typeof this.app.render === 'function')`

### `if(this.app && typeof this.app.saveCurrentWeek === 'function')`

### `if(this.app && typeof this.app.setupPostDragDrop === 'function')`

### `if(postsResult.success && postsResult.data)`

### `if(this.app && typeof this.app.renderPostsForConfig === 'function')`

### `if(this.app && typeof this.app.render === 'function')`

### `if(!cleanedPostData.label || cleanedPostData.label.length < 1)`

### `if(action === 'create')`

### `if(result.success && result.data)`

### `if(result.success)`

### `openVacationModal(vacation: any = null, employeeId: any = null)`

### `if(!modal)`

### `if(employeeSelect)`

### `if(employeeId && emp.id === employeeId)`

### `if(vacation && typeof vacation === 'object')`

### `if(vacation.employeeId && employeeSelect)`

### `if(employeeId && employeeSelect)`

### `saveVacation()`

### `if(!employeeSelect || !startDateInput || !endDateInput)`

### `if(!vacationData.employeeId)`

### `if(!vacationData.startDate || !vacationData.endDate)`

### `if(editId)`

### `if(index !== -1)`

### `if(this.app && typeof this.app.saveCurrentWeek === 'function')`

### `replacePostInStore(tempId: string, canonicalPost: any)`

### `if(index !== -1)`

### `updatePostDOM(tempId: string, canonicalId: string)`

### `if(availablePostsContainer)`

### `if(this.app && typeof this.app.setupPostDragDrop === 'function')`

### `logWorkingDaysFlow(step: string, data: any)`

### `if(existingLogs.length > 100)`

### `showWorkingDaysLogs()`

### `clearWorkingDaysLogs()`

### `if(createResult.success && createResult.data)`

### `if(workingDaysMatch)`

### `if(createResult.data.id)`

### `for(const postData of defaultPosts)`

### `if(!existingPost)`

### `if(result.success)`

### `if(!existingPost.workingDays || existingPost.workingDays.length === 0)`

### `if(result.success)`

### `if(postsResult.success && postsResult.data)`

### `if(!createResult.success)`

### `if(createdPost.id)`

### `applyRegularAssignmentsForWeek(weekKey)`

### `if(!this.data.regularAssignments || this.data.regularAssignments.length === 0)`

### `if(!weekInfo)`

### `if(!assignment.postId || !assignment.employeeId)`

### `if(assignmentStartDate > weekEndDate)`

### `if(assignmentEndDate && assignmentEndDate < weekStartDate)`

### `if(!post)`

### `for(let dayOffset = 0; dayOffset < 7; dayOffset++)`

### `if(hasRegularShift)`

### `if(!this.data.schedule[assignment.employeeId])`

### `if(!this.data.schedule[assignment.employeeId][dateKey])`

### `if(appliedCount > 0)`

### `if(weekKey === this.config._currentWeekKey)`

### `getWeekStartDayNumber()`

### `if(typeof this.config.appSettings.weekStartDay === 'number')`

### `if(this.config.appSettings.weekStartsOn)`

### `if(typeof mapped === 'number')`

### `setWeekStartDay(dayNumber)`

### `if(typeof dayNumber !== 'number' || dayNumber < 0 || dayNumber > 6)`

### `synchronizeWeekStartSettings()`

### `if(typeof this.config.appSettings.weekStartDay === 'number' && !this.config.appSettings.weekStartsOn)`

### `if(expectedName !== this.config.appSettings.weekStartsOn)`

### `showLoadingIndicator()`

### `if(scheduleContainer)`

### `hideLoadingIndicator()`

### `if(this._loadingIndicator && this._loadingIndicator.parentNode)`

### `getShiftsToSave()`

### `for(const employeeId in this.data.schedule)`

### `for(const dateKey in this.data.schedule[employeeId])`

### `if(!shift || typeof shift !== 'object')`

### `if(shiftsToSave.length > 500)`

### `getShiftsToSaveInRange(startDate: string, endDate: string)`

### `for(const employeeId in this.data.schedule)`

### `for(const dateKey in this.data.schedule[employeeId])`

### `if(dateObj < startDateObj || dateObj > endDateObj)`

### `if(!shift || typeof shift !== 'object')`

### `saveShiftsInBatches(shifts: any[])`

### `if(shifts.length > 1000)`

### `for(let i = 0; i < shifts.length; i += BATCH_SIZE)`

### `for(let i = 0; i < batches.length; i++)`

### `if(result.success)`

### `if(i < batches.length - 1)`

### `addEmergencyFixButton()`

### `if(this.data.regularAssignments && this.data.regularAssignments.length > 0)`

### `if(window.toastSystem)`

### `if(window.toastSystem)`

### `if(fixedCount > 0)`

### `if(window.toastSystem)`

### `hideEmergencyFixButton()`

### `if(existingButton)`

### `cleanupCorruptedDateKeys()`

### `for(const employeeId in this.data.schedule)`

### `for(const dateKey in this.data.schedule[employeeId])`

### `if(correctedKey)`

### `if(!cleanedSchedule[employeeId][correctedKey])`

### `if(!isDuplicate)`

### `if(cleanedCount > 0)`

### `cleanupDuplicateRegularShifts()`

### `if(!shift.isRegular)`

### `if(shiftsToKeep.length !== shifts.length)`

### `if(totalCleaned > 0)`

### `cleanupInvalidAssignmentIds()`

### `if(matchingAssignment)`

### `if(totalCleaned > 0)`

### `generateUUID()`

### `formatDateToKey(date)`

### `if(typeof date === 'string')`

### `getTodayKey()`

### `analyzeReplacementConflicts(employeeId, dateKey, newPostHours)`

### `if(existingShifts.length === 0)`

### `if(!newShiftTime)`

### `if(newShiftTime.end <= newShiftTime.start)`

### `if(shiftTime)`

### `if(shiftTime.end <= shiftTime.start)`

### `if(hasOverlap)`

### `if(totalHoursWithNew >= 24)`

### `checkTimeOverlap(timeSlot1, timeSlot2)`

### `if(slot.end <= slot.start)`

### `for(const s1 of slots1)`

### `for(const s2 of slots2)`

### `if(s1.start < s2.end && s1.end > s2.start)`

### `if(overlapDuration > 0)`

### `sortDatesByWeekOrder(dates)`

### `if(!dateInfo || typeof dateInfo !== 'object')`

### `if(validDates.length === 0)`

### `if(adjustedDayA === adjustedDayB)`

### `clearApplicationCache()`

### `for(let i = 0; i < localStorage.length; i++)`

### `for(let i = 0; i < sessionStorage.length; i++)`

### `if('caches' in window)`

### `loadWeekData(weekKey)`

### `preloadAdjacentWeeks()`

### `loadWeekDataSilent(weekKey)`

### `getWeekStartDate(year, week)`

### `getWeekKeyWithOffset(baseWeekKey, offset)`

### `generateWeekKey(date = new Date()`

### `parseWeekKey(weekKey)`

### `handleRegularDragStart(e)`

### `if(!assignmentId)`

### `handleRegularDragEnd(e)`

### `highlightEmployeeDropZones(highlight)`

### `if(highlight)`

### `allowRegularAssignmentDrop(e)`

### `if(targetRow)`

### `handleRegularAssignmentDrop(e, targetEmployeeId)`

### `if(!assignmentId)`

### `if(targetRow)`

### `handleRegularShiftMove(shift, sourceEmployeeId, sourceDateKey, targetEmployeeId, targetDateKey)`

### `if(!punctualReplacement.postId)`

### `if(this.config.standardPosts.length > 0)`

### `if(dayIndex === -1)`

### `handleIndividualShiftMove(shift, targetEmployeeId, targetDateKey)`

### `if(!this.data.schedule[targetEmployeeId])`

### `if(!this.data.schedule[targetEmployeeId][targetDateKey])`

### `if(!shiftExists)`

### `trackRegularAssignmentFragmentation(assignmentId, employeeId, dateKey)`

### `if(!this.data.fragmentationTracking)`

### `if(!this.data.fragmentationTracking[assignmentId])`

### `if(tracking.fragmentedDates.length > 7)`

### `promptForAssignmentMaintenance(assignmentId, employeeId, tracking)`

### `if(!assignment || !employee || !post)`

### `if(window.toastSystem?.confirm)`

### `if(confirmed)`

### `if(choice)`

### `showMaintenanceReasonDialog(assignmentId, employeeId, tracking)`

### `if(choiceIndex >= 0 && choiceIndex < reasons.length)`

### `if(choiceIndex === reasons.length - 1)`

### `if(customReason)`

### `saveMaintenanceReason(assignmentId, employeeId, reason, tracking)`

### `if(!this.data.maintenanceRecords)`

### `disableRegularAssignment(assignmentId, employeeId, reason)`

### `if(assignment)`

### `removeFutureRegularShifts(assignmentId)`

### `for(const employeeId in this.data.schedule)`

### `for(const dateKey in this.data.schedule[employeeId])`

### `if(dateKey >= today)`

### `if(shift.isRegular && shift.assignmentId === assignmentId)`

### `if(filteredShifts.length === 0)`

### `showRegularAssignmentConfirmationMenu(assignmentId, targetEmployeeId, referenceDate = null)`

### `if(!assignment || !targetEmployee || !sourceEmployee || !post)`

### `if(e.key === 'Escape')`

### `if(e.target === modal)`

### `handlePermanentRegularAssignmentChange(assignmentId, newEmployeeId, startDate = null, shouldDisableOriginal = false)`

### `showDateSelectionForReplacement(assignmentId, newEmployeeId, startDate = null)`

### `if(!assignment || !targetEmployee || !sourceEmployee || !post)`

### `for(let i = 0; i < 28; i++)`

### `if(isInRange)`

### `if(this.data?.schedule?.[newEmployeeId]?.[dateKey])`

### `if(advancedResult && typeof advancedResult === 'object')`

### `if(safeDateInfo)`

### `if(availableDates.length > 0)`

### `if(typeof this.sortDatesByWeekOrder === 'function')`

### `if(confirmBtn)`

### `if(selectedDates.length > 0)`

### `for(const dateKey of selectedDates)`

### `if(successCount > 0 && errorCount === 0)`

### `if(e.key === 'Escape')`

### `executeReplacementForDate(assignmentId, newEmployeeId, dateKey)`

### `if(!assignment || !targetEmployee || !post)`

### `if(!assignment.excludedDates)`

### `if(regularShift)`

### `if(sourceShifts.length === 0)`

### `if(!updateResult.success)`

### `if(saveResult && saveResult.success)`

### `if(excludedIndex !== -1)`

### `reassignRegularAssignment(assignmentId, newEmployeeId, startDate = null)`

### `if(assignment)`

### `if(!result.success)`

### `reassignRegularAssignmentFromDate(assignmentId, newEmployeeId, fromDateKey, shouldDisableOriginal = false)`

### `if(!assignment)`

### `if(shouldDisableOriginal)`

### `if(!updateResult.success)`

### `if(!createResult.success)`

### `if(assignmentIndex !== -1)`

### `if(this.data.schedule[employeeId][dateKey])`

### `if(this.data.schedule[employeeId][dateKey].length === 0)`

### `if(this._appliedWeeksCache)`

### `updateShiftsForDateBasedReassignment(oldAssignmentId, newAssignmentId, oldEmployeeId, newEmployeeId, fromDateKey)`

### `if(this.data.schedule[oldEmployeeId])`

### `for(const dateKey in this.data.schedule[oldEmployeeId])`

### `if(shiftDate >= fromDate)`

### `if(!this.data.schedule[newEmployeeId])`

### `if(!this.data.schedule[newEmployeeId][dateKey])`

### `if(shifts.length === 0)`

### `optimisticRegularAssignmentMove(assignmentId, newEmployeeId)`

### `if(assignment)`

### `rollbackRegularAssignmentMove(assignmentId)`

### `if(assignment && assignment._oldEmployeeId)`

### `updateGeneratedShiftsForAssignment(assignmentId, newEmployeeId)`

### `for(const employeeId in this.data.schedule)`

### `for(const dateKey in this.data.schedule[employeeId])`

### `if(!this.data.schedule[newEmployeeId])`

### `if(!this.data.schedule[newEmployeeId][dateKey])`

### `if(shifts.length === 0)`

### `cleanupRegularShiftsAfterDate(assignmentId, employeeId, fromDateKey)`

### `if(this.data.schedule[employeeId])`

### `for(const dateKey in this.data.schedule[employeeId])`

### `if(shiftDate >= fromDate)`

### `if(regularShiftsToClean.length > 0)`

### `if(shouldRemove)`

### `if(this.data.schedule[employeeId][dateKey].length === 0)`

### `if(debugInfo.length > 0)`

### `cleanupConflictingShiftsForNewAssignment(newAssignmentId, employeeId, fromDateKey)`

### `if(!newAssignment)`

### `if(this.data.schedule[employeeId])`

### `for(const dateKey in this.data.schedule[employeeId])`

### `if(shiftDate >= fromDate)`

### `if(!shouldKeep)`

### `handleReplacementRegularAssignmentChange(assignmentId, newEmployeeId)`

### `showModificationHistory()`

### `renderHistoryTimeline()`

### `if(modifications.length === 0)`

### `renderModificationItem(modification)`

### `renderHistoryCalendar()`

### `renderHistoryStats()`

### `setupHistoryModalEvents(modal)`

### `if(e.key === 'Escape')`

### `getModificationHistory()`

### `calculateModificationStats(modifications)`

### `switch(mod.type)`

### `getTimeAgo(timestamp)`

### `exportModificationHistory()`

### `logModification(type, title, description, additionalData = {})`

### `if(existingHistory.length > 1000)`

### `showGripGuide()`

### `if(interactiveTutorial)`

### `if(e.key === 'Escape')`

### `if(e.target === overlay)`

### `handleConvertToRegular(employeeId, dayIndex, shiftIndex, shiftData)`

### `if(!shiftData.isPunctual || shiftData.isReplacement || shiftData.isRegular)`

### `if(!employee || !post || !day)`

### `showConvertToRegularModal(employeeId, dayIndex, shiftIndex, shiftData, employee, post, day)`

### `if(e.key === 'Escape')`

### `executeConvertToRegular(modal, employeeId, dayIndex, shiftIndex, shiftData, post)`

### `if(selectedDays.length === 0)`

### `if(!startDate)`

### `if(this.data.schedule[employeeId] && this.data.schedule[employeeId][dateKey])`

### `if(this.data.schedule[employeeId][dateKey].length === 0)`

### `if(typeof window !== 'undefined')`

### `if(TeamCalendarApp.ModalManager)`

### `if(TeamCalendarApp.ModalManager)`

### `if(TeamCalendarApp.ModalManager)`

### `if(TeamCalendarApp.ModalManager)`

### `if(TeamCalendarApp.setupPostDragDrop)`

### `if(TeamCalendarApp.setupCentralizedDropZone)`

### `if(elements.length > 0)`

### `if(TeamCalendarApp.ensureDropZonesExist)`

### `if(TeamCalendarApp.createTemporaryDropZones)`

### `if(TeamCalendarApp.ModalManager && TeamCalendarApp.ModalManager.testCompleteWorkingDaysFlow)`

