import fs from 'fs';
import path from 'path';

console.log('🔧 Correction de la structure de l\'objet cassée...');

const teamCalendarAppPath = './src/teamCalendarApp.ts';

try {
  // Lire le fichier
  let content = fs.readFileSync(teamCalendarAppPath, 'utf8');
  
  // Trouver et corriger l'accolade fermante en trop
  const problematicPattern = /}\s*},\s*\n\s*renderSafe:/;
  const correctedPattern = '},\n    \n    renderSafe:';
  
  if (content.includes('},') && content.includes('renderSafe:')) {
    content = content.replace(problematicPattern, correctedPattern);
    console.log('✅ Accolade fermante en trop supprimée');
  }
  
  // Vérifier et corriger la fonction checkDataIntegrity qui manque d'accolade fermante
  const checkDataIntegrityPattern = /checkDataIntegrity:\s*function\(\)\s*\{[\s\S]*?return\s+issues;\s*\n\s*\/\/\s*Fonction utilitaire/;
  const correctedCheckDataIntegrity = `checkDataIntegrity: function() {
        let issues = 0;
        if (!this.data || !this.data.schedule) issues++;
        if (!Array.isArray(this.data.employees)) issues++;
        return issues;
    },
    
    // Fonction utilitaire`;
  
  if (content.includes('checkDataIntegrity: function()')) {
    content = content.replace(checkDataIntegrityPattern, correctedCheckDataIntegrity);
    console.log('✅ Fonction checkDataIntegrity corrigée');
  }
  
  // Écrire le fichier corrigé
  fs.writeFileSync(teamCalendarAppPath, content);
  
  console.log('✅ Structure de l\'objet corrigée avec succès');
  
  // Vérifier que la correction est valide
  const validationContent = fs.readFileSync(teamCalendarAppPath, 'utf8');
  if (!validationContent.includes('},') || validationContent.includes('renderSafe: function()')) {
    console.log('✅ Structure de l\'objet confirmée');
  } else {
    console.log('❌ Structure de l\'objet encore problématique');
  }
  
} catch (error) {
  console.error('❌ Erreur lors de la correction:', error.message);
  process.exit(1);
}

console.log('🎯 Script terminé. Redémarrez le serveur pour appliquer les changements.'); 