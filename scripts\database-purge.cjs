const { Pool } = require('pg');
const readline = require('readline');
const fs = require('fs');

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || '*************',
  database: process.env.DB_NAME || 'glive_db',
  password: process.env.DB_PASSWORD || 'SebbZ12342323!!',
  port: process.env.DB_PORT || 5432,
});

// Noms des employés de base à conserver
const BASE_EMPLOYEE_NAMES = [
  '<PERSON>',
  '<PERSON>', 
  '<PERSON>',
  '<PERSON>',
  '<PERSON>'
];

async function purgeDatabaseData() {
  console.log('🧹 [PURGE] Début de la purge de la base de données...\n');
  
  let client;
  try {
    // Test de connexion
    console.log('🔗 [CONNEXION] Test de connexion à la base...');
    client = await pool.connect();
    console.log('✅ Connexion établie avec succès\n');
    
    // 1. État initial
    console.log('📊 [ÉTAT INITIAL] Vérification des données...');
    const initialShifts = await client.query('SELECT COUNT(*) as total FROM shifts');
    const initialEmployees = await client.query('SELECT COUNT(*) as total FROM employees');
    const initialPosts = await client.query('SELECT COUNT(*) as total FROM standard_posts');
    const initialAssignments = await client.query('SELECT COUNT(*) as total FROM regular_assignments');
    
    console.log(`   📈 Quarts actuels: ${initialShifts.rows[0].total}`);
    console.log(`   👥 Employés actuels: ${initialEmployees.rows[0].total}`);
    console.log(`   🏢 Postes actuels: ${initialPosts.rows[0].total}`);
    console.log(`   📋 Assignements actuels: ${initialAssignments.rows[0].total}`);
    
    // 2. Supprimer TOUS les quarts
    console.log('\n🗑️ [PURGE] Suppression de tous les quarts...');
    const deleteShifts = await client.query('DELETE FROM shifts');
    console.log(`   ✅ ${deleteShifts.rowCount || 0} quarts supprimés`);
    
    // 3. Supprimer les assignements reguliers
    console.log('\n🗑️ [PURGE] Suppression des assignements réguliers...');
    const deleteAssignments = await client.query('DELETE FROM regular_assignments');
    console.log(`   ✅ ${deleteAssignments.rowCount || 0} assignements supprimés`);
    
    // 4. Supprimer les congés
    console.log('\n🗑️ [PURGE] Suppression des congés...');
    const deleteVacations = await client.query('DELETE FROM vacation_periods');
    console.log(`   ✅ ${deleteVacations.rowCount || 0} congés supprimés`);
    
    // 5. Identifier et supprimer les employés en doublon
    console.log('\n🔍 [ANALYSE] Recherche des employés en doublon...');
    const duplicates = await client.query(`
      SELECT name, COUNT(*) as count, array_agg(id) as ids
      FROM employees 
      GROUP BY name 
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `);
    
    console.log(`   🔍 ${duplicates.rows.length} groupes de doublons trouvés`);
    
    let totalDeleted = 0;
    
    for (const duplicate of duplicates.rows) {
      console.log(`   📝 "${duplicate.name}": ${duplicate.count} exemplaires`);
      
      // Garder le premier ID de chaque groupe, supprimer les autres
      const idsToDelete = duplicate.ids.slice(1);
      
      if (idsToDelete.length > 0) {
        const deleteDuplicateEmployees = await client.query(
          'DELETE FROM employees WHERE id = ANY($1)',
          [idsToDelete]
        );
        totalDeleted += deleteDuplicateEmployees.rowCount || 0;
        console.log(`     ✅ ${deleteDuplicateEmployees.rowCount || 0} doublons supprimés`);
      }
    }
    
    // 6. Supprimer les employés qui ne sont PAS dans la liste de base
    console.log('\n🗑️ [PURGE] Suppression des employés non-essentiels...');
    const deleteNonBaseEmployees = await client.query(
      'DELETE FROM employees WHERE name != ALL($1)',
      [BASE_EMPLOYEE_NAMES]
    );
    console.log(`   ✅ ${deleteNonBaseEmployees.rowCount || 0} employés non-essentiels supprimés`);
    totalDeleted += deleteNonBaseEmployees.rowCount || 0;
    
    // 7. Vérifier que les employés de base existent, sinon les recréer
    console.log('\n✅ [VÉRIFICATION] Employés de base restants...');
    const remainingEmployees = await client.query(
      'SELECT id, name, status FROM employees WHERE name = ANY($1) ORDER BY name',
      [BASE_EMPLOYEE_NAMES]
    );
    
    console.log(`   📊 ${remainingEmployees.rows.length} employés de base trouvés`);
    
    // Si certains employés manquent, les recréer
    const existingNames = remainingEmployees.rows.map(emp => emp.name);
    const missingEmployees = BASE_EMPLOYEE_NAMES.filter(name => !existingNames.includes(name));
    
    if (missingEmployees.length > 0) {
      console.log(`\n🔧 [RESTAURATION] Recréation de ${missingEmployees.length} employés manquants...`);
      
      // Récupérer l'ID du template par défaut
      const templateResult = await client.query("SELECT id FROM employee_templates WHERE is_default = true LIMIT 1");
      const templateId = templateResult.rows[0]?.id;
      
      if (!templateId) {
        console.log('⚠️  Aucun template par défaut trouvé, création sans template');
      }
      
      const employeeData = {
        'Jean Dupont': ['Temps Plein', JSON.stringify({"email": "<EMAIL>", "department": "Production"})],
        'Marie Martin': ['Temps Plein', JSON.stringify({"email": "<EMAIL>", "department": "Administration"})],
        'Pierre Durand': ['Temps Partiel', JSON.stringify({"email": "<EMAIL>", "department": "Maintenance"})],
        'Sophie Leblanc': ['Temps Plein', JSON.stringify({"email": "<EMAIL>", "department": "Qualité"})],
        'Lucas Bernard': ['Stagiaire', JSON.stringify({"email": "<EMAIL>", "department": "Logistique"})]
      };
      
      for (const name of missingEmployees) {
        const [status, extraFields] = employeeData[name];
        
        if (templateId) {
          await client.query(`
            INSERT INTO employees (name, status, template_id, extra_fields, avatar_url) 
            VALUES ($1, $2, $3, $4, $5)
          `, [name, status, templateId, extraFields, null]);
        } else {
          await client.query(`
            INSERT INTO employees (name, status, extra_fields, avatar_url) 
            VALUES ($1, $2, $3, $4)
          `, [name, status, extraFields, null]);
        }
        console.log(`     ✅ ${name} recréé`);
      }
    }
    
    // Récupérer la liste finale des employés
    const finalEmployeesList = await client.query(
      'SELECT id, name, status FROM employees ORDER BY name'
    );
    
    console.log('\n👥 [EMPLOYÉS FINAUX] Liste complète:');
    finalEmployeesList.rows.forEach((emp, i) => {
      const isBase = BASE_EMPLOYEE_NAMES.includes(emp.name);
      const marker = isBase ? '🛡️' : '❓';
      console.log(`   ${i+1}. ${marker} ${emp.name} (${emp.status}) - ID: ${emp.id.substring(0, 8)}...`);
    });
    
    // 8. Reset des paramètres d'application
    console.log('\n⚙️ [RESET] Remise à zéro des paramètres...');
    await client.query('DELETE FROM app_settings');
    await client.query(`
      INSERT INTO app_settings (setting_key, setting_value, description) VALUES
      ('weekStartsOn', '"sunday"', 'Premier jour de la semaine'),
      ('weekStartDay', '0', 'Premier jour de la semaine (numérique)'),
      ('viewMode', '"week"', 'Mode d''affichage par défaut'),
      ('currentWeekOffset', '0', 'Décalage de semaine actuel'),
      ('appVersion', '"1.0.0"', 'Version de l''application'),
      ('lastBackup', 'null', 'Timestamp de la dernière sauvegarde')
    `);
    console.log('   ✅ 6 paramètres d\'application réinitialisés');
    
    // 9. Reset des postes standards
    console.log('\n🏢 [RESET] Remise à zéro des postes standards...');
    await client.query('DELETE FROM standard_posts');
    
    const posts = [
      ['Poste Matin', '#0ea5e9', '08:00-16:00', 8, 'standard', null, JSON.stringify([1, 2, 3, 4, 5])],
      ['Poste Soir', '#f59e0b', '16:00-00:00', 8, 'standard', null, JSON.stringify([1, 2, 3, 4, 5])],
      ['Poste Nuit', '#6366f1', '00:00-08:00', 8, 'night', 'night', JSON.stringify([1, 2, 3, 4, 5])],
      ['Poste WE1', '#10b981', '00:00-12:00', 12, 'weekend', 'weekend', JSON.stringify([0, 6])],
      ['Poste WE2', '#f43f5e', '12:00-24:00', 12, 'weekend', 'weekend', JSON.stringify([0, 6])]
    ];
    
    for (const [label, color, hours, duration, type, category, working_days] of posts) {
      await client.query(`
        INSERT INTO standard_posts (label, color, hours, duration, type, category, working_days) 
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [label, color, hours, duration, type, category, working_days]);
    }
    console.log('   ✅ 5 postes standards de base recréés');
    
    // 10. État final
    console.log('\n📊 [ÉTAT FINAL] Vérification après purge...');
    const finalShifts = await client.query('SELECT COUNT(*) as total FROM shifts');
    const finalEmployees = await client.query('SELECT COUNT(*) as total FROM employees');
    const finalPosts = await client.query('SELECT COUNT(*) as total FROM standard_posts');
    const finalSettings = await client.query('SELECT COUNT(*) as total FROM app_settings');
    const finalAssignmentsFinal = await client.query('SELECT COUNT(*) as total FROM regular_assignments');
    
    console.log(`   📈 Quarts restants: ${finalShifts.rows[0].total}`);
    console.log(`   👥 Employés restants: ${finalEmployees.rows[0].total}`);
    console.log(`   🏢 Postes restants: ${finalPosts.rows[0].total}`);
    console.log(`   ⚙️ Paramètres restants: ${finalSettings.rows[0].total}`);
    console.log(`   📋 Assignements restants: ${finalAssignmentsFinal.rows[0].total}`);
    
    // 11. Résumé final
    const summary = {
      timestamp: new Date().toISOString(),
      initial: {
        shifts: parseInt(initialShifts.rows[0].total),
        employees: parseInt(initialEmployees.rows[0].total),
        posts: parseInt(initialPosts.rows[0].total),
        assignments: parseInt(initialAssignments.rows[0].total)
      },
      final: {
        shifts: parseInt(finalShifts.rows[0].total),
        employees: parseInt(finalEmployees.rows[0].total),
        posts: parseInt(finalPosts.rows[0].total),
        settings: parseInt(finalSettings.rows[0].total),
        assignments: parseInt(finalAssignmentsFinal.rows[0].total)
      },
      deleted: {
        shifts: parseInt(initialShifts.rows[0].total),
        employees: totalDeleted,
        duplicates_removed: totalDeleted
      }
    };
    
    console.log('\n✅ [RÉSUMÉ] Purge terminée avec succès!');
    console.log(`   🗑️ ${summary.deleted.shifts} quarts supprimés`);
    console.log(`   👥 ${summary.deleted.employees} employés supprimés`);
    console.log(`   ✅ ${summary.final.employees} employés conservés`);
    console.log(`   🏢 ${summary.final.posts} postes standards`);
    console.log(`   ⚙️ ${summary.final.settings} paramètres d'application`);
    console.log(`   📋 ${summary.final.assignments} assignements restants`);
    
    // Sauvegarder le rapport
    const reportFile = `purge-report-${Date.now()}.json`;
    fs.writeFileSync(reportFile, JSON.stringify(summary, null, 2));
    console.log(`\n📄 Rapport sauvegardé: ${reportFile}`);
    
    return summary;
    
  } catch (error) {
    console.error('\n❌ [ERREUR] Purge échouée:', error.message);
    console.error('Stack trace:', error.stack);
    throw error;
  } finally {
    if (client) {
      client.release();
      console.log('\n🔒 Connexion fermée');
    }
    await pool.end();
  }
}

// Fonction pour purge automatique (sans confirmation)
async function purgeAuto() {
  console.log('🤖 [AUTO-PURGE] Purge automatique sans confirmation...\n');
  return await purgeDatabaseData();
}

// Point d'entrée principal
async function main() {
  console.log('🧹 SCRIPT DE PURGE DE LA BASE DE DONNÉES 🧹');
  console.log('⚠️  Ce script va supprimer TOUTES les données sauf les 5 employés de base');
  console.log('📋 Employés qui seront conservés:');
  BASE_EMPLOYEE_NAMES.forEach((name, index) => {
    console.log(`   ${index + 1}. ${name}`);
  });
  console.log('');
  
  // Vérifier si on est en mode automatique
  const isAuto = process.argv.includes('--auto') || process.argv.includes('-a');
  
  if (isAuto) {
    console.log('🤖 Mode automatique détecté, purge sans confirmation...\n');
    try {
      await purgeAuto();
      process.exit(0);
    } catch (error) {
      console.error('❌ Erreur lors de la purge automatique:', error.message);
      process.exit(1);
    }
  } else {
    // ✅ MODIFICATION : Purge automatique par défaut sans confirmation
    console.log('🚀 Démarrage de la purge automatique...\n');
    try {
      await purgeDatabaseData();
      console.log('\n🎉 Purge terminée avec succès!');
      process.exit(0);
    } catch (error) {
      console.error('\n❌ Erreur lors de la purge:', error.message);
      process.exit(1);
    }
  }
}

// Exécution si appelé directement
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Erreur fatale:', error.message);
    process.exit(1);
  });
}

module.exports = { purgeDatabaseData, purgeAuto }; 