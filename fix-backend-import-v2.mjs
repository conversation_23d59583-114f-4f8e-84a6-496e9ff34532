import { readFile, writeFile } from 'fs/promises';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const TARGET_FILE = join(__dirname, 'server', 'app.js');
const LOG_PREFIX = '🔧 [fix-backend-import-v2]';

async function applyBackendImportFix() {
    console.log(`${LOG_PREFIX} Démarrage du correctif d'import backend...`);
    try {
        let content = await readFile(TARGET_FILE, 'utf-8');

        // Bloc d'import correct et complet
        const correctImportBlock = `import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createRequire } from 'module';

const require = createRequire(import.meta.url);`;

        // Anciennes tentatives ou imports incorrects à nettoyer
        const patternsToRemove = [
            /const \{ safeWrite \} = require\('\.\/utils\/safeWrite.cjs'\);/g,
            /import \{ createRequire \} from 'module';\s*const require = createRequire\(import.meta.url\);/g,
            /import helmet from 'helmet';/g,
            /import { createRequire } from 'module';/g
        ];

        if (content.includes("const { safeWrite } = require('./utils/safeWrite.cjs');")) {
             console.log(`${LOG_PREFIX} L'import de safeWrite est déjà présent, mais peut-être mal placé. Nettoyage...`);
        }

        // Nettoyer les anciens imports pour éviter les doublons
        patternsToRemove.forEach(pattern => {
            content = content.replace(pattern, '');
        });

        // Supprimer les lignes vides excessives
        content = content.replace(/\n{3,}/g, '\n\n');

        // Placer le bloc d'import correct au tout début du fichier
        content = `${correctImportBlock}\n\n${content}`;
        
        await writeFile(TARGET_FILE, content, 'utf-8');
        console.log(`${LOG_PREFIX} ✅ Correction appliquée avec succès à server/app.js.`);

    } catch (error) {
        console.error(`${LOG_PREFIX} ❌ Erreur lors de l'application du correctif:`, error);
        process.exit(1);
    }
}

applyBackendImportFix(); 