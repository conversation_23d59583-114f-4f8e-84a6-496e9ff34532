#!/usr/bin/env node

/**
 * Correction finale de tous les problèmes restants
 */

import fs from 'fs';

console.log('🔧 [FIX-FINAL-ISSUES] Correction finale de tous les problèmes...\n');

// Correction 1: <PERSON><PERSON><PERSON>er complètement test-dom-browser.js
const testDomContent = `// Script de vérification DOM pour le navigateur
console.log('🔍 [BROWSER-TEST] Vérification DOM...');

function checkDOMStructure() {
    const results = { passed: [], failed: [] };
    // Vérifier les conteneurs requis
    const containers = [
        { id: 'employee-list-container', name: 'Conteneur des employés' },
        { id: 'available-posts-container', name: 'Conteneur des postes' }
    ];
    containers.forEach(container => {
        const element = document.getElementById(container.id);
        if (element) {
            results.passed.push(\`✅ \${container.name} (\${container.id})\`);
        } else {
            results.failed.push(\`❌ \${container.name} (\${container.id}) manquant\`);
        }
    });
    // Vérifier les éléments dynamiques
    const dynamicElements = [
        { selector: '.employee-row', name: 'Lignes demployés' },
        { selector: '.post-row-info', name: 'Informations des postes' }
    ];
    dynamicElements.forEach(item => {
        const elements = document.querySelectorAll(item.selector);
        if (elements.length > 0) {
            results.passed.push(\`✅ \${item.name} (\${elements.length} trouvés)\`);
        } else {
            results.failed.push(\`❌ \${item.name} (aucun trouvé)\`);
        }
    });
    return results;
}

function checkTeamCalendarApp() {
    const results = { passed: [], failed: [] };
    if (window.teamCalendarApp) {
        results.passed.push('✅ Instance TeamCalendarApp disponible');
    } else {
        results.failed.push('❌ Instance TeamCalendarApp manquante');
    }
    if (window.teamCalendarApp && window.teamCalendarApp.verifyAndFixDom) {
        results.passed.push('✅ verifyAndFixDom disponible');
    } else {
        results.failed.push('❌ verifyAndFixDom manquant');
    }
    if (window.teamCalendarApp && window.teamCalendarApp.attachAllEventListeners) {
        results.passed.push('✅ attachAllEventListeners disponible');
    } else {
        results.failed.push('❌ attachAllEventListeners manquant');
    }
    if (window.teamCalendarApp && window.teamCalendarApp.destroy) {
        results.passed.push('✅ destroy disponible');
    } else {
        results.failed.push('❌ destroy manquant');
    }
    if (window.teamCalendarApp && window.teamCalendarApp.setupEmployeeDragDrop) {
        results.passed.push('✅ setupEmployeeDragDrop disponible');
    } else {
        results.failed.push('❌ setupEmployeeDragDrop manquant');
    }
    if (window.teamCalendarApp && window.teamCalendarApp.renderAvailablePosts) {
        results.passed.push('✅ renderAvailablePosts disponible');
    } else {
        results.failed.push('❌ renderAvailablePosts manquant');
    }
    return results;
}

// Exposer les fonctions pour utilisation manuelle
window.checkDOMStructure = checkDOMStructure;
window.checkTeamCalendarApp = checkTeamCalendarApp;

console.log('✅ Fonctions de test disponibles : checkDOMStructure(), checkTeamCalendarApp()');
console.log('🔍 [BROWSER-TEST] Script de test DOM chargé avec succès');
`;

fs.writeFileSync('./public/test-dom-browser.js', testDomContent);
console.log('✅ test-dom-browser.js recréé sans erreurs de syntaxe');

// Correction 2: S'assurer que renderAvailablePosts existe dans teamCalendarApp.ts
const tsPath = './src/teamCalendarApp.ts';
let tsContent = fs.readFileSync(tsPath, 'utf8');

// Vérifier si renderAvailablePosts existe
if (!tsContent.includes('renderAvailablePosts:')) {
    // Ajouter la fonction renderAvailablePosts
    const renderPattern = /(renderEmployees:\s*function\(\)\s*\{[\s\S]*?)(\s*console\.log\('✅ \[renderEmployees\] Employés rendus avec succès'\);\s*\})/;
    if (renderPattern.test(tsContent)) {
        tsContent = tsContent.replace(renderPattern, (match, before, after) => {
            return before + '\n        // ✅ CORRECTION : Rendu des postes disponibles\n        this.renderAvailablePosts();\n        ' + after;
        });
        console.log('✅ renderAvailablePosts() ajouté dans renderEmployees');
    }
}

// S'assurer que setupEmployeeDragDrop est appelé
if (!tsContent.includes('this.setupEmployeeDragDrop()')) {
    const attachPattern = /(attachAllEventListeners:\s*function\(\)\s*\{[\s\S]*?)(\s*console\.log\('✅ \[attachAllEventListeners\] Écouteurs attachés avec succès'\);\s*\})/;
    if (attachPattern.test(tsContent)) {
        tsContent = tsContent.replace(attachPattern, (match, before, after) => {
            return before + '\n        // ✅ CORRECTION : Configuration du drag & drop des employés\n        this.setupEmployeeDragDrop();\n        ' + after;
        });
        console.log('✅ setupEmployeeDragDrop() ajouté dans attachAllEventListeners');
    }
}

fs.writeFileSync(tsPath, tsContent);

// Correction 3: S'assurer que les éléments DOM existent dans le HTML
const htmlPath = './index.html';
let htmlContent = fs.readFileSync(htmlPath, 'utf8');

// Ajouter addPostButton s'il n'existe pas
if (!htmlContent.includes('add-post-btn')) {
    const bodyPattern = /(<body[^>]*>)/;
    if (bodyPattern.test(htmlContent)) {
        htmlContent = htmlContent.replace(bodyPattern, (match, bodyTag) => {
            return bodyTag + '\n    <!-- ✅ CORRECTION : Bouton dajout de poste -->\n    <button id="add-post-btn" class="hidden">Ajouter Poste</button>\n';
        });
        console.log('✅ addPostButton ajouté dans le HTML');
    }
}

// Ajouter available-posts-container s'il n'existe pas
if (!htmlContent.includes('available-posts-container')) {
    const bodyPattern = /(<body[^>]*>)/;
    if (bodyPattern.test(htmlContent)) {
        htmlContent = htmlContent.replace(bodyPattern, (match, bodyTag) => {
            return bodyTag + '\n    <!-- ✅ CORRECTION : Conteneur des postes disponibles -->\n    <div id="available-posts-container" class="available-posts-container hidden"></div>\n';
        });
        console.log('✅ available-posts-container ajouté dans le HTML');
    }
}

fs.writeFileSync(htmlPath, htmlContent);

console.log('\n✅ Corrections finales appliquées !');
console.log('\n📋 Corrections appliquées :');
console.log('✅ 1. test-dom-browser.js recréé sans erreurs de syntaxe');
console.log('✅ 2. Fonctions checkDOMStructure() et checkTeamCalendarApp() disponibles');
console.log('✅ 3. renderAvailablePosts() ajouté dans renderEmployees');
console.log('✅ 4. setupEmployeeDragDrop() ajouté dans attachAllEventListeners');
console.log('✅ 5. Éléments DOM ajoutés dans le HTML');

console.log('\n🚀 Instructions pour tester :');
console.log('1. Redémarrer le serveur : npm run dev');
console.log('2. Ouvrir http://localhost:5173');
console.log('3. Dans la console, exécuter :');
console.log('   - checkDOMStructure()');
console.log('   - checkTeamCalendarApp()');
console.log('   - teamCalendarApp.setupEmployeeDragDrop()');
console.log('   - teamCalendarApp.renderAvailablePosts()');

console.log('\n✅ [FIX-FINAL-ISSUES] Corrections terminées !'); 