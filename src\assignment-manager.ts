// Gestionnaire simplifié pour les attributions régulières

import { DateUtils, DayData } from './date-utils';
import { StandardPost } from './calendar-config';

export interface RegularAssignment {
    id: string;
    employeeId: string;
    postId: string;
    selectedDays: number[]; // Jours de la semaine (0=dimanche, 1=lundi, etc.)
    startDate?: string; // Format YYYY-MM-DD
    endDate?: string; // Format YYYY-MM-DD
    isLimited: boolean;
}

export interface ShiftData {
    postId: string;
    text: string;
    type: string;
    isRegular: boolean;
    assignmentId?: string;
    dateKey?: string;
}

export class AssignmentManager {
    private assignments: RegularAssignment[] = [];
    private schedule: Record<string, Record<string, ShiftData[]>> = {}; // employeeId -> dateKey -> shifts
    
    constructor() {}
    
    /**
     * Ajoute une attribution régulière
     */
    addRegularAssignment(assignment: RegularAssignment): void {
        // Valider l'attribution
        if (!this.validateAssignment(assignment)) {
            throw new Error('Attribution invalide');
        }
        
        // Ajouter l'attribution
        this.assignments.push(assignment);
    }
    
    /**
     * Supprime une attribution régulière
     */
    removeRegularAssignment(assignmentId: string): void {
        this.assignments = this.assignments.filter(a => a.id !== assignmentId);
        
        // Supprimer les shifts associés
        this.removeShiftsByAssignmentId(assignmentId);
    }
    
    /**
     * Obtient toutes les attributions régulières
     */
    getRegularAssignments(): RegularAssignment[] {
        return [...this.assignments];
    }
    
    /**
     * Applique les attributions régulières pour une semaine donnée
     */
    applyAssignmentsForWeek(days: DayData[], posts: StandardPost[]): number {
        let appliedCount = 0;
        
        for (const assignment of this.assignments) {
            const post = posts.find(p => p.id === assignment.postId);
            if (!post) continue;
            
            // Définir les jours par défaut si manquants
            if (!assignment.selectedDays || assignment.selectedDays.length === 0) {
                assignment.selectedDays = post.category === 'weekend' ? [0, 6] : [1, 2, 3, 4, 5];
            }
            
            for (const day of days) {
                if (this.shouldApplyAssignmentToDay(assignment, post, day)) {
                    if (this.addShiftForDay(assignment, post, day)) {
                        appliedCount++;
                    }
                }
            }
        }
        
        return appliedCount;
    }
    
    /**
     * Vérifie si une attribution doit être appliquée à un jour donné
     */
    private shouldApplyAssignmentToDay(assignment: RegularAssignment, post: StandardPost, day: DayData): boolean {
        // Vérifier si le jour est sélectionné
        if (!assignment.selectedDays.includes(day.date.getDay())) {
            return false;
        }
        
        // Vérifier les jours de travail autorisés
        if (post.workingDays && !post.workingDays.includes(day.date.getDay())) {
            return false;
        }
        
        // Vérifier les postes de week-end
        if (post.category === 'weekend' && !day.isWeekend) {
            return false;
        }
        
        if (day.isWeekend && post.category !== 'weekend' && !post.workingDays) {
            return false;
        }
        
        // Vérifier les dates de début et fin
        if (assignment.startDate && day.date < DateUtils.parseDateKey(assignment.startDate)) {
            return false;
        }
        
        if (assignment.isLimited && assignment.endDate && day.date > DateUtils.parseDateKey(assignment.endDate)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Ajoute un shift pour un jour donné
     */
    private addShiftForDay(assignment: RegularAssignment, post: StandardPost, day: DayData): boolean {
        const dateKey = day.dateKey;
        const existingShifts = this.getShiftsForEmployeeAndDate(assignment.employeeId, dateKey);
        
        // Vérifier les doublons
        const hasRegularShift = existingShifts.some(shift => 
            (shift.assignmentId === assignment.id && shift.isRegular) ||
            (shift.postId === assignment.postId && shift.isRegular)
        );
        
        if (hasRegularShift) {
            return false; // Shift déjà existant
        }
        
        // Créer le shift
        const shiftData: ShiftData = {
            postId: post.id,
            text: post.hours,
            type: post.type,
            isRegular: true,
            assignmentId: assignment.id,
            dateKey: dateKey
        };
        
        // Ajouter le shift
        this.addShift(assignment.employeeId, dateKey, shiftData);
        return true;
    }
    
    /**
     * Ajoute un shift au planning
     */
    addShift(employeeId: string, dateKey: string, shiftData: ShiftData): void {
        if (!this.schedule[employeeId]) {
            this.schedule[employeeId] = {};
        }
        
        if (!this.schedule[employeeId][dateKey]) {
            this.schedule[employeeId][dateKey] = [];
        }
        
        this.schedule[employeeId][dateKey].push(shiftData);
    }
    
    /**
     * Supprime un shift du planning
     */
    removeShift(employeeId: string, dateKey: string, shiftIndex: number): void {
        const shifts = this.schedule[employeeId]?.[dateKey];
        if (shifts && shiftIndex >= 0 && shiftIndex < shifts.length) {
            shifts.splice(shiftIndex, 1);
            
            // Supprimer le jour s'il n'y a plus de shifts
            if (shifts.length === 0) {
                delete this.schedule[employeeId][dateKey];
            }
        }
    }
    
    /**
     * Obtient les shifts pour un employé et une date
     */
    getShiftsForEmployeeAndDate(employeeId: string, dateKey: string): ShiftData[] {
        return this.schedule[employeeId]?.[dateKey] || [];
    }
    
    /**
     * Obtient tout le planning
     */
    getSchedule(): Record<string, Record<string, ShiftData[]>> {
        return { ...this.schedule };
    }
    
    /**
     * Définit le planning complet
     */
    setSchedule(schedule: Record<string, Record<string, ShiftData[]>>): void {
        this.schedule = { ...schedule };
    }
    
    /**
     * Supprime tous les shifts associés à une attribution
     */
    private removeShiftsByAssignmentId(assignmentId: string): void {
        for (const employeeId in this.schedule) {
            for (const dateKey in this.schedule[employeeId]) {
                this.schedule[employeeId][dateKey] = this.schedule[employeeId][dateKey].filter(
                    shift => shift.assignmentId !== assignmentId
                );
                
                // Supprimer le jour s'il n'y a plus de shifts
                if (this.schedule[employeeId][dateKey].length === 0) {
                    delete this.schedule[employeeId][dateKey];
                }
            }
        }
    }
    
    /**
     * Valide une attribution
     */
    private validateAssignment(assignment: RegularAssignment): boolean {
        // Vérifier les champs obligatoires
        if (!assignment.id || !assignment.employeeId || !assignment.postId) {
            return false;
        }
        
        // Vérifier les jours sélectionnés
        if (!assignment.selectedDays || !Array.isArray(assignment.selectedDays)) {
            return false;
        }
        
        // Vérifier que les jours sont valides (0-6)
        if (assignment.selectedDays.some(day => day < 0 || day > 6)) {
            return false;
        }
        
        // Vérifier les dates si limitées
        if (assignment.isLimited) {
            if (!assignment.endDate) {
                return false;
            }
            
            if (assignment.startDate && assignment.endDate) {
                const startDate = DateUtils.parseDateKey(assignment.startDate);
                const endDate = DateUtils.parseDateKey(assignment.endDate);
                
                if (startDate >= endDate) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * Nettoie les attributions invalides
     */
    cleanupAssignments(validEmployeeIds: string[], validPostIds: string[]): number {
        const initialCount = this.assignments.length;
        
        this.assignments = this.assignments.filter(assignment => {
            // Vérifier que l'employé et le poste existent
            const hasValidEmployee = validEmployeeIds.includes(assignment.employeeId);
            const hasValidPost = validPostIds.includes(assignment.postId);
            
            return hasValidEmployee && hasValidPost && this.validateAssignment(assignment);
        });
        
        return initialCount - this.assignments.length;
    }
    
    /**
     * Exporte les données
     */
    exportData(): { assignments: RegularAssignment[]; schedule: Record<string, Record<string, ShiftData[]>> } {
        return {
            assignments: [...this.assignments],
            schedule: { ...this.schedule }
        };
    }
    
    /**
     * Importe les données
     */
    importData(data: { assignments?: RegularAssignment[]; schedule?: Record<string, Record<string, ShiftData[]>> }): void {
        if (data.assignments) {
            this.assignments = data.assignments.filter(a => this.validateAssignment(a));
        }
        
        if (data.schedule) {
            this.schedule = data.schedule;
        }
    }
}
