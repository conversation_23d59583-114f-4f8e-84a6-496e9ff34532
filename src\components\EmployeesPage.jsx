import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Users, 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  Filter,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Award,
  Clock,
  Save,
  X,
  ChevronLeft
} from 'lucide-react';
import { Dialog, ConfirmDialog } from './ui/Dialog';
import { useLogs } from '../hooks/useLogs';

/**
 * Composant Page de gestion des employés
 */
const EmployeesPage = ({ 
  employees = [], 
  onAddEmployee, 
  onUpdateEmployee, 
  onDeleteEmployee,
  onBack,
  isVisible = false 
}) => {
  // États locaux
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [editDialog, setEditDialog] = useState({ isOpen: false, employee: null });
  const [deleteDialog, setDeleteDialog] = useState({ isOpen: false, employeeId: null });
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    status: 'Temps Plein',
    position: '',
    department: '',
    startDate: '',
    salary: '',
    address: ''
  });
  const [formErrors, setFormErrors] = useState({});

  // Hook logs
  const { logInfo, logError, logSuccess } = useLogs();

  // Réinitialiser le formulaire
  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      status: 'Temps Plein',
      position: '',
      department: '',
      startDate: '',
      salary: '',
      address: ''
    });
    setFormErrors({});
  };

  // Validation du formulaire
  const validateForm = () => {
    const errors = {};

    // Validation nom (obligatoire, min 2 caractères)
    if (!formData.name.trim()) {
      errors.name = 'Le nom est obligatoire';
    } else if (formData.name.trim().length < 2) {
      errors.name = 'Le nom doit contenir au moins 2 caractères';
    }

    // Validation email (format valide)
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      errors.email = 'Format d\'email invalide';
    }

    // Validation téléphone (format français)
    const phoneRegex = /^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/;
    if (formData.phone && !phoneRegex.test(formData.phone)) {
      errors.phone = 'Format de téléphone invalide';
    }

    // Validation salaire (nombre positif)
    if (formData.salary && (isNaN(formData.salary) || parseFloat(formData.salary) < 0)) {
      errors.salary = 'Le salaire doit être un nombre positif';
    }

    // Validation date de début
    if (formData.startDate) {
      const startDate = new Date(formData.startDate);
      const now = new Date();
      if (startDate > now) {
        errors.startDate = 'La date de début ne peut pas être dans le futur';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Ouvrir le dialog d'édition
  const openEditDialog = (employee = null) => {
    if (employee) {
      setFormData({ ...formData, ...employee });
      logInfo(`Ouverture modification employé: ${employee.name}`);
    } else {
      resetForm();
      logInfo('Ouverture création nouvel employé');
    }
    setEditDialog({ isOpen: true, employee });
  };

  // Sauvegarder l'employé
  const saveEmployee = async () => {
    if (!validateForm()) {
      logError('Validation formulaire échouée', formErrors);
      return;
    }

    try {
      const employeeData = {
        ...formData,
        id: editDialog.employee?.id || `emp_${Date.now()}`,
        avatar: editDialog.employee?.avatar || `/api/placeholder/40/40`
      };

      if (editDialog.employee) {
        await onUpdateEmployee?.(employeeData);
        logSuccess(`Employé modifié: ${employeeData.name}`);
      } else {
        await onAddEmployee?.(employeeData);
        logSuccess(`Nouvel employé créé: ${employeeData.name}`);
      }

      setEditDialog({ isOpen: false, employee: null });
      resetForm();
    } catch (error) {
      logError('Erreur sauvegarde employé', error);
    }
  };

  // Supprimer un employé
  const deleteEmployee = async () => {
    if (deleteDialog.employeeId) {
      try {
        await onDeleteEmployee?.(deleteDialog.employeeId);
        const employee = employees.find(e => e.id === deleteDialog.employeeId);
        logSuccess(`Employé supprimé: ${employee?.name || deleteDialog.employeeId}`);
        setDeleteDialog({ isOpen: false, employeeId: null });
      } catch (error) {
        logError('Erreur suppression employé', error);
      }
    }
  };

  // Filtrage des employés
  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (employee.email && employee.email.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStatus = filterStatus === 'all' || employee.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="p-6 max-w-7xl mx-auto"
    >
      {/* Header avec bouton retour */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={onBack}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <ChevronLeft className="w-6 h-6 text-gray-600" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Users className="w-8 h-8 text-blue-600" />
                Gestion des Employés
              </h1>
              <p className="text-gray-600 mt-2">
                Gérez votre équipe et leurs informations
              </p>
            </div>
          </div>
          <button
            onClick={() => openEditDialog()}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-5 h-5" />
            Nouvel Employé
          </button>
        </div>
      </div>

      {/* Filtres */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Rechercher un employé..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">Tous les statuts</option>
          <option value="Temps Plein">Temps Plein</option>
          <option value="Temps Partiel">Temps Partiel</option>
          <option value="Contractuel">Contractuel</option>
          <option value="Stagiaire">Stagiaire</option>
        </select>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center gap-3">
            <Users className="w-8 h-8 text-blue-600" />
            <div>
              <div className="text-2xl font-bold text-blue-900">{employees.length}</div>
              <div className="text-blue-600 text-sm">Total</div>
            </div>
          </div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <div className="flex items-center gap-3">
            <Clock className="w-8 h-8 text-green-600" />
            <div>
              <div className="text-2xl font-bold text-green-900">
                {employees.filter(e => e.status === 'Temps Plein').length}
              </div>
              <div className="text-green-600 text-sm">Temps Plein</div>
            </div>
          </div>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <div className="flex items-center gap-3">
            <Award className="w-8 h-8 text-yellow-600" />
            <div>
              <div className="text-2xl font-bold text-yellow-900">
                {employees.filter(e => e.status === 'Temps Partiel').length}
              </div>
              <div className="text-yellow-600 text-sm">Temps Partiel</div>
            </div>
          </div>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg">
          <div className="flex items-center gap-3">
            <Filter className="w-8 h-8 text-purple-600" />
            <div>
              <div className="text-2xl font-bold text-purple-900">{filteredEmployees.length}</div>
              <div className="text-purple-600 text-sm">Filtrés</div>
            </div>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow border overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Employé</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Contact</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Statut</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Poste</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            <AnimatePresence>
              {filteredEmployees.map((employee, index) => (
                <motion.tr
                  key={employee.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.05 }}
                  className="hover:bg-gray-50"
                >
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <img 
                        src={employee.avatar} 
                        alt={employee.name}
                        className="w-10 h-10 rounded-full mr-3"
                      />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{employee.name}</div>
                        {employee.department && (
                          <div className="text-sm text-gray-500">{employee.department}</div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="space-y-1">
                      {employee.email && (
                        <div className="flex items-center text-sm text-gray-600">
                          <Mail className="w-4 h-4 mr-2" />
                          {employee.email}
                        </div>
                      )}
                      {employee.phone && (
                        <div className="flex items-center text-sm text-gray-600">
                          <Phone className="w-4 h-4 mr-2" />
                          {employee.phone}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      employee.status === 'Temps Plein' ? 'bg-green-100 text-green-800' :
                      employee.status === 'Temps Partiel' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {employee.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {employee.position || 'Non défini'}
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex gap-2">
                      <button
                        onClick={() => openEditDialog(employee)}
                        className="text-blue-600 hover:text-blue-800 p-2 rounded hover:bg-blue-50"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => setDeleteDialog({ isOpen: true, employeeId: employee.id })}
                        className="text-red-600 hover:text-red-800 p-2 rounded hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </AnimatePresence>
          </tbody>
        </table>
      </div>

      {/* Dialog d'édition */}
      <Dialog
        isOpen={editDialog.isOpen}
        onClose={() => setEditDialog({ isOpen: false, employee: null })}
        title={editDialog.employee ? 'Modifier l\'employé' : 'Nouvel employé'}
        className="max-w-2xl"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Nom complet *</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${
                  formErrors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Jean Dupont"
              />
              {formErrors.name && <p className="text-red-500 text-xs mt-1">{formErrors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${
                  formErrors.email ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="<EMAIL>"
              />
              {formErrors.email && <p className="text-red-500 text-xs mt-1">{formErrors.email}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Téléphone</label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${
                  formErrors.phone ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="01 23 45 67 89"
              />
              {formErrors.phone && <p className="text-red-500 text-xs mt-1">{formErrors.phone}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Statut</label>
              <select
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="Temps Plein">Temps Plein</option>
                <option value="Temps Partiel">Temps Partiel</option>
                <option value="Contractuel">Contractuel</option>
                <option value="Stagiaire">Stagiaire</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Poste</label>
              <input
                type="text"
                value={formData.position}
                onChange={(e) => setFormData(prev => ({ ...prev, position: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="Développeur Frontend"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Département</label>
              <input
                type="text"
                value={formData.department}
                onChange={(e) => setFormData(prev => ({ ...prev, department: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="Informatique"
              />
            </div>
          </div>

          <div className="flex gap-3 justify-end pt-4 border-t">
            <button
              onClick={() => setEditDialog({ isOpen: false, employee: null })}
              className="px-4 py-2 text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg"
            >
              Annuler
            </button>
            <button
              onClick={saveEmployee}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-lg"
            >
              <Save className="w-4 h-4" />
              Sauvegarder
            </button>
          </div>
        </div>
      </Dialog>

      {/* Dialog de suppression */}
      <ConfirmDialog
        isOpen={deleteDialog.isOpen}
        onClose={() => setDeleteDialog({ isOpen: false, employeeId: null })}
        onConfirm={deleteEmployee}
        title="Supprimer l'employé"
        message="Êtes-vous sûr ? Cette action supprimera aussi tous les horaires associés."
        confirmText="Supprimer"
        cancelText="Annuler"
        variant="danger"
      />
    </motion.div>
  );
};

export default EmployeesPage; 