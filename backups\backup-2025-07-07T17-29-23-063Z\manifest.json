{"timestamp": "2025-07-07T17:29:23.097Z", "backupName": "backup-2025-07-07T17-29-23-063Z", "description": "Sauvegarde automatique avant corrections drag & drop", "files": [{"path": "index.html", "size": 1780, "modified": "2025-07-07T16:35:30.888Z"}, {"path": "package-lock.json", "size": 456737, "modified": "2025-07-03T17:44:18.897Z"}, {"path": "package.json", "size": 4581, "modified": "2025-07-03T17:38:15.971Z"}, {"path": "postcss.config.cjs", "size": 82, "modified": "2025-06-11T19:27:48.560Z"}, {"path": "server\\app.js", "size": 39969, "modified": "2025-07-07T15:24:11.680Z"}, {"path": "server\\config\\database.js", "size": 2068, "modified": "2025-06-18T14:04:50.793Z"}, {"path": "server\\config\\logger.js", "size": 7430, "modified": "2025-07-07T15:24:15.440Z"}, {"path": "src\\Agenda.tsx", "size": 35979, "modified": "2025-07-07T15:24:09.377Z"}, {"path": "src\\api.ts", "size": 16969, "modified": "2025-06-30T19:30:43.366Z"}, {"path": "src\\main.tsx", "size": 2525, "modified": "2025-07-04T14:36:32.861Z"}, {"path": "src\\styles\\fonts.css", "size": 2584, "modified": "2025-06-19T00:07:25.132Z"}, {"path": "src\\styles\\fullscreen.css", "size": 10452, "modified": "2025-07-07T16:41:49.155Z"}, {"path": "src\\styles\\interactive-tutorial.css", "size": 8724, "modified": "2025-06-25T15:31:20.328Z"}, {"path": "src\\styles\\regular-assignment-confirmation.css", "size": 4892, "modified": "2025-06-25T15:31:20.328Z"}, {"path": "src\\styles\\regular-assignment-drag.css", "size": 3172, "modified": "2025-06-25T15:31:20.328Z"}, {"path": "src\\teamCalendarApp.ts", "size": 749676, "modified": "2025-07-07T16:36:05.338Z"}, {"path": "tailwind.config.cjs", "size": 184, "modified": "2025-06-11T19:27:48.560Z"}, {"path": "tsconfig.json", "size": 1100, "modified": "2025-06-11T19:27:48.560Z"}, {"path": "vite.config.ts", "size": 1784, "modified": "2025-06-16T16:29:14.386Z"}], "gitInfo": {"branch": "Nouvelle-version-après-perte", "commit": "9d492784943ea63c943fc6cd0f42fb86d3a5f5fd", "hasUncommittedChanges": true, "status": "M fix-apostrophes.mjs\n M index.html\n M public/fix-employee-drag-conflicts.js\n M server/app.js\n M src/Agenda.tsx\n M src/main.tsx\n M src/pages/Logs.tsx\n M src/styles/fullscreen.css\n M src/teamCalendarApp.ts\n?? ARCHITECTURE_DETAILS.md\n?? CORRECTION_PERSISTANCE_ORDRE_EMPLOYES.md\n?? DOCS_teamCalendarApp.md\n?? DOCUMENTATION_COMPLETE_PROJET.md\n?? FONCTIONNALITES_DETAILS.md\n?? GUIDE_ARCHITECTURE_IA.md\n?? RESOLUTION_CONFLITS_DRAG_DROP_FINAL.md\n?? RESOLUTION_PROBLEME_DRAG_DROP_EMPLOYES.md\n?? RESUME_VALIDATION_CENTRALISEE.md\n?? apply-critical-fixes.mjs\n?? backups/\n?? cleanup-destroy-duplicate.mjs\n?? diagnose-dom-issues.mjs\n?? diagnose-employee-drag-complete.mjs\n?? diagnose-persistence-issue.mjs\n?? extract-functions-docs.mjs\n?? fix-browser-errors.mjs\n?? fix-critical-issues.mjs\n?? fix-destroy-method-v2.mjs\n?? fix-destroy-method.mjs\n?? fix-dom-initialization.mjs\n?? fix-employee-conflicts-simple.mjs\n?? fix-employee-drag-conflicts-final.mjs\n?? fix-final-issues.mjs\n?? fix-function-placement.mjs\n?? fix-missing-elements.mjs\n?? fix-object-structure.mjs\n?? fix-parasite-code.mjs\n?? fix-render-available-posts.mjs\n?? fix-saveFixedShifts-syntax.mjs\n?? fix-structure-final.mjs\n?? fix-structure-teamcalendarapp.mjs\n?? fix-syntax-error.mjs\n?? integration-plan.md\n?? public/test-dom-browser.js\n?? refactor-plan.md\n?? scripts/add-employee-position-logs.mjs\n?? scripts/backup-data.js\n?? scripts/backup-data.mjs\n?? scripts/clean-init-duplicates.mjs\n?? scripts/fix-init-render-duplications.mjs\n?? src/dummyTeamCalendarApp.js\n?? src/services/\n?? src/utils/dateHelpers.ts\n?? src/utils/validation.ts\n?? test-complete-system.mjs\n?? test-critical-functionality.mjs\n?? test-dom-corrections.mjs\n?? test-dom-fixes.mjs\n?? test-employee-drag.mjs\n?? test-final-dom.mjs\n?? test-final-functionality.mjs\n?? test-persistence-fix.mjs\n?? test-utilitaires-centralises.mjs\n?? test-validation-integration.mjs\n?? test-validation-simple.mjs\n?? validation-finale.mjs"}}