// Script de diagnostic pour le drag & drop
console.log('🔧 [DEBUG] Script de diagnostic du drag & drop');

// Fonction pour vérifier les éléments draggables
function checkDraggableElements() {
    console.log('\n🔍 [DEBUG] Vérification des éléments draggables...');
    
    // Vérifier les postes
    const posts = document.querySelectorAll('.post-row-info');
    console.log(`📋 [DEBUG] Postes trouvés: ${posts.length}`);
    
    posts.forEach((post, index) => {
        const postId = post.dataset.postId;
        const isDraggable = post.draggable;
        const hasListeners = post.dataset.dragListenerAttached;
        
        console.log(`  ${index + 1}. Poste ${postId} - Draggable: ${isDraggable}, Listeners: ${hasListeners}`);
    });
    
    // Vérifier les zones de drop
    const dropZones = document.querySelectorAll('[data-employee-id]');
    console.log(`\n🎯 [DEBUG] Zones de drop trouvées: ${dropZones.length}`);
    
    dropZones.forEach((zone, index) => {
        const employeeId = zone.dataset.employeeId;
        console.log(`  ${index + 1}. Zone employé ${employeeId}`);
    });
}

// Fonction pour simuler un drag & drop
function simulateDragDrop() {
    console.log('\n🚀 [DEBUG] Simulation d\'un drag & drop...');
    
    const firstPost = document.querySelector('.post-row-info[draggable="true"]');
    const firstDropZone = document.querySelector('[data-employee-id]');
    
    if (!firstPost) {
        console.error('❌ [DEBUG] Aucun poste draggable trouvé');
        return;
    }
    
    if (!firstDropZone) {
        console.error('❌ [DEBUG] Aucune zone de drop trouvée');
        return;
    }
    
    console.log(`📦 [DEBUG] Poste source: ${firstPost.dataset.postId}`);
    console.log(`🎯 [DEBUG] Zone cible: ${firstDropZone.dataset.employeeId}`);
    
    // Simuler dragstart
    const dragStartEvent = new DragEvent('dragstart', {
        bubbles: true,
        cancelable: true,
        dataTransfer: new DataTransfer()
    });
    
    dragStartEvent.dataTransfer.setData('text/plain', firstPost.dataset.postId);
    firstPost.dispatchEvent(dragStartEvent);
    console.log('✅ [DEBUG] Événement dragstart simulé');
    
    // Simuler dragover
    const dragOverEvent = new DragEvent('dragover', {
        bubbles: true,
        cancelable: true,
        dataTransfer: dragStartEvent.dataTransfer
    });
    
    firstDropZone.dispatchEvent(dragOverEvent);
    console.log('✅ [DEBUG] Événement dragover simulé');
    
    // Simuler drop
    const dropEvent = new DragEvent('drop', {
        bubbles: true,
        cancelable: true,
        dataTransfer: dragStartEvent.dataTransfer
    });
    
    firstDropZone.dispatchEvent(dropEvent);
    console.log('✅ [DEBUG] Événement drop simulé');
}

// Fonction pour vérifier les listeners
function checkEventListeners() {
    console.log('\n👂 [DEBUG] Vérification des listeners...');
    
    const posts = document.querySelectorAll('.post-row-info');
    posts.forEach((post, index) => {
        const postId = post.dataset.postId;
        
        // Tester si les listeners sont attachés
        const hasListeners = post.dataset.dragListenerAttached === 'true';
        console.log(`  ${index + 1}. Poste ${postId} - Listeners attachés: ${hasListeners}`);
    });
}

// Fonction principale de diagnostic
function runDragDropDiagnostic() {
    console.log('🔧 [DEBUG] === DIAGNOSTIC DRAG & DROP ===');
    
    // Attendre que le DOM soit prêt
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                checkDraggableElements();
                checkEventListeners();
                
                // Proposer la simulation
                console.log('\n💡 [DEBUG] Pour simuler un drag & drop, tapez: simulateDragDrop()');
            }, 1000);
        });
    } else {
        setTimeout(() => {
            checkDraggableElements();
            checkEventListeners();
            
            // Proposer la simulation
            console.log('\n💡 [DEBUG] Pour simuler un drag & drop, tapez: simulateDragDrop()');
        }, 1000);
    }
}

// Exposer les fonctions globalement pour les tests
window.checkDraggableElements = checkDraggableElements;
window.simulateDragDrop = simulateDragDrop;
window.checkEventListeners = checkEventListeners;
window.runDragDropDiagnostic = runDragDropDiagnostic;

// Lancer le diagnostic automatiquement
runDragDropDiagnostic(); 