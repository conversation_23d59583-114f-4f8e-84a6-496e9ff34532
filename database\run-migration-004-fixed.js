import { query } from '../server/config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runMigration004Fixed() {
  console.log('🚀 [Migration 004 Fixed] Début de la refactorisation des attributions régulières...');
  
  try {
    // Étape 1: Vérifier l'état actuel
    console.log('🔍 [Migration 004] Vérification de l\'état actuel...');
    const currentStructure = await query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'regular_assignments' 
      ORDER BY ordinal_position
    `);
    
    console.log('📊 [Migration 004] Structure actuelle:', currentStructure.rows);
    
    // Vérifier si la migration a déjà été appliquée
    const hasNewStructure = currentStructure.rows.some(col => col.column_name === 'days_of_week');
    if (hasNewStructure) {
      console.log('✅ [Migration 004] Migration déjà appliquée - structure nouvelle détectée');
      return {
        alreadyApplied: true,
        message: 'Migration déjà appliquée'
      };
    }
    
    // Compter les attributions existantes
    const countResult = await query('SELECT COUNT(*) as count FROM regular_assignments');
    const originalCount = parseInt(countResult.rows[0].count);
    console.log(`📈 [Migration 004] ${originalCount} attributions existantes à migrer`);
    
    // Analyser les doublons potentiels
    const duplicatesResult = await query(`
      SELECT employee_id, post_id, start_date, end_date, COUNT(*) as count
      FROM regular_assignments
      WHERE is_active = true
      GROUP BY employee_id, post_id, start_date, end_date
      HAVING COUNT(*) > 1
      ORDER BY count DESC
      LIMIT 5
    `);
    
    console.log(`🔍 [Migration 004] ${duplicatesResult.rows.length} groupes de doublons détectés`);
    console.log('📋 [Migration 004] Aperçu des doublons:', duplicatesResult.rows);
    
    // Étape 2: Créer la nouvelle table
    console.log('⚡ [Migration 004] Étape 1: Création de la nouvelle table...');
    await query(`
      CREATE TABLE regular_assignments_new (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
        post_id UUID NOT NULL REFERENCES standard_posts(id) ON DELETE CASCADE,
        days_of_week INTEGER[] NOT NULL DEFAULT '{}',
        start_date DATE NOT NULL,
        end_date DATE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ [Migration 004] Nouvelle table créée');
    
    // Étape 3: Migrer les données
    console.log('⚡ [Migration 004] Étape 2: Migration des données...');
    await query(`
      INSERT INTO regular_assignments_new (id, employee_id, post_id, days_of_week, start_date, end_date, is_active, created_at, updated_at)
      SELECT
        uuid_generate_v4() as id,
        employee_id,
        post_id,
        array_agg(DISTINCT day_of_week ORDER BY day_of_week) as days_of_week,
        MIN(start_date) as start_date,
        MAX(end_date) as end_date,
        bool_and(is_active) as is_active,
        MIN(created_at) as created_at,
        MAX(updated_at) as updated_at
      FROM regular_assignments
      WHERE is_active = true
      GROUP BY employee_id, post_id, start_date, end_date
      HAVING COUNT(*) > 0
    `);
    console.log('✅ [Migration 004] Données migrées');
    
    // Étape 4: Supprimer l'ancienne table et renommer
    console.log('⚡ [Migration 004] Étape 3: Remplacement de la table...');
    await query('DROP TABLE IF EXISTS regular_assignments CASCADE');
    await query('ALTER TABLE regular_assignments_new RENAME TO regular_assignments');
    console.log('✅ [Migration 004] Table remplacée');
    
    // Étape 5: Créer les index
    console.log('⚡ [Migration 004] Étape 4: Création des index...');
    await query('CREATE INDEX IF NOT EXISTS idx_regular_assignments_employee ON regular_assignments(employee_id)');
    await query('CREATE INDEX IF NOT EXISTS idx_regular_assignments_post ON regular_assignments(post_id)');
    await query('CREATE INDEX IF NOT EXISTS idx_regular_assignments_dates ON regular_assignments(start_date, end_date)');
    await query('CREATE INDEX IF NOT EXISTS idx_regular_assignments_active ON regular_assignments(is_active)');
    await query('CREATE INDEX IF NOT EXISTS idx_regular_assignments_days ON regular_assignments USING GIN(days_of_week)');
    console.log('✅ [Migration 004] Index créés');
    
    // Étape 6: Créer la fonction trigger si elle n'existe pas
    console.log('⚡ [Migration 004] Étape 5: Création de la fonction trigger...');
    await query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = CURRENT_TIMESTAMP;
          RETURN NEW;
      END;
      $$ language 'plpgsql'
    `);
    
    // Créer le trigger
    await query(`
      CREATE OR REPLACE TRIGGER update_regular_assignments_updated_at
      BEFORE UPDATE ON regular_assignments
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()
    `);
    console.log('✅ [Migration 004] Trigger créé');
    
    // Étape 7: Ajouter les contraintes
    console.log('⚡ [Migration 004] Étape 6: Ajout des contraintes...');
    await query(`
      ALTER TABLE regular_assignments
      ADD CONSTRAINT check_days_of_week_not_empty
      CHECK (array_length(days_of_week, 1) > 0)
    `);
    
    await query(`
      ALTER TABLE regular_assignments
      ADD CONSTRAINT check_date_range_valid
      CHECK (end_date IS NULL OR end_date >= start_date)
    `);
    console.log('✅ [Migration 004] Contraintes ajoutées');
    
    // Étape 8: Ajouter les commentaires
    console.log('⚡ [Migration 004] Étape 7: Ajout des commentaires...');
    await query(`
      COMMENT ON TABLE regular_assignments IS 'Attributions automatiques récurrentes avec support multi-jours optimisé'
    `);
    await query(`
      COMMENT ON COLUMN regular_assignments.days_of_week IS 'Tableau des jours de semaine (0=dimanche, 1=lundi, ..., 6=samedi)'
    `);
    await query(`
      COMMENT ON COLUMN regular_assignments.start_date IS 'Date de début de l''attribution'
    `);
    await query(`
      COMMENT ON COLUMN regular_assignments.end_date IS 'Date de fin de l''attribution (NULL = illimité)'
    `);
    console.log('✅ [Migration 004] Commentaires ajoutés');
    
    // Statistiques finales
    const finalCountResult = await query('SELECT COUNT(*) as count FROM regular_assignments');
    const finalCount = parseInt(finalCountResult.rows[0].count);
    
    const statsResult = await query(`
      SELECT 
        COUNT(*) as total_assignments,
        AVG(array_length(days_of_week, 1)) as avg_days_per_assignment,
        MIN(array_length(days_of_week, 1)) as min_days,
        MAX(array_length(days_of_week, 1)) as max_days
      FROM regular_assignments
    `);
    
    const stats = statsResult.rows[0];
    
    console.log('✅ [Migration 004] Migration terminée avec succès !');
    console.log(`📊 [Migration 004] Statistiques finales: ${finalCount} attributions, ${parseFloat(stats.avg_days_per_assignment).toFixed(1)} jours/attribution`);
    console.log(`📊 [Migration 004] Plage jours: ${stats.min_days} - ${stats.max_days}`);
    
    return {
      success: true,
      originalCount,
      finalCount,
      reduction: originalCount - finalCount,
      averageDaysPerAssignment: parseFloat(stats.avg_days_per_assignment),
      minDays: parseInt(stats.min_days),
      maxDays: parseInt(stats.max_days)
    };
    
  } catch (error) {
    console.error('❌ [Migration 004] Erreur lors de la migration:', error);
    throw error;
  }
}

export { runMigration004Fixed };

// Si exécuté directement
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigration004Fixed()
    .then(result => {
      console.log('✅ Migration 004 terminée:', result);
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Migration 004 échouée:', error);
      process.exit(1);
    });
} 