#!/usr/bin/env node

/**
 * Script pour diagnostiquer et corriger les problèmes critiques
 * 1. Drag & drop des employés non fonctionnel
 * 2. Modales des paramètres inaccessibles  
 * 3. Postes disponibles absents
 */

import fs from 'fs';

console.log('🔧 [FIX-CRITICAL-ISSUES] Diagnostic et correction des problèmes critiques...\n');

const filePath = './src/teamCalendarApp.ts';
let content = fs.readFileSync(filePath, 'utf8');

// Diagnostic des problèmes
const issues = [];

// 1. Vérifier si setupEmployeeDragDrop est appelé
if (!content.includes('this.setupEmployeeDragDrop()')) {
    issues.push('❌ setupEmployeeDragDrop() n\'est pas appelé');
} else {
    console.log('✅ setupEmployeeDragDrop() est appelé');
}

// 2. Vérifier si addPostButton est initialisé
if (!content.includes('addPostButton: document.getElementById')) {
    issues.push('❌ addPostButton n\'est pas initialisé dans elements');
} else {
    console.log('✅ addPostButton est initialisé');
}

// 3. Vérifier si availablePostsContainer est créé
if (!content.includes('createAvailablePostsContainer')) {
    issues.push('❌ createAvailablePostsContainer n\'est pas appelé');
} else {
    console.log('✅ createAvailablePostsContainer existe');
}

// 4. Vérifier si ModalManager est initialisé
if (!content.includes('this.ModalManager.init(this)')) {
    issues.push('❌ ModalManager.init() n\'est pas appelé');
} else {
    console.log('✅ ModalManager.init() est appelé');
}

console.log('\n📋 Problèmes détectés :');
issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue}`);
});

if (issues.length === 0) {
    console.log('✅ Aucun problème critique détecté dans le code');
} else {
    console.log(`\n🔧 Application des corrections...`);
    
    // Correction 1: S'assurer que setupEmployeeDragDrop est appelé dans attachAllEventListeners
    if (!content.includes('this.setupEmployeeDragDrop()')) {
        const attachPattern = /(attachAllEventListeners:\s*function\(\)\s*\{[\s\S]*?)(\s*console\.log\('✅ \[attachAllEventListeners\] Écouteurs attachés avec succès'\);\s*\})/;
        if (attachPattern.test(content)) {
            content = content.replace(attachPattern, (match, before, after) => {
                return before + '\n        // ✅ CORRECTION : Configuration du drag & drop des employés\n        this.setupEmployeeDragDrop();\n        ' + after;
            });
            console.log('✅ setupEmployeeDragDrop() ajouté dans attachAllEventListeners');
        }
    }
    
    // Correction 2: S'assurer que addPostButton est initialisé
    const elementsPattern = /(elements:\s*\{[\s\S]*?)(\s*\},)/;
    if (elementsPattern.test(content)) {
        content = content.replace(elementsPattern, (match, before, after) => {
            if (!before.includes('addPostButton:')) {
                return before + '\n        addPostButton: document.getElementById(\'add-post-btn\'),\n        ' + after;
            }
            return match;
        });
        console.log('✅ addPostButton ajouté dans elements');
    }
    
    // Correction 3: S'assurer que availablePostsContainer est créé dans renderEmployees
    const renderPattern = /(renderEmployees:\s*function\(\)\s*\{[\s\S]*?)(\s*console\.log\('✅ \[renderEmployees\] Employés rendus avec succès'\);\s*\})/;
    if (renderPattern.test(content)) {
        content = content.replace(renderPattern, (match, before, after) => {
            if (!before.includes('this.createAvailablePostsContainer()')) {
                return before + '\n        // ✅ CORRECTION : Créer le conteneur des postes disponibles\n        if (!this.elements.availablePostsContainer) {\n            this.elements.availablePostsContainer = this.createAvailablePostsContainer();\n        }\n        ' + after;
            }
            return match;
        });
        console.log('✅ createAvailablePostsContainer ajouté dans renderEmployees');
    }
    
    // Correction 4: S'assurer que ModalManager est initialisé
    const modalPattern = /(ModalManager:\s*\{[\s\S]*?)(\s*init:\s*function\(app\)\s*\{[\s\S]*?\},\s*)/;
    if (modalPattern.test(content)) {
        if (!content.includes('this.ModalManager.init(this)')) {
            const initPattern = /(attachAllEventListeners:\s*function\(\)\s*\{[\s\S]*?)(\s*console\.log\('✅ \[attachAllEventListeners\] Écouteurs attachés avec succès'\);\s*\})/;
            if (initPattern.test(content)) {
                content = content.replace(initPattern, (match, before, after) => {
                    return before + '\n        // ✅ CORRECTION : Initialiser le ModalManager\n        if (this.ModalManager && !this.ModalManager._initialized) {\n            this.ModalManager.init(this);\n            this.ModalManager._initialized = true;\n        }\n        ' + after;
                });
                console.log('✅ ModalManager.init() ajouté dans attachAllEventListeners');
            }
        }
    }
    
    // Écrire les corrections
    fs.writeFileSync(filePath, content);
    console.log('\n✅ Corrections appliquées !');
}

console.log('\n🚀 Instructions pour tester :');
console.log('1. Redémarrer le serveur : npm run dev');
console.log('2. Ouvrir http://localhost:5173');
console.log('3. Vérifier que :');
console.log('   - Le drag & drop des employés fonctionne');
console.log('   - Les modales des paramètres s\'ouvrent');
console.log('   - Les postes disponibles sont visibles');
console.log('4. Si problèmes persistants, exécuter dans la console :');
console.log('   - checkDOMStructure()');
console.log('   - checkTeamCalendarApp()');

console.log('\n✅ [FIX-CRITICAL-ISSUES] Diagnostic terminé !'); 