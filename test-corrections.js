// 🧪 Script de test pour vérifier les corrections effectuées
console.log('🧪 [TEST-CORRECTIONS] Début des tests de validation...');

// Test 1: Vérifier que le conteneur assignments-list est trouvé
function testAssignmentsContainer() {
    console.log('📋 [TEST-1] Test du conteneur assignments-list...');
    
    // Simuler la recherche comme dans renderAssignments()
    let container = document.getElementById('regular-assignments-list'); // React
    if (!container) {
        container = document.getElementById('assignments-list'); // Modal externe
    }
    
    if (container) {
        console.log('✅ [TEST-1] Conteneur trouvé:', container.id);
        return true;
    } else {
        console.error('❌ [TEST-1] Aucun conteneur trouvé');
        return false;
    }
}

// Test 2: Vérifier que les dates invalides sont corrigées
function testDateValidation() {
    console.log('📅 [TEST-2] Test de validation des dates...');
    
    // Simuler des dates invalides comme dans les logs
    const testAssignments = [
        {
            id: '18cd6e1a-test',
            startDate: '2025-07-09',
            endDate: '2025-07-07' // end_date < start_date
        },
        {
            id: 'bca6e806-test',
            startDate: '2025-07-15',
            endDate: '2025-07-13' // end_date < start_date
        },
        {
            id: 'valid-test',
            startDate: '2025-07-01',
            endDate: '2025-07-31' // Dates valides
        }
    ];
    
    let fixedCount = 0;
    
    testAssignments.forEach(assignment => {
        if (assignment.startDate && assignment.endDate) {
            const startDate = new Date(assignment.startDate);
            const endDate = new Date(assignment.endDate);
            
            if (endDate < startDate) {
                console.warn(`⚠️ [TEST-2] Date invalide détectée pour ${assignment.id}:`);
                console.warn(`   Start: ${assignment.startDate} > End: ${assignment.endDate}`);
                
                // Correction : échanger les dates
                const tempDate = assignment.startDate;
                assignment.startDate = assignment.endDate;
                assignment.endDate = tempDate;
                
                console.log(`✅ [TEST-2] Dates corrigées pour ${assignment.id}:`);
                console.log(`   Nouveau Start: ${assignment.startDate}, Nouveau End: ${assignment.endDate}`);
                
                fixedCount++;
            }
        }
    });
    
    console.log(`📊 [TEST-2] ${fixedCount}/2 dates invalides corrigées (attendu: 2)`);
    return fixedCount === 2;
}

// Test 3: Vérifier que les fonctions obsolètes ont été supprimées
function testObsoleteFunctions() {
    console.log('🗑️ [TEST-3] Test de suppression des fonctions obsolètes...');
    
    const obsoleteFunctions = [
        'closeSettingsModal',
        'createSettingsModal', 
        'setupTabNavigation',
        'attachSettingsButtonIfMissing'
    ];
    
    let removedCount = 0;
    
    if (window.TeamCalendarApp) {
        obsoleteFunctions.forEach(funcName => {
            if (typeof window.TeamCalendarApp[funcName] === 'undefined') {
                console.log(`✅ [TEST-3] Fonction ${funcName} correctement supprimée`);
                removedCount++;
            } else {
                console.error(`❌ [TEST-3] Fonction ${funcName} encore présente`);
            }
        });
    } else {
        console.warn('⚠️ [TEST-3] TeamCalendarApp non disponible pour le test');
        return false;
    }
    
    console.log(`📊 [TEST-3] ${removedCount}/${obsoleteFunctions.length} fonctions supprimées`);
    return removedCount === obsoleteFunctions.length;
}

// Test 4: Vérifier que openSettingsModal délègue au gestionnaire externe
function testModalDelegation() {
    console.log('🔧 [TEST-4] Test de délégation des modales...');
    
    if (window.TeamCalendarApp && typeof window.TeamCalendarApp.openSettingsModal === 'function') {
        console.log('✅ [TEST-4] openSettingsModal présente');
        
        // Vérifier que modalFunctionalities est disponible
        if (window.modalFunctionalities) {
            console.log('✅ [TEST-4] modalFunctionalities disponible');
            return true;
        } else {
            console.warn('⚠️ [TEST-4] modalFunctionalities non disponible');
            return false;
        }
    } else {
        console.error('❌ [TEST-4] openSettingsModal non disponible');
        return false;
    }
}

// Exécuter tous les tests
function runAllTests() {
    console.log('🚀 [TEST-CORRECTIONS] Exécution de tous les tests...');
    
    const results = {
        assignmentsContainer: testAssignmentsContainer(),
        dateValidation: testDateValidation(),
        obsoleteFunctions: testObsoleteFunctions(),
        modalDelegation: testModalDelegation()
    };
    
    const passedTests = Object.values(results).filter(result => result === true).length;
    const totalTests = Object.keys(results).length;
    
    console.log('📊 [TEST-CORRECTIONS] Résultats finaux:');
    console.log(`   ✅ Tests réussis: ${passedTests}/${totalTests}`);
    console.log(`   📋 Détails:`, results);
    
    if (passedTests === totalTests) {
        console.log('🎉 [TEST-CORRECTIONS] Tous les tests sont passés !');
    } else {
        console.warn(`⚠️ [TEST-CORRECTIONS] ${totalTests - passedTests} test(s) échoué(s)`);
    }
    
    return results;
}

// Attendre que la page soit chargée puis exécuter les tests
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
} else {
    runAllTests();
}
