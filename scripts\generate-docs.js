#!/usr/bin/env node

/**
 * @fileoverview Générateur de documentation automatique
 * @description Génère la documentation à partir des commentaires JSDoc et du code
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

/**
 * @description Couleurs pour la console
 */
const colors = {
  green: '\x1b[32m',
  blue: '\x1b[34m',
  yellow: '\x1b[33m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

/**
 * @description Log avec couleur
 */
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

/**
 * @description Extrait les commentaires JSDoc d'un fichier
 * @param {string} filePath - Chemin du fichier
 * @returns {Array} Liste des commentaires JSDoc trouvés
 */
function extractJSDocComments(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const jsdocRegex = /\/\*\*[\s\S]*?\*\//g;
    const matches = content.match(jsdocRegex) || [];
    
    return matches.map(match => {
      // Extraire les informations du JSDoc
      const descriptionMatch = match.match(/@description\s+(.+?)(?=@|\*\/)/s);
      const paramMatches = match.match(/@param\s+\{([^}]+)\}\s+(\w+)\s*-?\s*(.+?)(?=@|\*\/)/gs) || [];
      const returnsMatch = match.match(/@returns\s+\{([^}]+)\}\s+(.+?)(?=@|\*\/)/s);
      const exampleMatch = match.match(/@example\s+([\s\S]+?)(?=@|\*\/)/s);
      
      return {
        raw: match,
        description: descriptionMatch ? descriptionMatch[1].trim() : '',
        params: paramMatches.map(param => {
          const paramMatch = param.match(/@param\s+\{([^}]+)\}\s+(\w+)\s*-?\s*(.+)/s);
          return paramMatch ? {
            type: paramMatch[1],
            name: paramMatch[2],
            description: paramMatch[3].trim()
          } : null;
        }).filter(Boolean),
        returns: returnsMatch ? {
          type: returnsMatch[1],
          description: returnsMatch[2].trim()
        } : null,
        example: exampleMatch ? exampleMatch[1].trim() : null
      };
    });
  } catch (error) {
    log(`⚠️ Erreur lors de la lecture de ${filePath}: ${error.message}`, colors.yellow);
    return [];
  }
}

/**
 * @description Analyse un fichier TypeScript pour extraire les fonctions
 * @param {string} filePath - Chemin du fichier
 * @returns {Array} Liste des fonctions trouvées
 */
function analyzeFunctions(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Regex pour trouver les fonctions
    const functionRegex = /(?:export\s+)?(?:async\s+)?function\s+(\w+)\s*\([^)]*\)|(\w+):\s*(?:async\s+)?function\s*\([^)]*\)/g;
    const functions = [];
    let match;
    
    while ((match = functionRegex.exec(content)) !== null) {
      const functionName = match[1] || match[2];
      if (functionName) {
        functions.push({
          name: functionName,
          line: content.substring(0, match.index).split('\n').length
        });
      }
    }
    
    return functions;
  } catch (error) {
    log(`⚠️ Erreur lors de l'analyse de ${filePath}: ${error.message}`, colors.yellow);
    return [];
  }
}

/**
 * @description Génère la documentation pour un fichier
 * @param {string} filePath - Chemin du fichier
 * @returns {string} Documentation générée en Markdown
 */
function generateFileDocumentation(filePath) {
  const relativePath = path.relative(rootDir, filePath);
  const fileName = path.basename(filePath);
  
  log(`📄 Analyse de ${relativePath}...`, colors.blue);
  
  const jsdocComments = extractJSDocComments(filePath);
  const functions = analyzeFunctions(filePath);
  
  let markdown = `# Documentation - ${fileName}\n\n`;
  markdown += `> **Fichier**: \`${relativePath}\`\n\n`;
  
  if (jsdocComments.length > 0) {
    markdown += `## 📚 Fonctions Documentées\n\n`;
    
    jsdocComments.forEach((comment, index) => {
      if (comment.description) {
        markdown += `### Fonction ${index + 1}\n\n`;
        markdown += `**Description**: ${comment.description}\n\n`;
        
        if (comment.params.length > 0) {
          markdown += `**Paramètres**:\n`;
          comment.params.forEach(param => {
            markdown += `- \`${param.name}\` (\`${param.type}\`): ${param.description}\n`;
          });
          markdown += '\n';
        }
        
        if (comment.returns) {
          markdown += `**Retour**: \`${comment.returns.type}\` - ${comment.returns.description}\n\n`;
        }
        
        if (comment.example) {
          markdown += `**Exemple**:\n\`\`\`typescript\n${comment.example}\n\`\`\`\n\n`;
        }
        
        markdown += '---\n\n';
      }
    });
  }
  
  if (functions.length > 0) {
    markdown += `## 🔧 Toutes les Fonctions\n\n`;
    markdown += `| Fonction | Ligne |\n`;
    markdown += `|----------|-------|\n`;
    
    functions.forEach(func => {
      markdown += `| \`${func.name}\` | ${func.line} |\n`;
    });
    
    markdown += '\n';
  }
  
  return markdown;
}

/**
 * @description Génère la documentation complète
 */
async function generateDocumentation() {
  log(`${colors.bold}📖 GÉNÉRATION DE DOCUMENTATION${colors.reset}`, colors.blue);
  log('='.repeat(40), colors.blue);
  
  const filesToDocument = [
    'src/teamCalendarApp.ts',
    'src/modalFunctionalities.ts',
    'src/api.ts',
    'src/logger.ts',
    'src/components/utils/dateUtils.ts',
    'src/components/utils/validationUtils.ts'
  ];
  
  const docsDir = path.join(rootDir, 'docs', 'api');
  
  // Créer le dossier docs/api s'il n'existe pas
  if (!fs.existsSync(docsDir)) {
    fs.mkdirSync(docsDir, { recursive: true });
    log(`📁 Dossier créé: ${path.relative(rootDir, docsDir)}`, colors.green);
  }
  
  let indexMarkdown = `# Documentation API - Team Calendar\n\n`;
  indexMarkdown += `> Documentation générée automatiquement le ${new Date().toLocaleString('fr-FR')}\n\n`;
  indexMarkdown += `## 📋 Fichiers Documentés\n\n`;
  
  for (const file of filesToDocument) {
    const fullPath = path.join(rootDir, file);
    
    if (fs.existsSync(fullPath)) {
      const documentation = generateFileDocumentation(fullPath);
      const outputFileName = path.basename(file, path.extname(file)) + '.md';
      const outputPath = path.join(docsDir, outputFileName);
      
      fs.writeFileSync(outputPath, documentation);
      log(`✅ Documentation générée: ${outputFileName}`, colors.green);
      
      // Ajouter à l'index
      const relativePath = path.relative(rootDir, fullPath);
      indexMarkdown += `- [${path.basename(file)}](api/${outputFileName}) - \`${relativePath}\`\n`;
    } else {
      log(`⚠️ Fichier non trouvé: ${file}`, colors.yellow);
    }
  }
  
  // Générer l'index
  const indexPath = path.join(docsDir, 'index.md');
  fs.writeFileSync(indexPath, indexMarkdown);
  log(`✅ Index généré: index.md`, colors.green);
  
  // Générer un résumé
  log('\n📊 RÉSUMÉ', colors.bold);
  log(`📁 Dossier de sortie: ${path.relative(rootDir, docsDir)}`, colors.blue);
  log(`📄 Fichiers documentés: ${filesToDocument.length}`, colors.blue);
  log(`✅ Documentation générée avec succès !`, colors.green);
}

// Exécution
generateDocumentation().catch(error => {
  log(`❌ Erreur lors de la génération: ${error.message}`, colors.red);
  process.exit(1);
});
