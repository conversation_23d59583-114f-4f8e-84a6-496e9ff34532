#!/usr/bin/env node

/**
 * CORRECTION COMPLÈTE - SYSTÈME DE LOGS AMÉLIORÉ
 * 
 * Objectifs :
 * 1. Capturer TOUS les logs backend (console.log, console.error, etc.)
 * 2. Intégrer les logs du nouveau système de déplacements
 * 3. Améliorer l'interface /logs pour récupérer tous les types
 * 4. Tester la capture complète
 * 
 * Date: 2025-07-02
 * Contexte: Amélioration prioritaire demandée par l'utilisateur
 */

import { readFileSync, writeFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fetch from 'node-fetch';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const API_BASE = 'http://localhost:3001';

console.log('🔧 [LOGS-FIX] Démarrage correction complète du système de logs...');

// Test 1: Vérifier si le backend capture bien ses logs
async function testBackendLogCapture() {
  console.log('\n🔄 [TEST-1] Test capture logs backend...');
  
  try {
    // Récupérer session courante
    const sessionResponse = await fetch(`${API_BASE}/api/debug/current-session`);
    if (!sessionResponse.ok) {
      throw new Error('Session API indisponible');
    }
    const sessionData = await sessionResponse.json();
    const sessionId = sessionData.sessionId;
    
    console.log(`   🔑 Session test: ${sessionId.substring(0, 8)}...`);
    
    // Générer un log de test backend
    const testMessage = `TEST-LOGS-${Date.now()}`;
    const testResponse = await fetch(`${API_BASE}/api/health`);
    
    // Attendre un peu pour que le log soit capturé
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Vérifier si le log apparaît dans les logs
    const logsResponse = await fetch(`${API_BASE}/api/debug/sessions/${sessionId}?mode=chronological&max=50`);
    if (!logsResponse.ok) {
      throw new Error('Impossible de récupérer les logs');
    }
    
    const logs = await logsResponse.json();
    const backendLogs = logs.filter(log => log.source === 'backend');
    
    console.log(`   📊 Logs trouvés: ${logs.length} total, ${backendLogs.length} backend`);
    
    if (backendLogs.length > 0) {
      console.log('   ✅ Capture backend ACTIVE');
      console.log(`   📝 Exemple: ${backendLogs[0].message.substring(0, 100)}...`);
      return true;
    } else {
      console.log('   ❌ Aucun log backend trouvé - Capture INACTIVE');
      return false;
    }
    
  } catch (error) {
    console.log('   ❌ Erreur test backend:', error.message);
    return false;
  }
}

// Test 2: Vérifier l'intégration des logs de déplacement
async function testDragLogsIntegration() {
  console.log('\n🔄 [TEST-2] Test intégration logs déplacements...');
  
  try {
    // Simuler l'ouverture de l'interface pour tester les logs
    const response = await fetch(`${API_BASE}/api/health`);
    console.log('   📡 API Health check:', response.ok ? 'OK' : 'ERREUR');
    
    // Vérifier si les scripts sont bien chargés
    const publicFiles = [
      'capture-logs-unified.js',
      'fix-drag-drop-protection.js',
      'test-employee-drag-logger.js'
    ];
    
    let allFilesOk = true;
    for (const file of publicFiles) {
      try {
        const fileResponse = await fetch(`${API_BASE}/${file}`);
        if (fileResponse.ok) {
          console.log(`   ✅ Script ${file} disponible`);
        } else {
          console.log(`   ❌ Script ${file} indisponible (${fileResponse.status})`);
          allFilesOk = false;
        }
      } catch (error) {
        console.log(`   ❌ Script ${file} erreur: ${error.message}`);
        allFilesOk = false;
      }
    }
    
    if (allFilesOk) {
      console.log('   ✅ Tous les scripts de capture sont disponibles');
      return true;
    } else {
      console.log('   ⚠️ Certains scripts de capture manquent');
      return false;
    }
    
  } catch (error) {
    console.log('   ❌ Erreur test intégration:', error.message);
    return false;
  }
}

// Test 3: Vérifier l'interface /logs
async function testLogsInterface() {
  console.log('\n🔄 [TEST-3] Test interface /logs...');
  
  try {
    // Tester les endpoints de l'interface
    const endpoints = [
      '/api/debug/sessions',
      '/api/debug/current-session'
    ];
    
    let allEndpointsOk = true;
    for (const endpoint of endpoints) {
      try {
        const response = await fetch(`${API_BASE}${endpoint}`);
        if (response.ok) {
          console.log(`   ✅ Endpoint ${endpoint} OK`);
        } else {
          console.log(`   ❌ Endpoint ${endpoint} erreur ${response.status}`);
          allEndpointsOk = false;
        }
      } catch (error) {
        console.log(`   ❌ Endpoint ${endpoint} erreur: ${error.message}`);
        allEndpointsOk = false;
      }
    }
    
    // Tester l'endpoint SSE
    const sessionResponse = await fetch(`${API_BASE}/api/debug/current-session`);
    if (sessionResponse.ok) {
      const sessionData = await sessionResponse.json();
      const sseEndpoint = `/api/debug/stream/${sessionData.sessionId}`;
      
      try {
        const sseResponse = await fetch(`${API_BASE}${sseEndpoint}`);
        if (sseResponse.ok) {
          console.log('   ✅ Endpoint SSE OK');
          // Fermer immédiatement pour éviter les connexions pendantes
          if (sseResponse.body) {
            sseResponse.body.cancel();
          }
        } else {
          console.log(`   ❌ Endpoint SSE erreur ${sseResponse.status}`);
          allEndpointsOk = false;
        }
      } catch (error) {
        console.log(`   ❌ Endpoint SSE erreur: ${error.message}`);
        allEndpointsOk = false;
      }
    }
    
    return allEndpointsOk;
    
  } catch (error) {
    console.log('   ❌ Erreur test interface:', error.message);
    return false;
  }
}

// Test 4: Générer des logs de test et vérifier qu'ils sont capturés
async function testLogGeneration() {
  console.log('\n🔄 [TEST-4] Test génération et capture de logs...');
  
  try {
    // Récupérer session courante
    const sessionResponse = await fetch(`${API_BASE}/api/debug/current-session`);
    if (!sessionResponse.ok) {
      throw new Error('Session API indisponible');
    }
    const sessionData = await sessionResponse.json();
    const sessionId = sessionData.sessionId;
    
    // Générer différents types de logs via l'API browser
    const testLogs = [
      { level: 'info', message: '[TEST-LOGS] Log de test frontend', source: 'frontend' },
      { level: 'debug', message: '[TEST-LOGS] Log de test drag system', source: 'drag-system' },
      { level: 'warn', message: '[TEST-LOGS] Log de test warning', source: 'browser' },
      { level: 'info', message: '[DRAG-SYSTEM] Test drag start', source: 'frontend' }
    ];
    
    console.log(`   📝 Génération de ${testLogs.length} logs de test...`);
    
    // Envoyer chaque log
    for (const testLog of testLogs) {
      try {
        const response = await fetch(`${API_BASE}/api/debug/browser`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            sessionId,
            level: testLog.level,
            message: testLog.message,
            data: { test: true, type: testLog.source }
          })
        });
        
        if (response.ok) {
          console.log(`   ✅ Log ${testLog.source} envoyé`);
        } else {
          console.log(`   ❌ Log ${testLog.source} échec (${response.status})`);
        }
        
        // Petit délai entre chaque log
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.log(`   ❌ Log ${testLog.source} erreur:`, error.message);
      }
    }
    
    // Attendre que les logs soient traités
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Vérifier que les logs sont bien récupérés
    const logsResponse = await fetch(`${API_BASE}/api/debug/sessions/${sessionId}?mode=chronological&max=100`);
    if (!logsResponse.ok) {
      throw new Error('Impossible de récupérer les logs');
    }
    
    const logs = await logsResponse.json();
    const testLogsFound = logs.filter(log => log.message.includes('[TEST-LOGS]'));
    const dragLogsFound = logs.filter(log => log.message.includes('[DRAG-SYSTEM]'));
    
    console.log(`   📊 Logs récupérés: ${logs.length} total`);
    console.log(`   🧪 Logs de test trouvés: ${testLogsFound.length}/${testLogs.length - 1}`);
    console.log(`   🎯 Logs drag trouvés: ${dragLogsFound.length}/1`);
    
    // Afficher répartition par source
    const sourceCount = {};
    logs.forEach(log => {
      sourceCount[log.source] = (sourceCount[log.source] || 0) + 1;
    });
    console.log('   📊 Répartition par source:', sourceCount);
    
    return testLogsFound.length > 0 && dragLogsFound.length > 0;
    
  } catch (error) {
    console.log('   ❌ Erreur test génération:', error.message);
    return false;
  }
}

// Test 5: Vérifier l'interface utilisateur
async function testUIInterface() {
  console.log('\n🔄 [TEST-5] Test interface utilisateur /logs...');
  
  try {
    // Vérifier que l'interface est accessible
    const response = await fetch('http://localhost:5173/logs');
    if (response.ok) {
      console.log('   ✅ Interface /logs accessible');
      return true;
    } else {
      console.log(`   ❌ Interface /logs inaccessible (${response.status})`);
      return false;
    }
  } catch (error) {
    console.log('   ❌ Interface /logs erreur:', error.message);
    console.log('   💡 Assurez-vous que le frontend est démarré (npm run dev)');
    return false;
  }
}

// Fonction principale de test
async function runAllTests() {
  console.log('\n=== TESTS COMPLETS DU SYSTÈME DE LOGS ===');
  
  const tests = [
    { name: 'Capture Backend', fn: testBackendLogCapture },
    { name: 'Intégration Drag Logs', fn: testDragLogsIntegration },
    { name: 'Interface API', fn: testLogsInterface },
    { name: 'Génération/Capture', fn: testLogGeneration },
    { name: 'Interface UI', fn: testUIInterface }
  ];
  
  const results = {};
  let totalTests = tests.length;
  let passedTests = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results[test.name] = result;
      if (result) {
        passedTests++;
        console.log(`✅ ${test.name}: RÉUSSI`);
      } else {
        console.log(`❌ ${test.name}: ÉCHOUÉ`);
      }
    } catch (error) {
      results[test.name] = false;
      console.log(`❌ ${test.name}: ERREUR - ${error.message}`);
    }
  }
  
  console.log('\n=== RÉSULTATS FINAUX ===');
  console.log(`📊 Tests réussis: ${passedTests}/${totalTests}`);
  console.log(`📊 Taux de réussite: ${Math.round((passedTests/totalTests)*100)}%`);
  
  // Recommandations
  console.log('\n=== RECOMMANDATIONS ===');
  
  if (!results['Capture Backend']) {
    console.log('🔧 Backend: Redémarrer le serveur pour activer la capture console');
    console.log('   → node server/app.js');
  }
  
  if (!results['Intégration Drag Logs']) {
    console.log('🔧 Scripts: Vérifier que les scripts sont bien servis');
    console.log('   → Vérifier le dossier public/');
  }
  
  if (!results['Interface API']) {
    console.log('🔧 API: Problème avec les endpoints de logs');
    console.log('   → Vérifier la base de données et les migrations');
  }
  
  if (!results['Génération/Capture']) {
    console.log('🔧 Capture: Système de capture incomplet');
    console.log('   → Vérifier capture-logs-unified.js');
  }
  
  if (!results['Interface UI']) {
    console.log('🔧 Frontend: Démarrer l\'interface React');
    console.log('   → npm run dev');
  }
  
  if (passedTests === totalTests) {
    console.log('\n🎉 TOUS LES TESTS RÉUSSIS !');
    console.log('✅ Le système de logs est complètement fonctionnel');
    console.log('🚀 Vous pouvez maintenant accéder à http://localhost:5173/logs');
  } else {
    console.log('\n⚠️ CORRECTIONS NÉCESSAIRES');
    console.log(`${totalTests - passedTests} problème(s) détecté(s)`);
  }
  
  return passedTests === totalTests;
}

// Exécution
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Erreur fatale:', error);
    process.exit(1);
  });
} 