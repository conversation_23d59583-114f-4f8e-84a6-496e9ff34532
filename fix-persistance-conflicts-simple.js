/**
 * 🚨 CORRECTION CRITIQUE : Conflits de Persistance Désastreux
 * Version JavaScript standard
 */

const fs = require('fs');
const path = require('path');

const TEAM_CALENDAR_FILE = 'src/teamCalendarApp.ts';

console.log('🚨 [PERSISTANCE-FIX] Démarrage correction conflits de persistance...');

try {
    let content = fs.readFileSync(TEAM_CALENDAR_FILE, 'utf8');
    let corrections = 0;
    
    // ✅ 1. AJOUTER UN SYSTÈME DE VERROUILLAGE TRANSACTIONNEL
    const transactionSystem = `
    // ✅ SYSTÈME DE VERROUILLAGE TRANSACTIONNEL
    _transactionLock: false,
    _pendingTransactions: [],
    _savingState: false,
    _loadingState: false,
    _renderingInProgress: false,
    
    // Modifier le schedule de manière sécurisée
    safeScheduleUpdate: async function(employeeId, dateKey, updateFn, operation = 'schedule-update') {
        if (this._transactionLock) {
            console.warn(\`⚠️ [TRANSACTION] Opération \${operation} en attente - verrou actif\`);
            return;
        }
        
        this._transactionLock = true;
        console.log(\`🔒 [TRANSACTION] Début: \${operation}\`);
        
        try {
            // Initialiser si nécessaire
            if (!this.data.schedule[employeeId]) {
                this.data.schedule[employeeId] = {};
            }
            if (!this.data.schedule[employeeId][dateKey]) {
                this.data.schedule[employeeId][dateKey] = [];
            }
            
            // Appliquer la modification
            const result = updateFn(this.data.schedule[employeeId][dateKey]);
            
            // Sauvegarder de manière asynchrone
            setTimeout(() => this.saveState(), 100);
            
            console.log(\`✅ [SAFE-UPDATE] \${operation} réussie pour \${employeeId} le \${dateKey}\`);
            return result;
            
        } catch (error) {
            console.error(\`❌ [SAFE-UPDATE] Erreur \${operation}:\`, error);
            throw error;
        } finally {
            this._transactionLock = false;
            console.log(\`🔓 [TRANSACTION] Fin: \${operation}\`);
        }
    },`;
    
    // Insérer le système de transaction
    const insertPoint = content.indexOf('const TeamCalendarApp: TeamCalendarAppType = {');
    if (insertPoint !== -1) {
        const nextBrace = content.indexOf('\n', insertPoint);
        content = content.slice(0, nextBrace) + transactionSystem + content.slice(nextBrace);
        corrections++;
        console.log('✅ [PERSISTANCE-FIX] Système de verrouillage transactionnel ajouté');
    }
    
    // ✅ 2. AJOUTER RENDER SÉCURISÉ
    const renderSafe = `
    // Rendu sécurisé avec throttling
    _lastRender: 0,
    _renderThrottle: 100,
    
    renderSafe: function() {
        const now = Date.now();
        if (now - this._lastRender < this._renderThrottle) {
            console.log('⚠️ [renderSafe] Throttled - trop fréquent');
            return;
        }
        
        if (this._renderingInProgress) {
            console.log('⚠️ [renderSafe] Rendu déjà en cours');
            return;
        }
        
        this._renderingInProgress = true;
        this._lastRender = now;
        
        try {
            if (typeof this.actualRender === 'function') {
                this.actualRender();
            } else if (typeof this.renderUnifiedCalendar === 'function') {
                this.renderUnifiedCalendar();
            } else {
                console.warn('⚠️ [renderSafe] Aucune fonction de rendu trouvée');
            }
        } catch (error) {
            console.error('❌ [renderSafe] Erreur rendu:', error);
        } finally {
            this._renderingInProgress = false;
        }
    },`;
    
    // Insérer render sécurisé
    const renderFunction = content.indexOf('render: function()');
    if (renderFunction !== -1) {
        content = content.slice(0, renderFunction) + renderSafe + '\n    ' + content.slice(renderFunction);
        corrections++;
        console.log('✅ [PERSISTANCE-FIX] Render sécurisé ajouté');
    }
    
    // ✅ 3. AJOUTER FONCTIONS DE NETTOYAGE
    const cleaningFunctions = `
    
    // Nettoyer les données avant sauvegarde
    cleanDataBeforeSave: function() {
        let cleaned = 0;
        
        try {
            // Nettoyer les shifts corrompus
            Object.keys(this.data.schedule || {}).forEach(employeeId => {
                Object.keys(this.data.schedule[employeeId] || {}).forEach(dateKey => {
                    const originalLength = this.data.schedule[employeeId][dateKey].length;
                    
                    // Supprimer les shifts invalides
                    this.data.schedule[employeeId][dateKey] = this.data.schedule[employeeId][dateKey].filter(shift => {
                        return shift && 
                               typeof shift === 'object' && 
                               (shift.id || shift.postId) &&
                               shift.text !== undefined;
                    });
                    
                    // Supprimer les doublons
                    const uniqueShifts = [];
                    const seenIds = new Set();
                    
                    this.data.schedule[employeeId][dateKey].forEach(shift => {
                        const shiftId = shift.id || \`\${shift.postId}-\${shift.text}\`;
                        if (!seenIds.has(shiftId)) {
                            seenIds.add(shiftId);
                            uniqueShifts.push(shift);
                        }
                    });
                    
                    this.data.schedule[employeeId][dateKey] = uniqueShifts;
                    
                    const newLength = this.data.schedule[employeeId][dateKey].length;
                    if (originalLength !== newLength) {
                        cleaned += (originalLength - newLength);
                    }
                    
                    // Supprimer les jours vides
                    if (this.data.schedule[employeeId][dateKey].length === 0) {
                        delete this.data.schedule[employeeId][dateKey];
                    }
                });
                
                // Supprimer les employés sans shifts
                if (Object.keys(this.data.schedule[employeeId] || {}).length === 0) {
                    delete this.data.schedule[employeeId];
                }
            });
            
            if (cleaned > 0) {
                console.log(\`🧹 [cleanDataBeforeSave] \${cleaned} éléments corrompus nettoyés\`);
            }
        } catch (error) {
            console.error('❌ [cleanDataBeforeSave] Erreur:', error);
        }
    },
    
    // Vérifier l'intégrité des données
    checkDataIntegrity: function() {
        let issues = 0;
        
        try {
            // Vérifier la structure de base
            if (!this.data || typeof this.data !== 'object') {
                issues++;
            }
            
            if (!this.data.schedule || typeof this.data.schedule !== 'object') {
                issues++;
            }
            
            // Vérifier les employés
            if (!Array.isArray(this.data.employees)) {
                issues++;
            }
            
            // Vérifier les shifts
            Object.keys(this.data.schedule || {}).forEach(employeeId => {
                if (typeof this.data.schedule[employeeId] !== 'object') {
                    issues++;
                    return;
                }
                
                Object.keys(this.data.schedule[employeeId] || {}).forEach(dateKey => {
                    if (!Array.isArray(this.data.schedule[employeeId][dateKey])) {
                        issues++;
                    }
                });
            });
            
            if (issues > 0) {
                console.warn(\`⚠️ [INTEGRITY] \${issues} problèmes détectés\`);
                if (issues >= 5) {
                    console.error('🚨 [INTEGRITY] Corruption critique détectée');
                    this.emergencyRecovery();
                }
            }
        } catch (error) {
            console.error('❌ [checkDataIntegrity] Erreur:', error);
            issues += 10;
        }
        
        return issues;
    },
    
    // Récupération d'urgence
    emergencyRecovery: function() {
        console.log('🚨 [EMERGENCY] Début récupération d\\'urgence...');
        
        try {
            // Sauvegarder l'état actuel
            const corruptedState = JSON.stringify(this.data);
            localStorage.setItem('teamCalendar_corrupted_backup', corruptedState);
            
            // Nettoyer les données corrompues
            this.data.schedule = {};
            
            // Recharger depuis l'API
            if (typeof this.loadState === 'function') {
                this.loadState().then(() => {
                    this.renderSafe();
                    console.log('✅ [EMERGENCY] Récupération réussie');
                    if (window.toastSystem) {
                        window.toastSystem.success('🚨 Récupération d\\'urgence terminée');
                    }
                }).catch(error => {
                    console.error('❌ [EMERGENCY] Échec loadState:', error);
                    localStorage.clear();
                    location.reload();
                });
            } else {
                console.warn('⚠️ [EMERGENCY] loadState non disponible, rechargement complet');
                localStorage.clear();
                location.reload();
            }
            
        } catch (error) {
            console.error('❌ [EMERGENCY] Échec récupération:', error);
            localStorage.clear();
            location.reload();
        }
    },
    
    // Fonction de réparation rapide
    quickRepair: function() {
        console.log('🔧 [QUICK-REPAIR] Démarrage réparation rapide...');
        
        let repaired = 0;
        
        try {
            // Réparer les structures manquantes
            if (!this.data) {
                this.data = {};
                repaired++;
            }
            
            if (!this.data.schedule) {
                this.data.schedule = {};
                repaired++;
            }
            
            if (!Array.isArray(this.data.employees)) {
                this.data.employees = [];
                repaired++;
            }
            
            // Réparer les employés sans schedule
            this.data.employees.forEach(employee => {
                if (employee && employee.id && !this.data.schedule[employee.id]) {
                    this.data.schedule[employee.id] = {};
                    repaired++;
                }
            });
            
            // Nettoyer les données corrompues
            this.cleanDataBeforeSave();
            
            console.log(\`✅ [QUICK-REPAIR] \${repaired} éléments réparés\`);
            
            // Sauvegarder les réparations
            if (typeof this.saveState === 'function') {
                this.saveState();
            }
            
        } catch (error) {
            console.error('❌ [QUICK-REPAIR] Erreur:', error);
        }
        
        return repaired;
    }`;
    
    // Ajouter les fonctions de nettoyage
    const insertCleaningPoint = content.lastIndexOf('};');
    if (insertCleaningPoint !== -1) {
        content = content.slice(0, insertCleaningPoint) + cleaningFunctions + '\n' + content.slice(insertCleaningPoint);
        corrections++;
        console.log('✅ [PERSISTANCE-FIX] Fonctions de nettoyage ajoutées');
    }
    
    // ✅ 4. CORRIGER LA FONCTION SAVESTATE
    const saveStatePattern = /saveState:\s*async\s*function\s*\([^)]*\)\s*{[^}]+(?:{[^}]*}[^}]*)*}/gs;
    
    const newSaveState = `saveState: async function() {
        // Éviter les sauvegardes en cascade
        if (this._savingState) {
            console.log('⚠️ [saveState] Sauvegarde déjà en cours, ignorée');
            return;
        }
        
        this._savingState = true;
        
        try {
            console.log('💾 [saveState] Début sauvegarde sécurisée...');
            
            // Nettoyer les données avant sauvegarde
            if (typeof this.cleanDataBeforeSave === 'function') {
                this.cleanDataBeforeSave();
            }
            
            // Sauvegarder en localStorage
            const stateToSave = {
                employees: this.data.employees || [],
                schedule: this.data.schedule || {},
                vacations: this.data.vacations || [],
                globalVacations: this.data.globalVacations || [],
                regularAssignments: this.data.regularAssignments || [],
                customPosts: this.data.customPosts || [],
                timestamp: Date.now()
            };
            
            localStorage.setItem(this.config.storageKey, JSON.stringify(stateToSave));
            localStorage.setItem('teamCalendarAppSettings', JSON.stringify(this.config.appSettings || {}));
            
            // Sauvegarder via API si disponible
            if (window.apiService && typeof window.apiService.isAvailable === 'function' && window.apiService.isAvailable()) {
                try {
                    // Sauvegarder les shifts via API
                    const allShifts = [];
                    Object.keys(this.data.schedule || {}).forEach(employeeId => {
                        Object.keys(this.data.schedule[employeeId] || {}).forEach(dateKey => {
                            (this.data.schedule[employeeId][dateKey] || []).forEach(shift => {
                                allShifts.push({
                                    ...shift,
                                    employee_id: employeeId,
                                    date: dateKey,
                                    shift_data: shift
                                });
                            });
                        });
                    });
                    
                    if (allShifts.length > 0 && typeof window.apiService.saveShifts === 'function') {
                        await window.apiService.saveShifts(allShifts);
                        console.log('✅ [saveState] Sauvegarde API réussie');
                    }
                } catch (apiError) {
                    console.warn('⚠️ [saveState] Erreur API, fallback localStorage:', apiError);
                }
            }
            
            console.log('✅ [saveState] Sauvegarde sécurisée terminée');
            
        } catch (error) {
            console.error('❌ [saveState] Erreur sauvegarde:', error);
        } finally {
            this._savingState = false;
        }
    }`;
    
    // Remplacer la fonction saveState
    if (saveStatePattern.test(content)) {
        content = content.replace(saveStatePattern, newSaveState);
        corrections++;
        console.log('✅ [PERSISTANCE-FIX] Fonction saveState sécurisée');
    }
    
    // Sauvegarder le fichier corrigé
    fs.writeFileSync(TEAM_CALENDAR_FILE, content);
    
    console.log(`✅ [PERSISTANCE-FIX] ${corrections} corrections appliquées avec succès`);
    console.log('🎯 [PERSISTANCE-FIX] Corrections appliquées :');
    console.log('   - Système de verrouillage transactionnel');
    console.log('   - Render sécurisé avec throttling');
    console.log('   - Fonctions de nettoyage et intégrité');
    console.log('   - Fonction saveState sécurisée');
    
    // Créer un script de test
    const testScript = `
// Test du système de persistance corrigé
console.log('🧪 [TEST] Démarrage test persistance...');

if (window.TeamCalendarApp) {
    // Vérifier l'intégrité
    const integrity = window.TeamCalendarApp.checkDataIntegrity();
    console.log(\`🔍 [TEST] Intégrité: \${integrity} problèmes\`);
    
    // Réparation rapide si nécessaire
    if (integrity > 0) {
        const repaired = window.TeamCalendarApp.quickRepair();
        console.log(\`🔧 [TEST] Réparation: \${repaired} éléments corrigés\`);
    }
    
    // Test render sécurisé
    if (typeof window.TeamCalendarApp.renderSafe === 'function') {
        window.TeamCalendarApp.renderSafe();
        console.log('✅ [TEST] Render sécurisé testé');
    }
    
    console.log('✅ [TEST] Test persistance terminé');
} else {
    console.warn('⚠️ [TEST] TeamCalendarApp non disponible');
}
`;
    
    fs.writeFileSync('test-persistance-fix.js', testScript);
    console.log('📝 [PERSISTANCE-FIX] Script de test créé : test-persistance-fix.js');
    
    // Créer un script de nettoyage d'urgence
    const emergencyScript = `
// Script de nettoyage d'urgence
console.log('🚨 [EMERGENCY] Nettoyage d\\'urgence...');

// Vider le localStorage corrompu
localStorage.removeItem('teamCalendarState_v1');
localStorage.removeItem('teamCalendarAppSettings');

// Nettoyer les données de session
sessionStorage.clear();

// Forcer la récupération d'urgence si TeamCalendarApp est disponible
if (window.TeamCalendarApp && typeof window.TeamCalendarApp.emergencyRecovery === 'function') {
    window.TeamCalendarApp.emergencyRecovery();
} else {
    console.log('🔄 [EMERGENCY] Rechargement complet de la page...');
    location.reload();
}

console.log('✅ [EMERGENCY] Nettoyage terminé');
`;
    
    fs.writeFileSync('emergency-cleanup.js', emergencyScript);
    console.log('🚨 [PERSISTANCE-FIX] Script d\\'urgence créé : emergency-cleanup.js');
    
} catch (error) {
    console.error('❌ [PERSISTANCE-FIX] Erreur critique:', error);
    process.exit(1);
}

console.log('🎉 [PERSISTANCE-FIX] Correction complète terminée !');
console.log('📋 [PERSISTANCE-FIX] Prochaines étapes :');
console.log('   1. Recharger l\\'application');
console.log('   2. Tester avec: script test-persistance-fix.js dans la console');
console.log('   3. En cas de problème: script emergency-cleanup.js dans la console'); 