import fs from 'fs';
import path from 'path';

const targetFile = path.resolve('src/teamCalendarApp.ts');

const searchBlock = `init: async function() {
        // ⚠️ VERROU D'INITIALISATION pour éviter les conflits HMR
        if (window.__TEAM_CALENDAR_READY__) {
            console.log('🛡️ [INIT] Blocage de la ré-initialisation');
            return;
        }
        window.__TEAM_CALENDAR_READY__ = true;`;

const replacementBlock = `init: async function() {
        // Le verrou est déplacé plus bas pour permettre le rechargement des données

        console.log('🚀 [init] Initialisation de TeamCalendarApp...');

        // Nettoyage initial
        this.clearApplicationCache();
        this.clearRenderCache();

        // Charger les données à chaque fois
        const stateLoaded = await this.loadState();
        if (!stateLoaded) {
            console.error("❌ [init] Le chargement de l'état a échoué. Arrêt de l'initialisation.");
            return;
        }

        // ⚠️ VERROU D'INITIALISATION pour les écouteurs d'événements
        if (window.__TEAM_CALENDAR_READY__) {
            console.log('🛡️ [INIT] Blocage de la ré-initialisation des écouteurs.');
            this.render(); // On fait quand même un rendu pour afficher les données chargées
            return;
        }
        window.__TEAM_CALENDAR_READY__ = true;`;

try {
    let content = fs.readFileSync(targetFile, 'utf8');

    if (content.includes(searchBlock)) {
        content = content.replace(searchBlock, replacementBlock);
        fs.writeFileSync(targetFile, content, 'utf8');
        console.log('✅ Logique d\'initialisation corrigée avec succès dans src/teamCalendarApp.ts');
    } else {
        console.log('🟡 Avertissement: La logique d\'initialisation semble déjà corrigée ou le bloc de code initial n\'a pas été trouvé.');
    }

} catch (error) {
    console.error('❌ Erreur lors de l\'application du patch d\'initialisation :', error);
} 