#!/usr/bin/env node

/**
 * Script de nettoyage et optimisation du système de logs
 * Nettoie les anciens logs, initialise le nouveau système optimisé IA
 */

// Nettoyage des logs localStorage
function cleanupLocalStorageLogs() {
  console.log('🧹 [CLEANUP] Nettoyage des anciens logs localStorage...');
  
  if (typeof window !== 'undefined') {
    // Nettoyer les anciens logs
    const oldLogKeys = [
      'app-logs',
      'workingDaysDebugLogs',
      'teamCalendarState_v1',
      'debugLogs'
    ];
    
    let cleaned = 0;
    oldLogKeys.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
        cleaned++;
        console.log(`   ✅ Supprimé: ${key}`);
      }
    });
    
    console.log(`🧹 [CLEANUP] ${cleaned} clés nettoyées du localStorage`);
  }
}

// Nettoyage des logs console
function setupConsoleOptimization() {
  console.log('⚙️ [SETUP] Configuration de l\'optimisation console...');
  
  // Sauvegarder les méthodes originales
  const originalConsole = {
    log: console.log,
    info: console.info,
    warn: console.warn,
    error: console.error,
    debug: console.debug
  };
  
  // Compteur de logs pour éviter le spam
  const logCounts = new Map();
  const MAX_SAME_LOG = 5;
  
  // Fonction d'optimisation
  function optimizeConsoleMethod(method, level) {
    return function(...args) {
      const message = args.join(' ');
      // Créer un hash simple sans btoa pour éviter les erreurs de caractères
      let hash = 0;
      for (let i = 0; i < message.length; i++) {
        const char = message.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convertir en entier 32-bit
      }
      const hashKey = Math.abs(hash).toString(16).substring(0, 8);
      
      const count = logCounts.get(hashKey) || 0;
      logCounts.set(hashKey, count + 1);
      
      if (count < MAX_SAME_LOG) {
        originalConsole[method].apply(console, args);
      } else if (count === MAX_SAME_LOG) {
        originalConsole[method].apply(console, [
          `${args[0]} (message répété, suppression des suivants)`,
          ...args.slice(1)
        ]);
      }
      // Sinon, on ignore les messages répétés
    };
  }
  
  // Appliquer l'optimisation uniquement en développement
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    console.log('🔧 [SETUP] Application de l\'optimisation console (dev mode)');
    
    // Ne pas optimiser les erreurs critiques
    console.warn = optimizeConsoleMethod('warn', 'warn');
    console.info = optimizeConsoleMethod('info', 'info');
    console.debug = optimizeConsoleMethod('debug', 'debug');
  }
  
  console.log('✅ [SETUP] Optimisation console configurée');
}

// Test du nouveau système de logs
function testNewLogSystem() {
  console.log('🧪 [TEST] Test du nouveau système de logs...');
  
  if (typeof window !== 'undefined' && window.logger) {
    const logger = window.logger;
    
    // Test des logs basiques
    logger.info('TestCleanup', 'Test du système de logs optimisé');
    logger.warn('TestCleanup', 'Test d\'un avertissement');
    logger.debug('TestCleanup', 'Test de debug (peut être filtré)');
    
    // Test de compression (logs répétés)
    for (let i = 0; i < 5; i++) {
      logger.info('TestCompression', 'Message répété pour test de compression');
    }
    
    // Test du package IA
    setTimeout(() => {
      if (window.getAIDebugPackage) {
        const aiPackage = window.getAIDebugPackage();
        console.log('📊 [TEST] Package IA généré:', {
          totalLogs: aiPackage.summary.totalLogs,
          tokenEstimate: aiPackage.tokenEstimate,
          criticalIssues: aiPackage.criticalIssues.length
        });
      }
    }, 1000);
    
    console.log('✅ [TEST] Tests du système de logs terminés');
  } else {
    console.log('⚠️ [TEST] Logger non disponible, tests ignorés');
  }
}

// Initialisation du nettoyage
function initializeLogCleanup() {
  console.log('🚀 [INIT] Initialisation du nettoyage des logs...');
  
  try {
    cleanupLocalStorageLogs();
    setupConsoleOptimization();
    // testNewLogSystem(); // Désactivé pour réduire le spam
    
    console.log('✅ [INIT] Nettoyage et optimisation terminés avec succès');
    
    // Programmer un nettoyage périodique
    if (typeof window !== 'undefined') {
      setInterval(() => {
        if (window.logger && window.logger.getHistory) {
          const history = window.logger.getHistory();
          if (history.length > 1000) {
            console.log('🧹 [AUTO] Nettoyage automatique des logs anciens');
            window.logger.clearHistory();
          }
        }
      }, 300000); // 5 minutes
    }
    
  } catch (error) {
    console.error('❌ [INIT] Erreur lors du nettoyage:', error);
  }
}

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    cleanupLocalStorageLogs,
    setupConsoleOptimization,
    testNewLogSystem,
    initializeLogCleanup
  };
}

// Auto-exécution si appelé directement
if (typeof window !== 'undefined') {
  // Attendre que le DOM soit prêt
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeLogCleanup);
  } else {
    initializeLogCleanup();
  }
} else if (typeof require !== 'undefined' && require.main === module) {
  // Exécution en Node.js
  console.log('📝 [INFO] Script de nettoyage des logs - exécution en Node.js');
  console.log('   Ce script est principalement conçu pour le navigateur');
  console.log('   Utilisez-le dans l\'application web pour un effet optimal');
} 