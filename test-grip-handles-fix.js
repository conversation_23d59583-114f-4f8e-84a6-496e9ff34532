// ========================================
// SCRIPT DE DIAGNOSTIC ET CORRECTION DES GRIP HANDLES
// ========================================

console.log('🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé');

// Fonction pour diagnostiquer l'état des GRIP handles
function diagnoseGripHandles() {
    console.log('🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...');
    
    const results = {
        totalShifts: 0,
        regularShifts: 0,
        gripsInDOM: 0,
        gripsWithEvents: 0,
        gripsResponsive: 0,
        issues: []
    };
    
    // 1. Compter tous les shifts
    const allShifts = document.querySelectorAll('.shift-card[data-shift-payload]');
    results.totalShifts = allShifts.length;
    console.log(`📊 [GRIP-FIX] Total shifts trouvés: ${results.totalShifts}`);
    
    // 2. Analyser les shifts réguliers
    allShifts.forEach((shift, index) => {
        try {
            const payload = JSON.parse(shift.dataset.shiftPayload || '{}');
            if (payload.isRegular && payload.assignmentId) {
                results.regularShifts++;
                
                const grip = shift.querySelector('.grip-regular');
                if (grip) {
                    results.gripsInDOM++;
                    
                    // Vérifier les attributs essentiels
                    const hasAssignmentId = grip.dataset.assignmentId === payload.assignmentId;
                    const isDraggable = grip.draggable === true;
                    const hasRegularFlag = grip.dataset.regular === 'true';
                    
                    if (!hasAssignmentId || !isDraggable || !hasRegularFlag) {
                        results.issues.push({
                            type: 'attributes',
                            assignmentId: payload.assignmentId,
                            shift: index,
                            details: { hasAssignmentId, isDraggable, hasRegularFlag }
                        });
                    }
                    
                    // Tester la responsivité du grip
                    const isResponsive = testGripResponsiveness(grip, payload.assignmentId);
                    if (isResponsive) {
                        results.gripsResponsive++;
                    } else {
                        results.issues.push({
                            type: 'unresponsive',
                            assignmentId: payload.assignmentId,
                            shift: index
                        });
                    }
                } else {
                    results.issues.push({
                        type: 'missing_grip',
                        assignmentId: payload.assignmentId,
                        shift: index
                    });
                }
            }
        } catch (error) {
            results.issues.push({
                type: 'parse_error',
                shift: index,
                error: error.message
            });
        }
    });
    
    console.log('📊 [GRIP-FIX] Résultats du diagnostic:', results);
    
    // Afficher les problèmes détaillés
    if (results.issues.length > 0) {
        console.log('⚠️ [GRIP-FIX] Problèmes détectés:');
        results.issues.forEach((issue, index) => {
            console.log(`${index + 1}. ${issue.type}:`, issue);
        });
    }
    
    return results;
}

// Fonction pour tester la responsivité d'un grip
function testGripResponsiveness(grip, assignmentId) {
    try {
        // Vérifier les listeners d'événements
        const hasMouseEvents = grip.onmousedown !== null || grip.ondragstart !== null;
        
        // Vérifier le style et la visibilité
        const computedStyle = window.getComputedStyle(grip);
        const isVisible = computedStyle.display !== 'none' && computedStyle.visibility !== 'hidden';
        const hasCorrectCursor = computedStyle.cursor === 'grab' || computedStyle.cursor === '-webkit-grab';
        
        // Vérifier la position et les dimensions
        const rect = grip.getBoundingClientRect();
        const hasValidDimensions = rect.width > 0 && rect.height > 0;
        
        console.log(`🔍 [GRIP-FIX] Test responsivité ${assignmentId}:`, {
            hasMouseEvents,
            isVisible,
            hasCorrectCursor,
            hasValidDimensions,
            rect: { width: rect.width, height: rect.height }
        });
        
        return isVisible && hasValidDimensions;
    } catch (error) {
        console.error(`❌ [GRIP-FIX] Erreur test responsivité ${assignmentId}:`, error);
        return false;
    }
}

// Fonction pour forcer la recréation des GRIP handles
function forceRecreateGripHandles() {
    console.log('🔧 [GRIP-FIX] Recréation forcée des GRIP handles...');
    
    if (!window.TeamCalendarApp) {
        console.error('❌ [GRIP-FIX] TeamCalendarApp non disponible');
        return false;
    }
    
    let gripsRecreated = 0;
    
    // Supprimer tous les grips existants
    const existingGrips = document.querySelectorAll('.grip-regular');
    existingGrips.forEach(grip => grip.remove());
    console.log(`🗑️ [GRIP-FIX] ${existingGrips.length} grips existants supprimés`);
    
    // Recréer les grips pour tous les shifts réguliers
    const regularShifts = document.querySelectorAll('.shift-card[data-shift-payload]');
    
    regularShifts.forEach(shift => {
        try {
            const payload = JSON.parse(shift.dataset.shiftPayload || '{}');
            
            if (payload.isRegular && payload.assignmentId) {
                console.log(`🔧 [GRIP-FIX] Recréation grip pour ${payload.assignmentId}`);
                
                // Créer le nouveau grip
                const gripDiv = document.createElement('div');
                gripDiv.className = 'grip-regular absolute inset-x-0 -bottom-[6px] h-[6px] cursor-grab bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 border border-blue-400 rounded-b-sm transition-all duration-150 opacity-60 hover:opacity-100';
                gripDiv.draggable = true;
                gripDiv.dataset.assignmentId = payload.assignmentId;
                gripDiv.dataset.regular = 'true';
                gripDiv.setAttribute('aria-label', 'Déplacer attribution régulière');
                gripDiv.title = '🎯 GRIP ATTRIBUTION RÉGULIÈRE\n\nGlissez cette barre vers un autre employé pour ouvrir le menu de confirmation.';
                
                // Attacher les événements manuellement
                attachGripEvents(gripDiv, payload.assignmentId);
                
                // Ajouter au shift
                shift.appendChild(gripDiv);
                gripsRecreated++;
                
                console.log(`✅ [GRIP-FIX] Grip recréé pour ${payload.assignmentId}`);
            }
        } catch (error) {
            console.error('❌ [GRIP-FIX] Erreur lors de la recréation:', error);
        }
    });
    
    console.log(`✅ [GRIP-FIX] ${gripsRecreated} grips recréés avec succès`);
    return gripsRecreated > 0;
}

// Fonction pour attacher les événements à un grip
function attachGripEvents(gripDiv, assignmentId) {
    console.log(`🔗 [GRIP-FIX] Attachement événements pour ${assignmentId}`);
    
    // Événement dragstart
    gripDiv.addEventListener('dragstart', (e) => {
        e.stopPropagation();
        console.log(`🎯 [GRIP-FIX] Début drag grip pour attribution ${assignmentId}`);
        
        e.dataTransfer.setData('regularAssignmentId', assignmentId);
        e.dataTransfer.setData('text/plain', '');
        e.dataTransfer.effectAllowed = 'move';
        
        // Ajouter classe de drag
        gripDiv.classList.add('dragging');
        
        // Surligner les zones de drop
        if (window.TeamCalendarApp.highlightEmployeeDropZones) {
            window.TeamCalendarApp.highlightEmployeeDropZones(true);
        }
        
        // Créer un fantôme personnalisé
        const ghost = document.createElement('div');
        ghost.className = 'drag-ghost-regular';
        ghost.textContent = 'Attribution régulière';
        ghost.style.position = 'absolute';
        ghost.style.top = '-1000px';
        document.body.appendChild(ghost);
        e.dataTransfer.setDragImage(ghost, 0, 0);
        
        // Nettoyer le fantôme après un délai
        setTimeout(() => {
            if (document.body.contains(ghost)) {
                document.body.removeChild(ghost);
            }
        }, 100);
    });
    
    // Événement dragend
    gripDiv.addEventListener('dragend', (e) => {
        console.log(`🎯 [GRIP-FIX] Fin drag grip pour attribution ${assignmentId}`);
        
        // Supprimer classe de drag
        gripDiv.classList.remove('dragging');
        
        // Supprimer surbrillance des zones de drop
        if (window.TeamCalendarApp.highlightEmployeeDropZones) {
            window.TeamCalendarApp.highlightEmployeeDropZones(false);
        }
    });
    
    // Événements de survol pour améliorer la responsivité
    gripDiv.addEventListener('mouseenter', () => {
        gripDiv.style.opacity = '1';
        gripDiv.style.transform = 'scaleY(1.2)';
    });
    
    gripDiv.addEventListener('mouseleave', () => {
        gripDiv.style.opacity = '0.6';
        gripDiv.style.transform = 'scaleY(1)';
    });
    
    console.log(`✅ [GRIP-FIX] Événements attachés pour ${assignmentId}`);
}

// Fonction pour tester un grip spécifique
function testSpecificGrip(assignmentId) {
    console.log(`🧪 [GRIP-FIX] Test spécifique du grip ${assignmentId}...`);
    
    const grip = document.querySelector(`.grip-regular[data-assignment-id="${assignmentId}"]`);
    
    if (!grip) {
        console.error(`❌ [GRIP-FIX] Grip non trouvé pour ${assignmentId}`);
        return false;
    }
    
    console.log(`✅ [GRIP-FIX] Grip trouvé pour ${assignmentId}`);
    
    // Tester les propriétés
    const tests = {
        isDraggable: grip.draggable === true,
        hasAssignmentId: grip.dataset.assignmentId === assignmentId,
        hasRegularFlag: grip.dataset.regular === 'true',
        isVisible: grip.offsetWidth > 0 && grip.offsetHeight > 0,
        hasCorrectCursor: window.getComputedStyle(grip).cursor.includes('grab')
    };
    
    console.log(`📊 [GRIP-FIX] Tests pour ${assignmentId}:`, tests);
    
    const allTestsPassed = Object.values(tests).every(Boolean);
    
    if (allTestsPassed) {
        console.log(`✅ [GRIP-FIX] Tous les tests passés pour ${assignmentId}`);
        
        // Test de simulation de drag
        console.log(`🧪 [GRIP-FIX] Simulation de drag pour ${assignmentId}...`);
        
        const dragStartEvent = new DragEvent('dragstart', {
            bubbles: true,
            cancelable: true,
            dataTransfer: new DataTransfer()
        });
        
        grip.dispatchEvent(dragStartEvent);
        console.log(`✅ [GRIP-FIX] Simulation de drag réussie pour ${assignmentId}`);
        
        return true;
    } else {
        console.error(`❌ [GRIP-FIX] Tests échoués pour ${assignmentId}`);
        return false;
    }
}

// Fonction pour corriger automatiquement les problèmes détectés
function autoFixGripIssues() {
    console.log('🔧 [GRIP-FIX] Correction automatique des problèmes...');
    
    const diagnosis = diagnoseGripHandles();
    
    if (diagnosis.issues.length === 0) {
        console.log('✅ [GRIP-FIX] Aucun problème détecté, tous les grips fonctionnent correctement');
        return true;
    }
    
    console.log(`🔧 [GRIP-FIX] ${diagnosis.issues.length} problèmes détectés, correction en cours...`);
    
    // Forcer la recréation si nécessaire
    const recreated = forceRecreateGripHandles();
    
    if (recreated) {
        // Re-diagnostiquer après correction
        setTimeout(() => {
            const newDiagnosis = diagnoseGripHandles();
            
            if (newDiagnosis.issues.length === 0) {
                console.log('🎉 [GRIP-FIX] Tous les problèmes ont été corrigés !');
                console.log(`✅ [GRIP-FIX] ${newDiagnosis.gripsResponsive}/${newDiagnosis.regularShifts} grips fonctionnels`);
            } else {
                console.warn(`⚠️ [GRIP-FIX] ${newDiagnosis.issues.length} problèmes persistent après correction`);
            }
        }, 500);
        
        return true;
    } else {
        console.error('❌ [GRIP-FIX] Échec de la correction automatique');
        return false;
    }
}

// Fonction de surveillance continue
function startGripMonitoring() {
    console.log('👁️ [GRIP-FIX] Démarrage de la surveillance continue des grips...');
    
    let monitoringInterval;
    
    const monitor = () => {
        const diagnosis = diagnoseGripHandles();
        
        if (diagnosis.issues.length > 0) {
            console.warn(`⚠️ [GRIP-MONITOR] ${diagnosis.issues.length} problèmes détectés, correction automatique...`);
            autoFixGripIssues();
        }
    };
    
    // Surveillance toutes les 10 secondes
    monitoringInterval = setInterval(monitor, 10000);
    
    // Surveillance lors des changements de DOM
    const observer = new MutationObserver((mutations) => {
        let shouldCheck = false;
        
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE && 
                        (node.classList?.contains('shift-card') || node.querySelector?.('.shift-card'))) {
                        shouldCheck = true;
                    }
                });
            }
        });
        
        if (shouldCheck) {
            setTimeout(monitor, 100);
        }
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    console.log('✅ [GRIP-FIX] Surveillance active');
    
    return {
        stop: () => {
            clearInterval(monitoringInterval);
            observer.disconnect();
            console.log('🛑 [GRIP-FIX] Surveillance arrêtée');
        }
    };
}

// Exposer les fonctions globalement
window.diagnoseGripHandles = diagnoseGripHandles;
window.forceRecreateGripHandles = forceRecreateGripHandles;
window.testSpecificGrip = testSpecificGrip;
window.autoFixGripIssues = autoFixGripIssues;
window.startGripMonitoring = startGripMonitoring;

// Auto-diagnostic au chargement
setTimeout(() => {
    console.log('🚀 [GRIP-FIX] Lancement du diagnostic automatique...');
    const diagnosis = diagnoseGripHandles();
    
    if (diagnosis.issues.length > 0) {
        console.log('🔧 [GRIP-FIX] Problèmes détectés, correction automatique...');
        autoFixGripIssues();
    }
}, 5000);

console.log('✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:');
console.log('- diagnoseGripHandles() : Diagnostic complet');
console.log('- forceRecreateGripHandles() : Recréation forcée');
console.log('- testSpecificGrip(assignmentId) : Test spécifique');
console.log('- autoFixGripIssues() : Correction automatique');
console.log('- startGripMonitoring() : Surveillance continue');
