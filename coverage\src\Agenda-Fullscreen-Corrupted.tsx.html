
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/Agenda-Fullscreen-Corrupted.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> / <a href="index.html">src</a> Agenda-Fullscreen-Corrupted.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/380</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/380</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" >import React, { useEffect, useRef } from 'react'</span></span></span>
<span class="cstat-no" title="statement not covered" >import TeamCalendarApp from './teamCalendarApp.js'</span>
<span class="cstat-no" title="statement not covered" >import './sidebar.css'</span>
<span class="cstat-no" title="statement not covered" >import './styles/fullscreen.css'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >interface AgendaProps {}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >const AgendaFullscreen: React.FC&lt;AgendaProps&gt; = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const appRef = useRef&lt;any&gt;(null)</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const initApp = async () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      try {</span>
<span class="cstat-no" title="statement not covered" >        await TeamCalendarApp.init()</span>
<span class="cstat-no" title="statement not covered" >        console.log('🎉 [Agenda] Application fullscreen initialisée avec succès')</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        // ✅ CORRECTION DIRECTE : Attacher les événements après l'init</span>
<span class="cstat-no" title="statement not covered" >        setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >          const ensureSettingsWorks = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            const settingsBtn = document.getElementById('settings-btn')</span>
<span class="cstat-no" title="statement not covered" >            const sidebarSettingsBtn = document.getElementById('sidebar-settings-btn')</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            if (settingsBtn &amp;&amp; window.TeamCalendarApp?.openSettingsModal) {</span>
<span class="cstat-no" title="statement not covered" >              console.log('🔧 [Agenda] Attachement settings header')</span>
<span class="cstat-no" title="statement not covered" >              // Supprimer les anciens listeners</span>
<span class="cstat-no" title="statement not covered" >              settingsBtn.replaceWith(settingsBtn.cloneNode(true))</span>
<span class="cstat-no" title="statement not covered" >              const newBtn = document.getElementById('settings-btn')</span>
<span class="cstat-no" title="statement not covered" >              newBtn?.addEventListener('click', (e) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                e.preventDefault()</span>
<span class="cstat-no" title="statement not covered" >                console.log('🎯 [Agenda] Clic settings header !')</span>
<span class="cstat-no" title="statement not covered" >                window.TeamCalendarApp.openSettingsModal()</span>
<span class="cstat-no" title="statement not covered" >              })</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            if (sidebarSettingsBtn &amp;&amp; window.TeamCalendarApp?.openSettingsModal) {</span>
<span class="cstat-no" title="statement not covered" >              console.log('🔧 [Agenda] Attachement settings sidebar')</span>
<span class="cstat-no" title="statement not covered" >              // Supprimer les anciens listeners</span>
<span class="cstat-no" title="statement not covered" >              sidebarSettingsBtn.replaceWith(sidebarSettingsBtn.cloneNode(true))</span>
<span class="cstat-no" title="statement not covered" >              const newSidebarBtn = document.getElementById('sidebar-settings-btn')</span>
<span class="cstat-no" title="statement not covered" >              newSidebarBtn?.addEventListener('click', (e) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                e.preventDefault()</span>
<span class="cstat-no" title="statement not covered" >                console.log('🎯 [Agenda] Clic settings sidebar !')</span>
<span class="cstat-no" title="statement not covered" >                window.TeamCalendarApp.openSettingsModal()</span>
<span class="cstat-no" title="statement not covered" >              })</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          // Tentatives multiples d'attachement</span>
<span class="cstat-no" title="statement not covered" >          ensureSettingsWorks()</span>
<span class="cstat-no" title="statement not covered" >          setTimeout(ensureSettingsWorks, 1000)</span>
<span class="cstat-no" title="statement not covered" >          setTimeout(ensureSettingsWorks, 3000)</span>
<span class="cstat-no" title="statement not covered" >        }, 500)</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >      } catch (error) {</span>
<span class="cstat-no" title="statement not covered" >        console.error('❌ [Agenda] Erreur lors de l\'initialisation:', error)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const timer = setTimeout(initApp, 100)</span>
<span class="cstat-no" title="statement not covered" >    return () =&gt; clearTimeout(timer)</span>
<span class="cstat-no" title="statement not covered" >  }, [])</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="fullscreen-app h-screen w-screen overflow-hidden bg-gradient-to-br from-slate-100 to-sky-100 text-slate-900 flex" </span>
<span class="cstat-no" title="statement not covered" >         style={{fontFamily: 'Inter, "Noto Sans", sans-serif'}}&gt;</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      {/* SIDEBAR FIXE RÉTRACTABLE */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;aside className="sidebar-fixed group fixed left-0 top-0 h-full bg-white/95 backdrop-blur-md border-r border-slate-200/60 shadow-2xl z-50 w-16 hover:w-64 transition-all duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="sidebar-content flex flex-col h-full"&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* Logo/Brand */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="sidebar-brand h-16 flex items-center justify-center border-b border-slate-200/50 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="size-8 text-blue-600 group-hover:scale-110 transition-transform duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;path d="M44 4H30.6666V17.3334H17.3334V30.6666H4V44H44V4Z" fill="currentColor" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;span className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 font-bold text-slate-800 whitespace-nowrap"&gt;</span>
<span class="cstat-no" title="statement not covered" >              TeamCalendar</span>
<span class="cstat-no" title="statement not covered" >            &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* Navigation */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;nav className="sidebar-nav flex-1 p-2 space-y-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;a href="#calendar" className="sidebar-link active flex items-center p-3 rounded-xl bg-blue-100/80 text-blue-700 shadow-md"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="material-icons-outlined text-xl"&gt;calendar_view_week&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap font-medium"&gt;Calendrier&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            &lt;a href="#employees" className="sidebar-link flex items-center p-3 rounded-xl text-slate-600 hover:bg-slate-100/80 hover:text-slate-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="material-icons-outlined text-xl"&gt;people&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap font-medium"&gt;Employés&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            &lt;button id="sidebar-settings-btn" className="sidebar-link flex items-center p-3 rounded-xl text-slate-600 hover:bg-slate-100/80 hover:text-slate-800 w-full text-left"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="material-icons-outlined text-xl"&gt;settings&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap font-medium"&gt;Paramètres&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/nav&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* Pied de sidebar */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="sidebar-footer p-4 border-t border-slate-200/50 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-full size-8 flex items-center justify-center text-white font-bold text-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                A</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-sm font-medium text-slate-700 whitespace-nowrap"&gt;Admin&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-xs text-slate-500 whitespace-nowrap"&gt;En ligne&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/aside&gt;</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      {/* CONTENU PRINCIPAL AVEC MARGE SIDEBAR */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="main-content flex flex-col w-full h-full ml-16"&gt;</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        {/* HEADER FIXE */}</span>
<span class="cstat-no" title="statement not covered" >        &lt;header className="header-fixed h-16 flex items-center justify-between px-6 bg-white/95 backdrop-blur-md border-b border-slate-300/60 shadow-sm flex-shrink-0 z-40"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex items-center gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;h2 className="text-slate-900 text-xl font-semibold"&gt;Calendrier d'Équipe&lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex items-center gap-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >            {/* Navigation temporelle */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;nav className="hidden md:flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button className="nav-btn text-slate-600 hover:text-blue-600 text-sm font-medium px-3 py-2 rounded-lg hover:bg-slate-200/70"&gt;Aujourd'hui&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button className="nav-btn text-blue-600 bg-blue-100/80 text-sm font-semibold px-3 py-2 rounded-lg shadow-sm"&gt;Semaine&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button className="nav-btn text-slate-600 hover:text-blue-600 text-sm font-medium px-3 py-2 rounded-lg hover:bg-slate-200/70"&gt;Mois&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/nav&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            {/* Actions header */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="settings-btn" className="header-btn flex items-center justify-center rounded-lg h-10 w-10 text-slate-500 hover:bg-slate-200/70 hover:text-slate-700"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="material-icons-outlined text-xl"&gt;settings&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-full size-10 flex items-center justify-center text-white font-bold"&gt;</span>
<span class="cstat-no" title="statement not covered" >                A</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/header&gt;</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        {/* ZONE DE CONTENU COMPARTIMENTÉE */}</span>
<span class="cstat-no" title="statement not covered" >        &lt;main className="content-area flex-1 flex flex-col overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* BARRE DE CONTRÔLES FIXE */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;section className="controls-bar h-20 flex items-center justify-between px-6 py-4 bg-white/80 backdrop-blur-sm border-b border-slate-200/40 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="prev-week-btn" className="control-btn p-2.5 rounded-full hover:bg-slate-200/80 text-slate-500 hover:text-slate-700"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="material-icons-outlined text-xl"&gt;chevron_left&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;h2 id="current-week-display" className="week-display text-slate-900 text-lg font-semibold min-w-[300px] text-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >                13 - 19 Octobre, 2024</span>
<span class="cstat-no" title="statement not covered" >              &lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="next-week-btn" className="control-btn p-2.5 rounded-full hover:bg-slate-200/80 text-slate-500 hover:text-slate-700"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="material-icons-outlined text-xl"&gt;chevron_right&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button className="action-btn flex items-center gap-2 text-slate-600 bg-white hover:bg-slate-50 border border-slate-300 font-medium py-2 px-4 rounded-lg text-sm shadow-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="material-icons-outlined text-lg"&gt;upload&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                Exporter</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="add-shift-button" className="action-btn flex items-center gap-2 text-white bg-blue-600 hover:bg-blue-700 font-medium py-2 px-4 rounded-lg text-sm shadow-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="material-icons-outlined text-lg"&gt;add&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                Ajouter shift</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/section&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* GRILLE CALENDRIER ADAPTATIVE */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;section className="calendar-grid flex-1 overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="calendar-container h-full border border-slate-300/40 rounded-lg shadow-lg bg-white/70 mx-6 mb-6" id="schedule-container"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="calendar-content h-full grid grid-cols-[240px_1fr] overflow-hidden" id="schedule-grid-scrollable-content"&gt;</span>
<span class="cstat-no" title="statement not covered" >                </span>
<span class="cstat-no" title="statement not covered" >                {/* COLONNE EMPLOYÉS/POSTES FIXE */}</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="employees-column bg-slate-100/60 border-r border-slate-300/40 shadow-sm flex flex-col overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  {/* Header employés */}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="employees-header h-16 flex items-center justify-start pl-6 border-b border-slate-300/40 bg-slate-50/80 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;p className="text-slate-700 text-sm font-semibold tracking-wider"&gt;Employé(e)&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  </span>
<span class="cstat-no" title="statement not covered" >                  {/* Liste employés scrollable si nécessaire */}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div id="employee-list-container" className="employees-list flex-1 overflow-y-auto min-h-0"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  </span>
<span class="cstat-no" title="statement not covered" >                  {/* Section postes non attribués */}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="posts-section flex-shrink-0 border-t border-slate-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="posts-header h-14 flex items-center pl-6 border-b border-slate-300/40 bg-slate-50/80"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;h3 className="text-slate-600 text-sm font-semibold tracking-wider"&gt;Postes disponibles&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div id="available-posts-container" className="posts-list max-h-32 overflow-y-auto"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                </span>
<span class="cstat-no" title="statement not covered" >                {/* GRILLE JOURS ADAPTATIVE */}</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="days-grid overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div id="schedule-grid-content" className="h-full grid grid-cols-7 overflow-hidden"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/section&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* BARRE DE STATUTS FIXE EN BAS */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;section className="stats-bar h-16 px-6 py-3 bg-white/80 backdrop-blur-sm border-t border-slate-200/40 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div id="stats-output" className="text-sm text-slate-600 flex items-center"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/section&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/main&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      {/* MODAL PARAMÈTRES COMPLET */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;div id="settings-modal" className="hidden fixed inset-0 bg-black/40 backdrop-blur-sm z-50 flex items-center justify-center p-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl h-[90vh] flex flex-col"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;header className="flex items-center justify-between p-4 border-b border-slate-200 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="material-icons-outlined text-blue-600 text-2xl"&gt;settings&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;h3 className="text-lg font-semibold text-slate-800"&gt;Paramètres de l'Application&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;button id="settings-modal-close" className="p-2 rounded-full hover:bg-slate-200/70"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="material-icons-outlined text-slate-600"&gt;close&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/header&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="p-2 border-b border-slate-200 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex gap-2 flex-wrap"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="tab-posts" className="tab-btn active px-4 py-2 rounded-lg text-sm font-medium"&gt;Postes&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="tab-vacations" className="tab-btn px-4 py-2 rounded-lg text-sm font-medium"&gt;Vacances&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="tab-assignments" className="tab-btn px-4 py-2 rounded-lg text-sm font-medium"&gt;Attributions&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="tab-employee-templates" className="tab-btn px-4 py-2 rounded-lg text-sm font-medium"&gt;🗂️ Modèle de fiche&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="tab-employees" className="tab-btn px-4 py-2 rounded-lg text-sm font-medium"&gt;👤 Gestion employés&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="tab-general" className="tab-btn px-4 py-2 rounded-lg text-sm font-medium"&gt;Général&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          {/* Contenu modal adaptatif */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex-1 overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            {/* Onglet Postes */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div id="tab-content-posts" className="tab-content h-full p-4 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div id="posts-config-container" className="space-y-3 mb-4"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button id="add-post-btn" className="w-full flex items-center justify-center gap-2 text-white bg-blue-600 hover:bg-blue-700 font-medium py-2.5 px-4 rounded-xl text-sm shadow-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="material-icons-outlined text-xl"&gt;add&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                Ajouter un nouveau poste</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            {/* Onglet Vacances */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div id="tab-content-vacations" className="tab-content hidden h-full p-4 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="mb-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;h4 className="font-medium text-slate-700 mb-3"&gt;Périodes de Vacances Globales&lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div id="global-vacations-list" className="space-y-2 mb-4"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;button id="add-vacation-btn" className="w-full flex items-center justify-center gap-2 text-white bg-green-600 hover:bg-green-700 font-medium py-2.5 px-4 rounded-xl text-sm shadow-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="material-icons-outlined text-xl"&gt;add&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  Ajouter une période</span>
<span class="cstat-no" title="statement not covered" >                &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            {/* Onglet Attributions */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div id="tab-content-assignments" className="tab-content hidden h-full p-4 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="mb-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;h4 className="font-medium text-slate-700 mb-3"&gt;Attributions Automatiques&lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div id="regular-assignments-list" className="space-y-2 mb-4"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="flex gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;button id="add-assignment-btn" className="flex-1 flex items-center justify-center gap-2 text-white bg-purple-600 hover:bg-purple-700 font-medium py-2.5 px-4 rounded-xl text-sm shadow-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span className="material-icons-outlined text-xl"&gt;add&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    Ajouter une attribution</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;button id="remove-assignments-btn" className="flex-1 flex items-center justify-center gap-2 text-white bg-red-600 hover:bg-red-700 font-medium py-2.5 px-4 rounded-xl text-sm shadow-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span className="material-icons-outlined text-xl"&gt;delete_sweep&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    Retirer attributions</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            {/* Onglet Modèle de fiche employé */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div id="tab-content-employee-templates" className="tab-content hidden h-full p-4 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="space-y-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="flex items-center justify-between"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;h4 className="font-medium text-slate-700 flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="material-icons-outlined text-blue-600"&gt;description&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                      Modèles de Fiches Employé</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;p className="text-sm text-slate-500 mt-1"&gt;Configurez les champs disponibles pour les fiches employé&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;button id="add-template-btn" className="flex items-center gap-2 text-white bg-blue-600 hover:bg-blue-700 font-medium py-2 px-4 rounded-lg text-sm shadow-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span className="material-icons-outlined text-lg"&gt;add&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    Nouveau modèle</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div id="employee-templates-list" className="space-y-3"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            {/* Onglet Gestion des employés */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div id="tab-content-employees" className="tab-content hidden h-full p-4 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="space-y-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="flex items-center justify-between"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;h4 className="font-medium text-slate-700 flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="material-icons-outlined text-green-600"&gt;people&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                      Gestion des Employés</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;p className="text-sm text-slate-500 mt-1"&gt;Modifiez les informations personnelles et avatars de vos employés&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;button id="add-employee-btn" className="flex items-center gap-2 text-white bg-green-600 hover:bg-green-700 font-medium py-2 px-4 rounded-lg text-sm shadow-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span className="material-icons-outlined text-lg"&gt;person_add&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    Nouvel employé</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="mb-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;label className="block text-sm font-medium text-slate-700 mb-2"&gt;Modèle de fiche à utiliser&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;select id="template-selector" className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"&gt;&lt;/select&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div id="employees-management-list" className="space-y-3"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >            {/* Onglet Paramètres Généraux */}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div id="tab-content-general" className="tab-content hidden h-full p-4 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="space-y-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;h4 className="font-medium text-slate-700 mb-3 flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span className="material-icons-outlined text-blue-600"&gt;calendar_view_week&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    Affichage de la Semaine</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="bg-slate-50 p-4 rounded-lg space-y-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;label className="block text-sm font-medium text-slate-700 mb-2"&gt;Premier jour de la semaine&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div id="week-start-container" className="grid grid-cols-4 gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;label className="flex items-center gap-2 cursor-pointer"&gt;&lt;input type="radio" name="week-start" value="monday" className="text-blue-600 focus:ring-blue-500" /&gt; &lt;span className="text-sm"&gt;Lundi&lt;/span&gt;&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;label className="flex items-center gap-2 cursor-pointer"&gt;&lt;input type="radio" name="week-start" value="tuesday" className="text-blue-600 focus:ring-blue-500" /&gt; &lt;span className="text-sm"&gt;Mardi&lt;/span&gt;&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;label className="flex items-center gap-2 cursor-pointer"&gt;&lt;input type="radio" name="week-start" value="wednesday" className="text-blue-600 focus:ring-blue-500" /&gt; &lt;span className="text-sm"&gt;Mercredi&lt;/span&gt;&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;label className="flex items-center gap-2 cursor-pointer"&gt;&lt;input type="radio" name="week-start" value="thursday" className="text-blue-600 focus:ring-blue-500" /&gt; &lt;span className="text-sm"&gt;Jeudi&lt;/span&gt;&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;label className="flex items-center gap-2 cursor-pointer"&gt;&lt;input type="radio" name="week-start" value="friday" className="text-blue-600 focus:ring-blue-500" /&gt; &lt;span className="text-sm"&gt;Vendredi&lt;/span&gt;&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;label className="flex items-center gap-2 cursor-pointer"&gt;&lt;input type="radio" name="week-start" value="saturday" className="text-blue-600 focus:ring-blue-500" /&gt; &lt;span className="text-sm"&gt;Samedi&lt;/span&gt;&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;label className="flex items-center gap-2 cursor-pointer"&gt;&lt;input type="radio" name="week-start" value="sunday" className="text-blue-600 focus:ring-blue-500" defaultChecked /&gt; &lt;span className="text-sm"&gt;Dimanche&lt;/span&gt;&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;p className="text-xs text-slate-500 mt-2"&gt;Modifie l'ordre d'affichage des jours dans le calendrier&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                </span>
<span class="cstat-no" title="statement not covered" >                &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;h4 className="font-medium text-slate-700 mb-3 flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span className="material-icons-outlined text-green-600"&gt;storage&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    Données de l'Application</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="bg-slate-50 p-4 rounded-lg space-y-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;button id="export-data-btn" className="flex-1 flex items-center justify-center gap-2 text-blue-600 bg-blue-50 hover:bg-blue-100 border border-blue-200 font-medium py-2.5 px-4 rounded-lg text-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;span className="material-icons-outlined text-lg"&gt;download&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                        Exporter</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;button id="import-data-btn" className="flex-1 flex items-center justify-center gap-2 text-green-600 bg-green-50 hover:bg-green-100 border border-green-200 font-medium py-2.5 px-4 rounded-lg text-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;span className="material-icons-outlined text-lg"&gt;upload&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                        Importer</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;button id="reset-data-btn" className="w-full flex items-center justify-center gap-2 text-red-600 bg-red-50 hover:bg-red-100 border border-red-200 font-medium py-2.5 px-4 rounded-lg text-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="material-icons-outlined text-lg"&gt;refresh&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                      Réinitialiser toutes les données</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;p className="text-xs text-slate-500"&gt;Les données sont sauvegardées automatiquement dans votre navigateur&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            </span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >  )</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >export default AgendaFullscreen </span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-07T17:35:30.510Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    