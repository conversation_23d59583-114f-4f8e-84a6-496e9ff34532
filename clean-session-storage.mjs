#!/usr/bin/env node

/**
 * Script pour nettoyer complètement les sessions logs
 * et forcer une nouvelle session propre
 */

import { execSync } from 'child_process';

console.log('🧹 [CLEAN-SESSIONS] Nettoyage complet des sessions logs');
console.log('='.repeat(60));

try {
  // 1. Purger complètement la base de données
  console.log('💀 [CLEAN] Purge complète base de données...');
  try {
    const response = await fetch('http://localhost:3001/api/debug/sessions/purge-all', {
      method: 'DELETE'
    });
    if (response.ok) {
      const result = await response.json();
      console.log(`✅ [CLEAN] ${result.message}`);
    } else {
      console.log('⚠️ [CLEAN] Serveur non disponible, purge manuelle nécessaire');
    }
  } catch (e) {
    console.log('⚠️ [CLEAN] Serveur non démarré, purge manuelle lors du prochain démarrage');
  }

  // 2. <PERSON><PERSON><PERSON>rer script de nettoyage localStorage
  console.log('🧹 [CLEAN] Génération script nettoyage navigateur...');
  
  const cleanupScript = `
// Script de nettoyage localStorage sessions logs
(function cleanupLogsSessions() {
  console.log('🧹 [CLEANUP] Nettoyage localStorage sessions logs...');
  
  // Supprimer toutes les clés liées aux logs
  const keysToRemove = [
    'logs_session_id',
    'logs_server_start',
    'logs_session_timestamp'
  ];
  
  keysToRemove.forEach(key => {
    if (localStorage.getItem(key)) {
      localStorage.removeItem(key);
      console.log('🗑️ [CLEANUP] Supprimé:', key);
    }
  });
  
  // Supprimer aussi toutes les clés commençant par 'logs_'
  const allKeys = Object.keys(localStorage);
  const logKeys = allKeys.filter(key => key.startsWith('logs_'));
  
  logKeys.forEach(key => {
    localStorage.removeItem(key);
    console.log('🗑️ [CLEANUP] Supprimé (pattern):', key);
  });
  
  console.log('✅ [CLEANUP] Nettoyage localStorage terminé');
  console.log('💡 [CLEANUP] Rechargez la page pour obtenir une nouvelle session');
  
  // Recharger automatiquement après 2 secondes
  setTimeout(() => {
    console.log('🔄 [CLEANUP] Rechargement automatique...');
    location.reload();
  }, 2000);
})();
`;

  // 3. Injecter le script dans la console (instructions)
  console.log('📋 [INSTRUCTIONS] Pour nettoyer le navigateur :');
  console.log('1. Ouvrir http://localhost:5173');
  console.log('2. Ouvrir la console développeur (F12)');
  console.log('3. Coller et exécuter ce script :');
  console.log('');
  console.log(cleanupScript);
  console.log('');
  
  console.log('🎯 [INFO] Ou simplement :');
  console.log('localStorage.clear(); location.reload();');
  console.log('');
  
  console.log('✅ [CLEAN-SESSIONS] Nettoyage préparé');
  console.log('🚀 [INFO] Le prochain démarrage aura une session totalement propre');
  
} catch (error) {
  console.error('❌ [CLEAN-SESSIONS] Erreur:', error.message);
  process.exit(1);
} 