import { query, getClient } from '../server/config/database.js';

console.log('🔧 [MIGRATION] Correction des clés de dates corrompues...');
console.log('============================================================');

async function fixCorruptedDateKeys() {
    const client = await getClient();
    
    try {
        await client.query('BEGIN');
        
        console.log('📊 [ANALYSE] Analyse des données corrompues...');
        
        // 1. Analyser l'état actuel des shifts
        const corruptedShiftsResult = await client.query(`
            SELECT 
                id,
                employee_id,
                date_key,
                LENGTH(date_key) as key_length,
                CASE 
                    WHEN date_key ~ '^\\d{4}-\\d{2}-\\d{2}T' THEN 'ISO_CORRUPTED'
                    WHEN date_key ~ '^\\d{4}-\\d{2}-\\d{2}$' THEN 'VALID'
                    ELSE 'OTHER_FORMAT'
                END as format_type
            FROM shifts
            WHERE date_key !~ '^\\d{4}-\\d{2}-\\d{2}$'
            ORDER BY date_key
        `);
        
        console.log(`📋 [ANALYSE] ${corruptedShiftsResult.rows.length} shifts avec des clés corrompues trouvés`);
        
        if (corruptedShiftsResult.rows.length > 0) {
            console.log('🔍 [EXEMPLE] Premiers exemples de corruption:');
            corruptedShiftsResult.rows.slice(0, 5).forEach((row, index) => {
                console.log(`  ${index + 1}. "${row.date_key}" (${row.format_type}, longueur: ${row.key_length})`);
            });
        }
        
        // 2. Corriger les clés de dates corrompues dans shifts
        console.log('🔄 [CORRECTION] Correction des date_key dans la table shifts...');
        
        const fixShiftsResult = await client.query(`
            UPDATE shifts 
            SET date_key = CASE 
                WHEN date_key ~ '^\\d{4}-\\d{2}-\\d{2}T' THEN 
                    SUBSTRING(date_key FROM 1 FOR 10)
                WHEN date_key ~ '^\\d{4}-\\d{2}-\\d{2}\\s' THEN 
                    SUBSTRING(date_key FROM 1 FOR 10)
                ELSE 
                    date_key
            END
            WHERE date_key !~ '^\\d{4}-\\d{2}-\\d{2}$'
            RETURNING id, date_key
        `);
        
        console.log(`✅ [CORRECTION] ${fixShiftsResult.rows.length} shifts corrigés dans la table shifts`);
        
        // 3. Vérifier et corriger les excluded_dates dans regular_assignments
        console.log('🔄 [CORRECTION] Vérification des excluded_dates...');
        
        const excludedDatesResult = await client.query(`
            SELECT 
                id,
                excluded_dates,
                array_length(excluded_dates, 1) as nb_exclusions
            FROM regular_assignments
            WHERE excluded_dates IS NOT NULL 
            AND array_length(excluded_dates, 1) > 0
        `);
        
        console.log(`📋 [ANALYSE] ${excludedDatesResult.rows.length} attributions avec des dates exclues`);
        
        let fixedExclusions = 0;
        
        for (const row of excludedDatesResult.rows) {
            const excludedDates = row.excluded_dates;
            let needsUpdate = false;
            const cleanedDates = [];
            
            for (const dateStr of excludedDates) {
                if (typeof dateStr === 'string' && dateStr.includes('T')) {
                    // Date corrompue avec timestamp
                    const cleanDate = dateStr.substring(0, 10);
                    cleanedDates.push(cleanDate);
                    needsUpdate = true;
                } else if (typeof dateStr === 'string' && /^\\d{4}-\\d{2}-\\d{2}$/.test(dateStr)) {
                    // Date déjà propre
                    cleanedDates.push(dateStr);
                } else {
                    // Essayer de parser la date
                    try {
                        const parsedDate = new Date(dateStr);
                        if (!isNaN(parsedDate.getTime())) {
                            const cleanDate = parsedDate.toISOString().substring(0, 10);
                            cleanedDates.push(cleanDate);
                            needsUpdate = true;
                        }
                    } catch (e) {
                        console.warn(`⚠️ [EXCLUSION] Date non parsable ignorée: ${dateStr}`);
                    }
                }
            }
            
            if (needsUpdate && cleanedDates.length > 0) {
                await client.query(`
                    UPDATE regular_assignments 
                    SET excluded_dates = $1
                    WHERE id = $2
                `, [cleanedDates, row.id]);
                
                fixedExclusions++;
                console.log(`✅ [EXCLUSION] Attribution ${row.id}: ${excludedDates.length} → ${cleanedDates.length} dates nettoyées`);
            }
        }
        
        console.log(`✅ [CORRECTION] ${fixedExclusions} attributions avec exclusions corrigées`);
        
        // 4. Supprimer les doublons potentiels créés par la correction
        console.log('🔄 [NETTOYAGE] Suppression des doublons...');
        
        const duplicatesResult = await client.query(`
            WITH duplicates AS (
                SELECT 
                    id,
                    employee_id,
                    post_id,
                    date_key,
                    ROW_NUMBER() OVER (
                        PARTITION BY employee_id, post_id, date_key, assignment_id 
                        ORDER BY created_at DESC
                    ) as rn
                FROM shifts
                WHERE assignment_id IS NOT NULL
            )
            DELETE FROM shifts 
            WHERE id IN (
                SELECT id FROM duplicates WHERE rn > 1
            )
            RETURNING id
        `);
        
        console.log(`✅ [NETTOYAGE] ${duplicatesResult.rows.length} doublons supprimés`);
        
        // 5. Vérification finale
        console.log('📊 [VÉRIFICATION] Vérification finale...');
        
        const finalCheckResult = await client.query(`
            SELECT 
                COUNT(*) as total_shifts,
                COUNT(CASE WHEN date_key ~ '^\\d{4}-\\d{2}-\\d{2}$' THEN 1 END) as valid_format,
                COUNT(CASE WHEN date_key !~ '^\\d{4}-\\d{2}-\\d{2}$' THEN 1 END) as invalid_format
            FROM shifts
        `);
        
        const stats = finalCheckResult.rows[0];
        console.log(`📈 [STATISTIQUES] ${stats.total_shifts} shifts total`);
        console.log(`✅ [STATISTIQUES] ${stats.valid_format} avec format valide`);
        console.log(`❌ [STATISTIQUES] ${stats.invalid_format} avec format invalide`);
        
        // 6. Créer un index pour optimiser les requêtes futures
        console.log('⚡ [OPTIMISATION] Création d\'index optimisés...');
        
        try {
            await client.query(`
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_shifts_date_key_optimized 
                ON shifts(date_key) 
                WHERE date_key ~ '^\\d{4}-\\d{2}-\\d{2}$'
            `);
            console.log('✅ [INDEX] Index sur date_key créé');
        } catch (e) {
            console.log('ℹ️ [INDEX] Index déjà existant ou erreur:', e.message);
        }
        
        await client.query('COMMIT');
        
        console.log('============================================================');
        console.log('🎉 [SUCCÈS] Migration terminée avec succès !');
        console.log(`📊 [RÉSUMÉ] ${fixShiftsResult.rows.length} shifts corrigés`);
        console.log(`📊 [RÉSUMÉ] ${fixedExclusions} attributions avec exclusions corrigées`);
        console.log(`📊 [RÉSUMÉ] ${duplicatesResult.rows.length} doublons supprimés`);
        
        if (stats.invalid_format === '0') {
            console.log('✅ [VALIDATION] Toutes les dates sont maintenant au format correct !');
        } else {
            console.log(`⚠️ [ATTENTION] ${stats.invalid_format} dates nécessitent encore une correction manuelle`);
        }
        
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('❌ [ERREUR] Erreur lors de la migration:', error);
        throw error;
    } finally {
        client.release();
    }
}

// Exécuter la migration
fixCorruptedDateKeys()
    .then(() => {
        console.log('🚀 [TERMINÉ] Script de migration exécuté avec succès');
        process.exit(0);
    })
    .catch((error) => {
        console.error('💥 [ÉCHEC] Échec de la migration:', error);
        process.exit(1);
    }); 