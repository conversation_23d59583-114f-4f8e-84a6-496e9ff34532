#!/usr/bin/env node

/**
 * Script de test de connexion Frontend ↔ Backend
 * Vérifie que toutes les communications fonctionnent correctement
 */

console.log('🧪 [TEST] Test de connexion Frontend ↔ Backend...\n');

class ConnectionTester {
  constructor() {
    this.results = [];
  }

  async testBackendDirect() {
    console.log('🔧 [TEST] Test Backend Direct...');
    
    try {
      const response = await fetch('http://localhost:3001/api/health', {
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.ok) {
        const data = await response.json();
        this.results.push({
          test: 'Backend Direct',
          status: '✅ SUCCÈS',
          details: `Status: ${data.status}`,
          url: 'http://localhost:3001/api/health'
        });
        console.log('✅ Backend accessible directement');
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      this.results.push({
        test: 'Backend Direct',
        status: '❌ ÉCHEC',
        details: error.message,
        url: 'http://localhost:3001/api/health'
      });
      console.log('❌ Backend non accessible:', error.message);
    }
  }

  async testProxyVite() {
    console.log('🎨 [TEST] Test Proxy Vite...');
    
    try {
      const response = await fetch('http://localhost:5173/api/health', {
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.ok) {
        const data = await response.json();
        this.results.push({
          test: 'Proxy Vite',
          status: '✅ SUCCÈS',
          details: `Status: ${data.status}`,
          url: 'http://localhost:5173/api/health'
        });
        console.log('✅ Proxy Vite fonctionnel');
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      this.results.push({
        test: 'Proxy Vite',
        status: '❌ ÉCHEC',
        details: error.message,
        url: 'http://localhost:5173/api/health'
      });
      console.log('❌ Proxy Vite non fonctionnel:', error.message);
    }
  }

  async testEmployeesAPI() {
    console.log('👥 [TEST] Test API Employés...');
    
    try {
      const response = await fetch('http://localhost:5173/api/employees', {
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.ok) {
        const employees = await response.json();
        this.results.push({
          test: 'API Employés',
          status: '✅ SUCCÈS',
          details: `${employees.length} employé(s) trouvé(s)`,
          url: 'http://localhost:5173/api/employees'
        });
        console.log(`✅ API Employés accessible: ${employees.length} employé(s)`);
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      this.results.push({
        test: 'API Employés',
        status: '❌ ÉCHEC',
        details: error.message,
        url: 'http://localhost:5173/api/employees'
      });
      console.log('❌ API Employés non accessible:', error.message);
    }
  }

  async testStandardPostsAPI() {
    console.log('📋 [TEST] Test API Postes Standards...');
    
    try {
      const response = await fetch('http://localhost:5173/api/standard-posts', {
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.ok) {
        const posts = await response.json();
        this.results.push({
          test: 'API Postes Standards',
          status: '✅ SUCCÈS',
          details: `${posts.length} poste(s) trouvé(s)`,
          url: 'http://localhost:5173/api/standard-posts'
        });
        console.log(`✅ API Postes Standards accessible: ${posts.length} poste(s)`);
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      this.results.push({
        test: 'API Postes Standards',
        status: '❌ ÉCHEC',
        details: error.message,
        url: 'http://localhost:5173/api/standard-posts'
      });
      console.log('❌ API Postes Standards non accessible:', error.message);
    }
  }

  async testShiftsAPI() {
    console.log('📅 [TEST] Test API Quarts...');
    
    try {
      const response = await fetch('http://localhost:5173/api/shifts', {
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.ok) {
        const shifts = await response.json();
        this.results.push({
          test: 'API Quarts',
          status: '✅ SUCCÈS',
          details: `${shifts.length} quart(s) trouvé(s)`,
          url: 'http://localhost:5173/api/shifts'
        });
        console.log(`✅ API Quarts accessible: ${shifts.length} quart(s)`);
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      this.results.push({
        test: 'API Quarts',
        status: '❌ ÉCHEC',
        details: error.message,
        url: 'http://localhost:5173/api/shifts'
      });
      console.log('❌ API Quarts non accessible:', error.message);
    }
  }

  async testFrontendAccess() {
    console.log('🌐 [TEST] Test Accès Frontend...');
    
    try {
      const response = await fetch('http://localhost:5173/', {
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.ok) {
        this.results.push({
          test: 'Frontend Access',
          status: '✅ SUCCÈS',
          details: `Code: ${response.status}`,
          url: 'http://localhost:5173/'
        });
        console.log('✅ Frontend accessible');
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      this.results.push({
        test: 'Frontend Access',
        status: '❌ ÉCHEC',
        details: error.message,
        url: 'http://localhost:5173/'
      });
      console.log('❌ Frontend non accessible:', error.message);
    }
  }

  async runAllTests() {
    console.log('🚀 Démarrage des tests de connexion...\n');
    
    await this.testBackendDirect();
    await this.testProxyVite();
    await this.testFrontendAccess();
    await this.testEmployeesAPI();
    await this.testStandardPostsAPI();
    await this.testShiftsAPI();
    
    this.displayResults();
  }

  displayResults() {
    console.log('\n📊 ========================================');
    console.log('📊 RÉSULTATS DES TESTS DE CONNEXION');
    console.log('📊 ========================================\n');
    
    const successful = this.results.filter(r => r.status.includes('✅')).length;
    const failed = this.results.filter(r => r.status.includes('❌')).length;
    
    this.results.forEach(result => {
      console.log(`${result.status} ${result.test}`);
      console.log(`   └─ ${result.details}`);
      console.log(`   └─ URL: ${result.url}\n`);
    });
    
    console.log('📊 ========================================');
    console.log(`📊 RÉSUMÉ: ${successful}/${this.results.length} tests réussis`);
    
    if (failed === 0) {
      console.log('🎉 TOUTES LES CONNEXIONS FONCTIONNENT !');
      console.log('🎉 Votre système est prêt à l\'emploi.');
    } else {
      console.log(`⚠️  ${failed} test(s) échoué(s) - Vérifiez les services`);
    }
    
    console.log('📊 ========================================\n');
    
    console.log('💡 URLS UTILES:');
    console.log('   Frontend:  http://localhost:5173');
    console.log('   Backend:   http://localhost:3001');
    console.log('   API:       http://localhost:5173/api/*');
    console.log('   Health:    http://localhost:5173/api/health\n');
  }
}

// Démarrer les tests
const tester = new ConnectionTester();
tester.runAllTests().catch(error => {
  console.error('❌ Erreur lors des tests:', error);
  process.exit(1);
}); 