-- =====================================================
-- MIGRATION 004: REFACTOR REGULAR ASSIGNMENTS
-- Date: 2025-06-18
-- Description: Refactoriser les attributions régulières
--              pour supporter les plages multi-jours
-- =====================================================

-- Étape 1: Créer une nouvelle table temporaire avec la structure optimisée
CREATE TABLE regular_assignments_new (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    post_id UUID NOT NULL REFERENCES standard_posts(id) ON DELETE CASCADE,
    days_of_week INTEGER[] NOT NULL DEFAULT '{}', -- <PERSON><PERSON> de <PERSON> se<PERSON>ine (0-6)
    start_date DATE NOT NULL,
    end_date DATE, -- NULL si illimité
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Étape 2: Migrer les données existantes en groupant par employé/poste/dates
INSERT INTO regular_assignments_new (id, employee_id, post_id, days_of_week, start_date, end_date, is_active, created_at, updated_at)
SELECT 
    uuid_generate_v4() as id,
    employee_id,
    post_id,
    array_agg(DISTINCT day_of_week ORDER BY day_of_week) as days_of_week,
    MIN(start_date) as start_date,
    MAX(end_date) as end_date,
    bool_and(is_active) as is_active,
    MIN(created_at) as created_at,
    MAX(updated_at) as updated_at
FROM regular_assignments
WHERE is_active = true
GROUP BY employee_id, post_id, start_date, end_date
HAVING COUNT(*) > 0;

-- Étape 3: Supprimer l'ancienne table et renommer la nouvelle
DROP TABLE IF EXISTS regular_assignments CASCADE;
ALTER TABLE regular_assignments_new RENAME TO regular_assignments;

-- Étape 4: Recréer les index optimisés
CREATE INDEX IF NOT EXISTS idx_regular_assignments_employee ON regular_assignments(employee_id);
CREATE INDEX IF NOT EXISTS idx_regular_assignments_post ON regular_assignments(post_id);
CREATE INDEX IF NOT EXISTS idx_regular_assignments_dates ON regular_assignments(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_regular_assignments_active ON regular_assignments(is_active);
CREATE INDEX IF NOT EXISTS idx_regular_assignments_days ON regular_assignments USING GIN(days_of_week);

-- Étape 5: Recréer le trigger pour updated_at
CREATE OR REPLACE TRIGGER update_regular_assignments_updated_at 
BEFORE UPDATE ON regular_assignments 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Étape 6: Ajouter des contraintes de validation simplifiées
ALTER TABLE regular_assignments
ADD CONSTRAINT check_days_of_week_not_empty
CHECK (array_length(days_of_week, 1) > 0);

ALTER TABLE regular_assignments
ADD CONSTRAINT check_date_range_valid
CHECK (end_date IS NULL OR end_date >= start_date);

-- Étape 7: Commentaires sur la nouvelle structure
COMMENT ON TABLE regular_assignments IS 'Attributions automatiques récurrentes avec support multi-jours optimisé';
COMMENT ON COLUMN regular_assignments.days_of_week IS 'Tableau des jours de semaine (0=dimanche, 1=lundi, ..., 6=samedi)';
COMMENT ON COLUMN regular_assignments.start_date IS 'Date de début de l''attribution';
COMMENT ON COLUMN regular_assignments.end_date IS 'Date de fin de l''attribution (NULL = illimité)';

-- =====================================================
-- FIN DE LA MIGRATION 004
-- ===================================================== 