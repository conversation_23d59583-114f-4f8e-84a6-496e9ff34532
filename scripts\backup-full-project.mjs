// SCRIPT DE SAUVEGARDE COMPLÈTE 1:1 DU PROJET
// Sauvegarde complète du projet (sans node_modules)

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('📦 [BACKUP-FULL] Début de la sauvegarde complète 1:1...');

// Configuration
const projectRoot = path.join(__dirname, '..');
const backupDir = path.join(projectRoot, '..', 'project-backups');
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
const backupName = `project-backup-${timestamp}`;
const backupPath = path.join(backupDir, backupName);

// Fichiers et dossiers à exclure
const excludePatterns = [
    'node_modules',
    '.git',
    'dist',
    'build',
    '.vscode',
    '.idea',
    '*.log',
    '.DS_Store',
    'Thumbs.db',
    '.env.local',
    '.env.development.local',
    '.env.test.local',
    '.env.production.local',
    'coverage',
    '.nyc_output',
    '.cache',
    'temp',
    'tmp',
    '*.tmp',
    '*.temp'
];

// Fonction pour vérifier si un fichier/dossier doit être exclu
function shouldExclude(filePath, relativePath) {
    const fileName = path.basename(filePath);
    const relativePathNormalized = relativePath.replace(/\\/g, '/');
    
    return excludePatterns.some(pattern => {
        if (pattern.includes('*')) {
            // Pattern avec wildcard
            const regex = new RegExp(pattern.replace(/\*/g, '.*'));
            return regex.test(fileName) || regex.test(relativePathNormalized);
        } else {
            // Pattern exact
            return fileName === pattern || 
                   relativePathNormalized.includes(pattern) ||
                   relativePathNormalized.startsWith(pattern + '/');
        }
    });
}

// Fonction récursive pour copier les fichiers
function copyRecursive(src, dest, relativePath = '') {
    const stats = fs.statSync(src);
    
    if (stats.isDirectory()) {
        // Créer le dossier de destination
        if (!fs.existsSync(dest)) {
            fs.mkdirSync(dest, { recursive: true });
        }
        
        // Lire le contenu du dossier
        const files = fs.readdirSync(src);
        
        files.forEach(file => {
            const srcPath = path.join(src, file);
            const destPath = path.join(dest, file);
            const fileRelativePath = path.join(relativePath, file);
            
            // Vérifier si le fichier doit être exclu
            if (!shouldExclude(srcPath, fileRelativePath)) {
                copyRecursive(srcPath, destPath, fileRelativePath);
            } else {
                console.log(`  ⏭️ Exclu: ${fileRelativePath}`);
            }
        });
    } else {
        // Copier le fichier
        fs.copyFileSync(src, dest);
        console.log(`  ✅ ${relativePath}`);
    }
}

// Fonction pour calculer la taille d'un dossier
function calculateSize(dirPath) {
    let totalSize = 0;
    let fileCount = 0;
    
    function traverse(currentPath) {
        const stats = fs.statSync(currentPath);
        
        if (stats.isDirectory()) {
            const files = fs.readdirSync(currentPath);
            files.forEach(file => {
                traverse(path.join(currentPath, file));
            });
        } else {
            totalSize += stats.size;
            fileCount++;
        }
    }
    
    traverse(dirPath);
    return { totalSize, fileCount };
}

try {
    // Créer le répertoire de sauvegarde
    if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
        console.log('📁 [BACKUP-FULL] Répertoire de sauvegarde créé');
    }

    if (!fs.existsSync(backupPath)) {
        fs.mkdirSync(backupPath, { recursive: true });
        console.log(`📁 [BACKUP-FULL] Dossier de sauvegarde créé: ${backupName}`);
    }

    console.log('📂 [BACKUP-FULL] Copie complète du projet...');
    console.log(`   Source: ${projectRoot}`);
    console.log(`   Destination: ${backupPath}`);
    console.log(`   Exclusions: ${excludePatterns.join(', ')}`);
    console.log('');

    // Copier tout le projet
    copyRecursive(projectRoot, backupPath);

    // Calculer les statistiques
    const { totalSize, fileCount } = calculateSize(backupPath);

    // Créer un manifeste détaillé
    console.log('📋 [BACKUP-FULL] Création du manifeste...');
    const manifest = {
        timestamp: new Date().toISOString(),
        backupName: backupName,
        description: 'Sauvegarde complète 1:1 du projet (sans node_modules)',
        projectPath: projectRoot,
        backupPath: backupPath,
        excludePatterns: excludePatterns,
        stats: {
            totalSize: totalSize,
            fileCount: fileCount,
            sizeHuman: `${(totalSize / 1024 / 1024).toFixed(2)} MB`
        },
        gitInfo: null,
        restoreInstructions: [
            "1. Copiez le contenu de cette sauvegarde dans un nouveau dossier",
            "2. Ouvrez un terminal dans ce dossier",
            "3. Exécutez 'npm install' pour installer les dépendances",
            "4. Exécutez 'npm run dev' pour démarrer l'application",
            "5. Pour la production, exécutez 'npm run build'"
        ]
    };

    // Ajouter les informations Git si disponible
    try {
        const gitBranch = execSync('git rev-parse --abbrev-ref HEAD', { 
            cwd: projectRoot, 
            encoding: 'utf8' 
        }).trim();
        const gitCommit = execSync('git rev-parse HEAD', { 
            cwd: projectRoot, 
            encoding: 'utf8' 
        }).trim();
        const gitStatus = execSync('git status --porcelain', { 
            cwd: projectRoot, 
            encoding: 'utf8' 
        }).trim();
        
        manifest.gitInfo = {
            branch: gitBranch,
            commit: gitCommit,
            commitShort: gitCommit.substring(0, 8),
            hasUncommittedChanges: gitStatus.length > 0,
            status: gitStatus
        };
        
        console.log(`  ✅ Git - Branche: ${gitBranch}`);
        console.log(`  ✅ Git - Commit: ${gitCommit.substring(0, 8)}`);
        if (gitStatus.length > 0) {
            console.log(`  ⚠️ Git - Changements non committés détectés`);
        }
    } catch (error) {
        console.log('  ⚠️ Git non disponible');
    }

    // Sauvegarder le manifeste
    const manifestPath = path.join(backupPath, 'BACKUP-MANIFEST.json');
    fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));

    // Créer un fichier README pour la restauration
    const readmePath = path.join(backupPath, 'README-RESTORE.md');
    const readmeContent = `# 🔄 RESTAURATION DE SAUVEGARDE

## 📋 Informations de sauvegarde
- **Date**: ${new Date().toLocaleString('fr-FR')}
- **Nom**: ${backupName}
- **Branche Git**: ${manifest.gitInfo?.branch || 'Non disponible'}
- **Commit**: ${manifest.gitInfo?.commitShort || 'Non disponible'}

## 🚀 Instructions de restauration

### 1. Préparation
\`\`\`bash
# Copiez ce dossier vers l'emplacement souhaité
cp -r "${backupName}" "/votre/nouveau/chemin/projet"
cd "/votre/nouveau/chemin/projet"
\`\`\`

### 2. Installation des dépendances
\`\`\`bash
npm install
\`\`\`

### 3. Démarrage de l'application
\`\`\`bash
# Mode développement
npm run dev

# Mode production
npm run build
npm run preview
\`\`\`

### 4. Configuration de la base de données (si nécessaire)
\`\`\`bash
# Vérifiez la configuration dans server/config/database.js
# Adaptez les paramètres de connexion selon votre environnement
\`\`\`

## 📊 Statistiques de sauvegarde
- **Fichiers**: ${fileCount}
- **Taille**: ${(totalSize / 1024 / 1024).toFixed(2)} MB
- **Exclusions**: ${excludePatterns.join(', ')}

## ⚠️ Important
Cette sauvegarde ne contient pas :
- Les modules Node.js (node_modules)
- Les fichiers de build (dist, build)
- Les fichiers temporaires et cache
- L'historique Git (.git)

Vous devez réinstaller les dépendances avec \`npm install\` après la restauration.
`;

    fs.writeFileSync(readmePath, readmeContent);

    // Créer un script de restauration automatique
    const restoreScriptPath = path.join(backupPath, 'restore.bat');
    const restoreScript = `@echo off
echo 🔄 Script de restauration automatique
echo.
echo 📦 Installation des dépendances...
npm install
echo.
echo ✅ Restauration terminée !
echo.
echo 🚀 Pour démarrer l'application :
echo    npm run dev
echo.
pause
`;

    fs.writeFileSync(restoreScriptPath, restoreScript);

    // Créer aussi un script bash pour Linux/Mac
    const restoreShPath = path.join(backupPath, 'restore.sh');
    const restoreShScript = `#!/bin/bash
echo "🔄 Script de restauration automatique"
echo ""
echo "📦 Installation des dépendances..."
npm install
echo ""
echo "✅ Restauration terminée !"
echo ""
echo "🚀 Pour démarrer l'application :"
echo "   npm run dev"
echo ""
`;

    fs.writeFileSync(restoreShPath, restoreShScript);
    
    // Rendre le script bash exécutable (sur Unix)
    try {
        fs.chmodSync(restoreShPath, '755');
    } catch (error) {
        // Ignore les erreurs de chmod sur Windows
    }

    // Résumé final
    console.log('\n🎉 [BACKUP-FULL] Sauvegarde complète terminée avec succès !');
    console.log(`📁 Emplacement: ${backupPath}`);
    console.log(`📊 Fichiers sauvegardés: ${fileCount}`);
    console.log(`💾 Taille totale: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
    console.log('\n📋 Fichiers créés:');
    console.log('   • BACKUP-MANIFEST.json - Informations détaillées');
    console.log('   • README-RESTORE.md - Instructions de restauration');
    console.log('   • restore.bat - Script de restauration Windows');
    console.log('   • restore.sh - Script de restauration Linux/Mac');
    console.log('\n🔄 Pour restaurer cette sauvegarde:');
    console.log('   1. Copiez le dossier vers l\'emplacement souhaité');
    console.log('   2. Exécutez restore.bat (Windows) ou restore.sh (Linux/Mac)');
    console.log('   3. Ou suivez les instructions dans README-RESTORE.md');
    console.log('\n✅ Votre projet est maintenant sauvegardé intégralement !');
    console.log('🚀 Cette sauvegarde peut être utilisée comme base pour un nouveau projet.');

} catch (error) {
    console.error('❌ [BACKUP-FULL] Erreur lors de la sauvegarde:', error);
    process.exit(1);
} 