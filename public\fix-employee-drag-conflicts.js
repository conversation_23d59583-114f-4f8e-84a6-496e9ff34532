/**
 * 🔧 DIAGNOSTIC ET CORRECTION - PROBLÈMES DRAG & DROP EMPLOYÉS
 * 
 * Script pour diagnostiquer et corriger les conflits lors du déplacement des employés
 * causés par l'erreur "TypeError: unifiedLogger.enableDebug is not a function"
 */

console.log('🔧 [FIX] Démarrage du diagnostic des conflits de drag & drop des employés...');

// ✅ FONCTION DE DIAGNOSTIC COMPLET
function diagnoseEmployeeDragConflicts() {
    console.log('🔍 [DIAGNOSTIC] === ANALYSE COMPLÈTE DES CONFLITS DRAG & DROP ===');
    
    const results = {
        unifiedLogger: null,
        dragDropSystem: null,
        employees: null,
        containers: null,
        errors: [],
        warnings: [],
        solutions: []
    };
    
    // 1. ✅ VÉRIFIER LE SYSTÈME UNIFIEDLOGGER
    console.log('📊 [DIAGNOSTIC] 1. Vérification du système unifiedLogger...');
    
    const unifiedLogger = window.unifiedLogger;
    if (!unifiedLogger) {
        results.errors.push('unifiedLogger non disponible');
        results.solutions.push('Recharger la page pour réinitialiser le système');
    } else {
        console.log('✅ [DIAGNOSTIC] unifiedLogger disponible');
        
        // Vérifier les méthodes critiques
        const criticalMethods = ['enableDebug', 'disableDebug', 'getDebugMode', 'frontend', 'browser'];
        const missingMethods = criticalMethods.filter(method => 
            typeof unifiedLogger[method] !== 'function' && typeof unifiedLogger[method] !== 'object'
        );
        
        if (missingMethods.length > 0) {
            results.errors.push(`Méthodes manquantes dans unifiedLogger: ${missingMethods.join(', ')}`);
            results.solutions.push('Recharger capture-logs-unified.js');
        } else {
            results.unifiedLogger = 'OK';
            console.log('✅ [DIAGNOSTIC] Toutes les méthodes unifiedLogger disponibles');
        }
    }
    
    // 2. ✅ VÉRIFIER LE SYSTÈME DE DRAG & DROP
    console.log('📊 [DIAGNOSTIC] 2. Vérification du système de drag & drop...');
        
        const employeeContainer = document.getElementById('employee-rows-container');
        if (!employeeContainer) {
        results.errors.push('Conteneur employee-rows-container non trouvé');
        results.solutions.push('Vérifier que TeamCalendarApp est bien initialisé');
    } else {
        console.log('✅ [DIAGNOSTIC] Conteneur des employés trouvé');
        
        // Vérifier SortableJS
        const sortableInstance = employeeContainer._sortable;
        if (!sortableInstance) {
            results.warnings.push('Instance SortableJS non attachée');
            results.solutions.push('Réinitialiser le drag & drop des employés');
        } else {
            results.dragDropSystem = 'OK';
            console.log('✅ [DIAGNOSTIC] SortableJS configuré correctement');
        }
    }
    
    // 3. ✅ VÉRIFIER LA LISTE DES EMPLOYÉS
    console.log('📊 [DIAGNOSTIC] 3. Vérification de la liste des employés...');
    
    const employeeRows = document.querySelectorAll('.employee-row, [data-employee-id]');
    if (employeeRows.length === 0) {
        results.warnings.push('Aucun employé trouvé dans le DOM');
        results.solutions.push('Attendre le chargement des employés ou recharger');
                        } else {
        results.employees = `${employeeRows.length} employés trouvés`;
        console.log(`✅ [DIAGNOSTIC] ${employeeRows.length} employés trouvés`);
    }
    
    // 4. ✅ VÉRIFIER LES ERREURS JAVASCRIPT RÉCENTES
    console.log('📊 [DIAGNOSTIC] 4. Vérification des erreurs JavaScript...');
    
    // Simuler un test de la fonction problématique
    try {
        if (unifiedLogger && typeof unifiedLogger.enableDebug === 'function') {
            console.log('✅ [DIAGNOSTIC] unifiedLogger.enableDebug est fonctionnel');
        } else {
            results.warnings.push('unifiedLogger.enableDebug non fonctionnel');
            results.solutions.push('Utiliser le fallback enableUltraDebug ou localStorage');
        }
    } catch (error) {
        results.errors.push(`Erreur lors du test enableDebug: ${error.message}`);
    }
    
    // 5. ✅ VÉRIFIER LE MODE DEBUG ACTUEL
    console.log('📊 [DIAGNOSTIC] 5. Vérification du mode debug...');
    
    const debugMode = localStorage.getItem('ULTRA_DEBUG_MODE');
    const debugLevel = localStorage.getItem('DEBUG_LEVEL');
    
    if (debugMode === 'true') {
        console.log(`⚠️ [DIAGNOSTIC] Mode debug ACTIF (niveau: ${debugLevel})`);
        results.warnings.push(`Mode debug ${debugLevel} actif - peut causer des ralentissements`);
        results.solutions.push('Désactiver le mode debug si non nécessaire');
    } else {
        console.log('✅ [DIAGNOSTIC] Mode debug normal');
    }
    
    // ✅ AFFICHAGE DES RÉSULTATS
    console.log('📋 [DIAGNOSTIC] === RÉSULTATS DU DIAGNOSTIC ===');
    console.log('🟢 Éléments OK:');
    if (results.unifiedLogger) console.log(`  ✅ UnifiedLogger: ${results.unifiedLogger}`);
    if (results.dragDropSystem) console.log(`  ✅ Drag & Drop: ${results.dragDropSystem}`);
    if (results.employees) console.log(`  ✅ Employés: ${results.employees}`);
    
    if (results.errors.length > 0) {
        console.log('🔴 Erreurs détectées:');
        results.errors.forEach(error => console.log(`  ❌ ${error}`));
    }
    
    if (results.warnings.length > 0) {
        console.log('🟡 Avertissements:');
        results.warnings.forEach(warning => console.log(`  ⚠️ ${warning}`));
    }
    
    if (results.solutions.length > 0) {
        console.log('💡 Solutions recommandées:');
        results.solutions.forEach(solution => console.log(`  🔧 ${solution}`));
    }
    
    return results;
}

// ✅ FONCTION DE CORRECTION AUTOMATIQUE
function fixEmployeeDragConflicts() {
    console.log('🔧 [FIX] Démarrage de la correction automatique...');
    
    try {
        // 1. ✅ NETTOYER LES ANCIENS LISTENERS D'ERREUR
        console.log('🧹 [FIX] Nettoyage des anciens listeners...');
        
        // 2. ✅ RÉINITIALISER LE SYSTÈME UNIFIEDLOGGER SI NÉCESSAIRE
        if (!window.unifiedLogger || typeof window.unifiedLogger.enableDebug !== 'function') {
            console.log('🔧 [FIX] Réinitialisation du système unifiedLogger...');
            
            // Forcer le rechargement du capture-logs-unified.js
            const existingScript = document.querySelector('script[src*="capture-logs-unified"]');
            if (existingScript) {
                existingScript.remove();
            }
            
            const newScript = document.createElement('script');
            newScript.src = '/capture-logs-unified.js?' + Date.now();
            newScript.onload = () => {
                console.log('✅ [FIX] UnifiedLogger rechargé');
                // Attendre un peu puis réinitialiser le drag & drop
                setTimeout(reinitializeEmployeeDragDrop, 1000);
            };
            document.head.appendChild(newScript);
        } else {
            // Si unifiedLogger fonctionne, directement réinitialiser le drag & drop
            reinitializeEmployeeDragDrop();
        }
        
    } catch (error) {
        console.error('❌ [FIX] Erreur lors de la correction automatique:', error);
        console.log('💡 [FIX] Essayez de recharger la page manuellement');
    }
}

// ✅ FONCTION DE RÉINITIALISATION DU DRAG & DROP
function reinitializeEmployeeDragDrop() {
    console.log('🔄 [FIX] Réinitialisation du drag & drop des employés...');
    
    try {
        // Attendre que TeamCalendarApp soit disponible
        if (typeof window.TeamCalendarApp !== 'undefined' && window.TeamCalendarApp.setupEmployeeDragDrop) {
            console.log('🔧 [FIX] Reconfiguration via TeamCalendarApp...');
            window.TeamCalendarApp.setupEmployeeDragDrop();
            console.log('✅ [FIX] Drag & drop des employés réinitialisé');
        } else {
            console.log('⚠️ [FIX] TeamCalendarApp non disponible, attente...');
            
            // Réessayer dans 2 secondes
            setTimeout(() => {
                if (typeof window.TeamCalendarApp !== 'undefined') {
                    window.TeamCalendarApp.setupEmployeeDragDrop?.();
                    console.log('✅ [FIX] Drag & drop des employés réinitialisé (2ème tentative)');
                } else {
                    console.log('❌ [FIX] Impossible de réinitialiser - rechargez la page');
                }
            }, 2000);
        }
        
    } catch (error) {
        console.error('❌ [FIX] Erreur lors de la réinitialisation du drag & drop:', error);
    }
}

// ✅ FONCTION DE TEST DU DRAG & DROP
function testEmployeeDragDrop() {
    console.log('🧪 [TEST] Test du système de drag & drop des employés...');
    
    const employeeContainer = document.getElementById('employee-rows-container');
    if (!employeeContainer) {
        console.log('❌ [TEST] Conteneur des employés non trouvé');
        return false;
    }
    
    const sortableInstance = employeeContainer._sortable;
    if (!sortableInstance) {
        console.log('❌ [TEST] SortableJS non configuré');
        return false;
    }
    
    const employees = employeeContainer.querySelectorAll('.employee-row, [data-employee-id]');
    if (employees.length < 2) {
        console.log('⚠️ [TEST] Moins de 2 employés - impossible de tester le drag & drop');
        return false;
    }
    
    console.log(`✅ [TEST] Système de drag & drop opérationnel (${employees.length} employés)`);
    console.log('💡 [TEST] Vous pouvez maintenant tester manuellement le déplacement des employés');
    
    return true;
}

// ✅ EXPOSITION DES FONCTIONS GLOBALEMENT
window.diagnoseEmployeeDragConflicts = diagnoseEmployeeDragConflicts;
window.fixEmployeeDragConflicts = fixEmployeeDragConflicts;
window.testEmployeeDragDrop = testEmployeeDragDrop;

// ✅ EXÉCUTION AUTOMATIQUE DU DIAGNOSTIC
console.log('🚀 [FIX] Script chargé. Fonctions disponibles:');
console.log('  📊 diagnoseEmployeeDragConflicts() - Diagnostic complet');
console.log('  🔧 fixEmployeeDragConflicts()     - Correction automatique'); 
console.log('  🧪 testEmployeeDragDrop()         - Test du système');

// Diagnostic automatique au chargement
    setTimeout(() => {
    console.log('🔍 [AUTO] Diagnostic automatique...');
    const results = diagnoseEmployeeDragConflicts();
    
    if (results.errors.length > 0) {
        console.log('🔧 [AUTO] Erreurs détectées - correction automatique recommandée');
        console.log('💡 Exécutez: fixEmployeeDragConflicts()');
    } else if (results.warnings.length > 0) {
        console.log('⚠️ [AUTO] Avertissements détectés - vérification recommandée');
    } else {
        console.log('✅ [AUTO] Système de drag & drop des employés semble fonctionnel');
    }
    }, 2000);
