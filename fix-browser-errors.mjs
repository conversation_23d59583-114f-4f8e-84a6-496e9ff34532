#!/usr/bin/env node

/**
 * Correction des erreurs de navigateur
 */

import fs from 'fs';

console.log('🔧 [FIX-BROWSER-ERRORS] Correction des erreurs de navigateur...\n');

// Correction 1: Erreur de syntaxe dans test-dom-browser.js
const testDomPath = './public/test-dom-browser.js';
let testDomContent = fs.readFileSync(testDomPath, 'utf8');

// Corriger l'apostrophe non échappée
testDomContent = testDomContent.replace(
    "console.log('🎉 TOUS LES TESTS PASSÉS ! L'application est prête.');",
    "console.log('🎉 TOUS LES TESTS PASSÉS ! L\\\\'application est prête.');"
);

fs.writeFileSync(testDomPath, testDomContent);
console.log('✅ Erreur de syntaxe corrigée dans test-dom-browser.js');

// Correction 2: Ajouter les éléments DOM manquants dans le HTML
const htmlPath = './index.html';
let htmlContent = fs.readFileSync(htmlPath, 'utf8');

// Ajouter addPostButton s'il n'existe pas
if (!htmlContent.includes('add-post-btn')) {
    const bodyPattern = /(<body[^>]*>)/;
    if (bodyPattern.test(htmlContent)) {
        htmlContent = htmlContent.replace(bodyPattern, (match, bodyTag) => {
            return bodyTag + '\n    <!-- ✅ CORRECTION : Bouton d\'ajout de poste -->\n    <button id="add-post-btn" class="hidden">Ajouter Poste</button>\n';
        });
        console.log('✅ addPostButton ajouté dans le HTML');
    }
}

// Ajouter available-posts-container s'il n'existe pas
if (!htmlContent.includes('available-posts-container')) {
    const bodyPattern = /(<body[^>]*>)/;
    if (bodyPattern.test(htmlContent)) {
        htmlContent = htmlContent.replace(bodyPattern, (match, bodyTag) => {
            return bodyTag + '\n    <!-- ✅ CORRECTION : Conteneur des postes disponibles -->\n    <div id="available-posts-container" class="available-posts-container hidden"></div>\n';
        });
        console.log('✅ available-posts-container ajouté dans le HTML');
    }
}

fs.writeFileSync(htmlPath, htmlContent);

// Correction 3: S'assurer que les éléments sont initialisés correctement dans teamCalendarApp.ts
const tsPath = './src/teamCalendarApp.ts';
let tsContent = fs.readFileSync(tsPath, 'utf8');

// S'assurer que addPostButton est initialisé avec getElementById
if (tsContent.includes('addPostButton: null,')) {
    tsContent = tsContent.replace(
        'addPostButton: null,',
        'addPostButton: document.getElementById(\'add-post-btn\'),'
    );
    console.log('✅ addPostButton initialisé avec getElementById');
}

// S'assurer que availablePostsContainer est initialisé avec getElementById
if (tsContent.includes('availablePostsContainer: null,')) {
    tsContent = tsContent.replace(
        'availablePostsContainer: null,',
        'availablePostsContainer: document.getElementById(\'available-posts-container\'),'
    );
    console.log('✅ availablePostsContainer initialisé avec getElementById');
}

fs.writeFileSync(tsPath, tsContent);

console.log('\n✅ Corrections appliquées !');
console.log('\n📋 Corrections appliquées :');
console.log('✅ 1. Erreur de syntaxe corrigée dans test-dom-browser.js');
console.log('✅ 2. addPostButton ajouté dans le HTML');
console.log('✅ 3. available-posts-container ajouté dans le HTML');
console.log('✅ 4. Éléments initialisés avec getElementById dans teamCalendarApp.ts');

console.log('\n🚀 Instructions pour tester :');
console.log('1. Redémarrer le serveur : npm run dev');
console.log('2. Ouvrir http://localhost:5173');
console.log('3. Vérifier que :');
console.log('   - Le drag & drop des employés fonctionne');
console.log('   - Les modales des paramètres s\'ouvrent');
console.log('   - Les postes disponibles sont visibles');
console.log('4. Dans la console, exécuter :');
console.log('   - checkDOMStructure()');
console.log('   - checkTeamCalendarApp()');

console.log('\n✅ [FIX-BROWSER-ERRORS] Corrections terminées !'); 