# 🗄️ Configuration Base de Données PostgreSQL - Team Calendar App

## 📋 Vue d'ensemble

L'application Team Calendar a été migrée vers PostgreSQL pour une gestion robuste et scalable des données. Cette documentation couvre l'installation, la configuration et l'utilisation de la base de données.

## 🔧 Configuration de la Base de Données

### Informations de Connexion

```javascript
// Configuration actuelle
const config = {
  host: '*************',
  port: 5432,
  database: 'glive_db',
  user: 'postgres',
  password: 'SebbZ12342323!!'
};
```

## 🏗️ Architecture de la Base de Données

### Tables Principales

1. **`employee_templates`** - Modèles de fiches employés
   - Stockage des champs personnalisables
   - Support JSONB pour flexibilité

2. **`employees`** - Informations des employés
   - Référence au modèle de fiche
   - Champs additionnels en JSONB

3. **`standard_posts`** - Postes de travail standards
   - Horaires, durées, types de postes
   - Postes personnalisés et prédéfinis

4. **`shifts`** - Quarts de travail
   - Assignations employé-poste-date
   - Support des attributions régulières et ponctuelles

5. **`regular_assignments`** - Attributions automatiques
   - Récurrence et plages de dates
   - Jours de la semaine sélectionnés

6. **`vacation_periods`** - Congés individuels
   - CP, RTT, maladie, etc.
   - Plages de dates par employé

7. **`global_vacations`** - Congés collectifs
   - Jours fériés
   - Fermetures d'entreprise

8. **`app_settings`** - Paramètres d'application
   - Configuration globale
   - Stockage clé-valeur en JSONB

## 🚀 Scripts de Migration

### 1. Migration Initiale
```bash
# Créer la structure complète de la base
npm run migrate
```

### 2. Vérification
```bash
# Vérifier l'état de la base de données
npm run db:verify

# Afficher les statistiques
npm run db:verify stats

# Vérifier uniquement la structure
npm run db:verify structure
```

### 3. Statut des Migrations
```bash
# Voir l'état des migrations
npm run db:status
```

## 🔄 Scripts Disponibles

| Script | Description |
|--------|-------------|
| `npm run migrate` | Exécuter les migrations |
| `npm run db:verify` | Vérification complète |
| `npm run db:status` | Statut des migrations |
| `npm run server` | Démarrer l'API backend |
| `npm run dev:full` | Frontend + Backend |

## 📊 Structure des Données

### Modèle Employé
```json
{
  "id": "uuid",
  "name": "string",
  "status": "Temps Plein|Temps Partiel|Stagiaire|Freelance",
  "avatar": "string|null",
  "template_id": "uuid",
  "extra_fields": {
    "email": "string",
    "department": "string",
    "position": "string",
    // ... autres champs personnalisés
  }
}
```

### Modèle Quart
```json
{
  "id": "uuid",
  "employee_id": "uuid",
  "post_id": "uuid",
  "date_key": "date",
  "shift_data": {
    "employeeId": "string",
    "employeeName": "string",
    "postId": "string",
    "postLabel": "string",
    "hours": "string",
    "type": "string"
  },
  "is_regular": "boolean",
  "is_punctual": "boolean",
  "assignment_id": "uuid|null",
  "week_offset": "integer"
}
```

## 🛡️ Sécurité et Performance

### Index Optimisés
- Recherche rapide par employé et date
- Index composites pour les requêtes fréquentes
- Index unique sur les clés de paramètres

### Constraints et Relations
- Clés étrangères avec CASCADE approprié
- Validation des données au niveau DB
- Contraintes d'unicité

### Triggers
- Mise à jour automatique des timestamps
- Audit trail sur toutes les tables

## 🔌 API REST

L'application expose une API REST complète :

### Employés
- `GET /api/employees` - Liste tous les employés
- `POST /api/employees` - Créer un employé
- `PUT /api/employees/:id` - Modifier un employé
- `DELETE /api/employees/:id` - Supprimer un employé

### Quarts
- `GET /api/shifts` - Liste des quarts (avec filtres)
- `POST /api/shifts` - Créer un quart
- `POST /api/shifts/bulk` - Création en lot
- `PUT /api/shifts/:id` - Modifier un quart
- `DELETE /api/shifts/:id` - Supprimer un quart

### Postes Standards
- `GET /api/standard-posts` - Liste des postes
- `POST /api/standard-posts` - Créer un poste
- `PUT /api/standard-posts/:id` - Modifier un poste
- `DELETE /api/standard-posts/:id` - Supprimer un poste

### Paramètres
- `GET /api/settings` - Récupérer les paramètres
- `PUT /api/settings` - Mettre à jour les paramètres

### Modèles de Fiches
- `GET /api/employee-templates` - Liste des modèles
- `POST /api/employee-templates` - Créer un modèle

## 🚨 Dépannage

### Problèmes de Connexion
1. Vérifier que PostgreSQL est accessible
2. Vérifier les informations de connexion
3. Tester avec `npm run db:verify`

### Erreurs de Migration
1. Vérifier les permissions de l'utilisateur PostgreSQL
2. S'assurer que la base de données existe
3. Consulter les logs pour plus de détails

### Performance
1. Les index sont optimisés pour les requêtes fréquentes
2. Utiliser `EXPLAIN ANALYZE` pour analyser les requêtes lentes
3. Surveiller les connexions au pool

## 📈 Évolution Future

- Support des migrations incrémentales
- Système de sauvegarde automatique
- Monitoring et alertes
- Mise en cache Redis (optionnel)
- Réplication pour haute disponibilité

## 🎯 Points Clés

✅ **Migration Complète** : Toutes les données localStorage ont été migrées vers PostgreSQL  
✅ **API REST** : Interface complète pour toutes les opérations  
✅ **Performances** : Index optimisés et requêtes efficaces  
✅ **Sécurité** : Constraints et validation au niveau DB  
✅ **Flexibilité** : Support JSONB pour les champs personnalisés  
✅ **Maintenance** : Scripts de migration et vérification automatisés  

La migration vers PostgreSQL offre une base solide pour la croissance future de l'application tout en conservant toutes les fonctionnalités existantes. 