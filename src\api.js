// Services API centralisés pour l'application TeamCalendar

// Use relative path; Vite dev server proxies /api to backend, removing CORS issues
const BASE_URL = '/api';

class ApiService {
    async checkConnection() {
        try {
            const response = await fetch(`${BASE_URL}/health`);
            return response.ok;
        } catch (error) {
            console.error('Erreur de connexion API:', error);
            return false;
        }
    }

    async getEmployees() {
        try {
            const response = await fetch(`${BASE_URL}/employees`);
            const data = await response.json();
            return {
                success: true,
                data: data.employees || []
            };
        } catch (error) {
            console.error('Erreur récupération employés:', error);
            return { success: false, data: [] };
        }
    }

    async getStandardPosts() {
        try {
            const response = await fetch(`${BASE_URL}/standard-posts`);
            const data = await response.json();
            return {
                success: true,
                data: data.posts || []
            };
        } catch (error) {
            console.error('Erreur récupération postes:', error);
            return { success: false, data: [] };
        }
    }

    async getRegularAssignments() {
        try {
            const response = await fetch(`${BASE_URL}/regular-assignments`);
            const data = await response.json();
            return {
                success: true,
                data: data.assignments || data || []
            };
        } catch (error) {
            console.error('Erreur récupération attributions régulières:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error),
                data: []
            };
        }
    }

    /**
     * Créer une attribution régulière
     * @param {Object} assignment - Données de l'attribution
     * @returns {Promise<{success:boolean, data?:any, error?:string}>}
     */
    async createRegularAssignment(assignment) {
        try {
            const response = await fetch(`${BASE_URL}/regular-assignments`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(assignment)
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status} - ${errorText}`);
            }
            const data = await response.json().catch(() => null);
            return { success: true, data };
        } catch (error) {
            console.error('Erreur création attribution régulière:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    /**
     * Mettre à jour une attribution régulière
     * @param {string} id - ID de l'attribution
     * @param {Object} assignment - Nouvelles données
     */
    async updateRegularAssignment(id, assignment) {
        try {
            const response = await fetch(`${BASE_URL}/regular-assignments/${id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(assignment)
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status} - ${errorText}`);
            }
            const data = await response.json().catch(() => null);
            return { success: true, data };
        } catch (error) {
            console.error('Erreur mise à jour attribution régulière:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    /**
     * Supprimer une attribution régulière
     * @param {string} id - ID de l'attribution
     */
    async deleteRegularAssignment(id) {
        try {
            const response = await fetch(`${BASE_URL}/regular-assignments/${id}`, {
                method: 'DELETE'
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status} - ${errorText}`);
            }
            return { success: true };
        } catch (error) {
            console.error('Erreur suppression attribution régulière:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    async getShifts() {
        try {
            const response = await fetch(`${BASE_URL}/shifts`);
            const data = await response.json();
            return {
                success: true,
                data: data.shifts || []
            };
        } catch (error) {
            console.error('Erreur récupération shifts:', error);
            return { success: false, data: [] };
        }
    }

    async saveWeekShifts(weekId, shifts, options = {}) {
        const { retries = 0 } = options;
        let attempt = 0;
        while (attempt <= retries) {
            try {
                const response = await fetch(`${BASE_URL}/weeks/${weekId}/shifts/bulk`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ shifts })
                });
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status} - ${errorText}`);
                }
                const data = await response.json().catch(() => null);
                return { success: true, data };
            } catch (error) {
                console.error(`Erreur sauvegarde shifts (tentative ${attempt + 1}/${retries + 1}):`, error);
                if (attempt >= retries) {
                    return {
                        success: false,
                        error: error instanceof Error ? error.message : String(error)
                    };
                }
            }
            attempt += 1;
        }
    }

    async getEmployeeOrder() {
        try {
            const response = await fetch(`${BASE_URL}/employee-order`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            const data = await response.json();
            return {
                success: true,
                data: data.employeeOrder || data || []
            };
        } catch (error) {
            console.error('Erreur récupération ordre employés:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error),
                data: []
            };
        }
    }

    async saveEmployeeOrder(employeeOrder) {
        try {
            const response = await fetch(`${BASE_URL}/employee-order`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ employeeOrder })
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status} - ${errorText}`);
            }
            return { success: true };
        } catch (error) {
            console.error('Erreur sauvegarde ordre employés:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    async updateShift(shiftId, shiftData) {
        try {
            const response = await fetch(`${BASE_URL}/shifts/${shiftId}`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(shiftData)
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status} - ${errorText}`);
            }
            // Le backend peut renvoyer le shift mis à jour ; l'utiliser si disponible, sinon null
            const data = await response.json().catch(() => null);
            return { success: true, data };
        } catch (error) {
            console.error('Erreur mise à jour shift:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    async getSettings() {
        try {
            const response = await fetch(`${BASE_URL}/settings`);
            const data = await response.json();
            return {
                success: true,
                data: data.settings || {}
            };
        } catch (error) {
            console.error('Erreur récupération paramètres:', error);
            return { 
                success: false, 
                error: error instanceof Error ? error.message : String(error),
                data: {} 
            };
        }
    }

    // Méthodes pour l'indicateur de connexion
    onStatusChange(callback) {
        this._statusCallback = callback;
    }

    getConnectionStatus() {
        return this._connectionStatus || 'disconnected';
    }

    // Méthode privée pour mettre à jour le statut
    _updateStatus(status) {
        this._connectionStatus = status;
        if (this._statusCallback) {
            this._statusCallback(status);
        }
    }
}

// Créer et exporter l'instance
const apiService = new ApiService();

// Exporter à la fois comme export par défaut et comme export nommé
export { apiService };
export default apiService; 