import { useState, useEffect, useCallback } from 'react';

/**
 * Hook personnalisé pour la gestion des logs internes
 * avec persistance localStorage et fonctionnalités avancées
 */
export const useLogs = (maxLogs = 1000) => {
  const [logs, setLogs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Charger les logs depuis localStorage au montage
  useEffect(() => {
    try {
      const storedLogs = localStorage.getItem('app-logs');
      if (storedLogs) {
        const parsedLogs = JSON.parse(storedLogs);
        setLogs(parsedLogs.slice(-maxLogs)); // Garder seulement les derniers logs
      }
    } catch (error) {
      console.error('Erreur lors du chargement des logs:', error);
    } finally {
      setIsLoading(false);
    }
  }, [maxLogs]);

  // Sauvegarder les logs dans localStorage
  const saveLogs = useCallback((newLogs) => {
    try {
      localStorage.setItem('app-logs', JSON.stringify(newLogs));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des logs:', error);
    }
  }, []);

  // Ajouter un nouveau log
  const addLog = useCallback((level, message, data = null) => {
    const newLog = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      level: level.toUpperCase(),
      message,
      data: data ? JSON.stringify(data, null, 2) : null,
      component: 'EnhancedSidebarMenu'
    };

    setLogs(prevLogs => {
      const updatedLogs = [...prevLogs, newLog].slice(-maxLogs);
      saveLogs(updatedLogs);
      return updatedLogs;
    });
  }, [maxLogs, saveLogs]);

  // Méthodes de log par niveau
  const logInfo = useCallback((message, data) => addLog('info', message, data), [addLog]);
  const logWarning = useCallback((message, data) => addLog('warning', message, data), [addLog]);
  const logError = useCallback((message, data) => addLog('error', message, data), [addLog]);
  const logSuccess = useCallback((message, data) => addLog('success', message, data), [addLog]);

  // Purger tous les logs
  const clearLogs = useCallback(() => {
    setLogs([]);
    localStorage.removeItem('app-logs');
    addLog('info', 'Logs purgés par l\'utilisateur');
  }, [addLog]);

  // Filtrer les logs par niveau
  const getLogsByLevel = useCallback((level) => {
    return logs.filter(log => log.level === level.toUpperCase());
  }, [logs]);

  // Obtenir les statistiques des logs
  const getLogStats = useCallback(() => {
    const stats = logs.reduce((acc, log) => {
      acc[log.level] = (acc[log.level] || 0) + 1;
      return acc;
    }, {});
    return {
      total: logs.length,
      ...stats,
      lastLog: logs[logs.length - 1] || null
    };
  }, [logs]);

  // Exporter les logs
  const exportLogs = useCallback(() => {
    const dataStr = JSON.stringify(logs, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `app-logs-${new Date().toISOString().slice(0, 10)}.json`;
    link.click();
    URL.revokeObjectURL(url);
    addLog('info', 'Logs exportés');
  }, [logs, addLog]);

  return {
    logs,
    isLoading,
    addLog,
    logInfo,
    logWarning,
    logError,
    logSuccess,
    clearLogs,
    getLogsByLevel,
    getLogStats,
    exportLogs
  };
}; 