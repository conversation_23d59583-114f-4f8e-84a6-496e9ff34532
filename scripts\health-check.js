#!/usr/bin/env node

/**
 * @fileoverview Script de vérification de santé de l'application
 * @description Vérifie l'état de tous les composants critiques
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

/**
 * @description Couleurs pour la console
 */
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

/**
 * @description Log avec couleur
 * @param {string} message - Message à afficher
 * @param {string} color - Couleur à utiliser
 */
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

/**
 * @description Vérifie l'existence d'un fichier
 * @param {string} filePath - Chemin du fichier
 * @returns {boolean} true si le fichier existe
 */
function checkFile(filePath) {
  try {
    return fs.existsSync(path.join(rootDir, filePath));
  } catch (error) {
    return false;
  }
}

/**
 * @description Vérifie la structure des dossiers
 * @returns {Object} Résultats de la vérification
 */
function checkDirectoryStructure() {
  const requiredDirs = [
    'src',
    'src/components',
    'src/components/utils',
    'src/components/types',
    'docs',
    'backend',
    'server'
  ];

  const results = {
    passed: 0,
    failed: 0,
    details: []
  };

  log('\n📁 Vérification de la structure des dossiers...', colors.blue);

  requiredDirs.forEach(dir => {
    const exists = fs.existsSync(path.join(rootDir, dir));
    if (exists) {
      log(`  ✅ ${dir}`, colors.green);
      results.passed++;
    } else {
      log(`  ❌ ${dir} (manquant)`, colors.red);
      results.failed++;
    }
    results.details.push({ path: dir, exists });
  });

  return results;
}

/**
 * @description Vérifie les fichiers critiques
 * @returns {Object} Résultats de la vérification
 */
function checkCriticalFiles() {
  const criticalFiles = [
    'src/teamCalendarApp.ts',
    'src/modalFunctionalities.ts',
    'src/api.ts',
    'src/logger.ts',
    'src/Agenda.tsx',
    'src/components/index.ts',
    'src/components/types/interfaces.ts',
    'src/components/utils/dateUtils.ts',
    'src/components/utils/validationUtils.ts',
    'docs/features-index.md',
    'package.json',
    'README.md'
  ];

  const results = {
    passed: 0,
    failed: 0,
    details: []
  };

  log('\n📄 Vérification des fichiers critiques...', colors.blue);

  criticalFiles.forEach(file => {
    const exists = checkFile(file);
    if (exists) {
      log(`  ✅ ${file}`, colors.green);
      results.passed++;
    } else {
      log(`  ❌ ${file} (manquant)`, colors.red);
      results.failed++;
    }
    results.details.push({ path: file, exists });
  });

  return results;
}

/**
 * @description Vérifie la configuration TypeScript
 * @returns {Object} Résultats de la vérification
 */
function checkTypeScriptConfig() {
  const results = {
    passed: 0,
    failed: 0,
    details: []
  };

  log('\n🔧 Vérification de la configuration TypeScript...', colors.blue);

  // Vérifier tsconfig.json
  if (checkFile('tsconfig.json')) {
    log('  ✅ tsconfig.json', colors.green);
    results.passed++;
  } else {
    log('  ❌ tsconfig.json (manquant)', colors.red);
    results.failed++;
  }

  // Vérifier les types
  const typeFiles = [
    'src/components/types/interfaces.ts'
  ];

  typeFiles.forEach(file => {
    if (checkFile(file)) {
      log(`  ✅ ${file}`, colors.green);
      results.passed++;
    } else {
      log(`  ❌ ${file} (manquant)`, colors.red);
      results.failed++;
    }
  });

  return results;
}

/**
 * @description Vérifie les dépendances npm
 * @returns {Object} Résultats de la vérification
 */
function checkDependencies() {
  const results = {
    passed: 0,
    failed: 0,
    details: []
  };

  log('\n📦 Vérification des dépendances...', colors.blue);

  try {
    const packageJson = JSON.parse(fs.readFileSync(path.join(rootDir, 'package.json'), 'utf8'));
    
    const criticalDeps = [
      'react',
      'react-dom',
      'typescript',
      'vite',
      'express',
      'pg',
      'sortablejs'
    ];

    const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };

    criticalDeps.forEach(dep => {
      if (allDeps[dep]) {
        log(`  ✅ ${dep} (${allDeps[dep]})`, colors.green);
        results.passed++;
      } else {
        log(`  ❌ ${dep} (manquant)`, colors.red);
        results.failed++;
      }
    });

    // Vérifier node_modules
    if (fs.existsSync(path.join(rootDir, 'node_modules'))) {
      log('  ✅ node_modules installé', colors.green);
      results.passed++;
    } else {
      log('  ❌ node_modules manquant (exécuter npm install)', colors.red);
      results.failed++;
    }

  } catch (error) {
    log('  ❌ Erreur lors de la lecture de package.json', colors.red);
    results.failed++;
  }

  return results;
}

/**
 * @description Vérifie la documentation
 * @returns {Object} Résultats de la vérification
 */
function checkDocumentation() {
  const results = {
    passed: 0,
    failed: 0,
    details: []
  };

  log('\n📚 Vérification de la documentation...', colors.blue);

  const docFiles = [
    'README.md',
    'docs/features-index.md'
  ];

  docFiles.forEach(file => {
    if (checkFile(file)) {
      // Vérifier que le fichier n'est pas vide
      try {
        const content = fs.readFileSync(path.join(rootDir, file), 'utf8');
        if (content.trim().length > 100) {
          log(`  ✅ ${file} (${content.length} caractères)`, colors.green);
          results.passed++;
        } else {
          log(`  ⚠️  ${file} (trop court: ${content.length} caractères)`, colors.yellow);
          results.failed++;
        }
      } catch (error) {
        log(`  ❌ ${file} (erreur de lecture)`, colors.red);
        results.failed++;
      }
    } else {
      log(`  ❌ ${file} (manquant)`, colors.red);
      results.failed++;
    }
  });

  return results;
}

/**
 * @description Fonction principale
 */
async function main() {
  log(`${colors.bold}🏥 VÉRIFICATION DE SANTÉ - TEAM CALENDAR${colors.reset}`, colors.blue);
  log('='.repeat(50), colors.blue);

  const checks = [
    { name: 'Structure des dossiers', fn: checkDirectoryStructure },
    { name: 'Fichiers critiques', fn: checkCriticalFiles },
    { name: 'Configuration TypeScript', fn: checkTypeScriptConfig },
    { name: 'Dépendances npm', fn: checkDependencies },
    { name: 'Documentation', fn: checkDocumentation }
  ];

  let totalPassed = 0;
  let totalFailed = 0;
  const allResults = [];

  for (const check of checks) {
    const result = check.fn();
    totalPassed += result.passed;
    totalFailed += result.failed;
    allResults.push({ name: check.name, ...result });
  }

  // Résumé final
  log('\n📊 RÉSUMÉ FINAL', colors.bold);
  log('='.repeat(30), colors.blue);

  allResults.forEach(result => {
    const status = result.failed === 0 ? '✅' : '❌';
    const color = result.failed === 0 ? colors.green : colors.red;
    log(`${status} ${result.name}: ${result.passed} réussi(s), ${result.failed} échoué(s)`, color);
  });

  log(`\n🎯 TOTAL: ${totalPassed} réussi(s), ${totalFailed} échoué(s)`, 
    totalFailed === 0 ? colors.green : colors.red);

  if (totalFailed === 0) {
    log('\n🎉 Toutes les vérifications sont passées ! L\'application est en bonne santé.', colors.green);
    process.exit(0);
  } else {
    log('\n⚠️  Certaines vérifications ont échoué. Veuillez corriger les problèmes identifiés.', colors.yellow);
    process.exit(1);
  }
}

// Exécution
main().catch(error => {
  log(`❌ Erreur lors de la vérification: ${error.message}`, colors.red);
  process.exit(1);
});
