import fs from 'fs/promises';
import path from 'path';

console.log('🔧 Nettoyage des doublons dragProtectionActive...');

async function run() {
  const file = path.join(process.cwd(), 'src', 'teamCalendarApp.ts');
  let code = await fs.readFile(file, 'utf-8');
  let changed = false;

  // supprimer les blocs imbriqués redondants (chercher 3 occurrences successives)
  const duplicatePattern = /(const now = Date\.now\(\);[\s\S]*?ordre des employés non rechargé'[\s\S]*?}){2,}/g;
  code = code.replace(duplicatePattern, (match) => {
    changed = true;
    // garder la première occurrence
    const first = match.match(/const now[\s\S]*?}/);
    return first ? first[0] : match;
  });

  if (changed) {
    await fs.writeFile(file, code, 'utf-8');
    console.log('✅ Doublons supprimés.');
  } else {
    console.log('ℹ️ Aucun doublon trouvé.');
  }
}

run().catch(err => { console.error(err); process.exit(1); }); 