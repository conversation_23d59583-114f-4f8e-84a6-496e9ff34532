/* ===== SYSTÈME DE TUTORIEL INTERACTIF ===== */

/* Icône d'aide flottante */
.tutorial-help-icon {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    z-index: 9999;
    transition: all 0.3s ease;
    animation: tutorial-pulse 2s infinite;
}

.tutorial-help-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.tutorial-help-icon .material-icons {
    color: white;
    font-size: 28px;
}

@keyframes tutorial-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Overlay principal du tutoriel */
.tutorial-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: tutorial-fade-in 0.5s ease;
}

@keyframes tutorial-fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Menu principal du tutoriel */
.tutorial-menu {
    background: white;
    border-radius: 20px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: tutorial-slide-up 0.6s ease;
    position: relative;
}

@keyframes tutorial-slide-up {
    from { 
        opacity: 0;
        transform: translateY(50px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

.tutorial-menu h2 {
    color: #4f46e5;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 10px;
    text-align: center;
}

.tutorial-menu .subtitle {
    color: #6b7280;
    font-size: 16px;
    text-align: center;
    margin-bottom: 25px;
}

.tutorial-options {
    display: grid;
    gap: 15px;
}

.tutorial-option {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
}

.tutorial-option:hover {
    border-color: #4f46e5;
    background: linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
}

.tutorial-option .icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.tutorial-option.beginner .icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.tutorial-option.advanced .icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.tutorial-option.features .icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.tutorial-option .content h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 5px;
}

.tutorial-option .content p {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

/* Overlay de mise en évidence */
.tutorial-highlight-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.7);
    z-index: 9998;
    pointer-events: none;
}

.tutorial-spotlight {
    position: absolute;
    border: 3px solid #4f46e5;
    border-radius: 12px;
    background: rgba(79, 70, 229, 0.1);
    box-shadow: 
        0 0 0 9999px rgba(0, 0, 0, 0.7),
        0 0 30px rgba(79, 70, 229, 0.5),
        inset 0 0 20px rgba(79, 70, 229, 0.2);
    animation: tutorial-spotlight-pulse 2s infinite;
    z-index: 9999;
}

@keyframes tutorial-spotlight-pulse {
    0%, 100% { 
        border-color: #4f46e5;
        box-shadow: 
            0 0 0 9999px rgba(0, 0, 0, 0.7),
            0 0 30px rgba(79, 70, 229, 0.5),
            inset 0 0 20px rgba(79, 70, 229, 0.2);
    }
    50% { 
        border-color: #06b6d4;
        box-shadow: 
            0 0 0 9999px rgba(0, 0, 0, 0.7),
            0 0 40px rgba(6, 182, 212, 0.7),
            inset 0 0 25px rgba(6, 182, 212, 0.3);
    }
}

/* Bulle d'explication */
.tutorial-bubble {
    position: fixed;
    background: white;
    border-radius: 16px;
    padding: 20px;
    max-width: 350px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    z-index: 10001;
    animation: tutorial-bubble-appear 0.5s ease;
}

@keyframes tutorial-bubble-appear {
    from { 
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    to { 
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.tutorial-bubble::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
}

.tutorial-bubble.top::before {
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 10px 10px 0 10px;
    border-color: white transparent transparent transparent;
}

.tutorial-bubble.bottom::before {
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 0 10px 10px 10px;
    border-color: transparent transparent white transparent;
}

.tutorial-bubble.left::before {
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    border-width: 10px 0 10px 10px;
    border-color: transparent transparent transparent white;
}

.tutorial-bubble.right::before {
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    border-width: 10px 10px 10px 0;
    border-color: transparent white transparent transparent;
}

.tutorial-bubble h3 {
    color: #4f46e5;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tutorial-bubble p {
    color: #374151;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
}

.tutorial-bubble .controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.tutorial-bubble .progress {
    font-size: 12px;
    color: #6b7280;
    background: #f3f4f6;
    padding: 4px 8px;
    border-radius: 12px;
}

.tutorial-bubble .buttons {
    display: flex;
    gap: 8px;
}

.tutorial-btn {
    padding: 8px 16px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.tutorial-btn.primary {
    background: #4f46e5;
    color: white;
}

.tutorial-btn.primary:hover {
    background: #4338ca;
    transform: translateY(-1px);
}

.tutorial-btn.secondary {
    background: #f3f4f6;
    color: #374151;
}

.tutorial-btn.secondary:hover {
    background: #e5e7eb;
}

.tutorial-btn.skip {
    background: transparent;
    color: #6b7280;
    text-decoration: underline;
}

.tutorial-btn.skip:hover {
    color: #374151;
}

/* Animation de pointeur */
.tutorial-pointer {
    position: fixed;
    width: 40px;
    height: 40px;
    background: #4f46e5;
    border-radius: 50%;
    z-index: 10002;
    animation: tutorial-pointer-bounce 1s infinite;
    pointer-events: none;
}

.tutorial-pointer::after {
    content: '👆';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
}

@keyframes tutorial-pointer-bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
    .tutorial-menu {
        background: #1f2937;
        color: white;
    }
    
    .tutorial-menu h2 {
        color: #8b5cf6;
    }
    
    .tutorial-option {
        background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
        border-color: #4b5563;
    }
    
    .tutorial-option:hover {
        border-color: #8b5cf6;
        background: linear-gradient(135deg, #4c1d95 0%, #5b21b6 100%);
    }
    
    .tutorial-bubble {
        background: #1f2937;
        color: white;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .tutorial-help-icon {
        bottom: 15px;
        right: 15px;
        width: 50px;
        height: 50px;
    }
    
    .tutorial-menu {
        padding: 20px;
        margin: 20px;
    }
    
    .tutorial-bubble {
        max-width: 280px;
        padding: 15px;
    }
} 