// Utilitaires de validation pour éviter les erreurs UUID
// et les crashes du serveur

/**
 * Valide si une chaîne est un UUID valide
 * @param {string} uuid - L'UUID à valider
 * @returns {boolean} - true si l'UUID est valide
 */
export function isValidUUID(uuid) {
  if (!uuid || typeof uuid !== 'string') {
    return false;
  }
  
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * Vérifie si un ID est un ancien format non-UUID
 * @param {string} id - L'ID à vérifier
 * @returns {boolean} - true s'il s'agit d'un ancien ID
 */
export function isLegacyId(id) {
  if (!id || typeof id !== 'string') {
    return false;
  }
  
  // Formats d'anciens IDs détectés : "e1", "e2", "temp_xxx", etc.
  return id.startsWith('e') || id.startsWith('temp_') || !isValidUUID(id);
}

/**
 * Middleware pour valider les paramètres UUID dans les routes
 * @param {string} paramName - Nom du paramètre à valider
 * @returns {Function} - Middleware Express
 */
export function validateUUIDParam(paramName = 'id') {
  return (req, res, next) => {
    const uuid = req.params[paramName];
    
    // Vérification stricte pour éviter les IDs vides ou undefined
    if (!uuid || uuid.trim() === '') {
      console.log(`⚠️  [UUID Validation] Paramètre ${paramName} manquant ou vide`);
      return res.status(400).json({ 
        error: `Paramètre ${paramName} requis et ne peut pas être vide`,
        code: 'MISSING_OR_EMPTY_PARAM'
      });
    }
    
    if (isLegacyId(uuid)) {
      console.log(`🚫 [UUID Validation] ID héritage détecté: "${uuid}"`);
      return res.status(400).json({ 
        error: `ID héritage non supporté: "${uuid}". Veuillez utiliser un UUID valide.`,
        code: 'LEGACY_ID_NOT_SUPPORTED',
        legacyId: uuid
      });
    }
    
    if (!isValidUUID(uuid)) {
      console.log(`❌ [UUID Validation] UUID invalide: "${uuid}"`);
      return res.status(400).json({ 
        error: `UUID invalide: "${uuid}". Format attendu: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`,
        code: 'INVALID_UUID_FORMAT',
        receivedValue: uuid
      });
    }
    
    // UUID valide, continuer
    console.log(`✅ [UUID Validation] UUID valide: ${uuid.substring(0, 8)}...`);
    next();
  };
}

/**
 * Gestionnaire d'erreur global pour les erreurs UUID PostgreSQL
 * @param {Error} error - L'erreur PostgreSQL
 * @param {Object} req - Requête Express
 * @param {Object} res - Réponse Express
 * @param {Function} next - Fonction next
 */
export function handlePostgreSQLUUIDError(error, req, res, next) {
  // Erreur UUID PostgreSQL (code 22P02)
  if (error.code === '22P02' && error.message.includes('uuid')) {
    console.error('🚨 [PostgreSQL UUID Error] Erreur UUID interceptée:', {
      error: error.message,
      route: req.originalUrl,
      method: req.method,
      params: req.params,
      body: req.body
    });
    
    return res.status(400).json({
      error: 'Format d\'identifiant invalide. UUID requis.',
      code: 'POSTGRESQL_UUID_ERROR',
      details: 'L\'identifiant fourni n\'est pas au format UUID valide.'
    });
  }
  
  // Autres erreurs PostgreSQL de format
  if (error.code === '22P02') {
    console.error('🚨 [PostgreSQL Format Error] Erreur de format interceptée:', {
      error: error.message,
      route: req.originalUrl,
      method: req.method
    });
    
    return res.status(400).json({
      error: 'Erreur de format de données',
      code: 'POSTGRESQL_FORMAT_ERROR'
    });
  }
  
  // Continuer vers le gestionnaire d'erreur suivant
  next(error);
}

/**
 * Valide les UUIDs dans un objet de données
 * @param {Object} data - Données à valider
 * @param {Array} uuidFields - Champs UUID à valider
 * @returns {Object} - Résultat de validation
 */
export function validateUUIDsInData(data, uuidFields = []) {
  const errors = [];
  const warnings = [];
  
  for (const field of uuidFields) {
    const value = data[field];
    
    if (value) {
      if (isLegacyId(value)) {
        warnings.push({
          field,
          value,
          message: `ID héritage détecté: ${field} = ${value}`
        });
      } else if (!isValidUUID(value)) {
        errors.push({
          field,
          value,
          message: `UUID invalide: ${field} = ${value}`
        });
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    hasLegacyIds: warnings.length > 0
  };
}

/**
 * Logs détaillés pour le debugging des erreurs UUID
 * @param {string} operation - Opération en cours
 * @param {Object} data - Données concernées
 */
export function logUUIDOperation(operation, data) {
  console.log(`🔍 [UUID Debug] ${operation}:`, {
    timestamp: new Date().toISOString(),
    operation,
    data: JSON.stringify(data, null, 2)
  });
} 