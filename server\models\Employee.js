import { query } from '../config/database.js';

class Employee {
  static async findAll() {
    const result = await query(`
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
    `);
    return result.rows;
  }

  static async findById(id) {
    const result = await query('SELECT * FROM employees WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async create(employeeData) {
    const { name, status, avatar, template_id, extra_fields } = employeeData;
    
    const result = await query(`
      INSERT INTO employees (name, status, avatar, template_id, extra_fields)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [name, status, avatar, template_id, extra_fields || {}]);
    
    return result.rows[0];
  }

  static async update(id, employeeData) {
    const { name, status, avatar, template_id, extra_fields } = employeeData;
    
    const result = await query(`
      UPDATE employees 
      SET name = $1, status = $2, avatar = $3, template_id = $4, extra_fields = $5
      WHERE id = $6
      RETURNING *
    `, [name, status, avatar, template_id, extra_fields || {}, id]);
    
    return result.rows[0];
  }

  static async delete(id) {
    const result = await query('DELETE FROM employees WHERE id = $1 RETURNING *', [id]);
    return result.rows[0];
  }

  static async findByStatus(status) {
    const result = await query('SELECT * FROM employees WHERE status = $1', [status]);
    return result.rows;
  }

  static async search(searchTerm) {
    const result = await query(`
      SELECT * FROM employees 
      WHERE name ILIKE $1 OR status ILIKE $1 
      ORDER BY name
    `, [`%${searchTerm}%`]);
    return result.rows;
  }
}

export default Employee; 