/**
 * 🧪 SCRIPT DE TEST - Corrections Mode Debug
 * Vérifier que les fonctions debug fonctionnent correctement
 */

console.log(`
🧪 TEST CORRECTIONS MODE DEBUG
=============================
`);

// ✅ Test 1: Vérifier unifiedLogger
console.log('1️⃣ Test unifiedLogger...');
if (window.unifiedLogger) {
  console.log('   ✅ unifiedLogger disponible');
  
  // Tester getDebugMode
  if (typeof window.unifiedLogger.getDebugMode === 'function') {
    console.log('   ✅ getDebugMode disponible');
    try {
      const debugMode = window.unifiedLogger.getDebugMode();
      console.log('   📊 Mode actuel:', debugMode);
    } catch (error) {
      console.log('   ❌ Erreur getDebugMode:', error.message);
    }
  } else {
    console.log('   ❌ getDebugMode manquant');
  }
  
  // Tester enableDebug
  if (typeof window.unifiedLogger.enableDebug === 'function') {
    console.log('   ✅ enableDebug disponible');
  } else {
    console.log('   ❌ enableDebug manquant');
  }
  
  // Tester disableDebug
  if (typeof window.unifiedLogger.disableDebug === 'function') {
    console.log('   ✅ disableDebug disponible');
  } else {
    console.log('   ❌ disableDebug manquant');
  }
  
} else {
  console.log('   ❌ unifiedLogger non disponible');
}

// ✅ Test 2: Vérifier fonctions globales
console.log('\n2️⃣ Test fonctions globales...');
if (typeof window.enableUltraDebug === 'function') {
  console.log('   ✅ window.enableUltraDebug disponible');
} else {
  console.log('   ❌ window.enableUltraDebug manquant');
}

if (typeof window.disableUltraDebug === 'function') {
  console.log('   ✅ window.disableUltraDebug disponible');
} else {
  console.log('   ❌ window.disableUltraDebug manquant');
}

// ✅ Test 3: Vérifier localStorage
console.log('\n3️⃣ Test localStorage...');
const debugEnabled = localStorage.getItem('ULTRA_DEBUG_MODE') === 'true';
const debugLevel = localStorage.getItem('DEBUG_LEVEL') || 'normal';
console.log(`   📊 Mode debug actuel: ${debugEnabled ? 'ACTIVÉ' : 'DÉSACTIVÉ'}`);
console.log(`   📊 Niveau: ${debugLevel.toUpperCase()}`);

// ✅ Test 4: Test fonctionnel
console.log('\n4️⃣ Test fonctionnel...');
console.log('   💡 Commandes disponibles:');
console.log('   - testEnableDebug() : Tester activation mode debug');
console.log('   - testDisableDebug() : Tester désactivation mode debug');
console.log('   - testUnifiedLogger() : Tester API unifiedLogger');

// Fonctions de test
window.testEnableDebug = () => {
  console.log('🧪 Test activation mode debug...');
  try {
    if (window.unifiedLogger && typeof window.unifiedLogger.enableDebug === 'function') {
      console.log('   ✅ Test via unifiedLogger.enableDebug()');
      // Ne pas vraiment activer pour le test
      console.log('   💡 Utiliser: window.unifiedLogger.enableDebug("verbose")');
    } else if (typeof window.enableUltraDebug === 'function') {
      console.log('   ✅ Test via window.enableUltraDebug()');
      console.log('   💡 Utiliser: window.enableUltraDebug("verbose")');
    } else {
      console.log('   ❌ Aucune méthode d\'activation disponible');
    }
  } catch (error) {
    console.log('   ❌ Erreur:', error.message);
  }
};

window.testDisableDebug = () => {
  console.log('🧪 Test désactivation mode debug...');
  try {
    if (window.unifiedLogger && typeof window.unifiedLogger.disableDebug === 'function') {
      console.log('   ✅ Test via unifiedLogger.disableDebug()');
      console.log('   💡 Utiliser: window.unifiedLogger.disableDebug()');
    } else if (typeof window.disableUltraDebug === 'function') {
      console.log('   ✅ Test via window.disableUltraDebug()');
      console.log('   💡 Utiliser: window.disableUltraDebug()');
    } else {
      console.log('   ❌ Aucune méthode de désactivation disponible');
    }
  } catch (error) {
    console.log('   ❌ Erreur:', error.message);
  }
};

window.testUnifiedLogger = () => {
  console.log('🧪 Test complet unifiedLogger...');
  try {
    if (!window.unifiedLogger) {
      console.log('   ❌ unifiedLogger non disponible');
      return;
    }
    
    const logger = window.unifiedLogger;
    
    // Test toutes les méthodes
    const methods = ['frontend', 'browser', 'getSessionId', 'reinit', 'enableDebug', 'disableDebug', 'getDebugMode', 'flushQueue', 'status'];
    
    methods.forEach(method => {
      if (typeof logger[method] !== 'undefined') {
        console.log(`   ✅ ${method} disponible`);
      } else {
        console.log(`   ❌ ${method} manquant`);
      }
    });
    
    // Test getDebugMode
    if (typeof logger.getDebugMode === 'function') {
      const debugInfo = logger.getDebugMode();
      console.log('   📊 Info debug:', debugInfo);
    }
    
    // Test status si disponible
    if (typeof logger.status === 'function') {
      logger.status();
    }
    
  } catch (error) {
    console.log('   ❌ Erreur test unifiedLogger:', error.message);
  }
};

// ✅ Diagnostic automatique final
console.log('\n🎯 DIAGNOSTIC FINAL');
console.log('================');

const issues = [];
const fixes = [];

if (!window.unifiedLogger) {
  issues.push('unifiedLogger non disponible');
  fixes.push('Recharger la page ou vérifier capture-logs-unified.js');
}

if (window.unifiedLogger && typeof window.unifiedLogger.enableDebug !== 'function') {
  issues.push('enableDebug manquant dans unifiedLogger');
  fixes.push('Vérifier que enableUltraDebug est défini avant unifiedLogger');
}

if (typeof window.enableUltraDebug !== 'function') {
  issues.push('enableUltraDebug global manquant');
  fixes.push('Vérifier capture-logs-unified.js et la définition des fonctions');
}

if (issues.length === 0) {
  console.log('🎉 TOUS LES TESTS RÉUSSIS !');
  console.log('✅ Le mode debug ultra-intensif est prêt à l\'emploi');
  console.log('');
  console.log('🚀 ACTIVATION RAPIDE:');
  console.log('   window.unifiedLogger.enableDebug("insane")  // Mode INSANE');
  console.log('   window.unifiedLogger.enableDebug("verbose") // Mode VERBOSE');
  console.log('   window.unifiedLogger.disableDebug()         // Désactiver');
} else {
  console.log(`❌ ${issues.length} problème(s) détecté(s):`);
  issues.forEach((issue, i) => {
    console.log(`   ${i + 1}. ${issue}`);
    console.log(`      💡 Solution: ${fixes[i]}`);
  });
}

console.log('\n🛠️ OUTILS DE DIAGNOSTIC:');
console.log('   testEnableDebug()   - Tester activation');
console.log('   testDisableDebug()  - Tester désactivation');
console.log('   testUnifiedLogger() - Test complet API');

console.log('\n✅ Script de test chargé !'); 