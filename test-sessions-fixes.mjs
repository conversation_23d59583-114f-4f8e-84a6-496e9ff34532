#!/usr/bin/env node

/**
 * Script de test automatisé pour valider les corrections de sessions logs
 */

import fetch from 'node-fetch';

console.log('🧪 [TEST] Validation des corrections sessions logs');
console.log('='.repeat(60));

const API_BASE = 'http://localhost:3001';

async function testSessionsAPI() {
  console.log('📡 [TEST-API] Test des endpoints...');
  
  try {
    // 1. Test session courante
    console.log('1️⃣ Test /api/debug/current-session...');
    const currentResponse = await fetch(`${API_BASE}/api/debug/current-session`);
    if (currentResponse.ok) {
      const currentData = await currentResponse.json();
      console.log(`✅ Session serveur: ${currentData.sessionId.substring(0, 8)}...`);
      console.log(`✅ Démarrage serveur: ${currentData.serverStarted}`);
    } else {
      throw new Error(`Erreur ${currentResponse.status}`);
    }

    // 2. Test liste sessions
    console.log('2️⃣ Test /api/debug/sessions...');
    const sessionsResponse = await fetch(`${API_BASE}/api/debug/sessions`);
    if (sessionsResponse.ok) {
      const sessionsData = await sessionsResponse.json();
      console.log(`✅ ${sessionsData.length} sessions trouvées`);
      sessionsData.forEach((session, i) => {
        console.log(`   ${i+1}. ${session.id.substring(0, 8)}... (${session.total} logs)`);
      });
    } else {
      throw new Error(`Erreur ${sessionsResponse.status}`);
    }

    // 3. Test purge anciennes sessions (si plusieurs sessions)
    const purgeResponse = await fetch(`${API_BASE}/api/debug/sessions/cleanup/0`, {
      method: 'DELETE'
    });
    if (purgeResponse.ok) {
      const purgeData = await purgeResponse.json();
      console.log(`3️⃣ Test purge: ${purgeData.message}`);
    }

    // 4. Test après purge
    console.log('4️⃣ Test sessions après purge...');
    const afterPurgeResponse = await fetch(`${API_BASE}/api/debug/sessions`);
    if (afterPurgeResponse.ok) {
      const afterPurgeData = await afterPurgeResponse.json();
      console.log(`✅ ${afterPurgeData.length} sessions restantes après purge`);
    }

    console.log('🎉 [TEST-API] Tous les tests API passés !');
    return true;
    
  } catch (error) {
    console.error('❌ [TEST-API] Erreur:', error.message);
    return false;
  }
}

async function testSessionLogic() {
  console.log('\n🔄 [TEST-LOGIC] Test logique session = serveur...');
  
  try {
    // Simuler deux appels à current-session
    const call1 = await fetch(`${API_BASE}/api/debug/current-session`);
    const data1 = await call1.json();
    
    await new Promise(resolve => setTimeout(resolve, 1000)); // Attendre 1s
    
    const call2 = await fetch(`${API_BASE}/api/debug/current-session`);
    const data2 = await call2.json();
    
    if (data1.serverStarted === data2.serverStarted) {
      console.log('✅ Timestamp serveur stable (session unique)');
      console.log(`   Timestamp: ${data1.serverStarted}`);
    } else {
      console.log('❌ Timestamp serveur instable (problème)');
    }
    
    if (data1.sessionId === data2.sessionId) {
      console.log('✅ Session ID stable');
    } else {
      console.log('❌ Session ID change (problème)');
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ [TEST-LOGIC] Erreur:', error.message);
    return false;
  }
}

async function showInstructions() {
  console.log('\n📋 [INSTRUCTIONS] Tests manuels à effectuer:');
  console.log('');
  console.log('1️⃣ Test session unique browser:');
  console.log('   - Ouvrir http://localhost:5173');
  console.log('   - Observer console: "🆕 NOUVEAU SERVEUR détecté" ou "♻️ Session réutilisée"');
  console.log('   - Aller sur /logs et vérifier une seule session active');
  console.log('');
  console.log('2️⃣ Test redémarrage serveur:');
  console.log('   - Arrêter serveur (Ctrl+C)');
  console.log('   - Redémarrer npm run dev:system');
  console.log('   - Observer: "🆕 NOUVEAU SERVEUR détecté"');
  console.log('   - Vérifier nouvelle session dans /logs');
  console.log('');
  console.log('3️⃣ Test purge interface:');
  console.log('   - Aller sur /logs');
  console.log('   - Cliquer menu "Nettoyage"');
  console.log('   - Tester "🔥 Purger anciennes sessions"');
  console.log('   - Tester "💀 PURGE TOTALE"');
  console.log('');
  console.log('4️⃣ Test modes de tri:');
  console.log('   - Tester "🎯 Groupé par source"');
  console.log('   - Tester "⏰ Chronologique pur"');
  console.log('   - Tester "🧠 IA compact"');
  console.log('   - Tester "🔀 Chaotique"');
  console.log('');
  console.log('🎯 [VALIDATION] Vérifier que:');
  console.log('   ✅ Une seule session capture les logs');
  console.log('   ✅ Nouvelle session à chaque redémarrage serveur');
  console.log('   ✅ Purge fonctionne correctement');
  console.log('   ✅ Modes de tri organisent les logs');
  console.log('   ✅ Temps réel fonctionne sans doublons');
}

// Exécution des tests
async function runTests() {
  const apiTests = await testSessionsAPI();
  const logicTests = await testSessionLogic();
  
  if (apiTests && logicTests) {
    console.log('\n🎉 [RÉSULTAT] Tous les tests automatisés PASSÉS !');
    await showInstructions();
  } else {
    console.log('\n❌ [RÉSULTAT] Certains tests ont ÉCHOUÉ !');
    console.log('   Vérifiez que le serveur est démarré avec npm run dev:system');
  }
}

runTests().catch(error => {
  console.error('💥 [FATAL] Erreur test:', error);
  process.exit(1);
}); 