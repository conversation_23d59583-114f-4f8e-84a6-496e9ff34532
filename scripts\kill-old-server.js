#!/usr/bin/env node

import { spawn, exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * Script pour fermer automatiquement l'ancien serveur Node.js
 * et démarrer le serveur stable
 */

async function killNodeProcesses() {
  console.log('🔄 Arrêt des anciens serveurs Node.js...');
  
  try {
    if (process.platform === 'win32') {
      // Windows
      await execAsync('taskkill /f /im node.exe 2>nul');
      console.log('✅ Processus Node.js Windows fermés');
    } else {
      // Unix/Linux/Mac
      try {
        await execAsync('pkill -f "node.*server"');
        console.log('✅ Processus serveur Node.js fermés');
      } catch (error) {
        // Pas de processus à fermer, c'est normal
        console.log('ℹ️  Aucun processus serveur à fermer');
      }
    }
  } catch (error) {
    console.log('ℹ️  Aucun processus Node.js à fermer ou erreur attendue');
  }
  
  // Attendre un moment pour que les processus se ferment
  await new Promise(resolve => setTimeout(resolve, 2000));
}

async function startStableServer() {
  console.log('🚀 Démarrage du serveur stable...');
  
  const serverProcess = spawn('node', ['server/app-simple.js'], {
    stdio: 'inherit',
    shell: true
  });
  
  serverProcess.on('error', (error) => {
    console.error('❌ Erreur lors du démarrage du serveur:', error);
    process.exit(1);
  });
  
  process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt du serveur...');
    serverProcess.kill();
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    console.log('\n🛑 Arrêt du serveur...');
    serverProcess.kill();
    process.exit(0);
  });
}

async function main() {
  try {
    await killNodeProcesses();
    await startStableServer();
  } catch (error) {
    console.error('💥 Erreur:', error);
    process.exit(1);
  }
}

main(); 