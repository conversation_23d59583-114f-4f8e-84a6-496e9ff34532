/* ✅ Styles pour les grips d'attributions régulières */

.grip-regular {
  z-index: 10;
  transition: all 0.15s ease-in-out;
}

.grip-regular.show-grip {
  display: block !important;
}

.grip-regular:hover {
  cursor: grab;
  background: rgba(29, 78, 216, 0.6) !important;
  border-color: rgba(29, 78, 216, 0.8) !important;
  transform: scaleY(1.2);
}

.grip-regular:active {
  cursor: grabbing;
  background: rgba(29, 78, 216, 0.8) !important;
}

/* ✅ Zones de drop pour attributions régulières */

.regular-assignment-drop-zone {
  position: relative;
  transition: all 0.15s ease-in-out;
}

.regular-assignment-drop-zone::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
  border: 2px dashed rgba(16, 185, 129, 0.3);
  border-radius: 8px;
  pointer-events: none;
  z-index: 1;
}

.regular-assignment-drop-zone:hover::before {
  background: linear-gradient(90deg, rgba(16, 185, 129, 0.15) 0%, rgba(16, 185, 129, 0.08) 100%);
  border-color: rgba(16, 185, 129, 0.5);
}

/* ✅ Animation pour le drag en cours */

.shift-card:has(.grip-regular.dragging) {
  opacity: 0.7;
  transform: scale(0.95);
}

/* ✅ Styles pour l'état de survol des blocs avec grip */

.shift-card:hover .grip-regular {
  display: block;
}

/* ✅ Mode show-grips global (si nécessaire pour debug) */

.show-grips .grip-regular {
  display: block !important;
  background: rgba(29, 78, 216, 0.4) !important;
}

/* ✅ Feedback visuel lors du drop */

.employee-row.bg-emerald-100 {
  background-color: rgba(16, 185, 129, 0.1) !important;
  border-color: rgba(16, 185, 129, 0.3) !important;
  transition: all 0.1s ease-in-out;
}

/* ✅ Amélioration de l'accessibilité */

.grip-regular:focus {
  outline: 2px solid rgba(29, 78, 216, 0.8);
  outline-offset: 1px;
}

/* ✅ Animation du fantôme de drag */

.drag-ghost-regular {
  background: rgba(29, 78, 216, 0.2) !important;
  border: 1px solid rgba(29, 78, 216, 0.4) !important;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 10px;
  color: #1d4ed8;
  text-align: center;
  line-height: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* ✅ Responsive - masquer les grips sur mobile pour éviter les conflits */

@media (max-width: 768px) {
  .grip-regular {
    display: none !important;
  }
  
  /* Alternative pour mobile : indicateur visuel sur les blocs réguliers */
  .shift-card[data-regular="true"]::after {
    content: '🔄';
    position: absolute;
    top: 2px;
    left: 2px;
    font-size: 8px;
    opacity: 0.6;
  }
}

/* ✅ Mode sombre (si implémenté dans le futur) */

@media (prefers-color-scheme: dark) {
  .grip-regular {
    background: rgba(96, 165, 250, 0.3) !important;
    border-color: rgba(96, 165, 250, 0.5) !important;
  }
  
  .regular-assignment-drop-zone::before {
    background: linear-gradient(90deg, rgba(34, 197, 94, 0.15) 0%, rgba(34, 197, 94, 0.08) 100%);
    border-color: rgba(34, 197, 94, 0.4);
  }
} 