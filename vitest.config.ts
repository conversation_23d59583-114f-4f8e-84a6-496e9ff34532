import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    // Configuration de l'environnement de test
    environment: 'jsdom',
    
    // Fichiers de setup
    setupFiles: ['./src/__tests__/setup.ts'],
    
    // Patterns de fichiers de test
    include: [
      'src/**/*.{test,spec}.{js,ts,jsx,tsx}',
      'src/__tests__/**/*.{js,ts,jsx,tsx}'
    ],
    
    // Exclusions
    exclude: [
      'node_modules',
      'dist',
      'build',
      'coverage',
      '**/setup.{ts,js}' // ✅ CORRECTION : Exclure setup.ts des tests
    ],
    
    // Configuration de la couverture
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage',
      
      // Seuils de couverture
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      },
      
      // Fichiers à inclure dans la couverture
      include: [
        'src/**/*.{js,ts,jsx,tsx}'
      ],
      
      // Fichiers à exclure de la couverture
      exclude: [
        'src/**/*.d.ts',
        'src/**/*.test.{js,ts,jsx,tsx}',
        'src/**/*.spec.{js,ts,jsx,tsx}',
        'src/__tests__/**/*',
        'src/main.tsx',
        'src/vite-env.d.ts'
      ]
    },
    
    // Timeout pour les tests
    testTimeout: 10000,
    
    // Configuration des globals
    globals: true,
    
    // Reporters
    reporter: ['verbose', 'json', 'html'],
    
    // Configuration des mocks
    mockReset: true,
    clearMocks: true,
    restoreMocks: true
  },
  
  // Résolution des modules
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@tests': resolve(__dirname, './src/__tests__')
    }
  },
  
  // Configuration pour les dépendances
  define: {
    'import.meta.vitest': undefined
  }
});
