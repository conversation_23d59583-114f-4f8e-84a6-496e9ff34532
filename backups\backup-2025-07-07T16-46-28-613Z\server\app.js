import express from 'express';
import cors from 'cors';
import Employee from './models/Employee.js';
import Shift from './models/Shift.js';
import RegularAssignmentV2 from './models/RegularAssignmentV2.js';
// Alias pour compatibilité
const RegularAssignment = RegularAssignmentV2;
import { query } from './config/database.js';
import {
  validateUUIDParam,
  handlePostgreSQLUUIDError,
  validateUUIDsInData,
  logUUIDOperation
} from './utils/validation.js';
import { logger, currentSession, logSystem, logError, logEmitter } from './config/logger.js';

// ✅ Définition du port (manquante auparavant)
const PORT = process.env.SERVER_PORT || 3001;

// =====================================================
// PATCH CONSOLE BACKEND ➜ ENVOI AU SYSTÈME DE LOGS UNIFIÉ
// =====================================================
const originalConsole = {
  log: console.log,
  info: console.info,
  warn: console.warn,
  error: console.error,
  debug: console.debug
};

function patchBackendConsole(sessionId) {
  const shouldSkip = (args) => {
    const txt = args.filter(a => typeof a === 'string').join(' ');
    return txt.includes('[query]') ||
           txt.includes('INSERT INTO logs') ||
           txt.includes('/api/logs') ||
           txt.includes('Executing query');
  };

  let isSending = false;
  const send = async (level, args) => {
    if (isSending || shouldSkip(args)) return;
    isSending = true;
    try {
      await fetch(`http://localhost:${PORT}/api/logs`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId,
          source: 'backend',
          level,
          message: args.map(a => (typeof a === 'object' ? JSON.stringify(a) : String(a))).join(' '),
          data: {},
          priority: level === 'error' ? 3 : level === 'warn' ? 2 : 1
        })
      });
    } catch {
      // silencieux
    } finally {
      isSending = false;
    }
  };

  console.log  = (...a) => { originalConsole.log(...a); send('info',  a); };
  console.info = (...a) => { originalConsole.info(...a); send('info',  a); };
  console.warn = (...a) => { originalConsole.warn(...a); send('warn',  a); };
  console.error= (...a) => { originalConsole.error(...a);send('error', a); };
  console.debug= (...a) => { originalConsole.debug(...a);send('debug', a); };
  originalConsole.log('✅ [LOGS] Patch console backend (SSE) activé');
}

// Utiliser la session globale actuelle pour lier les logs backend
patchBackendConsole(currentSession);

// ===============================================
// INITIALISATION DE L'APPLICATION EXPRESS AVANT TOUTE UTILISATION
// ===============================================
const app = express();

// Middleware JSON nécessaire AVANT les routes de logs
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Création automatique de la table 'logs' si elle n'existe pas déjà
query(`CREATE TABLE IF NOT EXISTS logs (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  source VARCHAR(50) NOT NULL,
  level VARCHAR(50) NOT NULL,
  message TEXT NOT NULL,
  data JSONB,
  priority INTEGER DEFAULT 0
)`).then(()=>query(`ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 0`)).catch(err => console.error('[LOGS] Échec création/alter table logs:', err));



// =====================================================
// ROUTE DE TEST - INTÉGRATION LOGS BACKEND
// =====================================================
app.get('/api/debug/test-backend-logs', (req, res) => {
  console.log('[TEST] Log info depuis backend');
  console.warn('[TEST] Log warning depuis backend');
  console.error('[TEST] Log error depuis backend');
  
  // Test avec des données complexes
  console.log('[TEST] Objet complexe:', { 
    timestamp: new Date().toISOString(),
    test: true,
    nested: { data: 'test' }
  });
  
  res.json({ 
    message: 'Logs de test envoyés depuis backend',
    timestamp: new Date().toISOString(),
    sessionId: serverSessionId
  });
});

// =====================================================
// NOUVEAU SYSTÈME DE LOGS SIMPLIFIÉ
// =====================================================
let logClients = [];

// === CORS GLOBAL (autorise frontend localhost:5173) ===
app.use(cors({
  origin: 'http://localhost:5173',
  methods: ['GET','POST','OPTIONS'],
  allowedHeaders: ['Content-Type','Authorization'],
  credentials: false
}));

// OPTIONS preflight spécifique au flux SSE
app.options('/api/logs/stream', (_req,res)=>{
  res.setHeader('Access-Control-Allow-Origin','http://localhost:5173');
  res.setHeader('Access-Control-Allow-Methods','GET,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers','Content-Type');
  return res.sendStatus(204);
});

// Route pour que le frontend s'abonne aux logs
app.get('/api/logs/stream', async (req, res) => {
    // Le middleware CORS global ajoute déjà les en-têtes nécessaires.
    // On évite de les redéfinir afin de ne pas créer de doublons invalides.

    // Désactive le buffering éventuel des proxys (SSE immédiat)
    res.setHeader('X-Accel-Buffering', 'no');
    
    // Headers SSE
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.flushHeaders();

    const clientId = Date.now();
    const newClient = {
        id: clientId,
        res
    };
    logClients.push(newClient);
    console.log(`[LOGS] Client connecté: ${clientId}`);

    req.on('close', () => {
        logClients = logClients.filter(client => client.id !== clientId);
        console.log(`[LOGS] Client déconnecté: ${clientId}`);
    });

    const backlogLimit = parseInt((req.query && req.query.limit) ? req.query.limit : '200', 10) || 200;
    try {
      const backlogResult = await query('SELECT * FROM logs ORDER BY ts DESC LIMIT $1',[backlogLimit]);
      backlogResult.rows.reverse().forEach(row=>safeWrite(`data: ${JSON.stringify(row)}\n\n`));
    } catch(err) {
      console.error('❌ [SSE] Erreur chargement backlog logs:', err.message);
      // On continue quand même la connexion SSE (aucune fermeture)
    }
});

// Fonction pour envoyer un log à tous les clients connectés
function broadcastLog(log) {
    const formattedLog = `data: ${JSON.stringify(log)}\n\n`;
    logClients.forEach(client => client.res.write(formattedLog));
}

// Route pour recevoir des logs de n'importe où (frontend, backend)
app.post('/api/logs', async (req, res) => {
    try {
        const { sessionId, source, level, message, data, priority } = req.body;
        
        if (!sessionId || !source || !level || !message) {
            return res.status(400).json({ error: 'Champs manquants' });
        }

        const insertLog = async () => {
            return await query(
                'INSERT INTO logs(session_id, source, level, message, data, priority) VALUES($1, $2, $3, $4, $5, $6) RETURNING *',
                [sessionId, source, level, message, data || {}, priority || 0]
            );
        };
        let result;
        try {
            result = await insertLog();
        } catch (err) {
            if (err.code === '42703' && err.message.includes('priority')) {
                await query('ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 0');
                result = await insertLog();
            } else {
                throw err;
            }
        }
        
        const newLog = result.rows[0];
        
        // Diffuser le log aux clients SSE
        broadcastLog(newLog);

        res.status(201).json({ success: true, log: newLog });
    } catch (error) {
        // Ne pas utiliser le logger ici pour éviter les boucles
        console.error('[POST /api/logs] Erreur interne:', error);
        res.status(500).json({ error: 'Erreur interne du serveur' });
    }
});

// =====================================================
// ROUTES ATTRIBUTIONS RÉGULIÈRES (Legacy - à migrer)
// =====================================================
app.get('/api/regular-assignments', async (req, res) => {
  try {
    const assignments = await RegularAssignmentV2.findAllGrouped();
    res.json(assignments);
  } catch (error) {
    console.error('Erreur lors de la récupération des attributions régulières:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// ✅ CORRECTION : Route POST manquante pour création d'attribution individuelle
app.post('/api/regular-assignments', async (req, res) => {
  try {
    console.log('➕ [RegularAssignments] Création d\'une attribution individuelle');
    console.log('📤 [RegularAssignments] Données reçues:', JSON.stringify(req.body, null, 2));

    const assignmentData = req.body;

    // Validation des données requises
    if (!assignmentData.employeeId || !assignmentData.postId) {
      return res.status(400).json({
        error: 'Données manquantes',
        required: ['employeeId', 'postId'],
        received: Object.keys(assignmentData)
      });
    }

    // Validation des UUIDs
    const uuidFields = ['employeeId', 'postId'];
    for (const field of uuidFields) {
      if (!validateUUIDParam(assignmentData[field])) {
        return res.status(400).json({
          error: `UUID invalide pour le champ ${field}`,
          value: assignmentData[field]
        });
      }
    }

    // Validation de l'ID s'il est fourni
    if (assignmentData.id && !validateUUIDParam(assignmentData.id)) {
      return res.status(400).json({
        error: `UUID invalide pour le champ id`,
        value: assignmentData.id
      });
    }

    // Créer l'attribution
    const result = await RegularAssignmentV2.create(assignmentData);

    console.log('✅ [RegularAssignments] Attribution créée avec succès:', result.id);

    res.status(201).json({
      message: 'Attribution régulière créée avec succès',
      assignment: result
    });

  } catch (error) {
    console.error('❌ [RegularAssignments] Erreur création attribution:', error);

    // Gestion spécifique des erreurs PostgreSQL
    if (error.code === '23505') { // Violation de contrainte unique
      return res.status(409).json({
        error: 'Attribution déjà existante',
        details: 'Une attribution similaire existe déjà pour cet employé et ce poste'
      });
    }

    if (error.code === '23503') { // Violation de clé étrangère
      return res.status(400).json({
        error: 'Référence invalide',
        details: 'L\'employé ou le poste spécifié n\'existe pas'
      });
    }

    res.status(500).json({
      error: 'Erreur serveur lors de la création',
      message: error.message
    });
  }
});

// ✅ CORRECTION : Route PUT pour mise à jour d'attribution individuelle
app.put('/api/regular-assignments/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const assignmentData = req.body;

    console.log(`✏️ [RegularAssignments] Mise à jour attribution ${id}`);

    // Validation UUID
    if (!validateUUIDParam(id)) {
      return res.status(400).json({ error: 'UUID invalide', value: id });
    }

    const result = await RegularAssignmentV2.update(id, assignmentData);

    if (!result) {
      return res.status(404).json({ error: 'Attribution non trouvée' });
    }

    console.log('✅ [RegularAssignments] Attribution mise à jour:', id);

    res.json({
      message: 'Attribution mise à jour avec succès',
      assignment: result
    });

  } catch (error) {
    console.error('❌ [RegularAssignments] Erreur mise à jour:', error);
    res.status(500).json({ error: 'Erreur serveur', message: error.message });
  }
});

// ✅ CORRECTION : Route DELETE pour suppression d'attribution individuelle
app.delete('/api/regular-assignments/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`🗑️ [RegularAssignments] Suppression attribution ${id}`);

    // Validation UUID
    if (!validateUUIDParam(id)) {
      return res.status(400).json({ error: 'UUID invalide', value: id });
    }

    const result = await RegularAssignmentV2.delete(id);

    if (!result) {
      return res.status(404).json({ error: 'Attribution non trouvée' });
    }

    console.log('✅ [RegularAssignments] Attribution supprimée:', id);

    res.json({
      message: 'Attribution supprimée avec succès',
      id: id
    });

  } catch (error) {
    console.error('❌ [RegularAssignments] Erreur suppression:', error);
    res.status(500).json({ error: 'Erreur serveur', message: error.message });
  }
});

app.post('/api/regular-assignments/bulk', async (req, res) => {
  try {
    const { assignments } = req.body;
    if (!assignments || !Array.isArray(assignments)) {
      return res.status(400).json({ error: 'Le champ "assignments" est requis et doit être un tableau.' });
    }
    const results = await RegularAssignmentV2.bulkUpsert(assignments);
    res.status(201).json(results);
  } catch (error) {
    console.error('Erreur lors de la sauvegarde en lot des attributions:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// =====================================================
// ROUTES PARAMÈTRES D'APPLICATION
// =====================================================
app.get('/api/settings', async (req, res) => {
  try {
    const result = await query('SELECT * FROM app_settings ORDER BY setting_key');
    
    // Convertir en objet clé-valeur
    const settings = {};
    result.rows.forEach(row => {
      settings[row.setting_key] = row.setting_value;
    });
    
    res.json(settings);
  } catch (error) {
    console.error('Erreur lors de la récupération des paramètres:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

app.put('/api/settings', async (req, res) => {
  try {
    const settings = req.body;
    
    // Mettre à jour chaque paramètre
    for (const [key, value] of Object.entries(settings)) {
      await query(`
        INSERT INTO app_settings (setting_key, setting_value, description)
        VALUES ($1, $2, $3)
        ON CONFLICT (setting_key) 
        DO UPDATE SET setting_value = $2
      `, [key, JSON.stringify(value), `Paramètre ${key}`]);
    }
    
    res.json({ message: 'Paramètres mis à jour avec succès' });
  } catch (error) {
    console.error('Erreur lors de la mise à jour des paramètres:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// =====================================================
// ROUTES MODÈLES D'EMPLOYÉS
// =====================================================
app.get('/api/employee-templates', async (req, res) => {
  try {
    const result = await query('SELECT * FROM employee_templates ORDER BY name');
    res.json(result.rows);
  } catch (error) {
    console.error('Erreur lors de la récupération des modèles:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

app.post('/api/employee-templates', async (req, res) => {
  try {
    const { name, description, fields, is_default } = req.body;
    const result = await query(`
      INSERT INTO employee_templates (name, description, fields, is_default)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [name, description, fields, is_default || false]);
    
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Erreur lors de la création du modèle:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// =====================================================
// ROUTES EMPLOYÉS
// =====================================================
app.get('/api/employees', async (req, res) => {
  try {
    const employees = await Employee.findAll();
    res.json(employees);
  } catch (error) {
    console.error('❌ [GET /api/employees] Erreur:', error);
    res.status(500).json({ error: error.message });
  }
});

// =====================================================
// ROUTES SHIFTS & STANDARD POSTS
// =====================================================
app.get('/api/shifts', async (req, res) => {
  try {
    const shifts = await Shift.findAll();
    res.json(shifts);
  } catch (error) {
    console.error('❌ [GET /api/shifts] Erreur:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/standard-posts', async (req, res) => {
  try {
    const result = await query('SELECT * FROM standard_posts ORDER BY label');
    res.json(result.rows);
  } catch (error) {
    console.error('❌ [GET /api/standard-posts] Erreur:', error);
    res.status(500).json({ error: error.message });
  }
});

// =====================================================
// ROUTE HEALTH CHECK (pour ApiService)
// =====================================================
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// =====================================================
// ROUTE DE PURGE DE LA BASE DE DONNÉES
// =====================================================
app.post('/api/database/purge', async (req, res) => {
  try {
    console.log('🧹 [API/Database/Purge] Début de la purge complète...');
    
    // Importer le module de purge avec la syntaxe ES6 et path absolu
    const { createRequire } = await import('module');
    const require = createRequire(import.meta.url);
    const { purgeDatabaseDataWithConnection } = require('../scripts/database-purge-server.cjs');
    
    // Fonction wrapper pour adapter l'interface query
    const queryWrapper = async (sql, params = []) => {
      const result = await query(sql, params);
      return result;
    };
    
    // Exécuter la purge avec la connexion du serveur
    const summary = await purgeDatabaseDataWithConnection(queryWrapper);
    
    console.log('✅ [API/Database/Purge] Purge terminée avec succès');
    
    res.json({
      success: true,
      message: 'Purge de la base de données terminée avec succès',
      summary: summary,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ [API/Database/Purge] Erreur lors de la purge:', error);
    
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la purge de la base de données',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// =====================================================
// GESTION DES ERREURS ROBUSTE
// =====================================================

// Gestionnaire d'erreur UUID spécifique (doit être avant le gestionnaire global)
app.use(handlePostgreSQLUUIDError);

// Gestionnaire d'erreur global 
app.use((err, req, res, next) => {
  // Log détaillé de l'erreur
  console.error('🚨 [Server Error] Erreur interceptée:', {
    message: err.message,
    stack: err.stack,
    route: req.originalUrl,
    method: req.method,
    params: req.params,
    timestamp: new Date().toISOString()
  });
  
  // Éviter le crash du serveur en toutes circonstances
  if (!res.headersSent) {
    res.status(500).json({ 
      error: 'Erreur interne du serveur - Serveur stable',
      code: 'INTERNAL_SERVER_ERROR',
      message: 'L\'erreur a été capturée et le serveur continue de fonctionner'
    });
  }
});

// Gestionnaire pour les rejections non capturées
process.on('unhandledRejection', (reason, promise) => {
  console.error('🚨 [Server] Rejection non capturée détectée:', {
    reason: reason,
    promise: promise,
    timestamp: new Date().toISOString()
  });
  // Ne pas fermer le serveur, juste logger
});

// Gestionnaire pour les exceptions non capturées - VERSION STABLE
process.on('uncaughtException', (error) => {
  console.error('🚨 [Server] Exception non capturée détectée:', {
    error: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString()
  });
  
  // Log mais continuer l'exécution pour les erreurs UUID connues
  if (error.message.includes('uuid') || error.code === '22P02') {
    console.log('⚠️  [Server] Erreur UUID connue - Serveur continue');
    return;
  }
  
  // Pour les erreurs de connexion réseau/socket, ne pas fermer le serveur
  if (error.code === 'ECONNRESET' || error.code === 'ECONNABORTED' || 
      error.code === 'EPIPE' || error.code === 'ETIMEDOUT' ||
      error.message.includes('socket') || error.message.includes('connection')) {
    console.log('⚠️  [Server] Erreur de connexion réseau - Serveur continue');
    return;
  }
  
  // Pour les erreurs de base de données temporaires, ne pas fermer
  if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED' ||
      error.message.includes('database') || error.message.includes('postgres')) {
    console.log('⚠️  [Server] Erreur de base de données temporaire - Serveur continue');
    return;
  }
  
  // Pour les erreurs JSON/parsing, ne pas fermer
  if (error.message.includes('JSON') || error.message.includes('parse') ||
      error.name === 'SyntaxError') {
    console.log('⚠️  [Server] Erreur de parsing - Serveur continue');
    return;
  }
  
  // Seulement fermer pour les erreurs vraiment critiques
  if (error.name === 'RangeError' || error.name === 'ReferenceError' ||
      error.message.includes('out of memory') || error.message.includes('FATAL')) {
    console.log('💀 [Server] Erreur critique détectée - Arrêt nécessaire');
    process.exit(1);
  }
  
  // Pour toutes les autres erreurs, logger mais continuer
  console.log('⚠️  [Server] Erreur non critique - Serveur continue de fonctionner');
});

// =====================================================
// DÉMARRAGE DU SERVEUR ROBUSTE
// =====================================================
const server = app.listen(PORT, () => {
  console.log('='.repeat(60));
  console.log('🚀 SERVEUR API DÉMARRÉ AVEC PROTECTION UUID');
  console.log('='.repeat(60));
  console.log(`📍 URL: http://localhost:${PORT}`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
  console.log(`🛡️  Protection UUID: ACTIVÉE`);
  console.log(`🔍 Logs détaillés: ACTIVÉS`);
  console.log(`⚠️  Gestion erreurs: ROBUSTE`);
  console.log('='.repeat(60));
  console.log(`⏰ Démarré le: ${new Date().toISOString()}`);
  console.log('✅ Serveur prêt à recevoir les requêtes');
  console.log('');
});

// Gestion propre de l'arrêt du serveur
process.on('SIGTERM', () => {
  console.log('🛑 [Server] Signal SIGTERM reçu - Arrêt propre du serveur...');
  server.close(() => {
    console.log('✅ [Server] Serveur fermé proprement');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 [Server] Signal SIGINT reçu (Ctrl+C) - Arrêt du serveur...');
  server.close(() => {
    console.log('✅ [Server] Serveur fermé proprement');
    process.exit(0);
  });
});

// =====================================================
// ROUTES SYSTÈME DE LOGS ET DIAGNOSTIC
// =====================================================

// Endpoint SSE pour logs en temps réel - VERSION ROBUSTE
app.get('/api/debug/stream/:sessionId', (req, res) => {
  const { sessionId } = req.params;
  
  // Configuration SSE avec headers robustes
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
    'X-Accel-Buffering': 'no' // Nginx
  });

  // Variables de tracking de la connexion
  let isConnected = true;
  let heartbeatCount = 0;
  
  // Fonction pour vérifier si la connexion est toujours active
  const checkConnection = () => {
    if (!isConnected || res.destroyed || res.finished) {
      return false;
    }
    return true;
  };

  // Fonction sécurisée pour écrire dans le stream
  const safeWrite = (data) => {
    if (!checkConnection()) {
      return false;
    }
    
    try {
      res.write(data);
      return true;
    } catch (error) {
      console.log('[SSE] Erreur écriture:', error.message);
      isConnected = false;
      return false;
    }
  };

  // Fonction pour envoyer les logs avec gestion d'erreur robuste - VERSION DEBUG
  const sendLogs = async () => {
    if (!checkConnection()) {
      return false;
    }

    try {
      // ✅ DÉTECTION MODE DEBUG depuis paramètres URL
      const debugParam = req.query?.debug === 'true' || req.headers['x-debug-mode'] === 'true';
      const isDebugMode = debugParam || sessionId.includes('debug');
      
      // ✅ LIMITES ADAPTATIVES pour SSE
      const sseLimit = isDebugMode ? 1000 : 100; // 10x plus en mode debug
      
      // Timeout sur la requête SQL (augmenté pour mode debug)
      const sqlTimeout = isDebugMode ? 30000 : 10000;
      
      const queryPromise = query(`
        WITH ordered_logs AS (
          
        WITH ordered_logs AS (
          SELECT id, ts, level, source, message, 
                 data,
                 CASE 
                   WHEN level = 'error' THEN 100
                   WHEN level = 'warn' THEN 85
                   WHEN message ILIKE '%DRAG-SYSTEM%' OR message ILIKE '%AI-DRAG-LOG%' THEN 90
                   WHEN source = 'backend' THEN 70
                   WHEN source = 'frontend' THEN 65
                   WHEN source = 'drag-system' THEN 80
                   ELSE 50
                 END as score
          FROM logs 
          WHERE session_id = $1 
        )
        SELECT id, ts, level, source, message, data, score 
        FROM ordered_logs 
        ORDER BY ts ASC, score DESC, id ASC
        LIMIT $2
      `, [sessionId, sseLimit]);
      
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('SQL timeout')), sqlTimeout)
      );
      
      const result = await Promise.race([queryPromise, timeoutPromise]);
      
      if (!checkConnection()) {
        return false;
      }
      
      const data = JSON.stringify({
        type: 'logs',
        timestamp: new Date().toISOString(),
        logs: result.rows,
        heartbeat: ++heartbeatCount
      });
      
      return safeWrite(`data: ${data}\n\n`);
      
    } catch (error) {
      console.log('[SSE] Erreur récupération logs:', error.message);
      
      if (!checkConnection()) {
        return false;
      }
      
      // Envoyer un heartbeat même en cas d'erreur pour maintenir connexion
      const errorData = JSON.stringify({
        type: 'heartbeat',
        timestamp: new Date().toISOString(),
        error: error.message,
        heartbeat: ++heartbeatCount
      });
      
      return safeWrite(`data: ${errorData}\n\n`);
    }
  };

  // Heartbeat séparé toutes les 15 secondes
  const sendHeartbeat = () => {
    if (!checkConnection()) {
      return false;
    }
    
    const heartbeatData = JSON.stringify({
      type: 'heartbeat',
      timestamp: new Date().toISOString(),
      heartbeat: ++heartbeatCount
    });
    
    return safeWrite(`data: ${heartbeatData}\n\n`);
  };

  // Envoi initial
  sendLogs();
  
  // Intervalle principal : logs toutes les 2 secondes
  const logsInterval = setInterval(() => {
    if (!sendLogs()) {
      clearInterval(logsInterval);
      clearInterval(heartbeatInterval);
    }
  }, 2000);
  
  // Intervalle heartbeat : toutes les 15 secondes
  const heartbeatInterval = setInterval(() => {
    if (!sendHeartbeat()) {
      clearInterval(logsInterval);
      clearInterval(heartbeatInterval);
    }
  }, 15000);
  
  // Nettoyage à la déconnexion
  const cleanup = () => {
    isConnected = false;
    clearInterval(logsInterval);
    clearInterval(heartbeatInterval);
    
    if (!res.destroyed && !res.finished) {
      try {
        res.end();
      } catch (error) {
        // Ignore les erreurs de fermeture
      }
    }
  };
  
  // Event listeners pour déconnexion
  req.on('close', cleanup);
  req.on('aborted', cleanup);
  res.on('close', cleanup);
  res.on('finish', cleanup);
  
  // Log de connexion
  console.log(`[SSE] Client connecté pour session: ${sessionId.substring(0, 8)}...`);
});

// Route pour capturer les logs du navigateur
app.post('/api/debug/browser', async (req, res) => {
  try {
    const { sessionId, level, message, data = {} } = req.body;
    
    if (!sessionId || !level || !message) {
      return res.status(400).json({ 
        error: 'Champs requis: sessionId, level, message' 
      });
    }

    // Vérifier si la table logs existe avant d'insérer
    try {
      await query(`
        INSERT INTO logs(session_id, source, level, message, data, capture_mode)
        VALUES($1, 'browser', $2, $3, $4, 'full')
      `, [sessionId, level, message, JSON.stringify(data)]);
      
      res.json({ ok: true });
    } catch (dbError) {
      // Si la table n'existe pas, retourner un succès silencieux
      if (dbError.code === '42P01') { // relation "logs" does not exist
        console.log('[DEBUG] Table logs non trouvée - Migration requise');
        return res.json({ ok: true, warning: 'Table logs non initialisée' });
      }
      throw dbError;
    }
  } catch (error) {
    // Log mais ne pas faire planter l'application
    console.error('[API Debug Browser] Erreur capture log:', error.message);
    res.json({ ok: true, error: 'Capture désactivée temporairement' });
  }
});

// Route pour récupérer la liste des sessions
app.get('/api/debug/sessions', async (req, res) => {
  try {
    logSystem('Récupération des sessions de logs');
    
    const result = await query(`
      SELECT session_id   AS id,
             MIN(ts)      AS started_at,
             MAX(ts)      AS last_log,
             COUNT(*)     AS total,
             COUNT(CASE WHEN level = 'error' THEN 1 END) AS errors,
             COUNT(CASE WHEN level = 'warn' THEN 1 END) AS warnings
      FROM   logs 
      GROUP BY session_id 
      ORDER BY started_at DESC
      LIMIT 50
    `);

    res.json(result.rows);
  } catch (error) {
    logError('Erreur récupération sessions logs', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// Route pour récupérer les logs d'une session - VERSION DEBUG INTENSIF
app.get('/api/debug/sessions/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { mode = 'grouped', max = 500, debug = 'false' } = req.query;
    
    // ✅ LIMITES ADAPTATIVES POUR MODE DEBUG
    const isDebugMode = debug === 'true' || mode === 'debug';
    const requestedMax = parseInt(max) || 500;
    
    let safeMaxLines;
    if (isDebugMode) {
      // Mode debug : limites astronomiques
      if (requestedMax > 50000) {
        safeMaxLines = 100000; // 100k logs max en mode debug insane
      } else if (requestedMax > 20000) {
        safeMaxLines = 50000;  // 50k logs max en mode debug verbose
      } else {
        safeMaxLines = 20000;  // 20k logs max en mode debug normal
      }
    } else {
      // Mode normal : limite conservatrice
      safeMaxLines = Math.min(requestedMax, 5000);
    }
    
    console.log(`[API] Session ${sessionId.substring(0, 8)}... - Mode: ${mode}, Max: ${safeMaxLines} (debug: ${isDebugMode})`);
    
    let sqlQuery, params = [];
    
    if (mode === 'debug') {
      // ✅ MODE DEBUG SPÉCIAL : TOUT CAPTURER AVEC MÉTADONNÉES COMPLÈTES
      sqlQuery = `
        SELECT id, ts, level, source, message, data,
               CASE 
                 WHEN level = 'error' THEN 100
                 WHEN level = 'warn' THEN 85
                 WHEN message ILIKE '%debug%' OR message ILIKE '%fetch%' OR message ILIKE '%mutation%' THEN 95
                 WHEN message ILIKE '%API%' OR message ILIKE '%TeamCalendar%' THEN 75
                 WHEN message ILIKE '%fail%' OR message ILIKE '%error%' THEN 90
                 WHEN source = 'backend' THEN 70
                 WHEN source = 'frontend' THEN 65
                 ELSE 50
               END as score
        FROM logs
        WHERE session_id = $1
        ORDER BY ts DESC, score DESC
        LIMIT $2
      `;
      params = [sessionId, safeMaxLines];
    } else if (mode === 'ai') {
      sqlQuery = `
        SELECT id, ts, level, source, message,
               CASE 
                 WHEN level = 'error' THEN 90
                 WHEN level = 'warn' THEN 70
                 WHEN message ILIKE '%API%' OR message ILIKE '%TeamCalendar%' OR message ILIKE '%debug%' THEN 80
                 WHEN message ILIKE '%fail%' OR message ILIKE '%error%' THEN 85
                 ELSE 30
               END as score
        FROM logs
        WHERE session_id = $1
        ORDER BY score DESC, ts DESC
        LIMIT $2
      `;
      params = [sessionId, safeMaxLines];
    } else if (mode === 'grouped') {
      sqlQuery = `
        SELECT id, ts, level, source, message, 0 as score
        FROM logs
        WHERE session_id = $1
        ORDER BY (CASE source
          WHEN 'backend' THEN 1
          WHEN 'frontend' THEN 2
          WHEN 'browser' THEN 3
          ELSE 4
        END), ts DESC
        LIMIT $2
      `;
      params = [sessionId, safeMaxLines];
    } else {
      sqlQuery = `
        SELECT id, ts, level, source, message, 0 as score
        FROM logs
        WHERE session_id = $1
        ORDER BY ts DESC
        LIMIT $2
      `;
      params = [sessionId, safeMaxLines];
    }
    
    const result = await query(sqlQuery, params);
    res.json(result.rows);
    
    console.log(`[API] Logs récupérés pour session ${sessionId.substring(0, 8)}... : ${result.rows.length} entrées`);
    
  } catch (error) {
    console.error('[API] Erreur récupération logs:', error.message);
    res.status(500).json({ error: 'Erreur lors de la récupération des logs' });
  }
});

// Variable pour tracker le démarrage du serveur
const serverStartTimestamp = new Date().toISOString();

// Route pour récupérer la session actuelle du serveur
app.get('/api/debug/current-session', async (req, res) => {
  try {
    res.json({ 
      sessionId: currentSession,
      serverStarted: serverStartTimestamp // Timestamp fixe du démarrage
    });
  } catch (error) {
    logError('Erreur récupération session actuelle', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// Route pour nettoyer les vieux logs
app.delete('/api/debug/sessions/cleanup/:days', async (req, res) => {
  try {
    const days = Math.max(1, Number(req.params.days));
    
    logSystem(`Nettoyage des logs antérieurs à ${days} jours`);
    
    // Si days === 0, purger TOUTES les sessions sauf la courante
    if (days === 0) {
      const result = await query(`
        DELETE FROM logs WHERE session_id != $1
      `, [currentSession]);
      
      res.json({ 
        message: `Toutes les anciennes sessions purgées (${result.rowCount} logs supprimés)`,
        success: true,
        deletedCount: result.rowCount
      });
    } else {
      // Utiliser la fonction de nettoyage standard
      const result = await query(`
        SELECT cleanup_old_logs($1)
      `, [days]);
      
      res.json({ 
        message: `Logs antérieurs à ${days} jours supprimés`,
        success: true 
      });
    }
  } catch (error) {
    logError('Erreur nettoyage logs', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// Route pour purger complètement toutes les sessions
app.delete('/api/debug/sessions/purge-all', async (req, res) => {
  try {
    logSystem('Purge complète de toutes les sessions de logs');
    
    const result = await query('DELETE FROM logs');
    
    res.json({ 
      message: `Purge complète effectuée (${result.rowCount} logs supprimés)`,
      success: true,
      deletedCount: result.rowCount
    });
  } catch (error) {
    logError('Erreur purge complète logs', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// === EMPLOYEE ORDER ROUTES ===
app.post('/api/employee-order', async (req, res) => {
    try {
        // ✅ FIX : Recevoir l'objet avec la propriété employeeOrder comme attendu
        const { employeeOrder } = req.body;
        
        if (!Array.isArray(employeeOrder)) {
            return res.status(400).json({ error: 'employeeOrder doit être un tableau' });
        }
        
        console.log('💾 [POST /api/employee-order] Sauvegarde ordre des employés:', employeeOrder);
        
        // Utiliser la table app_settings pour stocker l'ordre comme JSON
        const orderJson = JSON.stringify(employeeOrder);
        
        await query(`
            INSERT INTO app_settings (setting_key, setting_value, description) 
            VALUES ('employee_order', $1, 'Ordre personnalisé des employés')
            ON CONFLICT (setting_key) 
            DO UPDATE SET setting_value = $1, updated_at = CURRENT_TIMESTAMP
        `, [orderJson]);
        
        console.log('✅ [POST /api/employee-order] Ordre sauvegardé avec succès');
        
        res.json({ 
            success: true, 
            message: 'Ordre des employés sauvegardé'
        });
    } catch (error) {
        console.error('❌ [POST /api/employee-order] Erreur:', error);
        res.status(500).json({ error: error.message });
    }
});

app.get('/api/employee-order', async (req, res) => {
    try {
        console.log('📋 [GET /api/employee-order] Récupération ordre des employés');
        
        const result = await query(`
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
        `);
        
        if (result.rows.length === 0) {
            console.log('📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé');
            return res.json({ 
                success: true, 
                employeeOrder: null,
                message: 'Aucun ordre personnalisé défini' 
            });
        }
        
        // ✅ CORRECTION : S'assurer que les données sont bien un array
        let employeeOrder = result.rows[0].setting_value;
        
        // Si c'est une string JSON, la parser
        if (typeof employeeOrder === 'string') {
            try {
                employeeOrder = JSON.parse(employeeOrder);
            } catch (parseError) {
                console.error('❌ [GET /api/employee-order] Erreur parsing JSON:', parseError);
                employeeOrder = [];
            }
        }
        
        // Vérifier que c'est bien un array
        if (!Array.isArray(employeeOrder)) {
            console.warn('⚠️ [GET /api/employee-order] Données non-array détectées, fallback vers array vide');
            employeeOrder = [];
        }
        
        console.log('✅ [GET /api/employee-order] Ordre récupéré (array):', employeeOrder);
        
        // ✅ OPTION A : Format avec data.employeeOrder (comme actuellement)
        // res.json({ success: true, data: { employeeOrder: employeeOrder } });
        
        // ✅ OPTION B : Format simplifié aligné sur POST (recommandé)
        res.json({ 
            success: true, 
            employeeOrder: employeeOrder 
        });
    } catch (error) {
        console.error('❌ [GET /api/employee-order] Erreur:', error);
        res.status(500).json({ error: error.message });
    }
});

// ⏩ Diffuser directement les logs émis par le logger
logEmitter.on('log', (log) => {
  broadcastLog(log);
});

// ROUTE RECENT LOGS (backlog pour /logs)
app.get('/api/logs/recent', async (req,res)=>{
  try{
    const { limit = 500, sessionId } = req.query;
    const l = Math.min(Number(limit) || 500, 5000);
    const params = [];
    let sql = 'SELECT * FROM logs';
    if(sessionId){ sql += ' WHERE session_id = $1'; params.push(sessionId); }
    sql += ' ORDER BY ts DESC LIMIT '+l;
    const result = await query(sql, params);
    res.json(result.rows.reverse()); // envoyer du plus ancien au plus récent
  }catch(err){
    console.error('[GET /api/logs/recent] Erreur',err);
    res.status(500).json({error: err.message});
  }
});

export default app;
