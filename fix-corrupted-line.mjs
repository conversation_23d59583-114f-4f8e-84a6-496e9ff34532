#!/usr/bin/env node

import { readFileSync, writeFileSync } from 'fs';

console.log('🔧 CORRECTION LIGNE CORROMPUE dans teamCalendarApp.ts');

try {
  let content = readFileSync('src/teamCalendarApp.ts', 'utf8');
  
  // Corriger la ligne corrompue
  const beforeFix = content.includes('await // Auto-load supprimé');
  
  content = content.replace(
    'await // Auto-load supprimé // Charger l\'ordre des employés sauvegardé',
    'this.loadEmployeeOrder(); // Charger l\'ordre des employés sauvegardé'
  );
  
  const afterFix = !content.includes('await // Auto-load supprimé');
  
  if (beforeFix && afterFix) {
    writeFileSync('src/teamCalendarApp.ts', content);
    console.log('✅ Ligne corrompue corrigée avec succès !');
    console.log('📋 "await // Auto-load supprimé" → "this.loadEmployeeOrder();"');
  } else {
    console.log('❌ Ligne corrompue non trouvée ou déjà corrigée');
  }
  
} catch (error) {
  console.error('❌ Erreur:', error.message);
}

console.log('🎉 Correction terminée - Rechargez votre navigateur !'); 