import fs from 'fs/promises';
import path from 'path';

console.log('🔧 Script de nettoyage des duplications TeamCalendarApp...');

async function run() {
  const filePath = path.join(process.cwd(), 'src', 'teamCalendarApp.ts');
  let content = await fs.readFile(filePath, 'utf-8');
  let modified = false;

  // 1. Supprimer les déclarations globales invalides en haut
  content = content.replace(/declare global\s*{\s*_renderCount\?: number;[\s\S]*?_lastRenderTime\?: number;[\s\S]*?interface Window {/, match => {
    modified = true;
    return 'declare global {\n  interface Window {';
  });

  // 2. Ajouter les compteurs dans l'interface Window si absents
  if (!content.includes('_renderCount?: number') || !content.includes('_lastRenderTime?: number')) {
    content = content.replace(/employeeDragLogger\?: any;[^\n]*\n/, m => {
      modified = true;
      return m + '    // Compteurs de rendu\n    _renderCount?: number;\n    _lastRenderTime?: number;\n';
    });
  }

  // 3. Supprimer les duplications de bloc savedDragTime (conserver la première)
  const savedDragRegex = /\/\/ ✅ PROTECTION : Restaurer le timestamp de drag depuis localStorage[\s\S]*?_lastDragDropTime = null;\s*}\s*}/g;
  let savedMatches = [...content.matchAll(savedDragRegex)];
  if (savedMatches.length > 1) {
    // garder la première, supprimer suivantes
    for (let i = 1; i < savedMatches.length; i++) {
      content = content.replace(savedMatches[i][0], '');
      modified = true;
    }
  }

  // 4. Dé-doubler le bloc dragProtectionActive imbriqué (garder une seule fois)
  const dragBlockRegex = /const now = Date\.now\(\);[\s\S]*?this\.loadEmployeeOrder\(\);[\s\S]*?ordre des employés non rechargé'[\s\S]*?}/g;
  let dragMatches = [...content.matchAll(dragBlockRegex)];
  if (dragMatches.length > 1) {
    for (let i = 1; i < dragMatches.length; i++) {
      content = content.replace(dragMatches[i][0], '');
      modified = true;
    }
  }

  if (modified) {
    await fs.writeFile(filePath, content, 'utf-8');
    console.log('✅ Corrections appliquées.');
  } else {
    console.log('ℹ️ Aucune modification nécessaire.');
  }
}

run().catch(err => {
  console.error('❌ Erreur dans le script:', err);
  process.exit(1);
}); 