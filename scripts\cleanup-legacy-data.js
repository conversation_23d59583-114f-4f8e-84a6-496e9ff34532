#!/usr/bin/env node

/**
 * 🧹 SCRIPT DE NETTOYAGE DES DONNÉES HÉRITÉES
 * 
 * Ce script nettoie :
 * - Les anciens IDs d'employés ("e1", "e2", etc.)
 * - Les données corrompues dans localStorage
 * - Les schedules avec des références invalides
 * - Les attributions avec des employeeId obsolètes
 */

import { readFile, writeFile } from 'fs/promises';
import { existsSync } from 'fs';

async function cleanupLegacyData() {
    console.log('🧹 Démarrage du nettoyage des données héritées...\n');
    
    let cleanupActions = [];
    
    try {
        // 1. Nettoyer le localStorage (simulation pour navigateur)
        console.log('🔄 Simulation du nettoyage localStorage...');
        
        const legacyKeys = [
            'teamCalendarState',
            'teamCalendarAppSettings',
            'regularAssignments',
            'employeeSchedules'
        ];
        
        cleanupActions.push({
            action: 'localStorage_cleanup',
            keys: legacyKeys,
            description: 'Suppression des anciennes clés localStorage'
        });
        
        // 2. Identifier les patterns d'IDs problématiques
        console.log('🔍 Identification des patterns d\'IDs problématiques...');
        
        const problematicPatterns = [
            /^e[0-9]+$/,                    // e1, e2, e3, etc.
            /^temp_[0-9]+$/,                // temp_123, temp_456
            /^employee_[0-9]+$/,            // employee_1, employee_2
            /^emp[0-9]+$/,                  // emp1, emp2
        ];
        
        cleanupActions.push({
            action: 'pattern_detection',
            patterns: problematicPatterns.map(p => p.toString()),
            description: 'Détection des patterns d\'IDs obsolètes'
        });
        
        // 3. Générer des UUIDs de remplacement
        console.log('🔄 Génération des UUIDs de remplacement...');
        
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
        
        const migrationMap = {};
        for (let i = 1; i <= 10; i++) {
            migrationMap[`e${i}`] = generateUUID();
            migrationMap[`emp${i}`] = generateUUID();
            migrationMap[`temp_${i}`] = generateUUID();
            migrationMap[`employee_${i}`] = generateUUID();
        }
        
        cleanupActions.push({
            action: 'uuid_generation',
            migrationMap: migrationMap,
            description: 'Génération des UUIDs de remplacement'
        });
        
        // 4. Plan de migration des données
        console.log('📋 Création du plan de migration...');
        
        const migrationPlan = {
            employees: {
                action: 'migrate_ids',
                description: 'Migration des IDs d\'employés vers des UUIDs',
                fields: ['id'],
                cascadeUpdates: ['schedule', 'regularAssignments', 'vacations']
            },
            
            schedule: {
                action: 'migrate_keys_and_references',
                description: 'Migration des clés de schedule et des références employés',
                keyPattern: /^(e[0-9]+|temp_[0-9]+|employee_[0-9]+)_/,
                referenceFields: ['employee_id', 'employeeId']
            },
            
            regularAssignments: {
                action: 'migrate_references',
                description: 'Migration des références dans les attributions régulières',
                referenceFields: ['employeeId']
            },
            
            vacations: {
                action: 'migrate_references', 
                description: 'Migration des références dans les périodes de vacances',
                referenceFields: ['employeeId']
            }
        };
        
        cleanupActions.push({
            action: 'migration_plan',
            plan: migrationPlan,
            description: 'Plan de migration des données'
        });
        
        // 5. Script de nettoyage pour le navigateur
        console.log('📜 Génération du script de nettoyage navigateur...');
        
        const browserScript = `
// 🧹 SCRIPT DE NETTOYAGE AUTOMATIQUE - À exécuter dans la console du navigateur
(function() {
    console.log('🧹 Démarrage du nettoyage des données héritées...');
    
    // 1. Nettoyer localStorage
    const keysToRemove = ${JSON.stringify(legacyKeys)};
    let removedKeys = 0;
    
    keysToRemove.forEach(key => {
        if (localStorage.getItem(key)) {
            localStorage.removeItem(key);
            removedKeys++;
            console.log('🗑️ Supprimé:', key);
        }
    });
    
    console.log('✅ ' + removedKeys + ' clés supprimées du localStorage');
    
    // 2. Forcer le rechargement des données depuis l'API
    console.log('🔄 Forçage du rechargement depuis l\'API...');
    
    // 3. Nettoyer les données corrompues
    try {
        const currentData = localStorage.getItem('teamCalendarState');
        if (currentData) {
            const parsed = JSON.parse(currentData);
            let hasLegacyIds = false;
            
            // Vérifier les employés
            if (parsed.employees) {
                parsed.employees.forEach(emp => {
                    if (/^(e[0-9]+|temp_|employee_|emp[0-9]+)/.test(emp.id)) {
                        hasLegacyIds = true;
                        console.warn('⚠️ ID d\\'employé obsolète détecté:', emp.id, emp.name);
                    }
                });
            }
            
            if (hasLegacyIds) {
                console.log('🚫 Données avec IDs obsolètes détectées - Suppression...');
                localStorage.removeItem('teamCalendarState');
                console.log('✅ Données corrompues supprimées');
            }
        }
    } catch (e) {
        console.warn('⚠️ Erreur lors du nettoyage:', e.message);
    }
    
    console.log('✅ Nettoyage terminé. Rechargez la page pour charger des données propres.');
    
    // 4. Suggestion de rechargement
    if (confirm('🔄 Voulez-vous recharger la page maintenant pour appliquer les changements ?')) {
        window.location.reload();
    }
})();
`;
        
        // 6. Sauvegarder le rapport et le script
        const reportData = {
            timestamp: new Date().toISOString(),
            summary: {
                totalActions: cleanupActions.length,
                legacyKeysCount: legacyKeys.length,
                migrationMapSize: Object.keys(migrationMap).length,
                problematicPatternsCount: problematicPatterns.length
            },
            actions: cleanupActions,
            browserScript: browserScript
        };
        
        await writeFile('cleanup-report.json', JSON.stringify(reportData, null, 2));
        await writeFile('browser-cleanup-script.js', browserScript);
        
        // 7. Afficher le résumé
        console.log('\n📊 RÉSUMÉ DU NETTOYAGE');
        console.log('='.repeat(50));
        console.log(`✅ Actions planifiées: ${cleanupActions.length}`);
        console.log(`🗑️  Clés localStorage à supprimer: ${legacyKeys.length}`);
        console.log(`🔄 IDs à migrer: ${Object.keys(migrationMap).length}`);
        console.log(`🔍 Patterns problématiques: ${problematicPatterns.length}`);
        console.log('\n📁 FICHIERS GÉNÉRÉS:');
        console.log('- cleanup-report.json : Rapport détaillé');
        console.log('- browser-cleanup-script.js : Script à exécuter dans le navigateur');
        
        console.log('\n🔧 INSTRUCTIONS:');
        console.log('1. Copiez le contenu de browser-cleanup-script.js');
        console.log('2. Ouvrez la console du navigateur (F12)');
        console.log('3. Collez et exécutez le script');
        console.log('4. Rechargez la page pour des données propres');
        
        console.log('\n✅ Script de nettoyage généré avec succès !');
        
    } catch (error) {
        console.error('❌ Erreur lors du nettoyage:', error);
        process.exit(1);
    }
}

// Exécuter le nettoyage
cleanupLegacyData().catch(console.error); 