/* ===== ANIMATION DE CHARGEMENT MODERNE ===== */

.loading-animation {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: inherit;
  color: #64748b; /* text-slate-500 */
}

.loading-animation .dot {
  width: 6px;
  height: 6px;
  background-color: #3b82f6; /* bg-blue-500 */
  border-radius: 50%;
  animation: loading-bounce 1.4s infinite ease-in-out both;
}

.loading-animation .dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-animation .dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-animation .dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* ===== VARIANTE ALTERNATIVE : PULSE ===== */
.loading-pulse {
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.loading-pulse .dot {
  width: 8px;
  height: 8px;
  background-color: #3b82f6;
  border-radius: 50%;
  animation: loading-pulse-animation 1.5s infinite;
}

.loading-pulse .dot:nth-child(1) {
  animation-delay: 0s;
}

.loading-pulse .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-pulse .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes loading-pulse-animation {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  30% {
    transform: scale(1.4);
    opacity: 1;
  }
}

/* ===== VARIANTE MODERNE : WAVE ===== */
.loading-wave {
  display: inline-flex;
  align-items: center;
  gap: 3px;
}

.loading-wave .dot {
  width: 4px;
  height: 16px;
  background: linear-gradient(45deg, #3b82f6, #6366f1);
  border-radius: 2px;
  animation: loading-wave-animation 1.2s infinite ease-in-out;
}

.loading-wave .dot:nth-child(1) {
  animation-delay: 0s;
}

.loading-wave .dot:nth-child(2) {
  animation-delay: 0.1s;
}

.loading-wave .dot:nth-child(3) {
  animation-delay: 0.2s;
}

@keyframes loading-wave-animation {
  0%, 40%, 100% {
    transform: scaleY(0.4);
    opacity: 0.6;
  }
  20% {
    transform: scaleY(1);
    opacity: 1;
  }
}

/* ===== RESPONSIVE ET ACCESSIBILITÉ ===== */
@media (prefers-reduced-motion: reduce) {
  .loading-animation .dot,
  .loading-pulse .dot,
  .loading-wave .dot {
    animation: none;
    opacity: 0.7;
  }
  
  .loading-animation::after {
    content: " ...";
    animation: loading-text-blink 2s infinite;
  }
}

@keyframes loading-text-blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

/* ===== INTÉGRATION AVEC LE TITRE DE SEMAINE ===== */
#current-week-display .loading-animation {
  justify-content: center;
  min-width: 200px;
  font-weight: 500;
}

#current-week-display .loading-animation .dot {
  background-color: #1e40af; /* Plus foncé pour le titre */
}

/* ===== ÉTATS DE TRANSITION ===== */
.week-title-transition {
  transition: all 0.3s ease-in-out;
}

.week-title-fade-in {
  animation: fade-in 0.5s ease-out;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== LARGEUR FIXE POUR LE TITRE ===== */
#current-week-display {
  min-width: 320px;
  max-width: 320px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.2s ease-in-out;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  #current-week-display {
    min-width: 280px;
    max-width: 280px;
    font-size: 1rem;
  }
  
  .loading-animation .dot {
    width: 5px;
    height: 5px;
  }
}

@media (max-width: 480px) {
  #current-week-display {
    min-width: 240px;
    max-width: 240px;
    font-size: 0.9rem;
  }
  
  .loading-animation .dot {
    width: 4px;
    height: 4px;
  }
}
