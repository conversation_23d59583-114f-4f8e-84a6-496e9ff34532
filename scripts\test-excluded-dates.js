import { query } from '../server/config/database.js';

async function testExcludedDates() {
    try {
        console.log('🔍 Test des dates exclues dans la base de données...\n');
        
        // R<PERSON><PERSON><PERSON>rer toutes les attributions avec des dates exclues
        const result = await query(`
            SELECT 
                id,
                employee_id,
                post_id,
                excluded_dates,
                pg_typeof(excluded_dates) as type_colonne,
                array_length(excluded_dates, 1) as nombre_exclusions
            FROM regular_assignments
            WHERE excluded_dates IS NOT NULL AND array_length(excluded_dates, 1) > 0
        `);
        
        console.log(`📊 ${result.rows.length} attributions avec des dates exclues trouvées\n`);
        
        result.rows.forEach((row, index) => {
            console.log(`Attribution ${index + 1}:`);
            console.log(`  ID: ${row.id}`);
            console.log(`  Type de colonne: ${row.type_colonne}`);
            console.log(`  Nombre d'exclusions: ${row.nombre_exclusions}`);
            console.log(`  Dates exclues (raw):`, row.excluded_dates);
            
            // Afficher chaque date individuellement
            if (row.excluded_dates && Array.isArray(row.excluded_dates)) {
                row.excluded_dates.forEach((date, i) => {
                    console.log(`    Date ${i + 1}: "${date}" (type: ${typeof date})`);
                });
            }
            console.log('');
        });
        
        // Test spécifique pour l'attribution mentionnée dans les logs
        const specificResult = await query(`
            SELECT id, excluded_dates
            FROM regular_assignments
            WHERE id = '7fc697f7-bbba-48c3-9a98-39d1be84db91'
        `);
        
        if (specificResult.rows.length > 0) {
            console.log('🎯 Attribution spécifique 7fc697f7-bbba-48c3-9a98-39d1be84db91:');
            console.log('  Dates exclues:', JSON.stringify(specificResult.rows[0].excluded_dates));
            console.log('');
        }
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Erreur:', error);
        process.exit(1);
    }
}

testExcludedDates(); 