
// === FIX RAPIDE POUR FORCER L'AFFICHAGE DES POSTES ===
console.log('🔧 [FIX] Tentative de correction...');

if (window.teamCalendarApp) {
    const app = window.teamCalendarApp;
    
    // 1. S'assurer que les postes existent
    if (!app.config.standardPosts || app.config.standardPosts.length === 0) {
        console.log('⚠️ [FIX] Aucun poste trouvé, création de postes d'exemple...');
        app.config.standardPosts = [
            {
                id: 'fix-post-1',
                label: 'Poste Test Matin',
                hours: '08:00-16:00',
                category: 'weekday',
                workingDays: [1, 2, 3, 4, 5],
                type: 'sky'
            },
            {
                id: 'fix-post-2',
                label: 'Poste Test Soir',
                hours: '16:00-00:00',
                category: 'weekday',
                workingDays: [1, 2, 3, 4, 5],
                type: 'amber'
            }
        ];
        console.log(`✅ [FIX] ${app.config.standardPosts.length} postes d'exemple créés`);
    }
    
    // 2. Forcer le rendu
    if (typeof app.renderUnifiedCalendar === 'function') {
        app.renderUnifiedCalendar();
        console.log('✅ [FIX] Rendu forcé exécuté');
    }
    
    // 3. Vérifier le résultat
    const container = document.getElementById('available-posts-container');
    if (container && container.children.length > 0) {
        console.log(`✅ [FIX] SUCCESS! ${container.children.length} postes maintenant visibles`);
    } else {
        console.log('❌ [FIX] Les postes ne sont toujours pas visibles');
    }
} else {
    console.error('❌ [FIX] teamCalendarApp non disponible');
}
