#!/usr/bin/env node

import fs from 'fs';

console.log('🔧 [FIX-SIMPLE] Correction simple des erreurs critiques...');

const filePath = 'src/teamCalendarApp.ts';

try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // 1. Corriger l'erreur d'arguments manquants pour saveWeekShifts
    // Rechercher les appels incorrects
    const incorrectSaveWeekShifts = /await apiService\.saveWeekShifts\(\s*([^,)]+)\s*\)/g;
    content = content.replace(incorrectSaveWeekShifts, (match, arg1) => {
        console.log(`🔧 Correction saveWeekShifts: ${match}`);
        hasChanges = true;
        return `await apiService.saveWeekShifts(${arg1}, [])`;
    });

    // 2. Supprimer les références à _employeeDragLogger qui n'existe plus
    content = content.replace(/this\._employeeDragLogger[^;]*;/g, '// Logger supprimé pour compatibilité TypeScript');
    
    // 3. Corriger les erreurs de labels malformés
    content = content.replace(/^\s*[a-zA-Z_][a-zA-Z0-9_]*:\s*$/gm, '// Label corrigé');

    // 4. Supprimer les fonctions dupliquées (garder seulement la première occurrence)
    const functionNames = ['attachAllEventListeners', 'setupEmployeeDragDrop', 'reorderEmployees'];
    
    functionNames.forEach(funcName => {
        const regex = new RegExp(`(${funcName}\\s*:\\s*function[^}]*}(?:[^}]*})*?)`, 'g');
        const matches = content.match(regex);
        
        if (matches && matches.length > 1) {
            console.log(`🔧 Fonction dupliquée détectée: ${funcName} (${matches.length} occurrences)`);
            
            // Garder seulement la première occurrence
            let firstFound = false;
            content = content.replace(regex, (match) => {
                if (!firstFound) {
                    firstFound = true;
                    return match;
                } else {
                    hasChanges = true;
                    return `// ${funcName} dupliqué supprimé`;
                }
            });
        }
    });

    // 5. Corriger les erreurs de syntaxe communes
    content = content.replace(/,\s*,/g, ','); // Virgules doubles
    content = content.replace(/}\s*,\s*}/g, '}'); // Accolades doubles
    content = content.replace(/;\s*;/g, ';'); // Points-virgules doubles

    // 6. S'assurer que l'export default est à la fin
    if (!content.trim().endsWith('export default TeamCalendarApp;')) {
        if (!content.includes('export default TeamCalendarApp;')) {
            content += '\n\nexport default TeamCalendarApp;';
            hasChanges = true;
        }
    }

    if (hasChanges) {
        // Créer une sauvegarde
        fs.writeFileSync(filePath + '.backup', fs.readFileSync(filePath));
        
        // Sauvegarder le fichier corrigé
        fs.writeFileSync(filePath, content, 'utf8');
        console.log('✅ [FIX-SIMPLE] Fichier corrigé et sauvegardé');
        console.log('📄 [FIX-SIMPLE] Sauvegarde créée: ' + filePath + '.backup');
    } else {
        console.log('✅ [FIX-SIMPLE] Aucune correction nécessaire');
    }

    console.log('🎉 [FIX-SIMPLE] Correction terminée !');

} catch (error) {
    console.error('❌ [FIX-SIMPLE] Erreur:', error);
    process.exit(1);
} 