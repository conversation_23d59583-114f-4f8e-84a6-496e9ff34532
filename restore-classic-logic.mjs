#!/usr/bin/env node
import { promises as fs } from 'fs';
import path from 'path';

console.log('🚀 [PATCH] Restauration de la logique classique dans teamCalendarApp.ts...');

const TEAM_CALENDAR_APP_PATH = path.resolve(process.cwd(), 'src', 'teamCalendarApp.ts');

async function applyPatch() {
  try {
    let content = await fs.readFile(TEAM_CALENDAR_APP_PATH, 'utf8');
    console.log('✅ Fichier original lu.');

    // Remplacer la fonction loadState
    const loadStateRegex = /loadState: async function\(\) {[^}]*},/s;
    const newLoadState = `loadState: async function() {
        console.log('📂 [loadState] Chargement initial des données...');
        try {
            const { apiService } = await import('./api.js');
            const [employeesRes, postsRes, shiftsRes, assignmentsRes] = await Promise.all([
                apiService.getEmployees(),
                apiService.getStandardPosts(),
                apiService.getShifts(),
                apiService.getRegularAssignments()
            ]);

            if (employeesRes.success) this.data.employees = employeesRes.data;
            if (postsRes.success) this.config.standardPosts = postsRes.data;
            if (assignmentsRes.success) this.data.regularAssignments = assignmentsRes.data;

            if (shiftsRes.success) {
                this.data.schedule = {}; // Réinitialiser l'horaire
                shiftsRes.data.forEach(shift => {
                    const { employee_id, date_key } = shift;
                    if (!this.data.schedule[employee_id]) {
                        this.data.schedule[employee_id] = {};
                    }
                    if (!this.data.schedule[employee_id][date_key]) {
                        this.data.schedule[employee_id][date_key] = [];
                    }
                    this.data.schedule[employee_id][date_key].push({
                        ...(shift.shift_data || {}),
                        id: shift.id,
                        postId: shift.post_id,
                        text: shift.text,
                        type: shift.type,
                        isRegular: shift.is_regular,
                        assignmentId: shift.assignment_id,
                        dateKey: date_key,
                    });
                });
            }

            console.log('✅ [loadState] Toutes les données initiales ont été chargées avec succès.');
            return true;
        } catch (error) {
            console.error('❌ [loadState] Erreur lors du chargement initial des données:', error);
            window.toastSystem?.error('Erreur de chargement des données.');
            return false;
        }
    },`;

    if (loadStateRegex.test(content)) {
        content = content.replace(loadStateRegex, newLoadState);
        console.log('🔧 Fonction "loadState" remplacée.');
    } else {
        console.warn('⚠️ La fonction "loadState" n\'a pas été trouvée pour remplacement.');
    }

    // Remplacer saveCurrentWeek par saveState
    const saveCurrentWeekRegex = /saveCurrentWeek: async function\([^)]*\) {[^}]*},/s;
    const newSaveState = `saveState: async function() {
        console.log('💾 [saveState] Début de la sauvegarde globale...');
        try {
            const { apiService } = await import('./api.js');
            const shiftsToSave = [];
            for (const employeeId in this.data.schedule) {
                for (const dateKey in this.data.schedule[employeeId]) {
                    this.data.schedule[employeeId][dateKey].forEach(shift => {
                        shiftsToSave.push({
                            ...shift,
                            employee_id: employeeId,
                            date_key: dateKey,
                            post_id: shift.postId,
                            is_regular: shift.isRegular || false,
                            assignment_id: shift.assignmentId || null,
                            shift_data: { ...shift }
                        });
                    });
                }
            }

            const result = await apiService.saveAllShifts({ shifts: shiftsToSave });

            if (result.success) {
                console.log('✅ [saveState] Sauvegarde globale réussie !');
                window.toastSystem?.success('Toutes les données ont été sauvegardées.');
            } else {
                console.error('❌ [saveState] Échec de la sauvegarde globale:', result.error);
                window.toastSystem?.error('Échec de la sauvegarde des données.');
            }
        } catch (error) {
            console.error('❌ [saveState] Erreur critique lors de la sauvegarde:', error);
            window.toastSystem?.error('Erreur critique de sauvegarde.');
        }
    },`;

    if (saveCurrentWeekRegex.test(content)) {
        content = content.replace(saveCurrentWeekRegex, newSaveState);
        console.log('🔧 Fonction "saveCurrentWeek" remplacée par "saveState".');
    } else {
        console.warn('⚠️ La fonction "saveCurrentWeek" n\'a pas été trouvée pour remplacement.');
    }
    
    // Supprimer les fonctions de chargement par semaine
    const weekFunctionsToRemove = [
        /loadWeekData: \([^)]*\) => {[^}]*},/s,
        /preloadAdjacentWeeks: \([^)]*\) => {[^}]*},/s,
        /loadWeekDataSilent: \([^)]*\) => {[^}]*},/s
    ];

    weekFunctionsToRemove.forEach(regex => {
        if (regex.test(content)) {
            content = content.replace(regex, '');
            console.log(`🗑️ Fonction de chargement par semaine supprimée.`);
        }
    });

    await fs.writeFile(TEAM_CALENDAR_APP_PATH, content, 'utf8');
    console.log('✅ Patch appliqué avec succès !');

  } catch (error) {
    console.error('❌ [ERREUR] Impossible d\'appliquer le patch:', error);
    process.exit(1);
  }
}

applyPatch(); 