module.exports = {
  ci: {
    // Configuration pour les builds locaux
    collect: {
      // URL à tester (sera remplacée par l'URL de preview en CI)
      url: ['http://localhost:5173'],
      
      // Nombre de runs pour avoir une moyenne
      numberOfRuns: 3,
      
      // Configuration Chrome
      settings: {
        chromeFlags: '--no-sandbox --disable-dev-shm-usage',
        preset: 'desktop'
      }
    },
    
    // Configuration des assertions (seuils de performance)
    assert: {
      assertions: {
        // ✅ PERFORMANCE : Score minimum 90%
        'categories:performance': ['error', { minScore: 0.9 }],
        
        // ✅ ACCESSIBILITÉ : Score minimum 95%
        'categories:accessibility': ['error', { minScore: 0.95 }],
        
        // ✅ BONNES PRATIQUES : Score minimum 90%
        'categories:best-practices': ['error', { minScore: 0.9 }],
        
        // ✅ SEO : Score minimum 85%
        'categories:seo': ['error', { minScore: 0.85 }],
        
        // ✅ MÉTRIQUES SPÉCIFIQUES
        
        // First Contentful Paint < 2s
        'first-contentful-paint': ['error', { maxNumericValue: 2000 }],
        
        // Largest Contentful Paint < 2.5s
        'largest-contentful-paint': ['error', { maxNumericValue: 2500 }],
        
        // Cumulative Layout Shift < 0.1
        'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],
        
        // Total Blocking Time < 300ms
        'total-blocking-time': ['error', { maxNumericValue: 300 }],
        
        // Speed Index < 3s
        'speed-index': ['error', { maxNumericValue: 3000 }],
        
        // ✅ RESSOURCES
        
        // Taille des ressources
        'resource-summary:document:size': ['error', { maxNumericValue: 50000 }], // 50KB
        'resource-summary:script:size': ['error', { maxNumericValue: 500000 }], // 500KB
        'resource-summary:stylesheet:size': ['error', { maxNumericValue: 100000 }], // 100KB
        
        // Nombre de requêtes
        'resource-summary:total:count': ['error', { maxNumericValue: 50 }],
        
        // ✅ IMAGES
        'uses-optimized-images': 'error',
        'uses-webp-images': 'warn',
        'uses-responsive-images': 'error',
        
        // ✅ JAVASCRIPT
        'unused-javascript': ['warn', { maxNumericValue: 100000 }], // 100KB max unused
        'unminified-javascript': 'error',
        
        // ✅ CSS
        'unused-css-rules': ['warn', { maxNumericValue: 50000 }], // 50KB max unused
        'unminified-css': 'error',
        
        // ✅ FONTS
        'font-display': 'error',
        'preload-fonts': 'warn',
        
        // ✅ CACHE
        'uses-long-cache-ttl': 'warn',
        
        // ✅ COMPRESSION
        'uses-text-compression': 'error'
      }
    },
    
    // Configuration pour l'upload des résultats
    upload: {
      target: 'temporary-public-storage'
    },
    
    // Configuration du serveur pour les tests locaux
    server: {
      command: 'npm run preview',
      port: 4173,
      timeout: 30000
    }
  }
};
