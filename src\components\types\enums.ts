/**
 * @fileoverview Énumérations et constantes
 * @description Définitions des énumérations pour l'application
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

/**
 * @description Statuts possibles pour les employés
 */
export enum EmployeeStatus {
  ACTIVE = 'Actif',
  INACTIVE = 'Inactif',
  ON_LEAVE = 'En congé',
  TERMINATED = 'Parti'
}

/**
 * @description Types de postes
 */
export enum PostType {
  MORNING = 'morning',
  AFTERNOON = 'afternoon',
  EVENING = 'evening',
  NIGHT = 'night',
  FULL_DAY = 'full-day',
  CUSTOM = 'custom'
}

/**
 * @description Catégories de postes
 */
export enum PostCategory {
  REGULAR = 'regular',
  WEEKEND = 'weekend',
  HOLIDAY = 'holiday',
  EMERGENCY = 'emergency',
  TRAINING = 'training'
}

/**
 * @description Types de congés
 */
export enum VacationType {
  PAID_LEAVE = 'CP',
  RTT = 'RTT',
  SICK_LEAVE = 'Maladie',
  MATERNITY = 'Maternité',
  PATERNITY = 'Paternité',
  UNPAID_LEAVE = 'Sans solde',
  TRAINING = 'Formation',
  OTHER = 'Autre'
}

/**
 * @description Jours de la semaine
 */
export enum WeekDay {
  SUNDAY = 0,
  MONDAY = 1,
  TUESDAY = 2,
  WEDNESDAY = 3,
  THURSDAY = 4,
  FRIDAY = 5,
  SATURDAY = 6
}

/**
 * @description Modes d'affichage du calendrier
 */
export enum ViewMode {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month'
}

/**
 * @description Types de modales
 */
export enum ModalType {
  SUCCESS = 'success',
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info',
  CONFIRM = 'confirm'
}

/**
 * @description Niveaux de log
 */
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR'
}

/**
 * @description Types de champs pour les modèles d'employés
 */
export enum FieldType {
  TEXT = 'text',
  SELECT = 'select',
  EMAIL = 'email',
  TEL = 'tel',
  TEXTAREA = 'textarea',
  DATE = 'date',
  NUMBER = 'number'
}

/**
 * @description Actions possibles sur les attributions régulières
 */
export enum AssignmentAction {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  MOVE = 'move',
  COPY = 'copy'
}

/**
 * @description Types de drag & drop
 */
export enum DragType {
  EMPLOYEE = 'employee',
  POST = 'post',
  SHIFT = 'shift',
  REGULAR_ASSIGNMENT = 'regular-assignment'
}

/**
 * @description Constantes pour les couleurs des postes
 */
export const POST_COLORS = {
  BLUE: 'blue',
  GREEN: 'green',
  YELLOW: 'yellow',
  RED: 'red',
  PURPLE: 'purple',
  PINK: 'pink',
  INDIGO: 'indigo',
  GRAY: 'gray'
} as const;

/**
 * @description Constantes pour les tailles d'avatar
 */
export const AVATAR_SIZES = {
  SMALL: 32,
  MEDIUM: 48,
  LARGE: 64,
  EXTRA_LARGE: 96
} as const;

/**
 * @description Constantes pour les durées d'animation
 */
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500
} as const;

/**
 * @description Constantes pour les limites de l'application
 */
export const APP_LIMITS = {
  MAX_EMPLOYEES: 100,
  MAX_POSTS: 50,
  MAX_SHIFTS_PER_DAY: 10,
  MAX_VACATION_DAYS: 365,
  MAX_FILE_SIZE: 2 * 1024 * 1024, // 2MB
  MIN_EMPLOYEE_NAME_LENGTH: 2,
  MAX_EMPLOYEE_NAME_LENGTH: 50
} as const;

/**
 * @description Constantes pour les formats de date
 */
export const DATE_FORMATS = {
  DATE_KEY: 'YYYY-MM-DD',
  DISPLAY_DATE: 'DD/MM/YYYY',
  DISPLAY_DATETIME: 'DD/MM/YYYY HH:mm',
  TIME: 'HH:mm',
  WEEK_KEY: 'YYYY-[W]WW'
} as const;

/**
 * @description Constantes pour les messages d'erreur
 */
export const ERROR_MESSAGES = {
  EMPLOYEE_NOT_FOUND: 'Employé non trouvé',
  POST_NOT_FOUND: 'Poste non trouvé',
  INVALID_DATE: 'Date invalide',
  INVALID_EMAIL: 'Adresse email invalide',
  INVALID_PHONE: 'Numéro de téléphone invalide',
  FILE_TOO_LARGE: 'Fichier trop volumineux',
  INVALID_FILE_TYPE: 'Type de fichier non supporté',
  NETWORK_ERROR: 'Erreur de connexion réseau',
  PERMISSION_DENIED: 'Permission refusée',
  VALIDATION_FAILED: 'Validation échouée'
} as const;

/**
 * @description Constantes pour les messages de succès
 */
export const SUCCESS_MESSAGES = {
  EMPLOYEE_CREATED: 'Employé créé avec succès',
  EMPLOYEE_UPDATED: 'Employé mis à jour avec succès',
  EMPLOYEE_DELETED: 'Employé supprimé avec succès',
  POST_CREATED: 'Poste créé avec succès',
  POST_UPDATED: 'Poste mis à jour avec succès',
  POST_DELETED: 'Poste supprimé avec succès',
  ASSIGNMENT_CREATED: 'Attribution créée avec succès',
  ASSIGNMENT_UPDATED: 'Attribution mise à jour avec succès',
  ASSIGNMENT_DELETED: 'Attribution supprimée avec succès',
  DATA_SAVED: 'Données sauvegardées avec succès',
  DATA_IMPORTED: 'Données importées avec succès',
  DATA_EXPORTED: 'Données exportées avec succès'
} as const;

/**
 * @description Constantes pour les fuseaux horaires courants
 */
export const COMMON_TIMEZONES = [
  { value: 'Europe/Paris', label: 'Paris (UTC+1/+2)' },
  { value: 'Europe/London', label: 'Londres (UTC+0/+1)' },
  { value: 'America/New_York', label: 'New York (UTC-5/-4)' },
  { value: 'America/Los_Angeles', label: 'Los Angeles (UTC-8/-7)' },
  { value: 'Asia/Tokyo', label: 'Tokyo (UTC+9)' },
  { value: 'UTC', label: 'UTC (Temps universel)' }
] as const;

/**
 * @description Constantes pour les jours de début de semaine
 */
export const WEEK_START_OPTIONS = [
  { value: 0, label: 'Dimanche' },
  { value: 1, label: 'Lundi' }
] as const;

/**
 * @description Type pour les couleurs de postes
 */
export type PostColor = typeof POST_COLORS[keyof typeof POST_COLORS];

/**
 * @description Type pour les tailles d'avatar
 */
export type AvatarSize = typeof AVATAR_SIZES[keyof typeof AVATAR_SIZES];

/**
 * @description Type pour les durées d'animation
 */
export type AnimationDuration = typeof ANIMATION_DURATION[keyof typeof ANIMATION_DURATION];

/**
 * @description Utilitaire pour obtenir toutes les valeurs d'un enum
 * @param enumObject - L'objet enum
 * @returns Tableau des valeurs
 */
export function getEnumValues<T extends Record<string, string | number>>(enumObject: T): T[keyof T][] {
  return Object.values(enumObject);
}

/**
 * @description Utilitaire pour obtenir toutes les clés d'un enum
 * @param enumObject - L'objet enum
 * @returns Tableau des clés
 */
export function getEnumKeys<T extends Record<string, string | number>>(enumObject: T): (keyof T)[] {
  return Object.keys(enumObject) as (keyof T)[];
}

/**
 * @description Utilitaire pour vérifier si une valeur est dans un enum
 * @param enumObject - L'objet enum
 * @param value - La valeur à vérifier
 * @returns true si la valeur est dans l'enum
 */
export function isEnumValue<T extends Record<string, string | number>>(
  enumObject: T,
  value: any
): value is T[keyof T] {
  return Object.values(enumObject).includes(value);
}
