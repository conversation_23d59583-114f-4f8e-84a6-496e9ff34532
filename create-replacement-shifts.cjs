﻿const fs = require('fs');
const crypto = require('crypto');

console.log(' [REPLACEMENT-CREATOR] Générateur de remplacements ponctuels pour développement');

// Configuration du générateur
const CONFIG = {
    replacementCount: 5,
    dateRangeStart: 0,
    dateRangeEnd: 14,
    employees: [
        { id: 'emp_001', name: '<PERSON>', avatar: 'https://i.pravatar.cc/150?img=1' },
        { id: 'emp_002', name: '<PERSON>', avatar: 'https://i.pravatar.cc/150?img=2' },
        { id: 'emp_003', name: '<PERSON>', avatar: 'https://i.pravatar.cc/150?img=3' },
        { id: 'emp_004', name: '<PERSON>', avatar: 'https://i.pravatar.cc/150?img=4' },
        { id: 'emp_005', name: '<PERSON>', avatar: 'https://i.pravatar.cc/150?img=5' }
    ],
    posts: [
        { id: 'post_matin', label: 'Poste Matin', hours: '08:00-16:00', color: '#3b82f6' },
        { id: 'post_aprem', label: 'Poste Après-midi', hours: '14:00-22:00', color: '#10b981' },
        { id: 'post_nuit', label: 'Poste Nuit', hours: '22:00-06:00', color: '#8b5cf6' },
        { id: 'post_weekend', label: 'Poste Weekend', hours: '09:00-17:00', color: '#f59e0b' }
    ],
    replacementReasons: [
        'Congé maladie', 'Formation', 'Congé personnel', 'Urgence familiale',
        'Congé sans solde', 'Mission temporaire', 'Remplacement planifié', 'Absence exceptionnelle'
    ]
};

function generateUUID() {
    return crypto.randomUUID();
}

function generateRandomDate() {
    const today = new Date();
    const randomDays = Math.floor(Math.random() * (CONFIG.dateRangeEnd - CONFIG.dateRangeStart + 1)) + CONFIG.dateRangeStart;
    const targetDate = new Date(today);
    targetDate.setDate(today.getDate() + randomDays);
    return targetDate.toISOString().split('T')[0];
}

function randomChoice(array) {
    return array[Math.floor(Math.random() * array.length)];
}

function generateRegularAssignment(employeeId, postId) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 90);
    
    const selectedDays = [];
    const dayCount = Math.floor(Math.random() * 3) + 2;
    while (selectedDays.length < dayCount) {
        const day = Math.floor(Math.random() * 5) + 1;
        if (!selectedDays.includes(day)) {
            selectedDays.push(day);
        }
    }
    
    return {
        id: generateUUID(),
        employeeId: employeeId,
        postId: postId,
        selectedDays: selectedDays,
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        isActive: true,
        createdAt: new Date().toISOString(),
        createdBy: 'system'
    };
}

function generateReplacementShift(originalAssignment, replacementEmployeeId) {
    const post = CONFIG.posts.find(p => p.id === originalAssignment.postId);
    const originalEmployee = CONFIG.employees.find(e => e.id === originalAssignment.employeeId);
    const replacementEmployee = CONFIG.employees.find(e => e.id === replacementEmployeeId);
    const reason = randomChoice(CONFIG.replacementReasons);
    
    const createdAt = new Date();
    const dateKey = generateRandomDate();
    const targetDate = new Date(dateKey + 'T12:00:00');
    const dayOfWeek = targetDate.getDay();
    
    if (!originalAssignment.selectedDays.includes(dayOfWeek)) {
        const validDay = randomChoice(originalAssignment.selectedDays);
        const daysToAdd = (validDay - dayOfWeek + 7) % 7;
        targetDate.setDate(targetDate.getDate() + daysToAdd);
    }
    
    return {
        id: generateUUID(),
        postId: originalAssignment.postId,
        postLabel: post.label,
        hours: post.hours,
        dateKey: targetDate.toISOString().split('T')[0],
        employeeId: replacementEmployeeId,
        isPunctual: true,
        isReplacement: true,
        originalAssignmentId: originalAssignment.id,
        originalEmployeeId: originalAssignment.employeeId,
        createdAt: createdAt.toISOString(),
        createdBy: 'dev-script',
        reason: reason,
        visualStyle: 'replacement',
        colorOverride: '#f59e0b',
        metadata: {
            originalEmployeeName: originalEmployee.name,
            replacementEmployeeName: replacementEmployee.name,
            postLabel: post.label,
            hours: post.hours,
            reason: reason,
            createdAt: createdAt.toISOString(),
            createdBy: 'Système de développement'
        }
    };
}

console.log(' [GENERATOR] Génération du dataset de remplacements...');

const dataset = {
    timestamp: new Date().toISOString(),
    generatedBy: 'create-replacement-shifts.js',
    config: CONFIG,
    employees: CONFIG.employees,
    posts: CONFIG.posts,
    regularAssignments: [],
    replacementShifts: [],
    summary: {
        employeeCount: CONFIG.employees.length,
        postCount: CONFIG.posts.length,
        replacementCount: 0,
        assignmentCount: 0
    }
};

// Créer des attributions régulières pour chaque employé
CONFIG.employees.forEach(employee => {
    const assignmentCount = Math.floor(Math.random() * 2) + 1;
    for (let i = 0; i < assignmentCount; i++) {
        const post = randomChoice(CONFIG.posts);
        const assignment = generateRegularAssignment(employee.id, post.id);
        dataset.regularAssignments.push(assignment);
    }
});

dataset.summary.assignmentCount = dataset.regularAssignments.length;
console.log(` [GENERATOR] ${dataset.regularAssignments.length} attributions régulières créées`);

// Créer les remplacements ponctuels
for (let i = 0; i < CONFIG.replacementCount; i++) {
    const originalAssignment = randomChoice(dataset.regularAssignments);
    const availableEmployees = CONFIG.employees.filter(e => e.id !== originalAssignment.employeeId);
    const replacementEmployee = randomChoice(availableEmployees);
    
    const replacementShift = generateReplacementShift(originalAssignment, replacementEmployee.id);
    dataset.replacementShifts.push(replacementShift);
    
    console.log(` [GENERATOR] Remplacement ${i + 1}/${CONFIG.replacementCount}: ${replacementEmployee.name}  ${originalAssignment.postId} (${replacementShift.dateKey})`);
}

dataset.summary.replacementCount = dataset.replacementShifts.length;

const filename = `replacement-dataset-${Date.now()}.json`;
fs.writeFileSync(filename, JSON.stringify(dataset, null, 2), 'utf8');
console.log(` [SAVE] Dataset sauvegardé: ${filename}`);

console.log('\n [RAPPORT] Synthèse du dataset généré:');
console.log('='.repeat(50));
console.log(` Timestamp: ${dataset.timestamp}`);
console.log(` Employés: ${dataset.summary.employeeCount}`);
console.log(` Postes: ${dataset.summary.postCount}`);
console.log(` Attributions régulières: ${dataset.summary.assignmentCount}`);
console.log(` Remplacements ponctuels: ${dataset.summary.replacementCount}`);

console.log('\n Employés:');
dataset.employees.forEach((emp, index) => {
    console.log(`  ${index + 1}. ${emp.name} (${emp.id})`);
});

console.log('\n Postes:');
dataset.posts.forEach((post, index) => {
    console.log(`  ${index + 1}. ${post.label} - ${post.hours}`);
});

console.log('\n Remplacements générés:');
dataset.replacementShifts.forEach((replacement, index) => {
    const originalEmp = dataset.employees.find(e => e.id === replacement.originalEmployeeId);
    const replacementEmp = dataset.employees.find(e => e.id === replacement.employeeId);
    const post = dataset.posts.find(p => p.id === replacement.postId);
    
    console.log(`  ${index + 1}. ${replacement.dateKey}: ${originalEmp.name}  ${replacementEmp.name} (${post.label})`);
    console.log(`     Motif: ${replacement.reason}`);
});

console.log('\n [UTILISATION] Pour tester dans votre application:');
console.log('1. Copiez les données du fichier JSON généré');
console.log('2. Importez-les dans votre base de données de test');
console.log('3. Les remplacements apparaîtront avec des tooltips');
console.log('4. Testez le drag & drop vers les employés d origine');
console.log('='.repeat(50));

console.log('\n [SUCCESS] Génération terminée avec succès!');
console.log(` Fichier créé: ${filename}`);
