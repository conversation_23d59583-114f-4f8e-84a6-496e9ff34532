#!/usr/bin/env node

/**
 * Script pour supprimer le doublon de la méthode destroy
 */

import fs from 'fs';

console.log('🧹 [CLEANUP-DESTROY] Suppression du doublon destroy...');

const filePath = './src/teamCalendarApp.ts';
let lines = fs.readFileSync(filePath, 'utf8').split('\n');

console.log('📝 [CLEANUP-DESTROY] Recherche des doublons...');

let destroyCount = 0;
let correctedLines = [];
let inDestroyMethod = false;
let destroyMethodLines = [];

for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Détecter le début de destroy
    if (line.includes('destroy: function()')) {
        destroyCount++;
        console.log(`🔍 [CLEANUP-DESTROY] Ligne ${i + 1}: destroy #${destroyCount} détecté`);
        
        if (destroyCount === 1) {
            // Garder le premier destroy
            inDestroyMethod = true;
            destroyMethodLines = [line];
        } else {
            // Ignorer les doublons
            inDestroyMethod = true;
            continue;
        }
    } else if (inDestroyMethod) {
        if (destroyCount === 1) {
            // Continuer à collecter le premier destroy
            destroyMethodLines.push(line);
        }
        
        // Détecter la fin de la méthode destroy
        if (line.includes('console.log(\'✅ [destroy] Nettoyage terminé\');')) {
            inDestroyMethod = false;
            if (destroyCount === 1) {
                // Ajouter le premier destroy
                correctedLines.push(...destroyMethodLines);
            }
            continue;
        }
        
        // Continuer à ignorer les doublons
        if (destroyCount > 1) {
            continue;
        }
    } else {
        // Ajouter les lignes normales
        correctedLines.push(line);
    }
}

// Écrire le fichier corrigé
const correctedContent = correctedLines.join('\n');
fs.writeFileSync(filePath, correctedContent);

console.log('✅ [CLEANUP-DESTROY] Doublon supprimé !');
console.log(`📊 RÉSULTAT : ${destroyCount} occurrences trouvées, 1 conservée`);

console.log('\n🚀 Prochaines étapes :');
console.log('1. Redémarrer le serveur : npm run dev');
console.log('2. Recharger la page (Ctrl+F5)');
console.log('3. Tester les fonctions de diagnostic DOM');

console.log('\n✅ [CLEANUP-DESTROY] Script terminé !'); 