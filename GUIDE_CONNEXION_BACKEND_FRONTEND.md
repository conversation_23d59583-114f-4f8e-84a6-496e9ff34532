# 🚀 Guide de Connexion Backend ↔ Frontend

## **📋 Vue d'ensemble**

Votre système TeamCalendar est composé de :
- **Backend Express** (Port 3001) - API REST + Base PostgreSQL  
- **Frontend React/TypeScript** (Port 5173) - Interface Vite + Service API
- **Proxy Vite** - Redirection `/api/*` vers `http://localhost:3001`

## **⚡ Démarrage Rapide (RECOMMANDÉ)**

### **1. Méthode Simple - Script Automatique**

```bash
# Démarrer tout le système en une commande
npm run dev:system
```

Ce script va :
- ✅ Vérifier tous les prérequis
- ✅ Démarrer le backend (port 3001)
- ✅ Démarrer le frontend (port 5173)  
- ✅ Tester toutes les connexions
- ✅ Afficher les URLs d'accès

### **2. Si vous préférez le contrôle manuel**

```bash
# Option 1: Lancer les deux en parallèle
npm run dev:full

# Option 2: Lancer séparément (2 terminaux)
npm run server    # Terminal 1 - Backend
npm run dev       # Terminal 2 - Frontend
```

## **🔧 Configuration Actuelle (Déjà en place)**

### **Backend (server/app.js)**
```javascript
// Port d'écoute
const PORT = 3001

// CORS configuré pour Vite
origin: ['http://localhost:5173', 'http://localhost:4173']

// Routes API principales
/api/health         - Status du serveur
/api/employees      - Gestion employés
/api/shifts         - Gestion quarts
/api/standard-posts - Gestion postes
/api/regular-assignments - Attributions régulières
```

### **Frontend (vite.config.ts)**
```javascript
// Proxy configuré pour rediriger /api vers backend
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:3001',
      changeOrigin: true
    }
  }
}
```

### **Service API (src/api.ts)**
```typescript
// Configuration automatique
const API_BASE_URL = 'http://localhost:3001/api'

// Monitoring de connexion automatique
// Retry automatique sur les requêtes
// Gestion des erreurs réseau
```

## **🌐 URLs d'Accès**

Après démarrage, votre système sera accessible via :

| Service | URL | Description |
|---------|-----|-------------|
| **Frontend** | `http://localhost:5173` | Interface utilisateur principale |
| **Backend Direct** | `http://localhost:3001` | API backend directe |
| **API via Proxy** | `http://localhost:5173/api/*` | API via le proxy Vite (RECOMMANDÉ) |

## **🔍 Tests de Connexion**

### **1. Test Backend Direct**
```bash
curl http://localhost:3001/api/health
# Réponse: {"status":"ok","timestamp":"..."}
```

### **2. Test Proxy Vite**
```bash
curl http://localhost:5173/api/health  
# Réponse: {"status":"ok","timestamp":"..."}
```

### **3. Test Frontend → API**
Ouvrez `http://localhost:5173` et vérifiez dans la console développeur :
```
✅ [ApiService] Connexion stable rétablie
📅 [ApiService] Récupération des employés
```

## **🛠️ Architecture des Communications**

```
┌─────────────────┐    Proxy Vite     ┌─────────────────┐
│   Frontend      │ ──────────────→   │    Backend      │
│   (React/TS)    │   /api/* → :3001  │   (Express)     │
│   Port 5173     │ ←──────────────   │   Port 3001     │
└─────────────────┘    JSON/HTTP      └─────────────────┘
         │                                       │
         │                                       │
    ┌────▼────┐                            ┌─────▼─────┐
    │ Service │                            │PostgreSQL │
    │   API   │                            │ Database  │
    └─────────┘                            └───────────┘
```

## **📊 Monitoring de Connexion**

Le système inclut un monitoring automatique :

### **Indicateur de Connexion**
- 🟢 **Vert** : Backend accessible et fonctionnel
- 🟡 **Orange** : Tentative de reconnexion
- 🔴 **Rouge** : Backend inaccessible

### **Délais de Grâce**
- **2 minutes** avant notification de déconnexion
- **Vérifications** toutes les 30 secondes  
- **Retry automatique** avec exponential backoff

## **⚙️ Variables d'Environnement**

### **Base de Données (server/config/database.js)**
```bash
DB_HOST=*************        # Serveur PostgreSQL
DB_PORT=5432                 # Port standard
DB_NAME=glive_db            # Nom de la base
DB_USER=postgres            # Utilisateur
DB_PASSWORD=SebbZ12342323!! # Mot de passe (en dur pour simplifier)
```

### **Serveur (server/app.js)**
```bash
SERVER_PORT=3001    # Port backend (défaut: 3001)
NODE_ENV=development # Mode développement
```

## **🔧 Commandes Utiles**

### **Gestion du Système**
```bash
npm run dev:system     # Démarrage complet automatique
npm run dev:full       # Démarrage parallèle (concurrently)
npm run server         # Backend seul
npm run dev           # Frontend seul
```

### **Tests et Diagnostics**
```bash
npm run test:frontend-backend     # Test intégration
npm run test:grace-period        # Test monitoring connexion
npm run test:server-stability    # Test stabilité backend
```

### **Base de Données**
```bash
npm run db:check      # Vérifier la connexion DB
npm run db:state      # État de la base
npm run migrate       # Migrations base
```

## **🚨 Résolution de Problèmes**

### **Erreur: Port 3001 déjà utilisé**
```bash
# Trouver le processus utilisant le port
netstat -ano | findstr :3001

# Arrêter le processus si nécessaire
taskkill /PID <PID> /F
```

### **Erreur: Cannot connect to database**
1. Vérifiez que PostgreSQL est démarré
2. Testez la connexion : `npm run db:check`
3. Vérifiez les credentials dans `server/config/database.js`

### **Erreur: Frontend ne trouve pas l'API**
1. Vérifiez que le backend est démarré sur le port 3001
2. Testez : `curl http://localhost:3001/api/health`
3. Vérifiez le proxy Vite dans `vite.config.ts`

### **Erreur: CORS**
La configuration CORS est déjà en place, mais si problème :
```javascript
// server/app.js - ligne 20
origin: [
  'http://localhost:5173',
  'http://localhost:4173' 
]
```

## **📈 Fonctionnalités Connectées**

Votre frontend utilise déjà l'API pour :

| Fonctionnalité | Route API | Méthode |
|----------------|-----------|---------|
| **Employés** | `/api/employees` | GET, POST, PUT, DELETE |
| **Quarts** | `/api/shifts` | GET, POST, PUT, DELETE |
| **Postes Standards** | `/api/standard-posts` | GET, POST, PUT, DELETE |
| **Attributions Régulières** | `/api/regular-assignments` | GET, POST, PUT, DELETE |
| **Sauvegarde Semaine** | `/api/shifts/:weekId` | POST |

## **🎯 Points de Validation**

Après démarrage, vérifiez :

1. ✅ **Backend Health** : `http://localhost:3001/api/health`
2. ✅ **Frontend Access** : `http://localhost:5173`  
3. ✅ **API Proxy** : `http://localhost:5173/api/health`
4. ✅ **Console Frontend** : Pas d'erreurs de connexion
5. ✅ **Indicateur Connexion** : Vert dans l'interface

## **🚀 Ready to Go!**

Votre système est maintenant complètement connecté et prêt à l'emploi :

```bash
npm run dev:system
```

Et ouvrez : `http://localhost:5173`

**Bon développement ! 🎉** 