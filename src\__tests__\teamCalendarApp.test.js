// Tests unitaires pour TeamCalendarApp
// Focus sur les fonctions critiques : shouldApplyAssignmentToDay, actualRender, etc.

import { describe, it, expect, beforeEach, vi } from 'vitest';

// ✅ HELPER : Fonction debounce simple pour les tests
function debounce(func, delay) {
  let timeoutId;
  return function(...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
}

// Mock du TeamCalendarApp pour les tests
const createMockApp = () => ({
  data: {
    employees: [],
    schedule: {},
    regularAssignments: []
  },
  config: {
    days: [],
    standardPosts: []
  },
  
  // Fonction à tester
  shouldApplyAssignmentToDay: function(assignment, dayOfWeek, currentDate) {
    if (!assignment || typeof dayOfWeek !== 'number') return false;
    
    // Vérifier le jour de la semaine
    if (assignment.dayOfWeek !== undefined && assignment.dayOfWeek !== dayOfWeek) {
      return false;
    }
    
    // Vérifier les dates de début/fin
    if (assignment.startDate && currentDate < new Date(assignment.startDate)) {
      return false;
    }
    
    if (assignment.endDate && currentDate > new Date(assignment.endDate)) {
      return false;
    }
    
    return true;
  },
  
  // Mock de actualRender avec debounce
  _debouncedRender: null,
  actualRender: vi.fn(),
  
  render: function() {
    if (this._debouncedRender) {
      clearTimeout(this._debouncedRender);
    }
    
    this._debouncedRender = setTimeout(() => {
      this.actualRender();
    }, 50);
  }
});

describe('TeamCalendarApp - shouldApplyAssignmentToDay', () => {
  let app;
  
  beforeEach(() => {
    app = createMockApp();
  });

  // ✅ CAS 1 : Assignment valide pour le bon jour
  it('devrait appliquer assignment pour le bon jour de semaine', () => {
    const assignment = {
      dayOfWeek: 1, // Lundi
      startDate: '2025-01-01',
      endDate: '2025-12-31'
    };
    
    const currentDate = new Date('2025-06-16'); // Lundi
    const result = app.shouldApplyAssignmentToDay(assignment, 1, currentDate);
    
    expect(result).toBe(true);
  });

  // ✅ CAS 2 : Assignment pour mauvais jour
  it('ne devrait pas appliquer assignment pour mauvais jour de semaine', () => {
    const assignment = {
      dayOfWeek: 1, // Lundi
      startDate: '2025-01-01',
      endDate: '2025-12-31'
    };
    
    const currentDate = new Date('2025-06-17'); // Mardi
    const result = app.shouldApplyAssignmentToDay(assignment, 2, currentDate);
    
    expect(result).toBe(false);
  });

  // ✅ CAS 3 : Date avant startDate
  it('ne devrait pas appliquer assignment avant startDate', () => {
    const assignment = {
      dayOfWeek: 1,
      startDate: '2025-07-01',
      endDate: '2025-12-31'
    };
    
    const currentDate = new Date('2025-06-16');
    const result = app.shouldApplyAssignmentToDay(assignment, 1, currentDate);
    
    expect(result).toBe(false);
  });

  // ✅ CAS 4 : Date après endDate
  it('ne devrait pas appliquer assignment après endDate', () => {
    const assignment = {
      dayOfWeek: 1,
      startDate: '2025-01-01',
      endDate: '2025-06-01'
    };
    
    const currentDate = new Date('2025-06-16');
    const result = app.shouldApplyAssignmentToDay(assignment, 1, currentDate);
    
    expect(result).toBe(false);
  });

  // ✅ CAS 5 : Assignment sans dayOfWeek (devrait s'appliquer tous les jours)
  it('devrait appliquer assignment sans dayOfWeek spécifique', () => {
    const assignment = {
      startDate: '2025-01-01',
      endDate: '2025-12-31'
    };
    
    const currentDate = new Date('2025-06-16');
    const result = app.shouldApplyAssignmentToDay(assignment, 1, currentDate);
    
    expect(result).toBe(true);
  });

  // ✅ CAS 6 : Assignment null/undefined
  it('ne devrait pas appliquer assignment null', () => {
    const result1 = app.shouldApplyAssignmentToDay(null, 1, new Date());
    const result2 = app.shouldApplyAssignmentToDay(undefined, 1, new Date());
    
    expect(result1).toBe(false);
    expect(result2).toBe(false);
  });

  // ✅ CAS 7 : dayOfWeek invalide
  it('ne devrait pas appliquer avec dayOfWeek invalide', () => {
    const assignment = { dayOfWeek: 1 };
    
    const result1 = app.shouldApplyAssignmentToDay(assignment, null, new Date());
    const result2 = app.shouldApplyAssignmentToDay(assignment, 'invalid', new Date());
    
    expect(result1).toBe(false);
    expect(result2).toBe(false);
  });

  // ✅ CAS 8 : Assignment sans dates (devrait s'appliquer)
  it('devrait appliquer assignment sans dates de restriction', () => {
    const assignment = {
      dayOfWeek: 1
    };
    
    const currentDate = new Date('2025-06-16');
    const result = app.shouldApplyAssignmentToDay(assignment, 1, currentDate);
    
    expect(result).toBe(true);
  });
});

describe('TeamCalendarApp - Render Debounce', () => {
  let app;

  beforeEach(() => {
    app = createMockApp();
    // ✅ CORRECTION : Les faux timers sont déjà configurés dans setup.ts
    // Pas besoin de les redéfinir ici
  });

  afterEach(() => {
    // ✅ CORRECTION : Nettoyer les timers après chaque test
    vi.clearAllTimers();
  });

  // ✅ TEST DEBOUNCE : Plusieurs appels rapides = un seul render
  it('devrait debouncer les appels multiples à render', () => {
    // Appeler render 3 fois rapidement
    app.render();
    app.render();
    app.render();

    // Aucun render ne devrait avoir été appelé encore
    expect(app.actualRender).not.toHaveBeenCalled();

    // Avancer le temps de 50ms (délai du debounce)
    vi.advanceTimersByTime(50);

    // Maintenant actualRender devrait avoir été appelé une seule fois
    expect(app.actualRender).toHaveBeenCalledTimes(1);
  });

  // ✅ TEST DEBOUNCE : Annulation du timer précédent
  it('devrait annuler le timer précédent lors de nouveaux appels', () => {
    app.render();

    // Avancer de 25ms (moins que le délai de 50ms)
    vi.advanceTimersByTime(25);

    // Appeler render à nouveau (devrait annuler le premier timer)
    app.render();

    // Avancer encore de 25ms (total 50ms depuis le premier appel)
    vi.advanceTimersByTime(25);

    // actualRender ne devrait pas encore avoir été appelé car le timer a été annulé
    expect(app.actualRender).not.toHaveBeenCalled();

    // Avancer de 25ms supplémentaires (50ms depuis le second appel)
    vi.advanceTimersByTime(25);

    // Maintenant actualRender devrait être appelé
    expect(app.actualRender).toHaveBeenCalledTimes(1);
  });
});

export { createMockApp };
