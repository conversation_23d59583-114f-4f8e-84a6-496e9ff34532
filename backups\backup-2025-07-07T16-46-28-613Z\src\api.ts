// Service API pour gérer toutes les communications avec le backend PostgreSQL
// Avec gestion de la connexion en temps réel et logs détaillés

// Configuration directe pour éviter les problèmes d'importation
const DEBUG_MODE: boolean = true;
const API_BASE_URL: string = 'http://localhost:3001/api';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  statusCode?: number;
}

export interface BackendStatus {
  connected: boolean;
  lastCheck: Date;
  error?: string;
  shouldShowNotification?: boolean;
}

class ApiService {
  private baseUrl: string;
  private healthUrl: string;
  private isConnected = false;
  private lastConnectionCheck = new Date();
  private connectionCheckInterval: number | null = null;
  private statusChangeCallbacks: ((status: BackendStatus) => void)[] = [];
  
  // Gestion des délais de grâce pour éviter le spam
  private disconnectionTime: Date | null = null;
  private reconnectionTime: Date | null = null;
  private lastNotificationTime: Date | null = null;
  private readonly GRACE_PERIOD_MS = 2 * 60 * 1000; // 2 minutes
  private readonly CHECK_INTERVAL_MS = 30 * 1000; // 30 secondes au lieu de 10

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
    this.healthUrl = `${baseUrl}/health`;
    console.log('🔌 [ApiService] Initialisation du service API');
    this.startConnectionMonitoring();
  }

  // Gestion du monitoring de connexion avec délais de grâce
  startConnectionMonitoring() {
    console.log('🔄 [ApiService] Démarrage du monitoring de connexion (intervalles de 30s)');
    this.checkConnection();
    
    // Vérifier la connexion toutes les 30 secondes (plus respectueux)
    this.connectionCheckInterval = window.setInterval(() => {
      this.checkConnection();
    }, this.CHECK_INTERVAL_MS);
  }

  stopConnectionMonitoring() {
    if (this.connectionCheckInterval) {
      clearInterval(this.connectionCheckInterval);
      this.connectionCheckInterval = null;
    }
  }

  async checkConnection(): Promise<boolean> {
    try {
      // Logs moins verbeux en mode normal
      const response = await fetch(this.healthUrl, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        signal: AbortSignal.timeout(8000) // Timeout plus généreux de 8 secondes
      });

      if (response.ok) {
        const data = await response.json();
        const wasDisconnected = !this.isConnected;
        const now = new Date();
        
        this.isConnected = true;
        this.lastConnectionCheck = now;
        
        // Si on était déconnecté, marquer le début de la reconnexion
        if (wasDisconnected) {
          this.reconnectionTime = now;
          this.disconnectionTime = null;
          console.log('🔄 [ApiService] Connexion rétablie, début du délai de grâce...');
        }
        
        // Notifier seulement si la connexion est stable depuis le délai de grâce
        const shouldNotifyReconnection = this.reconnectionTime && 
          (now.getTime() - this.reconnectionTime.getTime()) >= this.GRACE_PERIOD_MS &&
          (!this.lastNotificationTime || (now.getTime() - this.lastNotificationTime.getTime()) > this.GRACE_PERIOD_MS);
        
        this.notifyStatusChange({
          connected: true,
          lastCheck: this.lastConnectionCheck,
          shouldShowNotification: shouldNotifyReconnection
        });
        
        if (shouldNotifyReconnection) {
          this.lastNotificationTime = now;
          console.log('✅ [ApiService] Connexion stable rétablie après délai de grâce');
        }
        
        return true;
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      const wasConnected = this.isConnected;
      const now = new Date();
      
      this.isConnected = false;
      this.lastConnectionCheck = now;
      this.reconnectionTime = null;
      
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      
      // Si on était connecté, marquer le début de la déconnexion
      if (wasConnected) {
        this.disconnectionTime = now;
        console.log('⚠️ [ApiService] Perte de connexion, début du délai de grâce...');
      }
      
      // Notifier seulement si la déconnexion dure depuis le délai de grâce
      const shouldNotifyDisconnection = this.disconnectionTime && 
        (now.getTime() - this.disconnectionTime.getTime()) >= this.GRACE_PERIOD_MS &&
        (!this.lastNotificationTime || (now.getTime() - this.lastNotificationTime.getTime()) > this.GRACE_PERIOD_MS);
      
      this.notifyStatusChange({
        connected: false,
        lastCheck: this.lastConnectionCheck,
        error: errorMessage,
        shouldShowNotification: shouldNotifyDisconnection
      });
      
      if (shouldNotifyDisconnection) {
        this.lastNotificationTime = now;
        console.error('❌ [ApiService] Déconnexion confirmée après délai de grâce:', errorMessage);
      }
      
      return false;
    }
  }

  // Callbacks pour les changements de statut
  onStatusChange(callback: (status: BackendStatus) => void) {
    this.statusChangeCallbacks.push(callback);
  }

  private notifyStatusChange(status: BackendStatus) {
    this.statusChangeCallbacks.forEach(callback => callback(status));
  }

  getConnectionStatus(): BackendStatus {
    return {
      connected: this.isConnected,
      lastCheck: this.lastConnectionCheck
    };
  }

  // Méthode générique pour les requêtes API avec retry
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    console.log(`🌐 [ApiService] ${options.method || 'GET'} ${url}`);
    
    if (options.body) {
      console.log('📤 [ApiService] Body:', JSON.parse(options.body as string));
    }

    const maxRetries = 3;
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const response = await fetch(url, {
          headers: {
            'Content-Type': 'application/json',
            ...options.headers,
          },
          signal: AbortSignal.timeout(10000), // Timeout de 10 secondes
          ...options,
        });

        // Gestion sécurisée du parsing JSON
        let data: any = null;
        const contentType = response.headers.get('content-type');

        if (contentType && contentType.includes('application/json')) {
          try {
            data = await response.json();
          } catch (parseError) {
            console.warn(`⚠️ [ApiService] Erreur parsing JSON pour ${response.status}:`, parseError);
            data = { error: `Erreur parsing JSON: ${parseError}` };
          }
        } else {
          const textData = await response.text();
          data = { error: `Réponse non-JSON (${response.status}): ${textData.substring(0, 100)}...` };
        }

        if (response.ok) {
          console.log(`✅ [ApiService] Succès ${response.status}:`, data);
          this.isConnected = true;
          return { success: true, data };
        } else {
          console.error(`❌ [ApiService] Erreur ${response.status}:`, data);
          return { success: false, error: data?.error || data?.message || `HTTP ${response.status}: ${response.statusText}` };
        }
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000;
          console.warn(`⚠️ [ApiService] Tentative ${attempt + 1}/${maxRetries + 1} échouée, retry dans ${delay}ms:`, error);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          break;
        }
      }
    }

    const errorMessage = lastError ? lastError.message : 'Erreur réseau';
    console.error('🚨 [ApiService] Erreur réseau après retries:', errorMessage);
    this.isConnected = false;
    return { success: false, error: errorMessage };
  }

  // === EMPLOYÉS ===
  async getEmployees(): Promise<ApiResponse<any[]>> {
    console.log('👥 [ApiService] Récupération des employés');
    return this.request('/employees');
  }

  async createEmployee(employee: any): Promise<ApiResponse<any>> {
    console.log('➕ [ApiService] Création employé:', employee.name);
    return this.request('/employees', {
      method: 'POST',
      body: JSON.stringify(employee),
    });
  }

  async updateEmployee(id: string, employee: any): Promise<ApiResponse<any>> {
    console.log('✏️ [ApiService] Modification employé ID:', id);
    return this.request(`/employees/${id}`, {
      method: 'PUT',
      body: JSON.stringify(employee),
    });
  }

  async deleteEmployee(id: string): Promise<ApiResponse<any>> {
    console.log('🗑️ [ApiService] Suppression employé ID:', id);
    return this.request(`/employees/${id}`, {
      method: 'DELETE',
    });
  }

  // === SHIFTS ===
  async getShifts(params: { [key: string]: string | number | undefined }): Promise<ApiResponse<any[]>> {
    const searchParams = new URLSearchParams();
    for (const key in params) {
        const value = params[key];
        if (value !== undefined) {
            searchParams.append(key, String(value));
        }
    }
    const queryString = searchParams.toString();
    console.log(`📅 [ApiService] Récupération des shifts ?${queryString}`);
    return this.request(`/shifts?${queryString}`);
  }

  async saveWeekShifts(weekIdentifier: string, shifts: any[], options?: any): Promise<ApiResponse<any>> {
    console.log(`💾 [ApiService] Sauvegarde des shifts pour la semaine: ${weekIdentifier}`, { options });
    return this.request(`/shifts/${weekIdentifier}`, {
      method: 'POST',
      body: JSON.stringify({ shifts }),
    });
  }

  async deleteShiftsByEmployeeAndDate(employeeId: string, date: string): Promise<ApiResponse<any>> {
    console.log(`🗑️ [ApiService] Suppression des shifts pour l'employé ${employeeId} à la date ${date}`);
    return this.request(`/shifts/employee/${employeeId}/date/${date}`, {
      method: 'DELETE',
    });
  }

  async updateShift(shiftId: string, updateData: any): Promise<ApiResponse<any>> {
    console.log(`✏️ [ApiService] Mise à jour shift ID: ${shiftId}`, updateData);
    return this.request(`/shifts/${shiftId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  }

  async deleteShift(shiftId: string): Promise<ApiResponse<any>> {
    console.log(`🗑️ [ApiService] Suppression shift ID: ${shiftId}`);
    return this.request(`/shifts/${shiftId}`, {
      method: 'DELETE',
    });
  }

  // === STANDARD POSTS ===
  async getStandardPosts(): Promise<ApiResponse<any[]>> {
    console.log('📋 [ApiService] Récupération des postes standards');
    return this.request('/standard-posts');
  }

  async createStandardPost(post: any): Promise<ApiResponse<any>> {
    console.log('➕ [ApiService] Création poste standard:', post.name);
    return this.request('/standard-posts', {
      method: 'POST',
      body: JSON.stringify(post),
    });
  }

  async updateStandardPost(id: string, post: any): Promise<ApiResponse<any>> {
    console.log('✏️ [ApiService] Modification poste standard ID:', id);
    return this.request(`/standard-posts/${id}`, {
      method: 'PUT',
      body: JSON.stringify(post),
    });
  }

  async deleteStandardPost(id: string): Promise<ApiResponse<any>> {
    console.log('🗑️ [ApiService] Suppression poste standard ID:', id);
    return this.request(`/standard-posts/${id}`, {
      method: 'DELETE',
    });
  }

  // === REGULAR ASSIGNMENTS ===
  async getRegularAssignments(): Promise<ApiResponse<any[]>> {
    console.log('🔄 [ApiService] Récupération des attributions régulières');
    return this.request('/regular-assignments');
  }

  async createRegularAssignment(assignment: any): Promise<ApiResponse<any>> {
    console.log('➕ [ApiService] Création attribution régulière:', assignment);
    return this.request('/regular-assignments', {
      method: 'POST',
      body: JSON.stringify(assignment),
    });
  }

  async updateRegularAssignment(id: string, assignment: any): Promise<ApiResponse<any>> {
    console.log('✏️ [ApiService] Modification attribution régulière ID:', id);
    return this.request(`/regular-assignments/${id}`, {
      method: 'PUT',
      body: JSON.stringify(assignment),
    });
  }

  async deleteRegularAssignment(id: string): Promise<ApiResponse<any>> {
    console.log('🗑️ [ApiService] Suppression attribution régulière ID:', id);
    return this.request(`/regular-assignments/${id}`, {
      method: 'DELETE',
    });
  }

  // === SETTINGS ===
  async getSettings(): Promise<ApiResponse<any>> {
    console.log('⚙️ [ApiService] Récupération des paramètres');
    return this.request('/settings');
  }

  // === WEEK DATA ===
  async getWeekData(weekIdentifier: string): Promise<ApiResponse<any>> {
    console.log(`📅 [ApiService] Récupération des données pour la semaine: ${weekIdentifier}`);
    return this.request(`/weeks/${weekIdentifier}`);
  }

  // === EMPLOYEE ORDER ===
  async saveEmployeeOrder(employeeOrder: Array<{id: string, order: number}>): Promise<ApiResponse<any>> {
    console.log('💾 [ApiService] Sauvegarde ordre des employés:', employeeOrder);
    const payload = { employeeOrder }; // ✅ FIX : Remballer dans un objet comme attendu par le backend
    return this.request('/employee-order', {
      method: 'POST',
      body: JSON.stringify(payload),
    });
  }

  async getEmployeeOrder(): Promise<ApiResponse<Array<{id: string, order: number}>>> {
    console.log('📋 [ApiService] Récupération ordre des employés');
    return this.request('/employee-order');
  }

  // === RETRY AVEC EXPONENTIAL BACKOFF ===
  private async requestWithRetry(url: string, options: RequestInit, maxRetries: number = 3): Promise<ApiResponse<any>> {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await this.request(url, options);
      } catch (error) {
        lastError = error as Error;

        if (attempt === maxRetries) {
          console.error(`❌ [ApiService] Échec final après ${maxRetries + 1} tentatives:`, error);
          break;
        }

        // Exponential backoff: 1s, 2s, 4s, 8s...
        const delay = Math.pow(2, attempt) * 1000;
        console.warn(`⚠️ [ApiService] Tentative ${attempt + 1}/${maxRetries + 1} échouée, retry dans ${delay}ms:`, error);

        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  // === SYSTÈME DE TOAST ===
  private showToast(type: 'success' | 'error' | 'warning' | 'info', message: string): void {
    try {
      // Utiliser le système de toast global s'il existe
      if (typeof window !== 'undefined' && (window as any).toastSystem) {
        (window as any).toastSystem[type](message);
      } else {
        // Fallback vers console si pas de système de toast
        console.log(`🍞 [Toast ${type.toUpperCase()}] ${message}`);
      }
    } catch (error) {
      console.warn('⚠️ [ApiService] Erreur affichage toast:', error);
    }
  }

  // === FALLBACK VERS LOCALSTORAGE ===
  // Utilisé uniquement quand le backend n'est pas disponible
  private getLocalStorageData(key: string): any {
    try {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('❌ [ApiService] Erreur localStorage:', error);
      return null;
    }
  }

  private setLocalStorageData(key: string, data: any): void {
    try {
      localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
      console.error('❌ [ApiService] Erreur sauvegarde localStorage:', error);
    }
  }

  // Méthodes de fallback
  async getEmployeesFallback(): Promise<ApiResponse<any[]>> {
    console.log('🔄 [ApiService] Fallback localStorage pour employés');
    const data = this.getLocalStorageData('teamCalendarState_v1');
    return { success: true, data: data?.employees || [] };
  }

  async saveEmployeesFallback(employees: any[]): Promise<ApiResponse<any>> {
    console.log('💾 [ApiService] Sauvegarde fallback employés');
    const existingData = this.getLocalStorageData('teamCalendarState_v1') || {};
    existingData.employees = employees;
    this.setLocalStorageData('teamCalendarState_v1', existingData);
    return { success: true, data: employees };
  }
}

// =====================================================
// SINGLETON INSTANCE & EXPORT
// =====================================================

// Crée une seule instance du service
const apiServiceInstance = new ApiService(API_BASE_URL);

// Exporte l'instance pour être utilisée dans toute l'application
export { apiServiceInstance as apiService };
