# 🔄 Guide Système de Logs en Temps Réel - TeamCalendar

## ✅ Corrections Implémentées

### 1. **Gestion Unique des Sessions**
- **Problème résolu** : Sessions multiples identiques causées par les reloads Vite
- **Solution** : Anti-doublon avec vérification `window.__logCaptureInitialized`
- **Durée de session** : 1 heure d'inactivité max
- **Heartbeat** : Toutes les 30 secondes pour maintenir la session active

### 2. **Interface Temps Réel (SSE)**
- **Nouveau endpoint** : `GET /api/debug/stream/:sessionId`
- **Technologie** : Server-Sent Events (SSE)
- **Fréquence** : Mise à jour toutes les 2 secondes
- **Fallback** : Mode snapshot si SSE indisponible

### 3. **Interface Utilisateur Enrichie**
- **Toggle Temps Réel** : Checkbox avec indicateur visuel
- **Statut Connexion** : <PERSON><PERSON> (connecté), <PERSON><PERSON><PERSON> (connexion), <PERSON><PERSON> (déconnecté)
- **Gestion Reconnexion** : Automatique en cas de perte de connexion

## 🚀 Utilisation

### Démarrage Propre
```bash
npm run start:clean
```
> **Nettoie** les sessions précédentes et démarre le système

### Accès Interface
```
http://localhost:5173/logs
```

### Fonctionnalités Disponibles

#### **1. Mode Temps Réel**
1. Sélectionner une session de logs
2. ✅ Cocher "Temps réel"
3. Observer le point de statut (🟢 connecté)
4. Les logs se mettent à jour automatiquement

#### **2. Modes de Tri**
- **Standard** : Chronologique (plus récent en premier)
- **IA Compact** : Tri par importance (score intelligent)

#### **3. Personnalisation**
- **Limite** : 50 à 1000 lignes (slider)
- **Fusion répétitions** : Logs identiques groupés
- **Export IA** : Copie optimisée pour analyse

#### **4. Nettoyage**
- **1 jour** : Supprime logs récents
- **7 jours** : Nettoyage hebdomadaire  
- **30 jours** : Archive mensuelle

## 🔧 Architecture Technique

### Frontend (React)
```typescript
// Composant Temps Réel
const [isRealTime, setIsRealTime] = useState(false);
const [streamStatus, setStreamStatus] = useState('disconnected');

// Server-Sent Events
const eventSource = new EventSource(`/api/debug/stream/${sessionId}`);
```

### Backend (Express)
```javascript
// Endpoint SSE
app.get('/api/debug/stream/:sessionId', (req, res) => {
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive'
  });
  
  const interval = setInterval(sendLogs, 2000);
});
```

### Capture Logs (Anti-Doublon)
```javascript
// Vérification unicité
if (window.__logCaptureInitialized) {
  return; // Évite doublons
}
window.__logCaptureInitialized = true;

// Session intelligente (1h TTL)
const oneHour = 60 * 60 * 1000;
if (now - sessionTimestamp > oneHour) {
  // Nouvelle session
}
```

## 📊 Scoring IA des Logs

| Niveau | Score | Priorité |
|--------|-------|----------|
| FATAL  | 100   | Critique |
| ERROR  | 80    | Haute    |
| WARN   | 60    | Moyenne  |
| INFO   | 30    | Basse    |
| DEBUG  | 10    | Minimal  |

## 🐛 Tests et Diagnostic

### Vérifier les Sessions
```bash
# Accéder à l'interface
http://localhost:5173/logs

# Observer la console pour :
🆕 [LogCapture] Nouvelle session: 12345678...
♻️ [LogCapture] Session existante: 12345678...
```

### Tester le Temps Réel
1. Ouvrir 2 onglets : interface logs + application
2. Activer temps réel dans l'interface logs
3. Générer des actions dans l'application
4. Observer les logs apparaître en temps réel

### Débugger les Sessions
```javascript
// Console navigateur
console.log('Session actuelle:', window.logCapture?.sessionId());

// Forcer nouvelle session
localStorage.removeItem('logs_session_id');
localStorage.removeItem('logs_session_timestamp');
location.reload();
```

## 🎯 Fonctionnalités Avancées

### Export IA Optimisé
- Fusion automatique des répétitions
- Format compact pour analyse IA
- Copie directe dans presse-papiers

### Gestion Robuste des Erreurs  
- Reconnexion automatique SSE
- Fallback mode snapshot
- Logs silencieux si DB indisponible

### Performance
- Cap 2000 lignes maximum
- Requêtes optimisées avec index
- Nettoyage automatique ancien logs

## 📈 Métriques et Statistiques

L'interface affiche :
- **Entrées affichées** : Nombre de logs visibles
- **Erreurs** : Count logs niveau ERROR
- **Avertissements** : Count logs niveau WARN  
- **Total répétitions** : Nombre réel d'événements

## 🔄 Intégration Continue

Le système s'intègre parfaitement avec :
- ✅ Configuration PostgreSQL existante
- ✅ Système de purge de base de données
- ✅ Architecture backend/frontend actuelle
- ✅ Scripts de démarrage automatiques

---

## 🎉 Résultat Final

**Système de logs complètement opérationnel** avec :
- 🔄 **Temps réel** via Server-Sent Events
- 🎯 **Sessions uniques** sans doublons
- 🧠 **Tri intelligent IA** avec scoring
- 📊 **Interface moderne** responsive
- 🛡️ **Gestion d'erreurs** robuste
- 🧹 **Nettoyage automatique** configurable

**Prêt pour production** ! 🚀 