import { query } from '../config/database.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * Service robuste pour les attributions régulières
 * Architecture lazy-loading avec cache intelligent
 */
class RegularAssignmentService {
    constructor() {
        this.cache = new Map(); // Cache en mémoire (Redis à venir)
        this.cacheTTL = 3 * 60 * 60 * 1000; // 3 heures
    }

    /**
     * Calcule la plage de dates pour une semaine ISO
     * @param {number} startDay - Jour de début (0=dimanche, 1=lundi, etc.)
     * @param {string} isoWeek - Format YYYY-WW
     * @returns {Object} { start: Date, end: Date, dateKeys: string[] }
     */
    calcWeekRange(startDay, isoWeek) {
        console.log(`📅 [calcWeekRange] Calcul pour semaine ${isoWeek}, début: ${startDay}`);
        
        const [year, week] = isoWeek.split('-W').map(Number);
        
        // Calculer le premier jour de l'année
        const jan1 = new Date(year, 0, 1);
        const jan1Day = jan1.getDay();
        
        // Calculer le premier lundi de l'année (semaine ISO 1)
        const firstMonday = new Date(year, 0, 1 + (jan1Day <= 4 ? 1 - jan1Day : 8 - jan1Day));
        
        // Calculer le lundi de la semaine demandée
        const weekMonday = new Date(firstMonday);
        weekMonday.setDate(firstMonday.getDate() + (week - 1) * 7);
        
        // Ajuster selon le jour de début configuré
        const weekStart = new Date(weekMonday);
        weekStart.setDate(weekMonday.getDate() + (startDay - 1));
        
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        
        // Générer les clés de dates
        const dateKeys = [];
        for (let i = 0; i < 7; i++) {
            const date = new Date(weekStart);
            date.setDate(weekStart.getDate() + i);
            dateKeys.push(date.toISOString().split('T')[0]);
        }
        
        console.log(`📅 [calcWeekRange] Résultat:`, {
            start: weekStart.toISOString().split('T')[0],
            end: weekEnd.toISOString().split('T')[0],
            dateKeys
        });
        
        return {
            start: weekStart,
            end: weekEnd,
            dateKeys,
            startDateKey: dateKeys[0],
            endDateKey: dateKeys[6]
        };
    }

    /**
     * Récupère les attributions pour une semaine donnée
     * @param {string} tenantId - ID du tenant (pour multi-tenant futur)
     * @param {string} isoWeek - Format YYYY-WW
     * @param {number} startDay - Jour de début de semaine
     * @returns {Promise<Object>} Vue complète de la semaine
     */
    async getWeekAssignments(tenantId = 'default', isoWeek, startDay = 1) {
        console.log(`🔍 [getWeekAssignments] Récupération semaine ${isoWeek} pour tenant ${tenantId}`);
        
        const cacheKey = `regular:${tenantId}:${isoWeek}:${startDay}`;
        
        // Vérifier le cache
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTTL) {
                console.log(`⚡ [getWeekAssignments] Cache hit pour ${cacheKey}`);
                return cached.data;
            } else {
                console.log(`🗑️ [getWeekAssignments] Cache expiré pour ${cacheKey}`);
                this.cache.delete(cacheKey);
            }
        }

        // Calculer la plage de la semaine
        const weekRange = this.calcWeekRange(startDay, isoWeek);
        
        // Récupérer les attributions régulières actives
        const assignments = await this.getActiveAssignments();
        
        // Récupérer les shifts existants pour cette semaine
        const existingShifts = await this.getExistingShifts(weekRange.startDateKey, weekRange.endDateKey);
        
        // Générer la vue de la semaine
        const weekView = await this.generateWeekView(assignments, weekRange, existingShifts);
        
        // Mettre en cache
        this.cache.set(cacheKey, {
            data: weekView,
            timestamp: Date.now()
        });
        
        console.log(`✅ [getWeekAssignments] Vue générée pour ${isoWeek}:`, {
            assignments: assignments.length,
            shifts: Object.keys(weekView.shifts).length,
            dateRange: `${weekRange.startDateKey} → ${weekRange.endDateKey}`
        });
        
        return weekView;
    }

    /**
     * Récupère toutes les attributions régulières actives
     */
    async getActiveAssignments() {
        const result = await query(`
            SELECT 
                ra.id,
                ra.employee_id,
                ra.post_id,
                ra.start_date,
                ra.end_date,
                array_agg(ra.day_of_week ORDER BY ra.day_of_week) as selected_days,
                e.name as employee_name,
                sp.label as post_label,
                sp.hours as post_hours,
                sp.type as post_type,
                sp.category as post_category
            FROM regular_assignments ra
            LEFT JOIN employees e ON ra.employee_id = e.id
            LEFT JOIN standard_posts sp ON ra.post_id = sp.id
            WHERE ra.is_active = true
            GROUP BY ra.id, ra.employee_id, ra.post_id, ra.start_date, ra.end_date, 
                     e.name, sp.label, sp.hours, sp.type, sp.category
            ORDER BY e.name, sp.label
        `);
        
        return result.rows.map(row => ({
            id: row.id,
            employeeId: row.employee_id,
            postId: row.post_id,
            startDate: row.start_date,
            endDate: row.end_date,
            selectedDays: row.selected_days || [],
            isLimited: row.end_date !== null,
            employee: {
                id: row.employee_id,
                name: row.employee_name
            },
            post: {
                id: row.post_id,
                label: row.post_label,
                hours: row.post_hours,
                type: row.post_type,
                category: row.post_category
            }
        }));
    }

    /**
     * Récupère les shifts existants pour une plage de dates
     */
    async getExistingShifts(startDateKey, endDateKey) {
        const result = await query(`
            SELECT 
                s.*,
                e.name as employee_name,
                sp.label as post_label
            FROM shifts s
            LEFT JOIN employees e ON s.employee_id = e.id
            LEFT JOIN standard_posts sp ON s.post_id = sp.id
            WHERE s.date_key >= $1 AND s.date_key <= $2
            ORDER BY s.date_key, e.name
        `, [startDateKey, endDateKey]);
        
        // Organiser par employé et date
        const shiftsByEmployee = {};
        result.rows.forEach(shift => {
            if (!shiftsByEmployee[shift.employee_id]) {
                shiftsByEmployee[shift.employee_id] = {};
            }
            if (!shiftsByEmployee[shift.employee_id][shift.date_key]) {
                shiftsByEmployee[shift.employee_id][shift.date_key] = [];
            }
            shiftsByEmployee[shift.employee_id][shift.date_key].push({
                id: shift.id,
                postId: shift.post_id,
                text: shift.shift_data?.text || shift.post_label,
                type: shift.shift_data?.type || 'sky',
                isRegular: shift.is_regular,
                isPunctual: shift.is_punctual,
                assignmentId: shift.assignment_id
            });
        });
        
        return shiftsByEmployee;
    }

    /**
     * Génère la vue complète de la semaine
     */
    async generateWeekView(assignments, weekRange, existingShifts) {
        const weekView = {
            isoWeek: weekRange.isoWeek,
            startDate: weekRange.startDateKey,
            endDate: weekRange.endDateKey,
            dateKeys: weekRange.dateKeys,
            shifts: { ...existingShifts }, // Copie des shifts existants
            assignments: assignments,
            generated: new Date().toISOString()
        };

        // Appliquer les attributions régulières pour chaque jour
        for (const assignment of assignments) {
            await this.applyAssignmentToWeek(assignment, weekRange, weekView);
        }

        return weekView;
    }

    /**
     * Applique une attribution régulière à une semaine
     */
    async applyAssignmentToWeek(assignment, weekRange, weekView) {
        for (let dayIndex = 0; dayIndex < weekRange.dateKeys.length; dayIndex++) {
            const dateKey = weekRange.dateKeys[dayIndex];
            const dayOfWeek = dayIndex; // 0=premier jour de la semaine configurée
            
            // Vérifier si cette attribution s'applique à ce jour
            if (!this.shouldApplyAssignmentToDay(assignment, dayOfWeek, dateKey)) {
                continue;
            }

            // Vérifier s'il y a déjà un shift pour cette attribution
            const employeeShifts = weekView.shifts[assignment.employeeId]?.[dateKey] || [];
            const hasExistingShift = employeeShifts.some(shift => 
                shift.assignmentId === assignment.id || 
                (shift.isRegular && shift.postId === assignment.postId)
            );

            if (hasExistingShift) {
                continue; // Skip si déjà présent
            }

            // Créer le shift
            const shift = {
                id: `generated-${uuidv4()}`,
                postId: assignment.postId,
                text: assignment.post.hours,
                type: assignment.post.type,
                isRegular: true,
                assignmentId: assignment.id,
                generated: true // Marquer comme généré
            };

            // Ajouter à la vue
            if (!weekView.shifts[assignment.employeeId]) {
                weekView.shifts[assignment.employeeId] = {};
            }
            if (!weekView.shifts[assignment.employeeId][dateKey]) {
                weekView.shifts[assignment.employeeId][dateKey] = [];
            }
            weekView.shifts[assignment.employeeId][dateKey].push(shift);
        }
    }

    /**
     * Vérifie si une attribution doit être appliquée à un jour donné
     */
    shouldApplyAssignmentToDay(assignment, dayOfWeek, dateKey) {
        // Vérifier les jours sélectionnés
        if (!assignment.selectedDays.includes(dayOfWeek)) {
            return false;
        }

        // Vérifier les dates de début et fin
        const currentDate = new Date(dateKey);
        
        if (assignment.startDate && currentDate < new Date(assignment.startDate)) {
            return false;
        }
        
        if (assignment.isLimited && assignment.endDate && currentDate > new Date(assignment.endDate)) {
            return false;
        }

        return true;
    }

    /**
     * Invalide le cache pour un tenant
     */
    invalidateCache(tenantId = 'default') {
        console.log(`🗑️ [invalidateCache] Invalidation cache pour tenant ${tenantId}`);
        
        const keysToDelete = [];
        for (const key of this.cache.keys()) {
            if (key.startsWith(`regular:${tenantId}:`)) {
                keysToDelete.push(key);
            }
        }
        
        keysToDelete.forEach(key => this.cache.delete(key));
        
        console.log(`✅ [invalidateCache] ${keysToDelete.length} entrées supprimées`);
    }

    /**
     * Statistiques du cache
     */
    getCacheStats() {
        const now = Date.now();
        let validEntries = 0;
        let expiredEntries = 0;
        
        for (const [key, value] of this.cache.entries()) {
            if (now - value.timestamp < this.cacheTTL) {
                validEntries++;
            } else {
                expiredEntries++;
            }
        }
        
        return {
            totalEntries: this.cache.size,
            validEntries,
            expiredEntries,
            hitRatio: validEntries / (validEntries + expiredEntries) || 0
        };
    }
}

export default new RegularAssignmentService();
