#!/usr/bin/env node
import pg from 'pg';
import { readFileSync, writeFileSync } from 'fs';
import { loadEnv } from './load-env.mjs';

const { Client } = pg;

// Charger automatiquement les variables d'environnement
loadEnv();

// Configuration PostgreSQL - Utilise vos identifiants existants
const dbConfig = {
  host: process.env.DB_HOST || '*************',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'glive_db',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'SebbZ12342323!!',
  // Configuration SSL pour serveurs distants
  ssl: process.env.DB_SSL === 'true' ? {
    rejectUnauthorized: false
  } : false,
  // Timeout pour connexions distantes
  connectionTimeoutMillis: 15000,
  query_timeout: 30000
};

console.log('🔧 [Configuration] PostgreSQL TeamCalendar');
console.log(`📡 [Connexion] ${dbConfig.user}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);
console.log('✅ [Config] Utilisation des identifiants existants du projet');

async function migrateLogsRemote() {
  console.log('\n🔄 [Migration Logs] Démarrage pour serveur distant...');
  
  const client = new Client(dbConfig);
  
  try {
    console.log('📡 [Migration] Connexion au serveur PostgreSQL distant...');
    console.log(`🔗 [Cible] ${dbConfig.host}:${dbConfig.port}`);
    
    await client.connect();
    console.log('✅ [Migration] Connexion PostgreSQL distante réussie!');
    
    // Test de permission
    console.log('🔐 [Migration] Vérification des permissions...');
    await client.query('SELECT version()');
    console.log('✅ [Migration] Permissions validées');
    
    // Vérifier si la table existe déjà
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'logs'
      );
    `);
    
    if (tableCheck.rows[0].exists) {
      console.log('ℹ️  [Migration] Table logs déjà existante');
      
      // Vérifier si la fonction existe
      const functionCheck = await client.query(`
        SELECT EXISTS (
          SELECT FROM pg_proc 
          WHERE proname = 'ai_ranked_logs' 
          AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
        );
      `);
      
      if (!functionCheck.rows[0].exists) {
        console.log('⚡ [Migration] Création de la fonction ai_ranked_logs...');
        await client.query(getFunctionSQL());
        console.log('✅ [Migration] Fonction ai_ranked_logs créée');
      }
      
      console.log('✅ [Migration] Système de logs déjà opérationnel');
      await testLogsSystem(client);
      return;
    }
    
    console.log('📋 [Migration] Création de la table logs sur serveur distant...');
    
    // Vérifier si uuid-ossp est disponible
    try {
      await client.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
      console.log('✅ [Migration] Extension uuid-ossp activée');
    } catch (extError) {
      console.log('⚠️  [Migration] Extension uuid-ossp non disponible - Utilisation gen_random_uuid()');
    }
    
    // Créer la table logs
    await client.query(`
      CREATE TABLE logs (
          id           BIGSERIAL PRIMARY KEY,
          session_id   UUID      NOT NULL,
          source       TEXT      NOT NULL,
          level        TEXT      NOT NULL,
          message      TEXT      NOT NULL,
          data         JSONB     DEFAULT '{}'::jsonb,
          ts           TIMESTAMPTZ DEFAULT now(),
          capture_mode TEXT      DEFAULT 'full'
      );
      
      CREATE INDEX logs_session_idx ON logs(session_id);
      CREATE INDEX logs_level_idx ON logs(level);
      CREATE INDEX logs_ts_idx ON logs(ts);
      CREATE INDEX logs_source_idx ON logs(source);
    `);
    
    console.log('✅ [Migration] Table logs créée sur serveur distant');
    
    // Créer la fonction ai_ranked_logs
    console.log('⚡ [Migration] Création de la fonction ai_ranked_logs...');
    await client.query(getFunctionSQL());
    console.log('✅ [Migration] Fonction ai_ranked_logs créée');
    
    // Créer la fonction de nettoyage
    await client.query(`
      CREATE OR REPLACE FUNCTION cleanup_old_logs(days_to_keep INTEGER DEFAULT 7)
      RETURNS VOID AS $$
      BEGIN
          DELETE FROM logs WHERE ts < NOW() - INTERVAL '1 day' * days_to_keep;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    console.log('✅ [Migration] Fonction cleanup_old_logs créée');
    
    // Test du système
    await testLogsSystem(client);
    
    console.log('\n🎉 [Succès] Système de logs complètement opérationnel sur serveur distant!');
    console.log('📍 [Info] Interface accessible via: http://localhost:5173/logs');
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.error('\n❌ [Erreur] Serveur PostgreSQL distant non accessible!');
      console.error(`🔧 [Vérification] Serveur: ${dbConfig.host}:${dbConfig.port}`);
      console.error('💡 [Solutions]:');
      console.error('   • Vérifiez que le serveur PostgreSQL est démarré');
      console.error('   • Vérifiez les paramètres de connexion (host, port, user, password)');
      console.error('   • Vérifiez les règles de firewall');
      console.error('   • Vérifiez si SSL est requis (DB_SSL=true)');
    } else if (error.code === 'ENOTFOUND') {
      console.error('\n❌ [Erreur] Nom de serveur PostgreSQL non trouvé!');
      console.error(`🔧 [Vérification] Host: ${dbConfig.host}`);
      console.error('💡 [Solution]: Vérifiez l\'adresse du serveur PostgreSQL');
    } else if (error.code === '3D000') {
      console.error('\n❌ [Erreur] Base de données non trouvée!');
      console.error(`🔧 [Vérification] Database: ${dbConfig.database}`);
      console.error('💡 [Solution]: Créez la base de données ou vérifiez le nom');
    } else if (error.code === '28P01') {
      console.error('\n❌ [Erreur] Authentification échouée!');
      console.error(`🔧 [Vérification] User: ${dbConfig.user}`);
      console.error('💡 [Solution]: Vérifiez les identifiants de connexion');
    } else if (error.code === '28000') {
      console.error('\n❌ [Erreur] Connexion SSL refusée!');
      console.error('💡 [Solution]: Essayez avec DB_SSL=true ou DB_SSL=false');
    } else {
      console.error('❌ [Erreur] Échec de la migration:', error.message);
      console.error('🔧 [Code]:', error.code);
    }
    process.exit(1);
  } finally {
    await client.end();
  }
}

async function testLogsSystem(client) {
  // Insérer quelques logs de test
  console.log('📝 [Test] Ajout de logs de test sur serveur distant...');
  const sessionId = crypto.randomUUID ? crypto.randomUUID() : '12345678-1234-1234-1234-123456789abc';
  
  await client.query(`
    INSERT INTO logs (session_id, source, level, message, data) VALUES
    ($1, 'backend', 'info', 'Migration réussie sur serveur distant', '{"host": "${dbConfig.host}"}'),
    ($1, 'system', 'success', 'Configuration PostgreSQL distant validée', '{"ssl": ${dbConfig.ssl ? 'true' : 'false'}}'),
    ($1, 'migration', 'info', 'Système de logs opérationnel', '{"timestamp": "${new Date().toISOString()}"}')
  `, [sessionId]);
  
  console.log('✅ [Test] Logs de test ajoutés');
  
  // Test de la fonction de scoring
  console.log('🎯 [Test] Test du scoring IA...');
  const scoredLogs = await client.query('SELECT * FROM ai_ranked_logs($1, $2)', [sessionId, 'full']);
  console.log(`✅ [Test] ${scoredLogs.rows.length} logs scorés et triés`);
  
  // Afficher les logs triés
  console.log('📊 [Résultat] Logs triés par score:');
  scoredLogs.rows.forEach(log => {
    console.log(`   ${log.level.toUpperCase().padEnd(7)} [${log.source.padEnd(9)}] Score: ${log.score.toString().padStart(3)} - ${log.message}`);
  });
}

function getFunctionSQL() {
  return `
    CREATE OR REPLACE FUNCTION ai_ranked_logs(
            p_session  UUID,
            p_mode     TEXT DEFAULT 'ai'
    ) RETURNS TABLE(
            id     BIGINT,
            ts     TIMESTAMPTZ,
            level  TEXT,
            source TEXT,
            message TEXT,
            score  INT
    ) AS $$
    WITH base AS (
       SELECT *,
              CASE level
                WHEN 'fatal' THEN 100
                WHEN 'error' THEN 80
                WHEN 'warn'  THEN 60
                WHEN 'info'  THEN 30
                WHEN 'success' THEN 40
                ELSE 10
              END                              AS lvl_score,
              ROW_NUMBER() OVER (PARTITION BY message ORDER BY id)
                                               AS dup_rank
       FROM   logs
       WHERE  session_id   = p_session
       AND    capture_mode = p_mode
    )
    SELECT id, ts, level, source, message,
           lvl_score - GREATEST(dup_rank-1,0)*5 AS score
    FROM   base
    ORDER  BY score DESC, id DESC;
    $$ LANGUAGE sql STABLE;
  `;
}

// Auto-détection crypto.randomUUID
if (typeof crypto === 'undefined') {
  global.crypto = {
    randomUUID: () => '12345678-1234-1234-1234-123456789abc'
  };
}

// Exécuter la migration
migrateLogsRemote().catch(console.error); 