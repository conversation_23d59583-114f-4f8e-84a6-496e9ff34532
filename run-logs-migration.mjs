import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import pg from 'pg';

const { Client } = pg;
const __dirname = dirname(fileURLToPath(import.meta.url));

// Configuration de la base de données
const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'team_calendar',
  user: 'postgres',
  password: 'admin123'
};

async function runLogsMigration() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 [Migration] Connexion à la base de données...');
    await client.connect();
    
    console.log('📋 [Migration] Lecture du fichier de migration...');
    const migrationPath = join(__dirname, 'database', 'migrations', '009_create_logs_table.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');
    
    console.log('⚡ [Migration] Exécution de la migration des logs...');
    await client.query(migrationSQL);
    
    console.log('✅ [Migration] Migration des logs terminée avec succès!');
    
    // Test de la fonction ai_ranked_logs
    console.log('🧪 [Test] Test de la fonction ai_ranked_logs...');
    const testResult = await client.query('SELECT * FROM ai_ranked_logs($1, $2) LIMIT 5', ['00000000-0000-0000-0000-000000000000', 'full']);
    console.log('✅ [Test] Fonction ai_ranked_logs opérationnelle');
    
    // Insérer quelques logs de test
    console.log('📝 [Test] Ajout de logs de test...');
    const sessionId = '12345678-1234-1234-1234-123456789abc';
    
    await client.query(`
      INSERT INTO logs (session_id, source, level, message, data) VALUES
      ($1, 'backend', 'info', 'Serveur démarré avec succès', '{"port": 3001}'),
      ($1, 'frontend', 'warn', 'Composant non monté', '{"component": "TestComponent"}'),
      ($1, 'browser', 'error', 'Erreur de réseau', '{"url": "/api/test"}'),
      ($1, 'backend', 'debug', 'Requête API reçue', '{"endpoint": "/api/debug"}'),
      ($1, 'frontend', 'info', 'Page chargée', '{"route": "/logs"}')
    `, [sessionId]);
    
    console.log('✅ [Test] Logs de test ajoutés');
    
    // Test de la fonction de scoring
    console.log('🎯 [Test] Test du scoring IA...');
    const scoredLogs = await client.query('SELECT * FROM ai_ranked_logs($1, $2)', [sessionId, 'full']);
    console.log(`✅ [Test] ${scoredLogs.rows.length} logs scorés et triés`);
    
    // Afficher les logs triés
    console.log('📊 [Résultat] Logs triés par score:');
    scoredLogs.rows.forEach(log => {
      console.log(`   ${log.level.toUpperCase().padEnd(5)} [${log.source.padEnd(8)}] Score: ${log.score} - ${log.message}`);
    });
    
    console.log('\n🎉 [Succès] Système de logs complètement opérationnel!');
    console.log('📍 [Info] Accessible via: http://localhost:5173/logs');
    
  } catch (error) {
    console.error('❌ [Erreur] Échec de la migration:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Exécuter la migration
runLogsMigration().catch(console.error); 