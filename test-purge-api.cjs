#!/usr/bin/env node

/**
 * Script de test simple pour l'API de purge
 */

const http = require('http');

async function testPurgeAPI() {
    console.log('🧪 Test de l\'API de purge...\n');
    
    const postData = JSON.stringify({});
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/database/purge',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
        }
    };
    
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            
            console.log(`📡 Status: ${res.statusCode} ${res.statusMessage}`);
            console.log(`📋 Headers:`, res.headers);
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    console.log('\n📄 Réponse brute:', data);
                    
                    if (data) {
                        const jsonData = JSON.parse(data);
                        console.log('\n📊 Réponse JSON:', JSON.stringify(jsonData, null, 2));
                        
                        if (jsonData.success) {
                            console.log('\n✅ Purge réussie !');
                        } else {
                            console.log('\n❌ Purge échouée:', jsonData.error);
                            console.log('💬 Message:', jsonData.message);
                        }
                    }
                    
                    resolve({ statusCode: res.statusCode, data });
                } catch (error) {
                    console.error('\n❌ Erreur parsing JSON:', error.message);
                    resolve({ statusCode: res.statusCode, data, parseError: error });
                }
            });
        });
        
        req.on('error', (error) => {
            console.error('❌ Erreur requête:', error.message);
            if (error.code === 'ECONNREFUSED') {
                console.error('\n💡 Le serveur n\'est probablement pas démarré.');
                console.error('   Lancez: npm run server');
            }
            reject(error);
        });
        
        req.write(postData);
        req.end();
    });
}

// Test de connexion d'abord
async function testConnection() {
    console.log('🔍 Test de connexion au serveur...');
    
    const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/health',
        method: 'GET'
    };
    
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => data += chunk);
            res.on('end', () => {
                console.log(`✅ Serveur accessible: ${res.statusCode}`);
                resolve(true);
            });
        });
        
        req.on('error', (error) => {
            console.error('❌ Serveur inaccessible:', error.message);
            resolve(false);
        });
        
        req.end();
    });
}

async function main() {
    try {
        const connected = await testConnection();
        if (!connected) {
            console.log('\n💡 Démarrez le serveur avec: npm run server');
            process.exit(1);
        }
        
        console.log('\n🧪 Test de l\'API de purge...');
        await testPurgeAPI();
        
    } catch (error) {
        console.error('❌ Erreur:', error.message);
        process.exit(1);
    }
}

main(); 