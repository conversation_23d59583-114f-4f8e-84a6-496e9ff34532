module.exports = {
  // ✅ CONFIGURATION PRETTIER POUR LE PROJET
  
  // Largeur de ligne
  printWidth: 100,
  
  // Indentation
  tabWidth: 2,
  useTabs: false,
  
  // Points-virgules
  semi: true,
  
  // Guillemets
  singleQuote: true,
  quoteProps: 'as-needed',
  
  // Virgules finales
  trailingComma: 'es5',
  
  // Espaces dans les objets
  bracketSpacing: true,
  
  // Balises JSX
  bracketSameLine: false,
  jsxSingleQuote: true,
  
  // Fonctions fléchées
  arrowParens: 'avoid',
  
  // Fin de ligne
  endOfLine: 'lf',
  
  // Formatage conditionnel
  requirePragma: false,
  insertPragma: false,
  
  // Prose (markdown)
  proseWrap: 'preserve',
  
  // HTML
  htmlWhitespaceSensitivity: 'css',
  
  // Vue
  vueIndentScriptAndStyle: false,
  
  // ✅ OVERRIDES SPÉCIFIQUES PAR TYPE DE FICHIER
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 80,
        tabWidth: 2
      }
    },
    {
      files: '*.md',
      options: {
        printWidth: 80,
        proseWrap: 'always'
      }
    },
    {
      files: '*.css',
      options: {
        printWidth: 120
      }
    },
    {
      files: ['*.ts', '*.tsx'],
      options: {
        parser: 'typescript',
        printWidth: 100
      }
    },
    {
      files: ['*.js', '*.jsx'],
      options: {
        parser: 'babel',
        printWidth: 100
      }
    }
  ]
};
