#!/usr/bin/env node

/**
 * Script de démarrage stable - Approche séquentielle simple
 * Évite les problèmes de fermeture prématurée
 */

const { spawn } = require('child_process');

console.log('🚀 [STARTUP] Démarrage stable du système TeamCalendar...\n');

// 1. Démarrer le backend
console.log('🔧 [BACKEND] Démarrage du serveur backend...');
const backend = spawn('node', ['server/app.js'], {
  stdio: 'inherit',
  env: { ...process.env, NODE_ENV: 'development' }
});

backend.on('error', (error) => {
  console.error('❌ [BACKEND] Erreur:', error);
  process.exit(1);
});

// 2. Attendre quelques secondes puis démarrer le frontend
setTimeout(() => {
  console.log('\n🎨 [FRONTEND] Démarrage du serveur de développement...');
  
  const frontend = spawn('npm', ['run', 'dev'], {
    stdio: 'inherit',
    shell: true
  });

  frontend.on('error', (error) => {
    console.error('❌ [FRONTEND] Erreur:', error);
    backend.kill();
    process.exit(1);
  });

  // 3. Gestion de l'arrêt propre
  const shutdown = () => {
    console.log('\n🛑 [SHUTDOWN] Arrêt du système...');
    frontend.kill('SIGTERM');
    backend.kill('SIGTERM');
    setTimeout(() => process.exit(0), 2000);
  };

  process.on('SIGINT', shutdown);
  process.on('SIGTERM', shutdown);

}, 5000); // Attendre 5 secondes

console.log('💡 [INFO] Appuyez sur Ctrl+C pour arrêter le système');
console.log('📡 [INFO] Backend: http://localhost:3001');
console.log('🎨 [INFO] Frontend: http://localhost:5173 (ou port suivant disponible)'); 