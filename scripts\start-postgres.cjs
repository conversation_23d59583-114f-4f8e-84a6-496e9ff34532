const { exec } = require('child_process');
const { Pool } = require('pg');

console.log('🐘 DÉMARRAGE ET CONFIGURATION PostgreSQL');
console.log('='.repeat(50));

async function startPostgres() {
  console.log('\n🚀 ÉTAPE 1: Démarrage du service PostgreSQL');
  console.log('-'.repeat(40));
  
  return new Promise((resolve, reject) => {
    // Essayer de démarrer PostgreSQL (Windows)
    exec('net start postgresql-x64-14', (error, stdout, stderr) => {
      if (error) {
        console.log('⚠️  Service PostgreSQL déjà démarré ou erreur:');
        console.log(`   ${error.message}`);
      } else {
        console.log('✅ Service PostgreSQL démarré');
      }
      
      // Essayer une alternative (brew sur macOS/Linux)
      exec('brew services start postgresql', (error2, stdout2, stderr2) => {
        if (error2) {
          console.log('⚠️  Alternative brew non disponible (normal sur Windows)');
        } else {
          console.log('✅ PostgreSQL démarré via brew');
        }
        resolve();
      });
    });
  });
}

async function testConnection() {
  console.log('\n🔗 ÉTAPE 2: Test de connexion');
  console.log('-'.repeat(40));
  
  const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'postgres', // Base par défaut
    password: '',
    port: 5432,
  });
  
  try {
    const client = await pool.connect();
    console.log('✅ Connexion PostgreSQL réussie!');
    
    const result = await client.query('SELECT version()');
    console.log(`📋 Version: ${result.rows[0].version.split(' ')[1]}`);
    
    client.release();
    await pool.end();
    return true;
    
  } catch (error) {
    console.error('❌ Connexion échouée:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Solutions possibles:');
      console.error('   1. Démarrez manuellement PostgreSQL');
      console.error('   2. Vérifiez que le port 5432 est libre');
      console.error('   3. Réinstallez PostgreSQL si nécessaire');
    }
    
    return false;
  }
}

async function createDatabase() {
  console.log('\n🗄️  ÉTAPE 3: Création de la base team_calendar');
  console.log('-'.repeat(40));
  
  const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'postgres',
    password: '',
    port: 5432,
  });
  
  try {
    const client = await pool.connect();
    
    // Vérifier si la base existe
    const checkDB = await client.query(
      "SELECT 1 FROM pg_database WHERE datname = 'team_calendar'"
    );
    
    if (checkDB.rows.length > 0) {
      console.log('✅ Base de données team_calendar existe déjà');
    } else {
      console.log('📝 Création de la base team_calendar...');
      await client.query('CREATE DATABASE team_calendar');
      console.log('✅ Base de données team_calendar créée!');
    }
    
    client.release();
    await pool.end();
    return true;
    
  } catch (error) {
    console.error('❌ Erreur création base:', error.message);
    return false;
  }
}

async function verifyFinalConnection() {
  console.log('\n✅ ÉTAPE 4: Vérification finale');
  console.log('-'.repeat(40));
  
  const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'team_calendar',
    password: '',
    port: 5432,
  });
  
  try {
    const client = await pool.connect();
    console.log('✅ Connexion à team_calendar réussie!');
    
    // Vérifier les tables existantes
    const tables = await client.query(`
      SELECT table_name FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    
    console.log(`📊 ${tables.rows.length} table(s) trouvée(s)`);
    
    if (tables.rows.length === 0) {
      console.log('💡 Base vide - exécutez les migrations pour créer les tables');
    } else {
      tables.rows.forEach((row, i) => {
        console.log(`   ${i+1}. ${row.table_name}`);
      });
    }
    
    client.release();
    await pool.end();
    return true;
    
  } catch (error) {
    console.error('❌ Vérification finale échouée:', error.message);
    return false;
  }
}

// Fonction principale
async function main() {
  try {
    console.log('🏁 Initialisation PostgreSQL pour team_calendar...\n');
    
    // Étape 1: Démarrer PostgreSQL
    await startPostgres();
    
    // Étape 2: Tester la connexion
    const connectionOK = await testConnection();
    if (!connectionOK) {
      console.error('\n❌ Impossible de se connecter à PostgreSQL');
      process.exit(1);
    }
    
    // Étape 3: Créer la base
    const dbCreated = await createDatabase();
    if (!dbCreated) {
      console.error('\n❌ Impossible de créer la base de données');
      process.exit(1);
    }
    
    // Étape 4: Vérification finale
    const finalCheck = await verifyFinalConnection();
    if (!finalCheck) {
      console.error('\n❌ Vérification finale échouée');
      process.exit(1);
    }
    
    console.log('\n🎉 POSTGRESQL PRÊT!');
    console.log('📋 Prochaines étapes:');
    console.log('   1. npm run db:check  - Diagnostic complet');
    console.log('   2. npm run migrate   - Création des tables');
    console.log('   3. npm run server    - Démarrage serveur API');
    
  } catch (error) {
    console.error('\n💥 ERREUR CRITIQUE:', error.message);
    process.exit(1);
  }
}

console.log('🚀 Démarrage du script...\n');
main(); 