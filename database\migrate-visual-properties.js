// ========================================
// 🗃️ MIGRATION : Propriétés Visuelles des Remplacements Ponctuels
// ========================================

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🗃️ [MIGRATION] Début de la migration des propriétés visuelles...');

async function runVisualPropertiesMigration() {
    try {
        console.log('🔗 [MIGRATION] Importation du module de base de données...');
        
        // Importer le module de base de données avec le bon chemin
        let query;
        try {
            const dbModule = await import('../server/config/database.js');
            query = dbModule.query;
            console.log('✅ [MIGRATION] Module de base de données importé');
        } catch (error) {
            console.error('❌ [MIGRATION] Impossible de charger le module de base de données:', error.message);
            return false;
        }
        
        if (!query || typeof query !== 'function') {
            console.error('❌ [MIGRATION] Fonction query non disponible');
            return false;
        }
        
        console.log('🔗 [MIGRATION] Test de connexion à la base de données...');
        
        // Test de connexion simple
        try {
            await query('SELECT 1');
            console.log('✅ [MIGRATION] Connexion à la base de données réussie');
        } catch (error) {
            console.error('❌ [MIGRATION] Erreur de connexion:', error.message);
            return false;
        }
        
        // Ajouter les colonnes une par une pour éviter les erreurs
        const columns = [
            { name: 'shape', type: 'VARCHAR(50)', default: 'NULL' },
            { name: 'is_temporary', type: 'BOOLEAN', default: 'FALSE' },
            { name: 'is_replacement', type: 'BOOLEAN', default: 'FALSE' },
            { name: 'original_assignment_id', type: 'VARCHAR(36)', default: 'NULL' },
            { name: 'replacement_date', type: 'DATE', default: 'NULL' },
            { name: 'replacement_reason', type: 'TEXT', default: 'NULL' },
            { name: 'visual_style', type: 'VARCHAR(50)', default: 'NULL' },
            { name: 'border_style', type: 'VARCHAR(50)', default: 'NULL' },
            { name: 'color_override', type: 'VARCHAR(50)', default: 'NULL' }
        ];

        console.log('📋 [MIGRATION] Ajout des colonnes de propriétés visuelles...');
        
        let addedCount = 0;
        let existingCount = 0;

        for (const column of columns) {
            try {
                const sql = `ALTER TABLE shifts ADD COLUMN IF NOT EXISTS ${column.name} ${column.type} DEFAULT ${column.default};`;
                console.log(`   ⚡ Ajout: ${column.name} (${column.type})`);
                
                await query(sql);
                addedCount++;
                
            } catch (error) {
                if (error.message.includes('already exists')) {
                    console.log(`   ✅ Existe déjà: ${column.name}`);
                    existingCount++;
                } else {
                    console.error(`   ❌ Erreur ${column.name}:`, error.message);
                    throw error;
                }
            }
        }

        console.log(`📊 [MIGRATION] Colonnes ajoutées: ${addedCount}, existantes: ${existingCount}`);
        
        // Ajouter les index
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_shifts_is_replacement ON shifts(is_replacement);',
            'CREATE INDEX IF NOT EXISTS idx_shifts_is_temporary ON shifts(is_temporary);',
            'CREATE INDEX IF NOT EXISTS idx_shifts_original_assignment_id ON shifts(original_assignment_id);',
            'CREATE INDEX IF NOT EXISTS idx_shifts_replacement_date ON shifts(replacement_date);'
        ];

        console.log('🔍 [MIGRATION] Création des index...');
        
        let indexCount = 0;

        for (const indexSql of indexes) {
            try {
                console.log(`   ⚡ Index: ${indexSql.match(/idx_shifts_(\w+)/)[1]}`);
                await query(indexSql);
                indexCount++;
            } catch (error) {
                if (error.message.includes('already exists')) {
                    console.log(`   ✅ Index existe déjà`);
                } else {
                    console.error(`   ❌ Erreur index:`, error.message);
                    // Ne pas arrêter pour les erreurs d'index
                }
            }
        }

        console.log(`📊 [MIGRATION] Index créés: ${indexCount}`);
        
        console.log('✅ [MIGRATION] Migration des propriétés visuelles terminée avec succès !');
        console.log('📋 [MIGRATION] Colonnes ajoutées :');
        console.log('   - shape (VARCHAR(50))');
        console.log('   - is_temporary (BOOLEAN)');
        console.log('   - is_replacement (BOOLEAN)');
        console.log('   - original_assignment_id (VARCHAR(36))');
        console.log('   - replacement_date (DATE)');
        console.log('   - replacement_reason (TEXT)');
        console.log('   - visual_style (VARCHAR(50))');
        console.log('   - border_style (VARCHAR(50))');
        console.log('   - color_override (VARCHAR(50))');
        
        return true;
        
    } catch (error) {
        console.error('❌ [MIGRATION] Erreur lors de la migration:', error);
        return false;
    }
}

// Alternative : Migration via API si la base de données directe n'est pas accessible
async function runMigrationViaAPI() {
    console.log('🌐 [MIGRATION] Tentative de migration via API...');
    
    try {
        const response = await fetch('http://localhost:3001/api/migrate/visual-properties', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                migration: '007_add_visual_properties_columns'
            })
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ [MIGRATION] Migration via API réussie:', result);
            return true;
        } else {
            console.error('❌ [MIGRATION] Échec migration via API:', response.status);
            return false;
        }
        
    } catch (error) {
        console.error('❌ [MIGRATION] Erreur migration via API:', error.message);
        return false;
    }
}

// Fonction principale
async function main() {
    console.log('🚀 [MIGRATION] === MIGRATION DES PROPRIÉTÉS VISUELLES ===');
    
    // Essayer d'abord la migration directe
    let success = await runVisualPropertiesMigration();
    
    // Si échec, essayer via API
    if (!success) {
        console.log('🔄 [MIGRATION] Tentative alternative via API...');
        success = await runMigrationViaAPI();
    }
    
    if (success) {
        console.log('🎉 [MIGRATION] SUCCÈS - La persistance des remplacements ponctuels est maintenant activée !');
        console.log('📋 [MIGRATION] Vous pouvez maintenant tester la persistance après refresh');
    } else {
        console.log('❌ [MIGRATION] ÉCHEC - Veuillez exécuter manuellement le SQL suivant :');
        console.log('');
        console.log('-- Colonnes pour propriétés visuelles');
        console.log('ALTER TABLE shifts ADD COLUMN IF NOT EXISTS shape VARCHAR(50) NULL;');
        console.log('ALTER TABLE shifts ADD COLUMN IF NOT EXISTS is_temporary BOOLEAN DEFAULT FALSE;');
        console.log('ALTER TABLE shifts ADD COLUMN IF NOT EXISTS original_assignment_id VARCHAR(36) NULL;');
        console.log('ALTER TABLE shifts ADD COLUMN IF NOT EXISTS replacement_date DATE NULL;');
        console.log('ALTER TABLE shifts ADD COLUMN IF NOT EXISTS replacement_reason TEXT NULL;');
        console.log('ALTER TABLE shifts ADD COLUMN IF NOT EXISTS visual_style VARCHAR(50) NULL;');
        console.log('ALTER TABLE shifts ADD COLUMN IF NOT EXISTS border_style VARCHAR(50) NULL;');
        console.log('ALTER TABLE shifts ADD COLUMN IF NOT EXISTS color_override VARCHAR(50) NULL;');
    }
}

// Exporter pour utilisation en tant que module
export {
    runVisualPropertiesMigration,
    runMigrationViaAPI
};

// Exécuter si appelé directement
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
} 