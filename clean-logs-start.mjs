#!/usr/bin/env node

/**
 * Script de nettoyage et démarrage propre du système avec logs
 * Évite les sessions multiples et problèmes de cache
 */

import { execSync } from 'child_process';

console.log('🧹 [CLEAN-START] Nettoyage et démarrage propre du système TeamCalendar');
console.log('='.repeat(70));

try {
  // 1. Nettoyer les processus Node existants
  console.log('🔥 [CLEAN] Arrêt des processus Node existants...');
  try {
    execSync('taskkill /F /IM node.exe', { stdio: 'inherit' });
  } catch (e) {
    console.log('   ℹ️  Aucun processus Node à arrêter');
  }

  // 2. Nettoyer localStorage via script JavaScript
  console.log('🧹 [CLEAN] Nettoyage cache navigateur...');
  const cleanupScript = `
    // Nettoie le localStorage des logs pour éviter les sessions multiples
    if (typeof localStorage !== 'undefined') {
      const keys = Object.keys(localStorage);
      const logKeys = keys.filter(k => k.startsWith('logs_'));
      logKeys.forEach(key => {
        console.log('🗑️ Suppression cache:', key);
        localStorage.removeItem(key);
      });
      console.log('✅ Cache localStorage nettoyé');
    }
  `;
  
  console.log('   💡 Script de nettoyage préparé (sera exécuté au démarrage du navigateur)');

  // 3. Attendre un peu pour que les processus se ferment proprement
  console.log('⏳ [CLEAN] Attente fermeture processus (2s)...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  // 4. Démarrer le système
  console.log('🚀 [START] Démarrage du système...');
  console.log('='.repeat(70));
  
  execSync('npm run dev:system', { stdio: 'inherit' });

} catch (error) {
  console.error('❌ [CLEAN-START] Erreur:', error.message);
  process.exit(1);
} 