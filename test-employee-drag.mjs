
// Script de test pour vérifier le drag & drop
console.log('🧪 [TEST] Test du système de drag & drop...');

// Simuler un déplacement
if (window.TeamCalendarApp) {
  const app = window.TeamCalendarApp;
  
  console.log('📊 [TEST] Positions initiales:');
  app.logEmployeePositions?.('TEST INITIAL');
  
  // Simuler un reorder
  if (app.data.employees.length > 1) {
    console.log('🔄 [TEST] Simulation d'un déplacement...');
    app.reorderEmployees(0, 1);
  }
}
