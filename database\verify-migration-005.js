import { query } from '../server/config/database.js';

async function verifyMigration005() {
    try {
        console.log('🔍 [Vérification Migration 005] Début de la vérification...');
        
        // 1. Vérifier que la colonne excluded_dates existe
        console.log('\n📋 [Étape 1] Vérification de l\'existence de la colonne excluded_dates...');
        const columnCheck = await query(`
            SELECT 
                column_name,
                data_type,
                is_nullable,
                column_default,
                udt_name
            FROM information_schema.columns 
            WHERE table_name = 'regular_assignments' 
            AND column_name = 'excluded_dates'
        `);
        
        if (columnCheck.rows.length === 0) {
            console.error('❌ [Étape 1] ÉCHEC : Colonne excluded_dates non trouvée !');
            return false;
        }
        
        const column = columnCheck.rows[0];
        console.log('✅ [Étape 1] Colonne excluded_dates trouvée :');
        console.log(`   - Type de données : ${column.data_type} (${column.udt_name})`);
        console.log(`   - Nullable : ${column.is_nullable}`);
        console.log(`   - Valeur par défaut : ${column.column_default}`);
        
        // 2. Vérifier que l'index GIN existe
        console.log('\n📋 [Étape 2] Vérification de l\'index GIN...');
        const indexCheck = await query(`
            SELECT 
                indexname,
                indexdef
            FROM pg_indexes 
            WHERE tablename = 'regular_assignments' 
            AND indexname = 'idx_regular_assignments_excluded_dates'
        `);
        
        if (indexCheck.rows.length === 0) {
            console.error('❌ [Étape 2] ÉCHEC : Index GIN non trouvé !');
            return false;
        }
        
        console.log('✅ [Étape 2] Index GIN trouvé :');
        console.log(`   - Nom : ${indexCheck.rows[0].indexname}`);
        console.log(`   - Définition : ${indexCheck.rows[0].indexdef}`);
        
        // 3. Vérifier la structure complète de la table
        console.log('\n📋 [Étape 3] Vérification de la structure complète de la table...');
        const tableStructure = await query(`
            SELECT 
                column_name,
                data_type,
                is_nullable,
                column_default
            FROM information_schema.columns 
            WHERE table_name = 'regular_assignments'
            ORDER BY ordinal_position
        `);
        
        console.log('✅ [Étape 3] Structure de la table regular_assignments :');
        tableStructure.rows.forEach((col, index) => {
            console.log(`   ${index + 1}. ${col.column_name} (${col.data_type}) - Nullable: ${col.is_nullable}`);
        });
        
        // 4. Test d'insertion et de mise à jour avec excluded_dates
        console.log('\n📋 [Étape 4] Test d\'insertion avec excluded_dates...');
        
        // Vérifier qu'il y a au moins un employé et un poste pour le test
        const employees = await query('SELECT id FROM employees LIMIT 1');
        const posts = await query('SELECT id FROM standard_posts LIMIT 1');
        
        if (employees.rows.length === 0 || posts.rows.length === 0) {
            console.log('⚠️ [Étape 4] IGNORÉ : Pas d\'employés ou de postes pour le test');
        } else {
            const testEmployeeId = employees.rows[0].id;
            const testPostId = posts.rows[0].id;
            
            // Insérer un test avec excluded_dates
            const testId = 'test-migration-005-' + Date.now();
            await query(`
                INSERT INTO regular_assignments 
                (id, employee_id, post_id, days_of_week, start_date, excluded_dates, is_active)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
            `, [
                testId,
                testEmployeeId,
                testPostId,
                [1, 2, 3, 4, 5],
                '2025-06-20',
                ['2025-06-25', '2025-06-26'],
                true
            ]);
            
            // Vérifier que l'insertion a fonctionné
            const insertCheck = await query(`
                SELECT id, excluded_dates 
                FROM regular_assignments 
                WHERE id = $1
            `, [testId]);
            
            if (insertCheck.rows.length > 0) {
                console.log('✅ [Étape 4] Test d\'insertion réussi :');
                console.log(`   - ID : ${insertCheck.rows[0].id}`);
                console.log(`   - Dates exclues : ${JSON.stringify(insertCheck.rows[0].excluded_dates)}`);
                
                // Nettoyer le test
                await query('DELETE FROM regular_assignments WHERE id = $1', [testId]);
                console.log('🧹 [Étape 4] Données de test nettoyées');
            } else {
                console.error('❌ [Étape 4] ÉCHEC : Insertion de test échouée !');
                return false;
            }
        }
        
        // 5. Vérifier les contraintes et commentaires
        console.log('\n📋 [Étape 5] Vérification des commentaires...');
        const commentCheck = await query(`
            SELECT 
                col_description(c.oid, a.attnum) as comment
            FROM pg_class c
            JOIN pg_attribute a ON a.attrelid = c.oid
            WHERE c.relname = 'regular_assignments'
            AND a.attname = 'excluded_dates'
            AND NOT a.attisdropped
        `);
        
        if (commentCheck.rows.length > 0 && commentCheck.rows[0].comment) {
            console.log('✅ [Étape 5] Commentaire de colonne trouvé :');
            console.log(`   - ${commentCheck.rows[0].comment}`);
        } else {
            console.log('⚠️ [Étape 5] Aucun commentaire trouvé (optionnel)');
        }
        
        console.log('\n🎉 [Vérification Migration 005] TOUTES LES VÉRIFICATIONS RÉUSSIES !');
        console.log('✅ La colonne excluded_dates est correctement configurée et fonctionnelle');
        console.log('✅ L\'index GIN est en place pour les performances');
        console.log('✅ Les opérations d\'insertion/mise à jour fonctionnent correctement');
        
        return true;
        
    } catch (error) {
        console.error('❌ [Vérification Migration 005] Erreur :', error);
        return false;
    }
}

// Exécuter si appelé directement
if (import.meta.url === `file://${process.argv[1]}`) {
    verifyMigration005()
        .then((success) => {
            if (success) {
                console.log('\n🎯 [Résultat] Migration 005 vérifiée avec succès !');
                process.exit(0);
            } else {
                console.log('\n💥 [Résultat] Échec de la vérification de la migration 005');
                process.exit(1);
            }
        })
        .catch((error) => {
            console.error('\n💥 [Erreur critique]', error);
            process.exit(1);
        });
}

export { verifyMigration005 }; 