const { Pool } = require('pg');

console.log('🔗 Test de connexion PostgreSQL DISTANT...');

const pool = new Pool({
  host: '*************',
  port: 5432,
  database: 'glive_db',
  user: 'postgres',
  password: 'SebbZ12342323!!'
});

async function testConnection() {
  try {
    console.log('📡 Tentative de connexion...');
    console.log(`   🌐 Serveur distant: *************:5432`);
    console.log(`   🗄️  Base de données: glive_db`);
    console.log(`   👤 Utilisateur: postgres`);
    
    const client = await pool.connect();
    console.log('✅ Connexion réussie!');
    
    // Tester la latence
    const startTime = Date.now();
    const result = await client.query('SELECT COUNT(*) as total FROM shifts');
    const latency = Date.now() - startTime;
    
    console.log(`📊 Nombre de quarts: ${result.rows[0].total}`);
    console.log(`⚡ Latence requête: ${latency}ms`);
    
    const employees = await client.query('SELECT COUNT(*) as total FROM employees');
    console.log(`👥 Nombre d'employés: ${employees.rows[0].total}`);
    
    // Test version PostgreSQL
    const version = await client.query('SELECT version()');
    console.log(`📋 Version PostgreSQL: ${version.rows[0].version.split(' ')[1]}`);
    
    client.release();
    await pool.end();
    
    console.log('\n🎉 Connexion à la base distante FONCTIONNELLE!');
    
  } catch (error) {
    console.error('❌ Erreur de connexion:', error.message);
    console.error('Code:', error.code);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Le serveur ************* refuse la connexion');
    } else if (error.code === 'ENOTFOUND') {
      console.error('💡 Serveur ************* introuvable');
    } else if (error.code === '28P01') {
      console.error('💡 Mot de passe incorrect');
    } else if (error.code === '3D000') {
      console.error('💡 Base de données glive_db introuvable');
    }
    
    process.exit(1);
  }
}

testConnection(); 