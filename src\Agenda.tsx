import React, { useEffect, useRef } from 'react'
import TeamCalendarApp from './teamCalendarApp.ts'
import './sidebar.css'
import './styles/fullscreen.css'

interface AgendaProps {}

const AgendaFullscreen: React.FC<AgendaProps> = () => {
  const appRef = useRef<any>(null)
  const initRef = useRef(false)
  const mountedRef = useRef(true)

  useEffect(() => {
    // ✅ Protection contre la double initialisation MAIS permettre la réinitialisation si nécessaire
    if (initRef.current && (window as any).team?.employees?.length > 0) {
      console.warn('⚠️ [Agenda] Initialisation déjà effectuée avec succès - IGNORÉ');
      return;
    }
    
    if (initRef.current) {
      console.log('🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)');
    }
    
    initRef.current = true;
    mountedRef.current = true;

    const initApp = async () => {
      try {
        console.log('🚀 [Agenda] Initialisation TeamCalendarApp...');

        // ✅ CORRECTION CRITIQUE : Rendre TeamCalendarApp disponible globalement AVANT l'initialisation
        (window as any).teamCalendarApp = TeamCalendarApp;
        (window as any).TeamCalendarApp = TeamCalendarApp;

        console.log('🔍 [Agenda] TeamCalendarApp assigné à window:', {
          teamCalendarApp: !!(window as any).teamCalendarApp,
          TeamCalendarApp: !!(window as any).TeamCalendarApp
        });

        await TeamCalendarApp.init()

        // ✅ VÉRIFICATION POST-INITIALISATION
        console.log('🔍 [Agenda] Vérification post-init:', {
          teamCalendarApp: !!(window as any).teamCalendarApp,
          TeamCalendarApp: !!(window as any).TeamCalendarApp,
          detectDropDateFromPosition: !!(window as any).teamCalendarApp?.detectDropDateFromPosition,
          showConfirmationMenu: !!(window as any).teamCalendarApp?.showConfirmationMenu,
          handleRegularAssignmentDrop: !!(window as any).teamCalendarApp?.handleRegularAssignmentDrop
        });

        if (mountedRef.current) {
          console.log('🎉 [Agenda] Application fullscreen initialisée avec succès')
        }
      } catch (error) {
        if (mountedRef.current) {
          console.error('❌ [Agenda] Erreur lors de l\'initialisation:', error)
        }
      }
    }

    const timer = setTimeout(initApp, 200)
    
    return () => {
      mountedRef.current = false;
      clearTimeout(timer);
      
      // ✅ Log du nettoyage - TeamCalendarApp gère son propre cleanup
      console.log('🧹 [Agenda] Nettoyage composant Agenda');
    }
  }, [])

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return (
    <div className="fullscreen-app h-screen w-screen overflow-hidden bg-gradient-to-br from-slate-100 to-sky-100 text-slate-900 flex" 
         style={{fontFamily: 'Inter, "Noto Sans", sans-serif'}}>
      
      {/* SIDEBAR FIXE RÉTRACTABLE */}
      <aside className="sidebar-fixed group fixed left-0 top-0 h-full bg-white/95 backdrop-blur-md border-r border-slate-200/60 shadow-2xl z-50 w-16 hover:w-64 transition-all duration-300">
        <div className="sidebar-content flex flex-col h-full">
          
          {/* Logo/Brand */}
          <div className="sidebar-brand h-16 flex items-center justify-center border-b border-slate-200/50 flex-shrink-0">
            <div className="size-8 text-blue-600 group-hover:scale-110 transition-transform duration-300">
              <svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                <path d="M44 4H30.6666V17.3334H17.3334V30.6666H4V44H44V4Z" fill="currentColor" />
              </svg>
            </div>
            <span className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 font-bold text-slate-800 whitespace-nowrap">
              TeamCalendar
            </span>
          </div>
          
          {/* Navigation */}
          <nav className="sidebar-nav flex-1 p-2 space-y-1">
            <a href="#calendar" className="sidebar-link active flex items-center p-3 rounded-xl bg-blue-100/80 text-blue-700 shadow-md">
              <span className="material-icons-outlined text-xl">calendar_view_week</span>
              <span className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap font-medium">Calendrier</span>
            </a>
            
            <a href="#employees" className="sidebar-link flex items-center p-3 rounded-xl text-slate-600 hover:bg-slate-100/80 hover:text-slate-800">
              <span className="material-icons-outlined text-xl">people</span>
              <span className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap font-medium">Employés</span>
            </a>
            
            <a href="/logs" className="sidebar-link flex items-center p-3 rounded-xl text-slate-600 hover:bg-slate-100/80 hover:text-slate-800">
              <span className="material-icons-outlined text-xl">bug_report</span>
              <span className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap font-medium">Logs</span>
            </a>
            
            <button id="sidebar-settings-btn" className="sidebar-link flex items-center p-3 rounded-xl text-slate-600 hover:bg-slate-100/80 hover:text-slate-800 w-full text-left">
              <span className="material-icons-outlined text-xl">settings</span>
              <span className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap font-medium">Paramètres</span>
            </button>
          </nav>
          
          {/* Pied de sidebar */}
          <div className="sidebar-footer p-4 border-t border-slate-200/50 flex-shrink-0">
            <div className="flex items-center">
              <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-full size-8 flex items-center justify-center text-white font-bold text-sm">
                A
              </div>
              <div className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <p className="text-sm font-medium text-slate-700 whitespace-nowrap">Admin</p>
                <p className="text-xs text-slate-500 whitespace-nowrap">En ligne</p>
              </div>
            </div>
          </div>
        </div>
      </aside>
      
      {/* CONTENU PRINCIPAL AVEC MARGE SIDEBAR */}
      <div className="main-content flex flex-col w-full h-full ml-16">
        
        {/* HEADER FIXE */}
        <header className="header-fixed h-16 flex items-center justify-between px-6 bg-white/95 backdrop-blur-md border-b border-slate-300/60 shadow-sm flex-shrink-0 z-40">
          <div className="flex items-center gap-3">
            <h2 className="text-slate-900 text-xl font-semibold">Calendrier d'Équipe</h2>
          </div>
          
          <div className="flex items-center gap-4">
            {/* Navigation temporelle */}
            <nav className="hidden md:flex items-center gap-2">
              <button className="nav-btn text-slate-600 hover:text-blue-600 text-sm font-medium px-3 py-2 rounded-lg hover:bg-slate-200/70">Aujourd'hui</button>
              <button className="nav-btn text-blue-600 bg-blue-100/80 text-sm font-semibold px-3 py-2 rounded-lg shadow-sm">Semaine</button>
              <button className="nav-btn text-slate-600 hover:text-blue-600 text-sm font-medium px-3 py-2 rounded-lg hover:bg-slate-200/70">Mois</button>
            </nav>
            
            {/* Actions header */}
            <div className="flex items-center gap-2">
              <button id="settings-btn" className="header-btn flex items-center justify-center rounded-lg h-10 w-10 text-slate-500 hover:bg-slate-200/70 hover:text-slate-700">
                <span className="material-icons-outlined text-xl">settings</span>
              </button>
              <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-full size-10 flex items-center justify-center text-white font-bold">
                A
              </div>
            </div>
          </div>
        </header>
        
        {/* ZONE DE CONTENU COMPARTIMENTÉE */}
        <main className="content-area flex-1 flex flex-col overflow-hidden">
          
          {/* BARRE DE CONTRÔLES FIXE */}
          <section className="controls-bar h-20 flex items-center justify-between px-6 py-4 bg-white/80 backdrop-blur-sm border-b border-slate-200/40 flex-shrink-0">
            <div className="flex items-center gap-3">
              <button id="prev-week-btn" className="control-btn p-2.5 rounded-full hover:bg-slate-200/80 text-slate-500 hover:text-slate-700">
                <span className="material-icons-outlined text-xl">chevron_left</span>
              </button>
              <h2 id="current-week-display" className="week-display text-slate-900 text-lg font-semibold min-w-[300px] text-center">
                13 - 19 Octobre, 2024
              </h2>
              <button id="next-week-btn" className="control-btn p-2.5 rounded-full hover:bg-slate-200/80 text-slate-500 hover:text-slate-700">
                <span className="material-icons-outlined text-xl">chevron_right</span>
              </button>
            </div>
            
            <div className="flex items-center gap-3">
              <button className="action-btn flex items-center gap-2 text-slate-600 bg-white hover:bg-slate-50 border border-slate-300 font-medium py-2 px-4 rounded-lg text-sm shadow-sm">
                <span className="material-icons-outlined text-lg">upload</span>
                Exporter
              </button>
              <button id="add-shift-button" className="action-btn flex items-center gap-2 text-white bg-blue-600 hover:bg-blue-700 font-medium py-2 px-4 rounded-lg text-sm shadow-sm">
                <span className="material-icons-outlined text-lg">add</span>
                Ajouter shift
              </button>
            </div>
          </section>
          
          {/* GRILLE CALENDRIER ADAPTATIVE */}
          <section className="calendar-grid flex-1 overflow-hidden">
            <div className="calendar-container h-full border border-slate-300/40 rounded-lg shadow-lg bg-white/70 mx-6 mb-6" id="schedule-container">
              
              {/* GRILLE UNIFIÉE : EMPLOYÉS + JOURS DANS LA MÊME STRUCTURE */}
              <div className="calendar-unified-grid h-full overflow-hidden" id="schedule-grid-scrollable-content">
                
                {/* HEADER ROW : Employé(e) + Jours de la semaine */}
                <div className="calendar-header grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-300/40 bg-slate-50/80 h-16 flex-shrink-0">
                  {/* Header Employé */}
                  <div className="header-employee flex items-center justify-start pl-6 border-r border-slate-300/40">
                    <p className="text-slate-700 text-sm font-semibold tracking-wider">Employé(e)</p>
                  </div>
                  
                  {/* Headers Jours - Ces éléments seront remplis par JavaScript */}
                  <div id="day-header-0" className="header-day flex items-center justify-center border-r border-slate-300/40 text-xs font-semibold text-slate-600"></div>
                  <div id="day-header-1" className="header-day flex items-center justify-center border-r border-slate-300/40 text-xs font-semibold text-slate-600"></div>
                  <div id="day-header-2" className="header-day flex items-center justify-center border-r border-slate-300/40 text-xs font-semibold text-slate-600"></div>
                  <div id="day-header-3" className="header-day flex items-center justify-center border-r border-slate-300/40 text-xs font-semibold text-slate-600"></div>
                  <div id="day-header-4" className="header-day flex items-center justify-center border-r border-slate-300/40 text-xs font-semibold text-slate-600"></div>
                  <div id="day-header-5" className="header-day flex items-center justify-center border-r border-slate-300/40 text-xs font-semibold text-slate-600"></div>
                  <div id="day-header-6" className="header-day flex items-center justify-center text-xs font-semibold text-slate-600 border-r border-slate-300/40"></div>
                </div>
                
                {/* SECTION EMPLOYÉS : Chaque ligne = employé + ses 7 colonnes de jours */}
                <div className="employees-section flex-1 overflow-y-auto min-h-0">
                  <div id="employee-rows-container" className="space-y-0">
                    {/* Les lignes d'employés seront injectées ici par JavaScript */}
                    {/* Structure attendue pour chaque employé :
                    <div class="employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px]">
                      <div class="employee-info">Info employé</div>
                      <div class="day-cell">Cellule jour 0</div>
                      <div class="day-cell">Cellule jour 1</div>
                      ...etc
                    </div>
                    */}
                  </div>
                </div>
                
                {/* SECTION POSTES DISPONIBLES : Remplacée par la grille unifiée JavaScript */}
                <div className="posts-section border-t border-slate-200 flex-shrink-0">
                  {/* Header Postes */}
                  <div className="posts-header grid grid-cols-[240px_repeat(7,1fr)] h-14 border-b border-slate-300/40 bg-slate-50/80">
                    <div className="posts-title flex items-center pl-6 border-r border-slate-300/40">
                      <h3 className="text-slate-600 text-sm font-semibold tracking-wider">Postes disponibles</h3>
                    </div>
                    {/* Colonnes vides pour les jours */}
                    <div className="border-r border-slate-300/40"></div>
                    <div className="border-r border-slate-300/40"></div>
                    <div className="border-r border-slate-300/40"></div>
                    <div className="border-r border-slate-300/40"></div>
                    <div className="border-r border-slate-300/40"></div>
                    <div className="border-r border-slate-300/40"></div>
                    <div className="border-r border-slate-300/40"></div>
                  </div>
                  
                  {/* ✅ NOUVELLE GRILLE : Liste des postes avec grille complète (générée par renderUnifiedCalendar) */}
                  <div className="posts-list grid grid-cols-[240px_repeat(7,1fr)] max-h-64 overflow-y-auto">
                    {/* Le contenu sera entièrement remplacé par JavaScript renderUnifiedCalendar() */}
                    
                    {/* ✅ CONTENEUR VISIBLE : Maintenu pour compatibilité drag & drop avec diagnostic */}
                    <div id="available-posts-container" className="col-span-8 h-0 overflow-hidden">
                      {/* Les éléments draggables simples seront injectés ici pour le drag & drop */}
                    </div>
                  </div>
                </div>
                
              </div>
            </div>
          </section>
          
          {/* BARRE DE STATUTS FIXE EN BAS */}
          <section className="stats-bar h-16 px-6 py-3 bg-white/80 backdrop-blur-sm border-t border-slate-200/40 flex-shrink-0">
            <div id="stats-output" className="text-sm text-slate-600 flex items-center"></div>
          </section>
        </main>
      </div>
      
      {/* MODAL PARAMÈTRES COMPLET */}
      <div id="settings-modal" className="hidden fixed inset-0 bg-black/40 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl h-[90vh] flex flex-col">
          <header className="flex items-center justify-between p-4 border-b border-slate-200 flex-shrink-0">
            <div className="flex items-center gap-3">
              <span className="material-icons-outlined text-blue-600 text-2xl">settings</span>
              <h3 className="text-lg font-semibold text-slate-800">Paramètres de l'Application</h3>
            </div>
            <button id="settings-modal-close" className="p-2 rounded-full hover:bg-slate-200/70">
              <span className="material-icons-outlined text-slate-600">close</span>
            </button>
          </header>
          
          <div className="p-2 border-b border-slate-200 flex-shrink-0">
            <div className="flex gap-2 flex-wrap">
              <button id="tab-posts" className="tab-btn active px-4 py-2 rounded-lg text-sm font-medium">Postes</button>
              <button id="tab-vacations" className="tab-btn px-4 py-2 rounded-lg text-sm font-medium">Vacances</button>
              <button id="tab-assignments" className="tab-btn px-4 py-2 rounded-lg text-sm font-medium">Attributions</button>
              <button id="tab-employee-templates" className="tab-btn px-4 py-2 rounded-lg text-sm font-medium">🗂️ Modèle de fiche</button>
              <button id="tab-employees" className="tab-btn px-4 py-2 rounded-lg text-sm font-medium">👤 Gestion employés</button>
              <button id="tab-general" className="tab-btn px-4 py-2 rounded-lg text-sm font-medium">Général</button>
            </div>
          </div>
          
          {/* Contenu modal adaptatif */}
          <div className="flex-1 overflow-hidden">
            
            {/* Onglet Postes */}
            <div id="tab-content-posts" className="tab-content h-full p-4 overflow-y-auto">
              <div id="posts-config-container" className="space-y-3 mb-4"></div>
              <button id="add-post-btn" className="w-full flex items-center justify-center gap-2 text-white bg-blue-600 hover:bg-blue-700 font-medium py-2.5 px-4 rounded-xl text-sm shadow-lg">
                <span className="material-icons-outlined text-xl">add</span>
                Ajouter un nouveau poste
              </button>
            </div>
            
            {/* Onglet Vacances */}
            <div id="tab-content-vacations" className="tab-content hidden h-full p-4 overflow-y-auto">
              <div className="mb-4">
                <h4 className="font-medium text-slate-700 mb-3">Périodes de Vacances Globales</h4>
                <div id="global-vacations-list" className="space-y-2 mb-4"></div>
                <button id="add-vacation-btn" className="w-full flex items-center justify-center gap-2 text-white bg-green-600 hover:bg-green-700 font-medium py-2.5 px-4 rounded-xl text-sm shadow-lg">
                  <span className="material-icons-outlined text-xl">add</span>
                  Ajouter une période
                </button>
              </div>
            </div>
            
            {/* Onglet Attributions */}
            <div id="tab-content-assignments" className="tab-content hidden h-full p-4 overflow-y-auto">
              <div className="mb-4">
                <h4 className="font-medium text-slate-700 mb-3">Attributions Automatiques</h4>
                <div id="regular-assignments-list" className="space-y-2 mb-4"></div>
                <div className="flex gap-2">
                  <button id="add-assignment-btn" className="flex-1 flex items-center justify-center gap-2 text-white bg-purple-600 hover:bg-purple-700 font-medium py-2.5 px-4 rounded-xl text-sm shadow-lg">
                    <span className="material-icons-outlined text-xl">add</span>
                    Ajouter une attribution
                  </button>
                  <button id="remove-assignments-btn" className="flex-1 flex items-center justify-center gap-2 text-white bg-red-600 hover:bg-red-700 font-medium py-2.5 px-4 rounded-xl text-sm shadow-lg">
                    <span className="material-icons-outlined text-xl">delete_sweep</span>
                    Retirer attributions
                  </button>
                </div>
              </div>
            </div>
            
            {/* Onglet Modèle de fiche employé */}
            <div id="tab-content-employee-templates" className="tab-content hidden h-full p-4 overflow-y-auto">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-slate-700 flex items-center gap-2">
                      <span className="material-icons-outlined text-blue-600">description</span>
                      Modèles de Fiches Employé
                    </h4>
                    <p className="text-sm text-slate-500 mt-1">Configurez les champs disponibles pour les fiches employé</p>
                  </div>
                  <button id="add-template-btn" className="flex items-center gap-2 text-white bg-blue-600 hover:bg-blue-700 font-medium py-2 px-4 rounded-lg text-sm shadow-lg">
                    <span className="material-icons-outlined text-lg">add</span>
                    Nouveau modèle
                  </button>
                </div>
                <div id="employee-templates-list" className="space-y-3"></div>
              </div>
            </div>
            
            {/* Onglet Gestion des employés */}
            <div id="tab-content-employees" className="tab-content hidden h-full p-4 overflow-y-auto">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-slate-700 flex items-center gap-2">
                      <span className="material-icons-outlined text-green-600">people</span>
                      Gestion des Employés
                    </h4>
                    <p className="text-sm text-slate-500 mt-1">Modifiez les informations personnelles et avatars de vos employés</p>
                  </div>
                  <button id="add-employee-btn" className="flex items-center gap-2 text-white bg-green-600 hover:bg-green-700 font-medium py-2 px-4 rounded-lg text-sm shadow-lg">
                    <span className="material-icons-outlined text-lg">person_add</span>
                    Nouvel employé
                  </button>
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-2">Modèle de fiche à utiliser</label>
                  <select id="template-selector" className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></select>
                </div>
                <div id="employees-management-list" className="space-y-3"></div>
              </div>
            </div>
            
            {/* Onglet Paramètres Généraux */}
            <div id="tab-content-general" className="tab-content hidden h-full p-4 overflow-y-auto">
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium text-slate-700 mb-3 flex items-center gap-2">
                    <span className="material-icons-outlined text-blue-600">calendar_view_week</span>
                    Affichage de la Semaine
                  </h4>
                  <div className="bg-slate-50 p-4 rounded-lg space-y-3">
                    <label className="block text-sm font-medium text-slate-700 mb-2">Premier jour de la semaine</label>
                    <div id="week-start-container" className="grid grid-cols-4 gap-3">
                      <label className="flex items-center gap-2 cursor-pointer"><input type="radio" name="week-start" value="monday" className="text-blue-600 focus:ring-blue-500" /> <span className="text-sm">Lundi</span></label>
                      <label className="flex items-center gap-2 cursor-pointer"><input type="radio" name="week-start" value="tuesday" className="text-blue-600 focus:ring-blue-500" /> <span className="text-sm">Mardi</span></label>
                      <label className="flex items-center gap-2 cursor-pointer"><input type="radio" name="week-start" value="wednesday" className="text-blue-600 focus:ring-blue-500" /> <span className="text-sm">Mercredi</span></label>
                      <label className="flex items-center gap-2 cursor-pointer"><input type="radio" name="week-start" value="thursday" className="text-blue-600 focus:ring-blue-500" /> <span className="text-sm">Jeudi</span></label>
                      <label className="flex items-center gap-2 cursor-pointer"><input type="radio" name="week-start" value="friday" className="text-blue-600 focus:ring-blue-500" /> <span className="text-sm">Vendredi</span></label>
                      <label className="flex items-center gap-2 cursor-pointer"><input type="radio" name="week-start" value="saturday" className="text-blue-600 focus:ring-blue-500" /> <span className="text-sm">Samedi</span></label>
                      <label className="flex items-center gap-2 cursor-pointer"><input type="radio" name="week-start" value="sunday" className="text-blue-600 focus:ring-blue-500" defaultChecked /> <span className="text-sm">Dimanche</span></label>
                    </div>
                    <p className="text-xs text-slate-500 mt-2">Modifie l'ordre d'affichage des jours dans le calendrier</p>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium text-slate-700 mb-3 flex items-center gap-2">
                    <span className="material-icons-outlined text-green-600">storage</span>
                    Données de l'Application
                  </h4>
                  <div className="bg-slate-50 p-4 rounded-lg space-y-3">
                    <div className="flex gap-3">
                      <button id="export-data-btn" className="flex-1 flex items-center justify-center gap-2 text-blue-600 bg-blue-50 hover:bg-blue-100 border border-blue-200 font-medium py-2.5 px-4 rounded-lg text-sm">
                        <span className="material-icons-outlined text-lg">download</span>
                        Exporter
                      </button>
                      <button id="import-data-btn" className="flex-1 flex items-center justify-center gap-2 text-green-600 bg-green-50 hover:bg-green-100 border border-green-200 font-medium py-2.5 px-4 rounded-lg text-sm">
                        <span className="material-icons-outlined text-lg">upload</span>
                        Importer
                      </button>
                    </div>
                    <button id="reset-data-btn" className="w-full flex items-center justify-center gap-2 text-red-600 bg-red-50 hover:bg-red-100 border border-red-200 font-medium py-2.5 px-4 rounded-lg text-sm">
                      <span className="material-icons-outlined text-lg">refresh</span>
                      Réinitialiser toutes les données
                    </button>
                    <p className="text-xs text-slate-500">Les données sont sauvegardées automatiquement dans votre navigateur</p>
                  </div>
                </div>
              </div>
            </div>
            
          </div>
        </div>
      </div>
      
      {/* Modal pour le menu d'attribution lors du drop */}
      <div id="assignment-context-modal" className="hidden fixed inset-0 bg-black/40 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md transform transition-all duration-300 ease-in-out">
          <header className="flex items-center justify-between p-4 border-b border-slate-200">
            <div className="flex items-center gap-3">
              <span className="material-icons-outlined text-blue-600 text-2xl">schedule</span>
              <h3 className="text-lg font-semibold text-slate-800">Type d'Attribution</h3>
            </div>
            <button id="assignment-context-close" className="p-2 rounded-full hover:bg-slate-200/70">
              <span className="material-icons-outlined text-slate-600">close</span>
            </button>
          </header>
          <div className="p-6 space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <div className="flex items-center gap-3 mb-2">
                <div className="size-8 rounded-full bg-blue-100 flex items-center justify-center">
                  <span className="material-icons-outlined text-blue-600 text-sm">person</span>
                </div>
                <div>
                  <p id="assignment-employee-name" className="text-sm font-medium text-slate-800"></p>
                  <p id="assignment-post-name" className="text-xs text-slate-500"></p>
                </div>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-3">Choisissez le type d'attribution :</label>
              <div className="space-y-3">
                <label className="flex items-start gap-3 p-4 rounded-lg border border-slate-200 hover:bg-blue-50 hover:border-blue-300 cursor-pointer transition-all duration-200">
                  <input type="radio" name="assignment-type" value="once" className="mt-1 text-blue-600 focus:ring-blue-500" defaultChecked />
                  <div className="flex items-start gap-3 flex-1">
                    <div className="size-10 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                      <span className="material-icons-outlined text-green-600 text-lg">event</span>
                    </div>
                    <div className="flex-1">
                      <span className="text-sm font-semibold text-slate-800 flex items-center gap-2">
                        Attribution ponctuelle
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Cette semaine</span>
                      </span>
                      <p className="text-xs text-slate-500 mt-1 leading-relaxed">Assigner uniquement pour cette semaine actuelle</p>
                    </div>
                  </div>
                </label>
                
                <label id="single-cell-option" className="hidden flex items-start gap-3 p-4 rounded-lg border border-slate-200 hover:bg-cyan-50 hover:border-cyan-300 cursor-pointer transition-all duration-200">
                  <input type="radio" name="assignment-type" value="single-cell" className="mt-1 text-blue-600 focus:ring-blue-500" />
                  <div className="flex items-start gap-3 flex-1">
                    <div className="size-10 rounded-full bg-cyan-100 flex items-center justify-center flex-shrink-0">
                      <span className="material-icons-outlined text-cyan-600 text-lg">crop_free</span>
                    </div>
                    <div className="flex-1">
                      <span className="text-sm font-semibold text-slate-800 flex items-center gap-2">
                        Attribution unique (cellule)
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800">Une cellule</span>
                      </span>
                      <p className="text-xs text-slate-500 mt-1 leading-relaxed">Assigner uniquement sur cette cellule spécifique</p>
                    </div>
                  </div>
                </label>
                
                <label className="flex items-start gap-3 p-4 rounded-lg border border-slate-200 hover:bg-purple-50 hover:border-purple-300 cursor-pointer transition-all duration-200">
                  <input type="radio" name="assignment-type" value="regular-indefinite" className="mt-1 text-blue-600 focus:ring-blue-500" />
                  <div className="flex items-start gap-3 flex-1">
                    <div className="size-10 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0">
                      <span className="material-icons-outlined text-purple-600 text-lg">repeat</span>
                    </div>
                    <div className="flex-1">
                      <span className="text-sm font-semibold text-slate-800 flex items-center gap-2">
                        Attribution régulière permanente
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">Indéfini</span>
                      </span>
                      <p className="text-xs text-slate-500 mt-1 leading-relaxed">Assigner automatiquement chaque semaine</p>
                    </div>
                  </div>
                </label>
                
                <label className="flex items-start gap-3 p-4 rounded-lg border border-slate-200 hover:bg-amber-50 hover:border-amber-300 cursor-pointer transition-all duration-200">
                  <input type="radio" name="assignment-type" value="regular-limited" className="mt-1 text-blue-600 focus:ring-blue-500" />
                  <div className="flex items-start gap-3 flex-1">
                    <div className="size-10 rounded-full bg-amber-100 flex items-center justify-center flex-shrink-0">
                      <span className="material-icons-outlined text-amber-600 text-lg">schedule</span>
                    </div>
                    <div className="flex-1">
                      <span className="text-sm font-semibold text-slate-800 flex items-center gap-2">
                        Attribution régulière temporaire
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">Limité</span>
                      </span>
                      <p className="text-xs text-slate-500 mt-1 leading-relaxed">Assigner automatiquement jusqu'à une date précise</p>
                    </div>
                  </div>
                </label>
              </div>
            </div>
            
            <div id="regular-assignment-options" className="hidden space-y-3">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">Date de début de l'attribution</label>
                <input type="date" id="assignment-start-date-context" className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                <p className="text-xs text-slate-500 mt-1">Par défaut : aujourd'hui</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">Jours de la semaine</label>
                <div className="grid grid-cols-4 gap-2">
                  <label className="flex items-center gap-2 text-xs"><input type="checkbox" className="rounded text-blue-600" value="1" /> Lun</label>
                  <label className="flex items-center gap-2 text-xs"><input type="checkbox" className="rounded text-blue-600" value="2" /> Mar</label>
                  <label className="flex items-center gap-2 text-xs"><input type="checkbox" className="rounded text-blue-600" value="3" /> Mer</label>
                  <label className="flex items-center gap-2 text-xs"><input type="checkbox" className="rounded text-blue-600" value="4" /> Jeu</label>
                  <label className="flex items-center gap-2 text-xs"><input type="checkbox" className="rounded text-blue-600" value="5" /> Ven</label>
                  <label className="flex items-center gap-2 text-xs"><input type="checkbox" className="rounded text-blue-600" value="6" /> Sam</label>
                  <label className="flex items-center gap-2 text-xs"><input type="checkbox" className="rounded text-blue-600" value="0" /> Dim</label>
                </div>
              </div>
              
              <div id="limited-assignment-date" className="hidden">
                <label className="block text-sm font-medium text-slate-700 mb-2">Date de fin</label>
                <input type="date" id="assignment-end-date-context" className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
              </div>
            </div>
          </div>
          <footer className="p-4 bg-slate-50/80 border-t border-slate-200 rounded-b-2xl flex gap-3">
            <button id="assignment-context-cancel" className="flex-1 px-4 py-2 text-slate-600 bg-slate-200 hover:bg-slate-300 rounded-lg transition-colors">Annuler</button>
            <button id="assignment-context-confirm" className="flex-1 px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">Confirmer</button>
          </footer>
        </div>
      </div>
      
    </div>
  )
}

export default AgendaFullscreen 