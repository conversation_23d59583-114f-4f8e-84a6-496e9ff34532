import { query } from './config/database.js';

async function fixDatabase() {
    console.log('🔧 Correction de la structure de la base de données...');
    
    try {
        // Supprimer la table existante si elle existe
        console.log('📋 Suppression de l\'ancienne table regular_assignments...');
        await query('DROP TABLE IF EXISTS regular_assignments');
        
        // Créer la nouvelle table avec la bonne structure
        console.log('🏗️ Création de la nouvelle table regular_assignments...');
        await query(`
            CREATE TABLE regular_assignments (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
                post_id UUID REFERENCES standard_posts(id) ON DELETE SET NULL,
                day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
                start_time TIME,
                end_time TIME,
                start_date DATE,
                end_date DATE,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        `);
        
        // Créer les index
        console.log('📊 Création des index...');
        await query('CREATE INDEX idx_regular_assignments_employee ON regular_assignments(employee_id)');
        await query('CREATE INDEX idx_regular_assignments_post ON regular_assignments(post_id)');
        await query('CREATE INDEX idx_regular_assignments_day ON regular_assignments(day_of_week)');
        await query('CREATE INDEX idx_regular_assignments_dates ON regular_assignments(start_date, end_date)');
        
        // Créer la fonction trigger
        console.log('⚙️ Création de la fonction trigger...');
        await query(`
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ language 'plpgsql'
        `);
        
        // Créer le trigger
        console.log('🔗 Création du trigger...');
        await query(`
            CREATE TRIGGER update_regular_assignments_updated_at 
                BEFORE UPDATE ON regular_assignments 
                FOR EACH ROW 
                EXECUTE FUNCTION update_updated_at_column()
        `);
        
        console.log('✅ Base de données corrigée avec succès !');
        console.log('🎉 La table regular_assignments est maintenant prête à être utilisée.');
        
    } catch (error) {
        console.error('❌ Erreur lors de la correction de la base de données:', error);
        process.exit(1);
    }
    
    process.exit(0);
}

// Exécuter la correction
fixDatabase(); 