# Fork des Attributions Régulières - Documentation

## Problème Corrigé

Lors du déplacement d'une attribution régulière vers un nouvel employé, l'ancien système supprimait tout l'historique passé, ce qui était incorrect.

## Nouveau Comportement (Fork)

Lorsqu'une attribution régulière est déplacée vers un nouvel employé à partir d'une date donnée :

### 1. **Préservation de l'Historique**
- L'attribution originale reste **active** mais se termine à la veille de la date de coupure
- Tous les shifts passés restent intacts dans l'historique
- L'employé original garde ses attributions jusqu'à la date de coupure

### 2. **Création d'une Nouvelle Attribution**
- Une nouvelle attribution est créée pour le nouvel employé
- Elle commence exactement à la date de coupure
- Elle hérite des mêmes paramètres (jours, horaires, etc.)

### 3. **Exemple Concret**

```
Situation initiale:
- <PERSON> a une attribution régulière "Poste Matin" du 01/01/2025 (sans fin)
- <PERSON><PERSON> au Vendredi, 8h-16h

Action le 15/03/2025:
- Déplacer l'attribution vers Marie à partir du 15/03/2025

Résultat:
- Jean : Attribution du 01/01/2025 au 14/03/2025 (ACTIVE)
- Marie : Attribution du 15/03/2025 à l'infini (ACTIVE)
```

## Changements Techniques

### Code Modifié

```typescript
// Ancien comportement (INCORRECT)
originalAssignment.endDate = fromDateKey;
originalAssignment.isActive = false; // ❌ Supprimait l'historique

// Nouveau comportement (CORRECT)
const endDate = new Date(fromDateKey);
endDate.setDate(endDate.getDate() - 1);
originalAssignment.endDate = endDate.toISOString().split('T')[0];
originalAssignment.isActive = true; // ✅ Préserve l'historique
```

### Impact Base de Données

Les deux attributions coexistent dans la table `regular_assignments` :
- L'ancienne avec une `end_date` définie
- La nouvelle avec une `start_date` au lendemain

## Avantages

1. **Traçabilité complète** : L'historique est préservé pour les rapports et analyses
2. **Continuité de service** : Aucune interruption dans les plannings
3. **Flexibilité** : Possibilité de voir qui était assigné à quelle période
4. **Conformité** : Respect des exigences légales de conservation des données

## Notes pour les Développeurs

- Toujours utiliser `isActive = true` lors de la création d'un fork
- La date de fin de l'ancienne attribution = date de début nouvelle - 1 jour
- Les shifts générés automatiquement respecteront ces bornes temporelles 