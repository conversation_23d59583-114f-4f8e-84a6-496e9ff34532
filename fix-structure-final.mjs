import fs from 'fs';

console.log('🔧 [FIX] Correction de la structure dupliquée dans teamCalendarApp.ts...');

const filePath = 'src/teamCalendarApp.ts';
let content = fs.readFileSync(filePath, 'utf8');

// Identifier et supprimer le bloc dupliqué
const lines = content.split('\n');
let newLines = [];
let inConfig = false;
let configClosed = false;
let skipDuplicatedBlock = false;

for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Détecter le début de config
    if (line.includes('config: {')) {
        inConfig = true;
        newLines.push(line);
        continue;
    }
    
    // Détecter la fermeture de config
    if (inConfig && line.trim() === '},') {
        inConfig = false;
        configClosed = true;
        newLines.push(line);
        continue;
    }
    
    // Détecter le début du bloc dupliqué
    if (configClosed && line.includes('// Verrous d\'idempotence pour éviter les doubles initialisations')) {
        console.log('🚨 [FIX] Bloc dupliqué détecté, suppression...');
        skipDuplicatedBlock = true;
        continue;
    }
    
    // Détecter la fin du bloc dupliqué
    if (skipDuplicatedBlock && line.includes('_saveStateTimer: null')) {
        skipDuplicatedBlock = false;
        newLines.push(line);
        continue;
    }
    
    // Ajouter la ligne si elle n'est pas dans le bloc dupliqué
    if (!skipDuplicatedBlock) {
        newLines.push(line);
    }
}

// Écrire le fichier corrigé
const correctedContent = newLines.join('\n');
fs.writeFileSync(filePath, correctedContent);

console.log('✅ [FIX] Structure corrigée !');
console.log('📊 [FIX] Lignes supprimées:', lines.length - newLines.length);

// Vérifier que la structure est correcte
const verification = correctedContent.includes('logEmployeePositions: function');
console.log('🔍 [FIX] Fonction logEmployeePositions présente:', verification); 