import fs from 'fs';
import path from 'path';

console.log('🔧 Application des corrections pour renderAvailablePosts...');

const teamCalendarAppPath = './src/teamCalendarApp.ts';

try {
  // Lire le fichier
  let content = fs.readFileSync(teamCalendarAppPath, 'utf8');
  
  // 1. Exposer renderAvailablePosts sur l'instance globale
  const exposeRenderAvailablePosts = `
  // Exposer renderAvailablePosts globalement pour debug
  if (typeof window !== 'undefined') {
    window.renderAvailablePosts = this.renderAvailablePosts.bind(this);
  }
`;
  
  // Trouver la fin de la classe TeamCalendarApp (avant la dernière accolade)
  const classEndIndex = content.lastIndexOf('}');
  if (classEndIndex === -1) {
    throw new Error('Impossible de trouver la fin de la classe TeamCalendarApp');
  }
  
  // Insérer l'exposition de la fonction avant la fin de la classe
  content = content.slice(0, classEndIndex) + exposeRenderAvailablePosts + '\n' + content.slice(classEndIndex);
  
  // 2. Nettoyer le code unreachable (lignes après return)
  const lines = content.split('\n');
  const cleanedLines = [];
  let inMethod = false;
  let foundReturn = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();
    
    // Détecter le début d'une méthode
    if (trimmedLine.includes('(') && trimmedLine.includes(')') && trimmedLine.includes('{')) {
      inMethod = true;
      foundReturn = false;
      cleanedLines.push(line);
      continue;
    }
    
    // Détecter la fin d'une méthode
    if (inMethod && trimmedLine === '}') {
      inMethod = false;
      foundReturn = false;
      cleanedLines.push(line);
      continue;
    }
    
    // Détecter un return dans une méthode
    if (inMethod && trimmedLine.startsWith('return')) {
      foundReturn = true;
      cleanedLines.push(line);
      continue;
    }
    
    // Ignorer les lignes après un return (code unreachable)
    if (inMethod && foundReturn && trimmedLine !== '' && !trimmedLine.startsWith('//')) {
      console.log(`🗑️ Suppression de code unreachable: ${trimmedLine}`);
      continue;
    }
    
    cleanedLines.push(line);
  }
  
  // Écrire le fichier corrigé
  fs.writeFileSync(teamCalendarAppPath, cleanedLines.join('\n'));
  
  console.log('✅ Corrections appliquées avec succès:');
  console.log('   - renderAvailablePosts exposé globalement');
  console.log('   - Code unreachable nettoyé');
  
  // Vérifier que le fichier est valide
  const validationContent = fs.readFileSync(teamCalendarAppPath, 'utf8');
  if (validationContent.includes('window.renderAvailablePosts')) {
    console.log('✅ Exposition de renderAvailablePosts confirmée');
  } else {
    console.log('❌ Échec de l\'exposition de renderAvailablePosts');
  }
  
} catch (error) {
  console.error('❌ Erreur lors de l\'application des corrections:', error.message);
  process.exit(1);
}

console.log('🎯 Script terminé. Redémarrez le serveur pour appliquer les changements.'); 