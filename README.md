# Team Calendar - Application de Gestion de Planning

> **Application de planning d'équipe moderne avec architecture modulaire et documentation AI-Ready**

## 🚀 Démarrage Rapide

```bash
# Installation des dépendances
npm install

# Démarrage du serveur de développement
npm run dev

# Démarrage du backend
npm run server

# Build de production
npm run build
```

## 📋 Fonctionnalités Principales

- **Gestion d'employés** : Création, édition, réorganisation avec drag & drop
- **Planning intelligent** : Grille unifiée avec navigation temporelle
- **Attributions régulières** : Automatisation des plannings récurrents
- **Gestion des congés** : Périodes individuelles et congés globaux
- **Postes configurables** : Création et gestion des postes de travail
- **Import/Export** : Sauvegarde et restauration des données
- **Multi-timezone** : Support des fuseaux horaires
- **Interface responsive** : Optimisée pour desktop et mobile

## 🏗️ Architecture

### Structure des Dossiers

```
src/
├── components/           # Modules réutilisables
│   ├── handlers/        # Gestionnaires d'événements
│   ├── types/           # Interfaces TypeScript
│   ├── ui/              # Composants d'interface
│   └── utils/           # Utilitaires (dates, validation, etc.)
├── teamCalendarApp.ts   # Application principale (orchestration)
├── modalFunctionalities.ts # Gestion externe des modales
├── api.ts               # Services API
├── logger.ts            # Système de logs unifié
└── Agenda.tsx           # Composant React principal

docs/
├── features-index.md    # Index des fonctionnalités (AI-Ready)
└── Erreurs.txt         # Logs d'erreurs pour debugging

backend/
├── server.js           # Serveur Express
├── database/           # Gestion PostgreSQL
└── api/               # Endpoints REST
```

### Flux de Données

```mermaid
graph TD
    A[Agenda.tsx] --> B[TeamCalendarApp]
    B --> C[modalFunctionalities.ts]
    B --> D[API Services]
    D --> E[PostgreSQL Database]
    B --> F[Components Utils]
    F --> G[Date Utils]
    F --> H[Validation Utils]
    C --> I[External Modals]
    B --> J[Logger System]
```

### Patterns Architecturaux

1. **Séparation des Responsabilités**
   - `teamCalendarApp.ts` : Logique métier et orchestration
   - `modalFunctionalities.ts` : Interface utilisateur externe
   - `components/` : Modules réutilisables et utilitaires

2. **Architecture Externe des Modales**
   - Modales découplées de la logique principale
   - Gestion centralisée dans `modalFunctionalities.ts`
   - Communication via événements et callbacks

3. **Cache Intelligent**
   - Cache des semaines chargées (`_weekCache`)
   - Préchargement des données adjacentes
   - Optimisation des performances

4. **Validation Centralisée**
   - Utilitaires de validation dans `components/utils/`
   - Contrôles d'intégrité automatiques
   - Correction automatique des erreurs

### Points d'Extension

#### Ajouter une nouvelle fonctionnalité :

1. **Logique métier** → `teamCalendarApp.ts`
2. **Interface** → `modalFunctionalities.ts`
3. **Types** → `components/types/interfaces.ts`
4. **Validation** → `components/utils/validationUtils.ts`
5. **API** → `api.ts`

#### Ajouter un nouveau composant :

```typescript
// 1. Créer le composant
src/components/ui/monComposant.ts

// 2. Ajouter les types
src/components/types/interfaces.ts

// 3. Exporter dans l'index
src/components/index.ts

// 4. Utiliser dans l'application
import { MonComposant } from './components';
```

## 🛠️ Technologies

- **Frontend** : React 18, TypeScript, Vite
- **Backend** : Node.js, Express, PostgreSQL
- **Styling** : Tailwind CSS
- **Build** : Vite avec HMR
- **Validation** : Utilitaires TypeScript personnalisés
- **Logging** : Système unifié frontend/backend

## 📚 Documentation

- **[Index des Fonctionnalités](docs/features-index.md)** : Mapping complet code ↔ fonctionnalités
- **[Types TypeScript](src/components/types/interfaces.ts)** : Définitions complètes
- **[Utilitaires](src/components/utils/)** : Fonctions réutilisables documentées

## 🧪 Scripts NPM

```bash
npm run dev          # Serveur de développement
npm run build        # Build de production
npm run preview      # Aperçu du build
npm run server       # Backend Express
npm run lint         # Linting ESLint
npm run test         # Tests (à implémenter)
```

## 🔧 Configuration

### Variables d'Environnement

```env
# Backend
PORT=3001
DATABASE_URL=postgresql://user:password@localhost:5432/teamcalendar

# Frontend
VITE_API_URL=http://localhost:3001
```

### Base de Données

L'application utilise PostgreSQL avec les tables principales :
- `employees` : Données des employés
- `shifts` : Planning des quarts
- `standard_posts` : Postes configurés
- `regular_assignments` : Attributions régulières
- `app_settings` : Configuration globale

## 🚀 Déploiement

1. **Build de production** : `npm run build`
2. **Configuration backend** : Variables d'environnement
3. **Base de données** : Migration PostgreSQL
4. **Serveur** : Déploiement Express + fichiers statiques

## 🤝 Contribution

Cette application est conçue pour être facilement extensible et maintenable par des développeurs humains et des IA. La documentation AI-Ready facilite les interventions automatisées.

## 📄 Licence

MIT License - Voir le fichier LICENSE pour plus de détails.
