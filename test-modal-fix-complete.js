// 🧪 Test complet de la correction modale
console.log('🚀 [TEST-MODAL-FIX] Script de test complet de la correction modale chargé');

// Fonction de test principal
function testModalFixComplete() {
    console.log('🧪 [TEST-MODAL-FIX] === TEST COMPLET DE LA CORRECTION MODALE ===');
    
    const results = {
        globalAvailability: false,
        modalCreation: false,
        eventHandling: false,
        dragDropIntegration: false,
        errors: []
    };
    
    try {
        // 1. Test de disponibilité globale
        console.log('📋 [TEST-MODAL-FIX] 1. Test de disponibilité globale...');
        if (window.modalFunctionalities && window.TeamCalendarApp) {
            results.globalAvailability = true;
            console.log('✅ [TEST-MODAL-FIX] modalFunctionalities et TeamCalendarApp disponibles');
        } else {
            results.errors.push('modalFunctionalities ou TeamCalendarApp non disponibles');
            console.error('❌ [TEST-MODAL-FIX] modalFunctionalities ou TeamCalendarApp manquants');
        }
        
        // 2. Test de création de modal
        console.log('📋 [TEST-MODAL-FIX] 2. Test de création de modal...');
        if (window.modalFunctionalities && typeof window.modalFunctionalities.createAssignmentContextModal === 'function') {
            try {
                // Supprimer le modal existant s'il y en a un
                const existingModal = document.getElementById('assignment-context-modal');
                if (existingModal) {
                    existingModal.remove();
                }
                
                const modal = window.modalFunctionalities.createAssignmentContextModal();
                if (modal && modal.id === 'assignment-context-modal') {
                    results.modalCreation = true;
                    console.log('✅ [TEST-MODAL-FIX] Modal créé avec succès');
                } else {
                    results.errors.push('Modal créé mais invalide');
                    console.error('❌ [TEST-MODAL-FIX] Modal créé mais invalide');
                }
            } catch (error) {
                results.errors.push(`Erreur création modal: ${error.message}`);
                console.error('❌ [TEST-MODAL-FIX] Erreur création modal:', error);
            }
        } else {
            results.errors.push('Fonction createAssignmentContextModal non disponible');
            console.error('❌ [TEST-MODAL-FIX] Fonction createAssignmentContextModal non disponible');
        }
        
        // 3. Test de gestion des événements
        console.log('📋 [TEST-MODAL-FIX] 3. Test de gestion des événements...');
        const modal = document.getElementById('assignment-context-modal');
        if (modal && window.modalFunctionalities && typeof window.modalFunctionalities.setupAssignmentModalEvents === 'function') {
            try {
                const testData = {
                    postData: { id: 'test-post', label: 'Test Post' },
                    employeeId: 'test-employee',
                    employeeName: 'Test Employee',
                    dateKey: '2025-07-18'
                };
                
                window.modalFunctionalities.setupAssignmentModalEvents(modal, testData);
                
                // Vérifier que les boutons ont des listeners
                const regularBtn = modal.querySelector('[data-action="regular"]');
                const temporaryBtn = modal.querySelector('[data-action="temporary"]');
                const cancelBtn = modal.querySelector('[data-action="cancel"]');
                
                if (regularBtn && temporaryBtn && cancelBtn) {
                    results.eventHandling = true;
                    console.log('✅ [TEST-MODAL-FIX] Événements configurés avec succès');
                } else {
                    results.errors.push('Boutons du modal manquants');
                    console.error('❌ [TEST-MODAL-FIX] Boutons du modal manquants');
                }
            } catch (error) {
                results.errors.push(`Erreur configuration événements: ${error.message}`);
                console.error('❌ [TEST-MODAL-FIX] Erreur configuration événements:', error);
            }
        } else {
            results.errors.push('Modal ou fonction setupAssignmentModalEvents non disponible');
            console.error('❌ [TEST-MODAL-FIX] Modal ou fonction setupAssignmentModalEvents non disponible');
        }
        
        // 4. Test d'intégration drag & drop
        console.log('📋 [TEST-MODAL-FIX] 4. Test d\'intégration drag & drop...');
        if (window.modalFunctionalities && typeof window.modalFunctionalities.openAssignmentContextModal === 'function') {
            try {
                const testData = {
                    postData: { id: 'test-post', label: 'Test Post' },
                    employeeId: 'test-employee',
                    employeeName: 'Test Employee',
                    dateKey: '2025-07-18',
                    position: { x: 100, y: 100 }
                };
                
                // Test d'ouverture
                window.modalFunctionalities.openAssignmentContextModal(testData);
                
                // Vérifier que le modal est visible
                const modal = document.getElementById('assignment-context-modal');
                if (modal && !modal.classList.contains('hidden')) {
                    results.dragDropIntegration = true;
                    console.log('✅ [TEST-MODAL-FIX] Intégration drag & drop fonctionnelle');
                    
                    // Fermer le modal après le test
                    setTimeout(() => {
                        modal.classList.add('hidden');
                        modal.style.display = 'none';
                    }, 1000);
                } else {
                    results.errors.push('Modal non visible après ouverture');
                    console.error('❌ [TEST-MODAL-FIX] Modal non visible après ouverture');
                }
            } catch (error) {
                results.errors.push(`Erreur intégration drag & drop: ${error.message}`);
                console.error('❌ [TEST-MODAL-FIX] Erreur intégration drag & drop:', error);
            }
        } else {
            results.errors.push('Fonction openAssignmentContextModal non disponible');
            console.error('❌ [TEST-MODAL-FIX] Fonction openAssignmentContextModal non disponible');
        }
        
    } catch (error) {
        results.errors.push(`Erreur générale: ${error.message}`);
        console.error('❌ [TEST-MODAL-FIX] Erreur générale:', error);
    }
    
    // 5. Résumé des résultats
    console.log('📊 [TEST-MODAL-FIX] === RÉSUMÉ DES TESTS ===');
    console.log('🔍 [TEST-MODAL-FIX] Résultats:', results);
    
    const totalTests = 4;
    const passedTests = Object.values(results).filter(v => v === true).length;
    const successRate = (passedTests / totalTests) * 100;
    
    console.log(`📊 [TEST-MODAL-FIX] Tests réussis: ${passedTests}/${totalTests} (${successRate.toFixed(1)}%)`);
    
    if (results.errors.length > 0) {
        console.error('❌ [TEST-MODAL-FIX] Erreurs détectées:');
        results.errors.forEach((error, index) => {
            console.error(`   ${index + 1}. ${error}`);
        });
    }
    
    if (successRate === 100) {
        console.log('🎉 [TEST-MODAL-FIX] TOUS LES TESTS SONT PASSÉS ! La correction modale est fonctionnelle.');
    } else if (successRate >= 75) {
        console.log('⚠️ [TEST-MODAL-FIX] La plupart des tests sont passés, mais quelques problèmes subsistent.');
    } else {
        console.log('❌ [TEST-MODAL-FIX] Plusieurs tests ont échoué. La correction nécessite des ajustements.');
    }
    
    return results;
}

// Fonction de test des boutons du modal
function testModalButtons() {
    console.log('🧪 [TEST-MODAL-FIX] === TEST DES BOUTONS DU MODAL ===');
    
    const modal = document.getElementById('assignment-context-modal');
    if (!modal) {
        console.error('❌ [TEST-MODAL-FIX] Modal non trouvé pour le test des boutons');
        return false;
    }
    
    // Ouvrir le modal pour le test
    modal.classList.remove('hidden');
    modal.style.display = 'block';
    
    const buttons = {
        regular: modal.querySelector('[data-action="regular"]'),
        temporary: modal.querySelector('[data-action="temporary"]'),
        replacement: modal.querySelector('[data-action="replacement"]'),
        cancel: modal.querySelector('[data-action="cancel"]'),
        close: modal.querySelector('[data-action="close"]')
    };
    
    console.log('🔍 [TEST-MODAL-FIX] Boutons trouvés:', Object.keys(buttons).filter(key => buttons[key]));
    
    // Test de clic sur chaque bouton
    Object.keys(buttons).forEach(buttonType => {
        const button = buttons[buttonType];
        if (button) {
            console.log(`🧪 [TEST-MODAL-FIX] Test du bouton ${buttonType}...`);
            
            // Simuler un clic
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            
            button.dispatchEvent(clickEvent);
            console.log(`✅ [TEST-MODAL-FIX] Bouton ${buttonType} cliqué avec succès`);
        } else {
            console.error(`❌ [TEST-MODAL-FIX] Bouton ${buttonType} non trouvé`);
        }
    });
    
    // Fermer le modal après le test
    setTimeout(() => {
        modal.classList.add('hidden');
        modal.style.display = 'none';
    }, 2000);
    
    return true;
}

// Fonctions disponibles globalement
window.testModalFixComplete = testModalFixComplete;
window.testModalButtons = testModalButtons;

console.log('✅ [TEST-MODAL-FIX] Fonctions de test disponibles:');
console.log('- testModalFixComplete() : Test complet de la correction');
console.log('- testModalButtons() : Test spécifique des boutons du modal');

// Exécution automatique après chargement de l'application
setTimeout(() => {
    console.log('🚀 [TEST-MODAL-FIX] Lancement automatique du test complet...');
    testModalFixComplete();
}, 3000);
