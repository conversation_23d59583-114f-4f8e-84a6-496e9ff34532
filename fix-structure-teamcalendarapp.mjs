#!/usr/bin/env node

import fs from 'fs';

const filePath = './src/teamCalendarApp.ts';
let content = fs.readFileSync(filePath, 'utf8');

// Chercher le bloc problématique : double accolade, commentaire, console.log, accolade
const pattern = /\n\s*},\s*},?\s*\n\s*\/\/ ?✅ SUPPRIMÉ : Script d'urgence remplacé par correction à la source dans loadState\s*\n\s*console\.log\(`✅ \[init\] Initialisation terminée avec succès`\);\s*\n\s*},?/;

if (pattern.test(content)) {
    content = content.replace(pattern, '\n  },\n');
    fs.writeFileSync(filePath, content);
    console.log('✅ Bloc orphelin supprimé avec succès !');
} else {
    // Variante sans commentaire
    const pattern2 = /\n\s*},\s*},?\s*\n\s*console\.log\(`✅ \[init\] Initialisation terminée avec succès`\);\s*\n\s*},?/;
    if (pattern2.test(content)) {
        content = content.replace(pattern2, '\n  },\n');
        fs.writeFileSync(filePath, content);
        console.log('✅ Bloc orphelin (variante sans commentaire) supprimé !');
    } else {
        console.log('❌ Aucun bloc orphelin trouvé à corriger.');
    }
} 