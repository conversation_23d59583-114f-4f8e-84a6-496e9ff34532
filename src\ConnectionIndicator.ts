// Indicateur de connexion backend discret pour l'interface utilisateur
// Affiche l'état de la connexion avec le serveur PostgreSQL

import { apiService, BackendStatus } from './api.js';

export class ConnectionIndicator {
  private element: HTMLElement | null = null;
  private isVisible = false;

  constructor() {
    this.createIndicator();
    this.setupEventListeners();
  }

  private createIndicator() {
    console.log('🔌 [ConnectionIndicator] Création de l\'indicateur de connexion');
    
    // Créer l'élément indicateur
    this.element = document.createElement('div');
    this.element.id = 'connection-indicator';
    this.element.className = 'connection-indicator';
    
    // Styles intégrés pour l'indicateur
    this.element.innerHTML = `
      <style>
        .connection-indicator {
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 9999;
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          border-radius: 8px;
          font-size: 13px;
          font-weight: 500;
          transition: all 0.3s ease;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          backdrop-filter: blur(10px);
          cursor: pointer;
          opacity: 0;
          transform: translateY(-10px);
          pointer-events: none;
        }
        
        .connection-indicator.visible {
          opacity: 1;
          transform: translateY(0);
          pointer-events: auto;
        }
        
        .connection-indicator.connected {
          background: rgba(16, 185, 129, 0.95);
          color: white;
          border: 1px solid rgba(16, 185, 129, 0.3);
        }
        
        .connection-indicator.disconnected {
          background: rgba(239, 68, 68, 0.95);
          color: white;
          border: 1px solid rgba(239, 68, 68, 0.3);
          animation: pulse-red 2s infinite;
        }
        
        .connection-indicator.reconnecting {
          background: rgba(245, 158, 11, 0.95);
          color: white;
          border: 1px solid rgba(245, 158, 11, 0.3);
          animation: pulse-yellow 2s infinite;
        }
        
        @keyframes pulse-red {
          0%, 100% { 
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
          }
          50% { 
            box-shadow: 0 4px 20px rgba(239, 68, 68, 0.6);
          }
        }
        
        @keyframes pulse-yellow {
          0%, 100% { 
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
          }
          50% { 
            box-shadow: 0 4px 20px rgba(245, 158, 11, 0.6);
          }
        }
        
        .connection-indicator .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: currentColor;
        }
        
        .connection-indicator .status-text {
          font-weight: 500;
        }
        
        .connection-indicator .last-check {
          font-size: 11px;
          opacity: 0.8;
          margin-top: 2px;
        }
        
        .connection-indicator:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
        
        .connection-indicator.connected:hover {
          background: rgba(16, 185, 129, 1);
        }
        
        .connection-indicator.disconnected:hover {
          background: rgba(239, 68, 68, 1);
        }
        
        .connection-indicator.reconnecting:hover {
          background: rgba(245, 158, 11, 1);
        }
      </style>
      <div class="status-dot"></div>
      <div class="status-content">
        <div class="status-text">Connexion...</div>
        <div class="last-check"></div>
      </div>
    `;

    // Ajouter l'indicateur au DOM
    document.body.appendChild(this.element);
    
    // Clic pour forcer une vérification
    this.element.addEventListener('click', () => {
      console.log('🔄 [ConnectionIndicator] Vérification manuelle de la connexion');
      this.updateStatus({ connected: false, lastCheck: new Date() }, true);
      apiService.checkConnection();
    });
  }

  private setupEventListeners() {
    // Écouter les changements de statut de l'API
    apiService.onStatusChange((status: BackendStatus) => {
      this.updateStatus(status);
    });

    // Vérification initiale
    setTimeout(() => {
      const status = apiService.getConnectionStatus();
      this.updateStatus(status);
    }, 1000);
  }

  private updateStatus(status: BackendStatus, isManualCheck = false) {
    if (!this.element) return;

    const statusText = this.element.querySelector('.status-text') as HTMLElement;
    const lastCheck = this.element.querySelector('.last-check') as HTMLElement;

    if (isManualCheck) {
      // État de reconnexion manuelle
      this.element.className = 'connection-indicator visible reconnecting';
      statusText.textContent = 'Vérification...';
      lastCheck.textContent = 'Contrôle manuel en cours';
      this.show();
      return;
    }

    // Respecter les délais de grâce - n'afficher que si shouldShowNotification est true
    if (!status.shouldShowNotification) {
      // Ne pas afficher de notification pendant les délais de grâce
      return;
    }

    if (status.connected) {
      // Connexion stable rétablie après délai de grâce
      this.element.className = 'connection-indicator visible connected';
      statusText.textContent = 'Connexion rétablie';
      lastCheck.textContent = `Stable depuis ${this.formatTime(status.lastCheck)}`;
      
      console.log('🎉 [ConnectionIndicator] Connexion stable rétablie');
      
      // Afficher la notification de reconnexion pendant 5 secondes
      this.show();
      setTimeout(() => {
        this.hide();
      }, 5000);
      
    } else {
      // Déconnexion confirmée après délai de grâce
      this.element.className = 'connection-indicator visible disconnected';
      statusText.textContent = 'Connexion instable';
      lastCheck.textContent = status.error ? 
        `Problème réseau detecté` : 
        `Déconnecté depuis ${this.formatTime(status.lastCheck)}`;
      
      console.log('🚨 [ConnectionIndicator] Déconnexion confirmée');
      this.show(); // Rester visible jusqu'à reconnexion
    }
  }

  private formatTime(date: Date): string {
    return date.toLocaleTimeString('fr-FR', { 
      hour: '2-digit', 
      minute: '2-digit', 
      second: '2-digit' 
    });
  }

  private show() {
    if (this.element && !this.isVisible) {
      this.element.classList.add('visible');
      this.isVisible = true;
    }
  }

  private hide() {
    if (this.element && this.isVisible) {
      this.element.classList.remove('visible');
      this.isVisible = false;
    }
  }

  // Méthode publique pour forcer l'affichage
  public forceShow() {
    this.show();
  }

  // Méthode publique pour forcer le masquage
  public forceHide() {
    this.hide();
  }

  // Destruction propre
  public destroy() {
    if (this.element) {
      this.element.remove();
      this.element = null;
    }
  }
}

// Instance globale
export const connectionIndicator = new ConnectionIndicator(); 