const fs = require('fs');

console.log('🚨 CORRECTION IMMÉDIATE DES CONFLITS DE PERSISTANCE');

try {
    const file = 'src/teamCalendarApp.ts';
    let content = fs.readFileSync(file, 'utf8');
    
    // Système de verrouillage simple
    const lockSystem = `
    _transactionLock: false,
    _savingState: false,
    _renderingInProgress: false,
    
    safeScheduleUpdate: function(employeeId, dateKey, updateFn, operation) {
        if (this._transactionLock) {
            console.warn('Transaction en cours, ignoré');
            return;
        }
        
        this._transactionLock = true;
        try {
            if (!this.data.schedule[employeeId]) this.data.schedule[employeeId] = {};
            if (!this.data.schedule[employeeId][dateKey]) this.data.schedule[employeeId][dateKey] = [];
            
            const result = updateFn(this.data.schedule[employeeId][dateKey]);
            setTimeout(() => this.saveState(), 100);
            return result;
        } finally {
            this._transactionLock = false;
        }
    },
    
    renderSafe: function() {
        if (this._renderingInProgress) return;
        this._renderingInProgress = true;
        try {
            if (typeof this.actualRender === 'function') {
                this.actualRender();
            } else if (typeof this.renderUnifiedCalendar === 'function') {
                this.renderUnifiedCalendar();
            }
        } catch (error) {
            console.error('Erreur render:', error);
        } finally {
            this._renderingInProgress = false;
        }
    },
    
    checkDataIntegrity: function() {
        let issues = 0;
        if (!this.data || !this.data.schedule) issues++;
        if (!Array.isArray(this.data.employees)) issues++;
        return issues;
    },
    
    quickRepair: function() {
        if (!this.data) this.data = {};
        if (!this.data.schedule) this.data.schedule = {};
        if (!Array.isArray(this.data.employees)) this.data.employees = [];
        console.log('Réparation rapide terminée');
    },`;

    // Insérer le système
    const insertPoint = content.indexOf('const TeamCalendarApp: TeamCalendarAppType = {');
    if (insertPoint !== -1) {
        const nextLine = content.indexOf('\n', insertPoint);
        content = content.slice(0, nextLine) + lockSystem + content.slice(nextLine);
        console.log('✅ Système de verrouillage ajouté');
    }

    // Sauvegarder
    fs.writeFileSync(file, content);
    
    // Script de test
    const testScript = `console.log('Test persistance...');
if (window.TeamCalendarApp) {
    const issues = window.TeamCalendarApp.checkDataIntegrity();
    console.log('Intégrité:', issues, 'problèmes');
    if (issues > 0) {
        window.TeamCalendarApp.quickRepair();
    }
    console.log('Test terminé');
}`;
    
    fs.writeFileSync('test-fix.js', testScript);
    
    console.log('🎉 CORRECTION TERMINÉE !');
    console.log('Actions à faire :');
    console.log('1. Recharger l\'application');
    console.log('2. Exécuter test-fix.js dans la console');
    
} catch (error) {
    console.error('❌ Erreur:', error);
} 