import pg from 'pg';

const { Pool } = pg;

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'team_calendar',
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT || 5432,
});

async function diagnosticDB() {
  console.log('🔍 [DIAGNOSTIC] Vérification de la base de données...\n');
  
  try {
    // Test de connexion
    const client = await pool.connect();
    console.log('✅ Connexion PostgreSQL établie');
    
    // Compter les quarts
    const shiftsResult = await client.query('SELECT COUNT(*) as total FROM shifts');
    const shiftsCount = parseInt(shiftsResult.rows[0].total);
    console.log(`📊 Total des quarts: ${shiftsCount}`);
    
    // Compter les employés
    const employeesResult = await client.query('SELECT COUNT(*) as total FROM employees');
    const employeesCount = parseInt(employeesResult.rows[0].total);
    console.log(`👥 Total des employés: ${employeesCount}`);
    
    // Vérifier les doublons
    const duplicatesResult = await client.query(`
      SELECT employee_id, date_key, COUNT(*) as count
      FROM shifts 
      GROUP BY employee_id, date_key
      HAVING COUNT(*) > 1
      ORDER BY count DESC
      LIMIT 5
    `);
    
    console.log(`\n🔍 Analyse des doublons:`);
    console.log(`   Groupes avec doublons: ${duplicatesResult.rows.length}`);
    
    if (duplicatesResult.rows.length > 0) {
      console.log('   Top 5 des doublons:');
      duplicatesResult.rows.forEach((row, i) => {
        console.log(`     ${i+1}. Employé ${row.employee_id} - ${row.date_key}: ${row.count} quarts`);
      });
    }
    
    // Vérifier les assignments
    const assignmentsResult = await client.query(`
      SELECT (shift_data->>'assignmentId') as assignment_id, COUNT(*) as count
      FROM shifts 
      WHERE shift_data->>'assignmentId' IS NOT NULL
      GROUP BY (shift_data->>'assignmentId')
      HAVING COUNT(*) > 10
      ORDER BY count DESC
      LIMIT 5
    `);
    
    console.log(`\n🎯 Analyse des assignments:`);
    console.log(`   Assignments avec >10 occurrences: ${assignmentsResult.rows.length}`);
    
    if (assignmentsResult.rows.length > 0) {
      assignmentsResult.rows.forEach((row, i) => {
        console.log(`     ${i+1}. Assignment ${row.assignment_id}: ${row.count} occurrences`);
      });
    }
    
    // Recommandations
    console.log(`\n💡 [RECOMMANDATIONS]`);
    if (shiftsCount > 1000) {
      console.log(`   ⚠️  ${shiftsCount} quarts est anormalement élevé`);
      console.log(`   🧹 Nettoyage recommandé`);
    }
    
    if (duplicatesResult.rows.length > 0) {
      console.log(`   ⚠️  ${duplicatesResult.rows.length} groupes de doublons détectés`);
      console.log(`   🔧 Suppression des doublons recommandée`);
    }
    
    client.release();
    
  } catch (error) {
    console.error('❌ [ERREUR] Diagnostic échoué:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 [INFO] PostgreSQL semble non démarré ou inaccessible');
      console.log('   Vérifiez que PostgreSQL est démarré sur le port 5432');
    }
  } finally {
    await pool.end();
  }
}

diagnosticDB(); 