#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

console.log('🔧 [FIX-EXPORT] Correction des problèmes d\'export dans teamCalendarApp.ts...');

const filePath = 'src/teamCalendarApp.ts';

try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // 1. Vérifier que l'export default est bien présent à la fin
    if (!content.includes('export default TeamCalendarApp;')) {
        console.log('❌ [FIX-EXPORT] Export default manquant !');
        
        // L'ajouter avant la dernière accolade si nécessaire
        if (content.endsWith('}')) {
            content = content.slice(0, -1) + '\n\nexport default TeamCalendarApp;\n}';
            hasChanges = true;
        } else {
            content += '\n\nexport default TeamCalendarApp;\n';
            hasChanges = true;
        }
    }

    // 2. S'assurer que l'objet TeamCalendarApp est bien fermé
    const teamCalendarAppStart = content.indexOf('const TeamCalendarApp = {');
    if (teamCalendarAppStart === -1) {
        console.log('❌ [FIX-EXPORT] Objet TeamCalendarApp non trouvé !');
        process.exit(1);
    }

    // 3. Corriger les erreurs de syntaxe communes
    
    // Corriger les fonctions dupliquées ou mal fermées
    content = content.replace(/,\s*,/g, ','); // Supprimer les virgules doubles
    content = content.replace(/}\s*,\s*}/g, '}'); // Corriger les fermetures doubles
    
    // 4. Vérifier la structure de base
    const lines = content.split('\n');
    let braceCount = 0;
    let inTeamCalendarApp = false;
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        if (line.includes('const TeamCalendarApp = {')) {
            inTeamCalendarApp = true;
            braceCount = 1;
            continue;
        }
        
        if (inTeamCalendarApp) {
            // Compter les accolades
            braceCount += (line.match(/\{/g) || []).length;
            braceCount -= (line.match(/\}/g) || []).length;
            
            if (braceCount === 0) {
                console.log(`✅ [FIX-EXPORT] Objet TeamCalendarApp fermé à la ligne ${i + 1}`);
                break;
            }
        }
    }

    if (braceCount !== 0) {
        console.log(`⚠️ [FIX-EXPORT] Problème de structure détecté (braceCount: ${braceCount})`);
    }

    // 5. Sauvegarder si des changements ont été faits
    if (hasChanges) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log('✅ [FIX-EXPORT] Fichier corrigé et sauvegardé');
    } else {
        console.log('✅ [FIX-EXPORT] Aucune correction nécessaire');
    }

    console.log('🎉 [FIX-EXPORT] Correction terminée avec succès !');

} catch (error) {
    console.error('❌ [FIX-EXPORT] Erreur:', error);
    process.exit(1);
} 