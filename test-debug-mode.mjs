#!/usr/bin/env node

/**
 * 🧪 SCRIPT DE TEST MODE DEBUG ULTRA-INTENSIF
 * Vérifie que toutes les fonctionnalités sont correctement implémentées
 */

import fs from 'fs';
import path from 'path';

console.log(`
🧪 TEST MODE DEBUG ULTRA-INTENSIF
================================
`);

const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function test(name, testFn) {
  try {
    const result = testFn();
    if (result) {
      console.log(`✅ ${name}`);
      testResults.passed++;
      testResults.tests.push({ name, status: 'PASS' });
    } else {
      console.log(`❌ ${name}`);
      testResults.failed++;
      testResults.tests.push({ name, status: 'FAIL', error: 'Test returned false' });
    }
  } catch (error) {
    console.log(`❌ ${name} - ${error.message}`);
    testResults.failed++;
    testResults.tests.push({ name, status: 'FAIL', error: error.message });
  }
}

// ✅ TEST 1: Fichiers créés
test('Fichier capture-logs-unified.js existe et contient mode debug', () => {
  const captureFile = fs.readFileSync('capture-logs-unified.js', 'utf8');
  return captureFile.includes('DEBUG_MODE') && 
         captureFile.includes('ULTRA_DEBUG_MODE') &&
         captureFile.includes('enableUltraDebug');
});

test('Fichier enable-ultra-debug.js créé', () => {
  return fs.existsSync('public/enable-ultra-debug.js');
});

test('Guide MODE_DEBUG_ULTRA_INTENSIF.md créé', () => {
  return fs.existsSync('GUIDE_MODE_DEBUG_ULTRA_INTENSIF.md');
});

// ✅ TEST 2: Système de capture unifié
test('Capture-logs contient configurations debug (insane, verbose)', () => {
  const captureFile = fs.readFileSync('capture-logs-unified.js', 'utf8');
  return captureFile.includes("case 'insane'") && 
         captureFile.includes("case 'verbose'") &&
         captureFile.includes('1000'); // 1000 logs/seconde en mode insane
});

test('Capture-logs contient métadonnées enrichies', () => {
  const captureFile = fs.readFileSync('capture-logs-unified.js', 'utf8');
  return captureFile.includes('performance.memory') && 
         captureFile.includes('userAgent') &&
         captureFile.includes('timing');
});

test('Capture-logs contient capture DOM mutations', () => {
  const captureFile = fs.readFileSync('capture-logs-unified.js', 'utf8');
  return captureFile.includes('MutationObserver') && 
         captureFile.includes('DOM MUTATION');
});

test('Capture-logs contient capture fetch', () => {
  const captureFile = fs.readFileSync('capture-logs-unified.js', 'utf8');
  return captureFile.includes('originalFetch') && 
         captureFile.includes('FETCH START') &&
         captureFile.includes('FETCH SUCCESS');
});

// ✅ TEST 3: Interface utilisateur
test('Logs.tsx contient nouveaux états debug', () => {
  const logsFile = fs.readFileSync('src/pages/Logs.tsx', 'utf8');
  return logsFile.includes('debugMode') && 
         logsFile.includes('isDebugEnabled') &&
         logsFile.includes('debugStats');
});

test('Logs.tsx contient fonctions enableDebugMode/disableDebugMode', () => {
  const logsFile = fs.readFileSync('src/pages/Logs.tsx', 'utf8');
  return logsFile.includes('enableDebugMode') && 
         logsFile.includes('disableDebugMode') &&
         logsFile.includes('getDebugLimits');
});

test('Logs.tsx contient mode debug dans le sélecteur', () => {
  const logsFile = fs.readFileSync('src/pages/Logs.tsx', 'utf8');
  return logsFile.includes("'debug'") && 
         logsFile.includes('DEBUG (avec métadonnées complètes)');
});

test('Logs.tsx contient limites élevées (100k)', () => {
  const logsFile = fs.readFileSync('src/pages/Logs.tsx', 'utf8');
  return logsFile.includes('100000') && 
         logsFile.includes('50000') &&
         logsFile.includes('20000');
});

test('Logs.tsx contient interface boutons debug', () => {
  const logsFile = fs.readFileSync('src/pages/Logs.tsx', 'utf8');
  return logsFile.includes('VERBOSE (20k)') && 
         logsFile.includes('INSANE (100k)') &&
         logsFile.includes('MODE DEBUG ULTRA-INTENSIF');
});

// ✅ TEST 4: Serveur API
test('Server.js contient mode debug avec limites élevées', () => {
  const serverFile = fs.readFileSync('server/app.js', 'utf8');
  return serverFile.includes('isDebugMode') && 
         serverFile.includes('100000') &&
         serverFile.includes('DEBUG INTENSIF');
});

test('Server.js contient requête SQL mode debug', () => {
  const serverFile = fs.readFileSync('server/app.js', 'utf8');
  return serverFile.includes("mode === 'debug'") && 
         serverFile.includes('TOUT CAPTURER') &&
         serverFile.includes('fetch%');
});

test('Server.js contient limites SSE adaptatives', () => {
  const serverFile = fs.readFileSync('server/app.js', 'utf8');
  return serverFile.includes('sseLimit') && 
         serverFile.includes('1000') && // limite SSE en mode debug
         serverFile.includes('sqlTimeout');
});

// ✅ TEST 5: Script activateur
test('Script enable-ultra-debug.js contient toutes les fonctions', () => {
  const scriptFile = fs.readFileSync('public/enable-ultra-debug.js', 'utf8');
  return scriptFile.includes('activerDebugVerbose') && 
         scriptFile.includes('activerDebugInsane') &&
         scriptFile.includes('statusDebug') &&
         scriptFile.includes('forceCaptureLogs') &&
         scriptFile.includes('testPerformanceDebug');
});

test('Script contient raccourcis clavier', () => {
  const scriptFile = fs.readFileSync('public/enable-ultra-debug.js', 'utf8');
  return scriptFile.includes('Ctrl+Shift+D') && 
         scriptFile.includes('Ctrl+Shift+V') &&
         scriptFile.includes('Ctrl+Shift+I');
});

// ✅ TEST 6: Guide documentation
test('Guide contient sections essentielles', () => {
  const guideFile = fs.readFileSync('GUIDE_MODE_DEBUG_ULTRA_INTENSIF.md', 'utf8');
  return guideFile.includes('Mode VERBOSE (20k logs)') && 
         guideFile.includes('Mode INSANE (100k logs)') &&
         guideFile.includes('Méthodes d\'Activation') &&
         guideFile.includes('Limites Augmentées');
});

test('Guide contient exemples pratiques', () => {
  const guideFile = fs.readFileSync('GUIDE_MODE_DEBUG_ULTRA_INTENSIF.md', 'utf8');
  return guideFile.includes('javascript') && 
         guideFile.includes('activerDebugInsane()') &&
         guideFile.includes('Stack trace') &&
         guideFile.includes('Export IA');
});

// ✅ RÉSULTATS
console.log(`
📊 RÉSULTATS DES TESTS
=====================
✅ Réussis: ${testResults.passed}
❌ Échoués: ${testResults.failed}
📝 Total: ${testResults.tests.length}
`);

if (testResults.failed === 0) {
  console.log(`🎉 TOUS LES TESTS RÉUSSIS !

🚨 MODE DEBUG ULTRA-INTENSIF PRÊT À L'EMPLOI 🚨

🎯 PROCHAINES ÉTAPES:
1. Démarrer l'application: npm run dev
2. Aller sur /logs
3. Cliquer sur "🚨 INSANE (100k)" pour test ultime
4. Ou utiliser la console: activerDebugInsane()

⚠️  RAPPEL: Utiliser avec modération - peut ralentir l'interface !
`);
  process.exit(0);
} else {
  console.log(`❌ ${testResults.failed} test(s) échoué(s):
${testResults.tests.filter(t => t.status === 'FAIL').map(t => `  - ${t.name}: ${t.error}`).join('\n')}

🔧 Vérifiez les fichiers modifiés et relancez le test.`);
  process.exit(1);
} 