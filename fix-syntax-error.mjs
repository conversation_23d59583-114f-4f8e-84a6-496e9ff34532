import fs from 'fs';
import path from 'path';

console.log('🔧 Correction de l\'erreur de syntaxe dans safeScheduleUpdate...');

const teamCalendarAppPath = './src/teamCalendarApp.ts';

try {
  // <PERSON><PERSON> le <PERSON>er
  let content = fs.readFileSync(teamCalendarAppPath, 'utf8');
  
  // Trouver et corriger la fonction safeScheduleUpdate
  const safeScheduleUpdatePattern = /safeScheduleUpdate:\s*function\([^)]*\)\s*\{[\s\S]*?if\s*\(!this\.data\.schedule\[employeeId\]\)[\s\S]*?return\s+result;\s*\}/;
  
  const correctedSafeScheduleUpdate = `safeScheduleUpdate: function(employeeId, dateKey, updateFn, operation) {
        if (this._transactionLock) {
            console.warn('⚠️ Transaction en cours, ignoré');
            return;
        }
        
        this._transactionLock = true;
        try {
            if (!this.data.schedule[employeeId]) this.data.schedule[employeeId] = {};
            if (!this.data.schedule[employeeId][dateKey]) this.data.schedule[employeeId][dateKey] = [];
            
            const result = updateFn(this.data.schedule[employeeId][dateKey]);
            setTimeout(() => this.saveState(), 100);
            return result;
        } catch (error) {
            console.error('❌ Erreur dans safeScheduleUpdate:', error);
            throw error;
        } finally {
            this._transactionLock = false;
        }
    }`;
  
  // Remplacer la fonction problématique
  if (content.includes('safeScheduleUpdate: function')) {
    content = content.replace(safeScheduleUpdatePattern, correctedSafeScheduleUpdate);
    console.log('✅ Fonction safeScheduleUpdate corrigée');
  } else {
    console.log('⚠️ Fonction safeScheduleUpdate non trouvée, recherche alternative...');
    
    // Recherche alternative par ligne
    const lines = content.split('\n');
    let inSafeScheduleUpdate = false;
    let startIndex = -1;
    let endIndex = -1;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      if (line.includes('safeScheduleUpdate: function')) {
        inSafeScheduleUpdate = true;
        startIndex = i;
      }
      
      if (inSafeScheduleUpdate && line.trim() === '},' && i > startIndex) {
        endIndex = i;
        break;
      }
    }
    
    if (startIndex !== -1 && endIndex !== -1) {
      // Remplacer la section problématique
      const beforeSection = lines.slice(0, startIndex);
      const afterSection = lines.slice(endIndex + 1);
      
      const correctedLines = [
        '    safeScheduleUpdate: function(employeeId, dateKey, updateFn, operation) {',
        '        if (this._transactionLock) {',
        '            console.warn(\'⚠️ Transaction en cours, ignoré\');',
        '            return;',
        '        }',
        '        ',
        '        this._transactionLock = true;',
        '        try {',
        '            if (!this.data.schedule[employeeId]) this.data.schedule[employeeId] = {};',
        '            if (!this.data.schedule[employeeId][dateKey]) this.data.schedule[employeeId][dateKey] = [];',
        '            ',
        '            const result = updateFn(this.data.schedule[employeeId][dateKey]);',
        '            setTimeout(() => this.saveState(), 100);',
        '            return result;',
        '        } catch (error) {',
        '            console.error(\'❌ Erreur dans safeScheduleUpdate:\', error);',
        '            throw error;',
        '        } finally {',
        '            this._transactionLock = false;',
        '        }',
        '    },'
      ];
      
      content = [...beforeSection, ...correctedLines, ...afterSection].join('\n');
      console.log('✅ Fonction safeScheduleUpdate corrigée par remplacement de lignes');
    }
  }
  
  // Écrire le fichier corrigé
  fs.writeFileSync(teamCalendarAppPath, content);
  
  console.log('✅ Erreur de syntaxe corrigée avec succès');
  
  // Vérifier que la correction est valide
  const validationContent = fs.readFileSync(teamCalendarAppPath, 'utf8');
  if (validationContent.includes('try {') && validationContent.includes('} catch (error) {') && validationContent.includes('} finally {')) {
    console.log('✅ Structure try-catch-finally confirmée');
  } else {
    console.log('❌ Structure try-catch-finally non trouvée');
  }
  
} catch (error) {
  console.error('❌ Erreur lors de la correction:', error.message);
  process.exit(1);
}

console.log('🎯 Script terminé. Redémarrez le serveur pour appliquer les changements.'); 