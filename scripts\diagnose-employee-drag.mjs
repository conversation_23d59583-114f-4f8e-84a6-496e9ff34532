// DIAGNOSTIC DRAG & DROP EMPLOYÉS (exécutable via `node` ou `import()` dans le navigateur)
// -----------------------------------------------------------------------------
// Utilisation côté navigateur (console) :
//   import('/scripts/diagnose-employee-drag.mjs').then(m => m.runFullDiagnostic());
// Utilisation côté Node :
//   node scripts/diagnose-employee-drag.mjs
// -----------------------------------------------------------------------------

// ⚠️ IMPORTANT : Ne pas importer les modules Node dans le navigateur.
let fs, path, __dirname;
if (typeof window === 'undefined') {
  // Chargements dynamiques autorisés côté Node uniquement
  const urlMod = await import('url');
  const pathMod = await import('path');
  const fsMod = await import('fs');
  const __filename = urlMod.fileURLToPath(import.meta.url);
  __dirname = pathMod.dirname(__filename);
  fs = fsMod.default ?? fsMod;
  path = pathMod.default ?? pathMod;
}

/* -------------------------------------------------------------------------- */
/* OUTILS COMMUNS                                                             */
/* -------------------------------------------------------------------------- */

const COLORS = {
  INFO: 'color: #0ea5e9',
  SUCCESS: 'color: #22c55e',
  WARN: 'color: #eab308',
  ERROR: 'color: #ef4444'
};

function log(label, message, level = 'INFO') {
  if (typeof window !== 'undefined') {
    console.log(`%c${label}`, COLORS[level] || COLORS.INFO, message);
  } else {
    console.log(`[${level}] ${label}`, message);
  }
}

/* -------------------------------------------------------------------------- */
/* DIAGNOSTIC CÔTÉ NAVIGATEUR                                                 */
/* -------------------------------------------------------------------------- */

function isBrowser() {
  return typeof window !== 'undefined' && typeof document !== 'undefined';
}

// Récupère une référence sûre à l'app globale (TeamCalendarApp)
function getApp() {
  return isBrowser() ? window.TeamCalendarApp : null;
}

/**
 * Retourne l'ordre courant des employés (tableau d'ID et de noms)
 */
function getCurrentEmployeeOrder(app) {
  return app.data.employees.map((e, idx) => ({ index: idx, id: e.id, name: e.name }));
}

/**
 * Affiche l'ordre courant dans la console sous forme de tableau.
 */
function logCurrentOrder(app, label = 'ORDRE ACTUEL') {
  const order = getCurrentEmployeeOrder(app);
  log(label, order.map(o => `${o.index}: ${o.name}`).join(' | '));
}

/**
 * Simule un déplacement via la méthode API interne.
 */
async function simulateDrag(app, oldIndex, newIndex) {
  log('SIMULATION', `Déplacement ${oldIndex} ➜ ${newIndex}`);
  if (typeof app.reorderEmployees !== 'function') {
    log('ERREUR', 'Fonction reorderEmployees indisponible', 'ERROR');
    return false;
  }

  // Conserver l'ordre avant
  const before = JSON.stringify(app.data.employees.map(e => e.id));

  app.reorderEmployees(oldIndex, newIndex);

  const after = JSON.stringify(app.data.employees.map(e => e.id));
  const success = before !== after;
  log('RÉSULTAT', success ? '✅ Order changé localement' : '❌ Order inchangé', success ? 'SUCCESS' : 'ERROR');
  return success;
}

/**
 * Vérifie la persistance côté backend après sauvegarde + rechargement.
 */
async function verifyPersistence(app) {
  if (typeof app.saveEmployeeOrder !== 'function' || typeof app.loadEmployeeOrder !== 'function') {
    log('ERREUR', 'Méthodes save/loadEmployeeOrder indisponibles', 'ERROR');
    return false;
  }

  await app.saveEmployeeOrder();
  const orderAfterSave = getCurrentEmployeeOrder(app);
  log('SAUVEGARDE', orderAfterSave);

  // Rechargement forcé
  await app.forceLoadEmployeeOrder?.();
  const orderAfterReload = getCurrentEmployeeOrder(app);
  log('RELOAD', orderAfterReload);

  const persisted = JSON.stringify(orderAfterSave) === JSON.stringify(orderAfterReload);
  log('PERSISTENCE', persisted ? '✅ La persistance est OK' : '❌ La persistance a échoué', persisted ? 'SUCCESS' : 'ERROR');
  return persisted;
}

/**
 * Diagnostic complet :
 *  1. Affiche l’ordre initial
 *  2. Teste tous les swaps possibles (i -> j) et vérifie la persistance
 */
export async function runFullDiagnostic() {
  if (!isBrowser()) {
    console.error('Ce diagnostic complet doit être lancé dans le navigateur (import)').
    return;
  }

  const app = getApp();
  if (!app) {
    log('ERREUR', 'TeamCalendarApp introuvable sur window', 'ERROR');
    return;
  }

  logCurrentOrder(app, 'ORDRE INITIAL');

  const n = app.data.employees.length;
  for (let oldIdx = 0; oldIdx < n; oldIdx++) {
    const newIdx = (oldIdx + 1) % n; // simple swap circulaire
    await simulateDrag(app, oldIdx, newIdx);
    const persisted = await verifyPersistence(app);
    if (!persisted) {
      log('ÉCHEC', `La persistance a cassé après déplacement ${oldIdx} ➜ ${newIdx}`, 'ERROR');
      break;
    }
  }

  log('DIAGNOSTIC', 'Terminé');
}

/* -------------------------------------------------------------------------- */
/* POINT D’ENTRÉE NODE                                                        */
/* -------------------------------------------------------------------------- */

if (!isBrowser()) {
  console.log('💡 Ce script est principalement destiné au navigateur.');
  console.log('   Lancez :  node scripts/diagnose-employee-drag.mjs  pour un test basique.');

  if (fs && path) {
    const employeesPath = path.join(__dirname, '..', 'src', 'teamCalendarApp.ts');
    console.log(fs.existsSync(employeesPath) ? '✔️  teamCalendarApp présent' : '❌ teamCalendarApp manquant');
  }
} 