#!/usr/bin/env node

/**
 * Script pour supprimer le code parasite qui cause l'erreur de syntaxe
 */

import fs from 'fs';

console.log('🧹 [FIX-PARASITE] Suppression du code parasite...');

const filePath = './src/teamCalendarApp.ts';
let content = fs.readFileSync(filePath, 'utf8');

// Identifier et supprimer le code parasite
const parasitePattern = /  \},\s*\s*\s*500\);\s*\s*\/\/ ✅ SUPPRIMÉ : Script d'urgence remplacé par correction à la source dans loadState\s*\s*console\.log\(`✅ \[init\] Initialisation terminée avec succès`\);/;

if (parasitePattern.test(content)) {
    console.log('🔍 [FIX-PARASITE] Code parasite détecté, suppression...');
    
    // Remplacer par la structure correcte
    content = content.replace(parasitePattern, `  },

    // ✅ SUPPRIMÉ : Script d'urgence remplacé par correction à la source dans loadState

    console.log(\`✅ [init] Initialisation terminée avec succès\`);`);
    
    console.log('✅ [FIX-PARASITE] Code parasite supprimé !');
} else {
    console.log('⚠️ [FIX-PARASITE] Pattern parasite non trouvé, recherche alternative...');
    
    // Recherche alternative plus spécifique
    const alternativePattern = /  \},\s*\s*\s*500\);/;
    if (alternativePattern.test(content)) {
        console.log('🔍 [FIX-PARASITE] Pattern alternatif trouvé, correction...');
        content = content.replace(alternativePattern, '  },');
        console.log('✅ [FIX-PARASITE] Correction appliquée !');
    } else {
        console.log('❌ [FIX-PARASITE] Aucun pattern trouvé');
    }
}

// Écrire le fichier corrigé
fs.writeFileSync(filePath, content);

console.log('✅ [FIX-PARASITE] Fichier corrigé !');

console.log('\n🚀 Prochaines étapes :');
console.log('1. Redémarrer le serveur : npm run dev');
console.log('2. Vérifier qu\'il n\'y a plus d\'erreurs de syntaxe');
console.log('3. Tester l\'application dans le navigateur');

console.log('\n✅ [FIX-PARASITE] Script terminé !'); 