#!/usr/bin/env node

import { pool, closePool } from '../server/config/database.js';

// Couleurs pour les logs
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.magenta}🚀 ${msg}${colors.reset}`)
};

async function addMissingColumns() {
  const client = await pool.connect();
  
  try {
    log.title('🔧 AJOUT DES COLONNES MANQUANTES');
    
    // 1. Ajouter les colonnes manquantes à la table employees
    log.info('Vérification de la table employees...');
    
    try {
      await client.query('ALTER TABLE employees ADD COLUMN IF NOT EXISTS avatar_url TEXT');
      log.success('Colonne avatar_url ajoutée à employees');
    } catch (error) {
      log.warning(`avatar_url déjà présente ou erreur: ${error.message}`);
    }
    
    // 2. Ajouter les colonnes manquantes à la table standard_posts
    log.info('Vérification de la table standard_posts...');
    
    try {
      await client.query('ALTER TABLE standard_posts ADD COLUMN IF NOT EXISTS color VARCHAR(50)');
      log.success('Colonne color ajoutée à standard_posts');
    } catch (error) {
      log.warning(`color déjà présente ou erreur: ${error.message}`);
    }
    
    // 3. Ajouter les colonnes manquantes à la table shifts
    log.info('Vérification de la table shifts...');
    
    const shiftColumns = [
      'ALTER TABLE shifts ADD COLUMN IF NOT EXISTS start_time TIME',
      'ALTER TABLE shifts ADD COLUMN IF NOT EXISTS end_time TIME',
      'ALTER TABLE shifts ADD COLUMN IF NOT EXISTS break_duration INTEGER DEFAULT 0',
      'ALTER TABLE shifts ADD COLUMN IF NOT EXISTS notes TEXT',
      'ALTER TABLE shifts ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT \'scheduled\''
    ];
    
    for (const columnQuery of shiftColumns) {
      try {
        await client.query(columnQuery);
        log.success(`Colonne ajoutée: ${columnQuery.split('ADD COLUMN IF NOT EXISTS ')[1].split(' ')[0]}`);
      } catch (error) {
        log.warning(`Colonne déjà présente ou erreur: ${error.message}`);
      }
    }
    
    // 4. Mettre à jour les postes standards avec les couleurs
    log.info('Mise à jour des couleurs des postes standards...');
    
    const colorUpdates = [
      ['Poste Matin', '#0ea5e9'],
      ['Poste Soir', '#f59e0b'],
      ['Poste Nuit', '#0ea5e9'],
      ['Poste WE1', '#10b981'],
      ['Poste WE2', '#10b981']
    ];
    
    for (const [label, color] of colorUpdates) {
      try {
        const result = await client.query(
          'UPDATE standard_posts SET color = $1 WHERE label = $2',
          [color, label]
        );
        if (result.rowCount > 0) {
          log.success(`Couleur mise à jour pour ${label}: ${color}`);
        }
      } catch (error) {
        log.warning(`Erreur mise à jour couleur ${label}: ${error.message}`);
      }
    }
    
    log.success('🎉 COLONNES MANQUANTES AJOUTÉES AVEC SUCCÈS !');
    
  } catch (error) {
    log.error(`Erreur lors de l'ajout des colonnes: ${error.message}`);
    throw error;
  } finally {
    client.release();
  }
}

async function main() {
  try {
    await addMissingColumns();
    log.success('✨ MISE À JOUR TERMINÉE AVEC SUCCÈS !');
  } catch (error) {
    log.error(`Erreur fatale: ${error.message}`);
    process.exit(1);
  } finally {
    await closePool();
  }
}

// Exécuter le script
main(); 