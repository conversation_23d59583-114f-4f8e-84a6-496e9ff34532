#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

console.log('🧪 [TEST] Vérification des corrections TypeScript...');

const filePath = 'src/teamCalendarApp.ts';

try {
    const content = fs.readFileSync(filePath, 'utf8');
    let testsPassed = 0;
    let testsTotal = 0;

    // Test 1: Vérifier que showReplacementOptions existe dans l'interface
    testsTotal++;
    if (content.includes('showReplacementOptions: (employeeId: string, dateKey: string, existingShifts: any[], newPostData: any, resolve: Function) => void;')) {
        console.log('✅ Test 1: showReplacementOptions présent dans l\'interface');
        testsPassed++;
    } else {
        console.log('❌ Test 1: showReplacementOptions manquant dans l\'interface');
    }

    // Test 2: Vérifier que handleChoiceSelection existe dans l'interface
    testsTotal++;
    if (content.includes('handleChoiceSelection: (choice: string | null, employeeId: string, dateKey: string, existingShifts: any[], newPostData: any, resolve: Function) => void;')) {
        console.log('✅ Test 2: handleChoiceSelection présent dans l\'interface');
        testsPassed++;
    } else {
        console.log('❌ Test 2: handleChoiceSelection manquant dans l\'interface');
    }

    // Test 3: Vérifier qu'il n'y a pas de setupGeneralSettings dupliqué
    testsTotal++;
    const setupGeneralSettingsMatches = content.match(/setupGeneralSettings:\s*function/g);
    if (setupGeneralSettingsMatches && setupGeneralSettingsMatches.length === 1) {
        console.log('✅ Test 3: setupGeneralSettings unique (pas de doublon)');
        testsPassed++;
    } else {
        console.log(`❌ Test 3: setupGeneralSettings dupliqué (${setupGeneralSettingsMatches ? setupGeneralSettingsMatches.length : 0} occurrences)`);
    }

    // Test 4: Vérifier que l'export par défaut existe
    testsTotal++;
    if (content.includes('export default TeamCalendarApp;')) {
        console.log('✅ Test 4: Export par défaut présent');
        testsPassed++;
    } else {
        console.log('❌ Test 4: Export par défaut manquant');
    }

    // Test 5: Vérifier la structure générale (accolades équilibrées)
    testsTotal++;
    const openBraces = (content.match(/\{/g) || []).length;
    const closeBraces = (content.match(/\}/g) || []).length;
    if (openBraces === closeBraces) {
        console.log('✅ Test 5: Accolades équilibrées');
        testsPassed++;
    } else {
        console.log(`❌ Test 5: Accolades déséquilibrées (${openBraces} ouvertes, ${closeBraces} fermées)`);
    }

    // Test 6: Vérifier que toutes les fonctions de remplacement sont dans l'interface
    testsTotal++;
    const replacementFunctions = [
        'handleStrictSinglePostPolicy',
        'handleStrictReplacement',
        'handleTemporaryReplacement',
        'handleAdvancedConflictResolution',
        'handleCompleteReplacement',
        'handleSimpleAddition',
        'handleSelectiveManagement'
    ];
    
    let allReplacementFunctionsPresent = true;
    for (const func of replacementFunctions) {
        if (!content.includes(`${func}: (`)) {
            console.log(`❌ Fonction manquante dans l'interface: ${func}`);
            allReplacementFunctionsPresent = false;
        }
    }
    
    if (allReplacementFunctionsPresent) {
        console.log('✅ Test 6: Toutes les fonctions de remplacement présentes dans l\'interface');
        testsPassed++;
    } else {
        console.log('❌ Test 6: Certaines fonctions de remplacement manquent dans l\'interface');
    }

    // Résumé
    console.log('\n📊 RÉSUMÉ DES TESTS:');
    console.log(`✅ Tests réussis: ${testsPassed}/${testsTotal}`);
    console.log(`❌ Tests échoués: ${testsTotal - testsPassed}/${testsTotal}`);
    
    if (testsPassed === testsTotal) {
        console.log('\n🎉 TOUS LES TESTS SONT PASSÉS ! Les corrections sont complètes.');
        process.exit(0);
    } else {
        console.log('\n⚠️ Certains tests ont échoué. Vérifiez les corrections.');
        process.exit(1);
    }

} catch (error) {
    console.error('❌ Erreur lors du test:', error);
    process.exit(1);
}
