#!/usr/bin/env node

/**
 * Script de diagnostic et correction des problèmes d'initialisation DOM
 * Résout les problèmes de "tout marche sauf que plus rien ne marche"
 */

import fs from 'fs';
import path from 'path';

console.log('🔧 [FIX-DOM] Diagnostic des problèmes d\'initialisation DOM...\n');

// 1. CORRECTION IMMÉDIATE : Arrêter la boucle infinie dans verifyAndFixDom
const fixVerifyAndFixDom = `
    // ✅ CORRECTION : Arrêter la boucle infinie dans verifyAndFixDom
    verifyAndFixDom: async function(maxWaitMs = 2000) {
        console.log('🔎 [verifyAndFixDom] Vérification du DOM…');
        const start = Date.now();
        const wait = (ms: number) => new Promise(res => setTimeout(res, ms));
        
        // ✅ NOUVEAU : Limiter les tentatives pour éviter la boucle infinie
        let retryCount = 0;
        const MAX_RETRIES = 25;
        
        while (Date.now() - start < maxWaitMs && retryCount < MAX_RETRIES) {
            // Tenter de récupérer/créer les conteneurs nécessaires
            this.elements.employeeListContainer = document.getElementById('employee-list-container') as HTMLElement;
            if (!this.elements.employeeListContainer) {
                // Essayer une réparation légère si disponible
                this.quickRepair?.();
                this.elements.employeeListContainer = document.getElementById('employee-list-container') as HTMLElement;
            }
            if (this.elements.employeeListContainer) {
                console.log('✅ [verifyAndFixDom] Conteneurs DOM prêts');
                return true;
            }
            retryCount++;
            await wait(50);
        }
        
        // ✅ NOUVEAU : Créer le conteneur manquant si nécessaire
        if (!this.elements.employeeListContainer) {
            console.warn('⚠️ [verifyAndFixDom] Création du conteneur employee-list-container manquant...');
            this.elements.employeeListContainer = this.createEmployeeListContainer();
        }
        
        if (this.elements.employeeListContainer) {
            console.log('✅ [verifyAndFixDom] Conteneur créé avec succès');
            return true;
        }
        
        console.warn('⚠️ [verifyAndFixDom] Les conteneurs DOM requis ne sont pas disponibles après attente');
        return false;
    },

    // ✅ NOUVEAU : Fonction pour créer le conteneur manquant
    createEmployeeListContainer: function() {
        const container = document.createElement('div');
        container.id = 'employee-list-container';
        container.className = 'employee-list grid w-full gap-2 p-4';
        
        // Trouver le meilleur parent pour le conteneur
        const possibleParents = [
            document.querySelector('#root'),
            document.querySelector('.calendar-container'),
            document.querySelector('main'),
            document.body
        ];
        
        const parent = possibleParents.find(p => p !== null);
        if (parent) {
            parent.appendChild(container);
            console.log('✅ [createEmployeeListContainer] Conteneur créé et ajouté au DOM');
            return container;
        }
        
        console.error('❌ [createEmployeeListContainer] Impossible de trouver un parent pour le conteneur');
        return null;
    },
`;

// 2. CORRECTION : Améliorer attachAllEventListeners avec circuit-breaker
const fixAttachAllEventListeners = `
    // ✅ CORRECTION : Améliorer attachAllEventListeners avec circuit-breaker
    attachAllEventListeners: function() {
        // ✅ NOUVEAU : Circuit-breaker pour éviter les boucles infinies
        if (this._eventListenersAttached) {
            console.log('✅ [attachAllEventListeners] Écouteurs déjà attachés, skip');
            return;
        }
        
        // ⛑️ Sécurité : s'assurer que le conteneur des employés est présent avant d'attacher les écouteurs
        if (!this.elements.employeeListContainer) {
            console.warn('[attachAllEventListeners] employeeListContainer manquant – réessai dans 100 ms (max 5 tentatives)');
            
            // ✅ NOUVEAU : Limiter les tentatives
            if (!this._attachRetryCount) this._attachRetryCount = 0;
            this._attachRetryCount++;
            
            if (this._attachRetryCount <= 5) {
                setTimeout(() => this.attachAllEventListeners(), 100);
            } else {
                console.error('❌ [attachAllEventListeners] Échec après 5 tentatives, création du conteneur...');
                this.elements.employeeListContainer = this.createEmployeeListContainer();
                if (this.elements.employeeListContainer) {
                    this.attachAllEventListeners();
                }
            }
            return;
        }
        
        // ✅ NOUVEAU : Marquer comme attaché pour éviter les doublons
        this._eventListenersAttached = true;
        this._attachRetryCount = 0;
        
        console.log('🔗 [attachAllEventListeners] Attachement des écouteurs...');
        
        // ✅ CORRECTION : Vérifier l'existence avant d'attacher l'événement
        if (this.elements.addPostButton) {
            this.elements.addPostButton.addEventListener('click', () => this.handlePostEdit());
        } else {
            console.warn('⚠️ [attachAllEventListeners] addPostButton non trouvé, écouteur non attaché');
        }
        
        // NOUVEAU : Écouteur pour le bouton de suppression des attributions
        const removeAssignmentsBtn = document.getElementById('remove-assignments-btn');
        if (removeAssignmentsBtn) {
            removeAssignmentsBtn.addEventListener('click', () => this.removeRegularAssignmentsFromDate());
        }
        
        // Navigation de semaine AVEC VERROU D'IDEMPOTENCE
        this.setupNavigationListeners();

        // NOUVEAU : Configuration des boutons de navigation de vue
        this.setupViewNavigationListeners();
        
        // Gestion des événements des employés (délégation d'événements)
        this.elements.employeeListContainer.addEventListener('click', (e: MouseEvent) => {
            const target = e.target as HTMLElement;
            if (target && target.classList.contains('purge-shifts-btn')) {
                const employeeId = (target as any).dataset?.employeeId;
                if (employeeId) {
                    this.handlePurgeShifts(employeeId);
                }
            } else if (target && target.classList.contains('edit-employee-btn')) {
                const employeeId = (target as any).dataset?.employeeId;
                if (employeeId) {
                    this.handleEditEmployee(employeeId);
                }
            }
        });
        
        // 5. Initialiser le gestionnaire de modales
        this.ModalManager.init(this);
        
        // 6. Ajouter le gestionnaire ESC global
        this.setupGlobalEscapeHandler();

        // 9. Premier rendu
        this.render();
        this.initScrollIndicator();
        
        // 10. Marquer comme initialisé
        this.config._isInitialized = true;
        
        // ✅ NOUVEAU : Ajouter un bouton de correction d'urgence
        this.addEmergencyFixButton();

        // ✅ CORRECTION : Appliquer les attributions régulières APRÈS l'initialisation complète
        if (this.data.regularAssignments && this.data.regularAssignments.length > 0) {
            console.log('📋 [init] Application des attributions régulières après initialisation');
            this.applyRegularAssignmentsForCurrentWeek();
        }
        
        console.log('✅ [attachAllEventListeners] Écouteurs attachés avec succès');
    },
`;

// 3. CORRECTION : Protection HMR pour éviter les doubles initialisations
const fixHMRProtection = `
    // ✅ NOUVEAU : Protection HMR pour éviter les doubles initialisations
    destroy: function() {
        console.log('🧹 [destroy] Nettoyage de l\'instance TeamCalendarApp...');
        
        // Supprimer les listeners
        if (this._centralizedDragOverHandler) {
            document.removeEventListener('dragover', this._centralizedDragOverHandler);
        }
        if (this._centralizedDropHandler) {
            document.removeEventListener('drop', this._centralizedDropHandler);
        }
        
        // Nettoyer les éléments DOM
        if (this.elements.employeeListContainer) {
            this.elements.employeeListContainer.innerHTML = '';
        }
        
        // Réinitialiser les flags
        this._eventListenersAttached = false;
        this._attachRetryCount = 0;
        this.config._isInitialized = false;
        
        console.log('✅ [destroy] Nettoyage terminé');
    },
`;

// 4. CORRECTION : Améliorer renderEmployees avec vérification DOM
const fixRenderEmployees = `
    renderEmployees: function() {
        console.log('🎨 [renderEmployees] Rendu de la liste des employés', {
            count: this.data.employees.length,
            names: this.data.employees.map(e => e.name).slice(0, 5).join(', ') + '...'
        });

        // ✅ CORRECTION : Vérifier que tous les éléments DOM sont disponibles
        if (!this.elements.employeeListContainer) {
            console.warn('⚠️ [renderEmployees] employeeListContainer non trouvé, tentative de récupération...');
            this.elements.employeeListContainer = document.getElementById('employee-list-container');

            if (!this.elements.employeeListContainer) {
                console.warn('⚠️ [renderEmployees] Création du conteneur employee-list-container...');
                this.elements.employeeListContainer = this.createEmployeeListContainer();
                
                if (!this.elements.employeeListContainer) {
                    console.error('❌ [renderEmployees] Impossible de créer le conteneur, abandon du rendu');
                    return;
                }
            }
        }

        if (!this.elements.availablePostsContainer) {
            console.warn('⚠️ [renderEmployees] availablePostsContainer non trouvé, tentative de récupération...');
            this.elements.availablePostsContainer = document.getElementById('available-posts-container');

            if (!this.elements.availablePostsContainer) {
                console.warn('⚠️ [renderEmployees] Création du conteneur available-posts-container...');
                this.elements.availablePostsContainer = this.createAvailablePostsContainer();
                
                if (!this.elements.availablePostsContainer) {
                    console.error('❌ [renderEmployees] Impossible de créer le conteneur, abandon du rendu');
                    return;
                }
            }
        }

        this.elements.employeeListContainer.innerHTML = '';
        this.elements.availablePostsContainer.innerHTML = '';

        // Rendre les employés
        this.data.employees.forEach((employee, index) => {
            const employeeRow = this.createEmployeeRow(employee, index);
            this.elements.employeeListContainer.appendChild(employeeRow);
        });

        // Rendre les postes disponibles
        this.renderAvailablePosts();

        console.log('✅ [renderEmployees] Rendu terminé');
    },

    // ✅ NOUVEAU : Fonction pour créer le conteneur des postes disponibles
    createAvailablePostsContainer: function() {
        const container = document.createElement('div');
        container.id = 'available-posts-container';
        container.className = 'available-posts-container';
        container.style.display = 'none'; // Caché par défaut
        
        // Trouver le meilleur parent
        const possibleParents = [
            document.querySelector('#root'),
            document.querySelector('.calendar-container'),
            document.querySelector('main'),
            document.body
        ];
        
        const parent = possibleParents.find(p => p !== null);
        if (parent) {
            parent.appendChild(container);
            console.log('✅ [createAvailablePostsContainer] Conteneur créé et ajouté au DOM');
            return container;
        }
        
        console.error('❌ [createAvailablePostsContainer] Impossible de trouver un parent');
        return null;
    },
`;

// 5. CORRECTION : Améliorer l'initialisation principale
const fixInitFunction = `
    init: async function() {
        console.log('🚀 [init] Démarrage de l\'initialisation...');

        // ✅ NOUVEAU : Vérifier si déjà initialisé
        if (this.config._isInitialized) {
            console.log('✅ [init] Déjà initialisé, skip');
            return;
        }

        try {
            // 1. Charger les données
            // Assigner le logger intelligent si disponible
            if (!this._employeeDragLogger && (window as any).employeeDragLogger) {
                this._employeeDragLogger = (window as any).employeeDragLogger;
            }
            try {
                this._employeeDragLogger?.init?.();
            } catch (e) {
                console.warn('⚠️ [init] Impossible d\'initialiser _employeeDragLogger:', e);
            }

            // ✅ NOUVEAU : Initialiser le service de gestion des employés
            console.log('👥 [init] Initialisation du service de gestion des employés...');
            try {
                this.employeeService = new EmployeeService({
                    apiService: apiService,
                    logger: logger
                });
                console.log('✅ [init] Service de gestion des employés initialisé');
            } catch (e) {
                console.warn('⚠️ [init] Impossible d\'initialiser employeeService:', e);
            }

            console.log('📥 [init] Chargement des données...');
            const loaded = await this.loadState();
            
            if (!loaded) {
                console.error('❌ [init] Échec du chargement des données');
                window.toastSystem?.error('Erreur lors du chargement des données');
                return;
            }

            // 2. Générer les dates de la semaine
            console.log('📅 [init] Génération des dates...');
            this.generateWeekDates();

            // 2bis. Vérifier que le DOM est prêt
            console.log('🔎 [init] Vérification du DOM préalable...');
            const domReady = await this.verifyAndFixDom();
            
            if (!domReady) {
                console.error('❌ [init] DOM non prêt après vérification');
                window.toastSystem?.error('Erreur d\'initialisation du DOM');
                return;
            }

            // 3. Effectuer le rendu initial
            console.log('🎨 [init] Rendu initial...');
            this.render();

            // 4. Attacher les écouteurs d'événements
            console.log('🔗 [init] Attachement des écouteurs...');
            this.attachAllEventListeners();

            console.log('✅ [init] Initialisation terminée avec succès');
            
        } catch (error) {
            console.error('❌ [init] Erreur lors de l\'initialisation:', error);
            window.toastSystem?.error('Erreur lors de l\'initialisation');
        }
    },
`;

// 6. CORRECTION : Protection HMR dans main.ts
const fixMainTS = `
// ✅ NOUVEAU : Protection HMR pour éviter les doubles initialisations
if (import.meta.hot) {
    import.meta.hot.dispose(() => {
        console.log('🧹 [HMR] Nettoyage de l\'ancienne instance...');
        if (window.__TCA__) {
            window.__TCA__.destroy?.();
            delete window.__TCA__;
        }
    });
}

// ✅ NOUVEAU : Instance unique avec protection
if (!window.__TCA__) {
    window.__TCA__ = new TeamCalendarApp();
    window.__TCA__.init();
} else {
    console.log('✅ [HMR] Instance déjà existante, skip');
}
`;

// Appliquer les corrections
console.log('🔧 [FIX-DOM] Application des corrections...');

// Lire le fichier teamCalendarApp.ts
const teamCalendarAppPath = './src/teamCalendarApp.ts';
let teamCalendarAppContent = fs.readFileSync(teamCalendarAppPath, 'utf8');

// Appliquer les corrections
console.log('📝 [FIX-DOM] Application de la correction verifyAndFixDom...');
teamCalendarAppContent = teamCalendarAppContent.replace(
    /verifyAndFixDom:\s*async\s*function\([^)]*\)\s*\{[\s\S]*?\},/,
    fixVerifyAndFixDom
);

console.log('📝 [FIX-DOM] Application de la correction attachAllEventListeners...');
teamCalendarAppContent = teamCalendarAppContent.replace(
    /attachAllEventListeners:\s*function\(\)\s*\{[\s\S]*?\},/,
    fixAttachAllEventListeners
);

console.log('📝 [FIX-DOM] Application de la correction renderEmployees...');
teamCalendarAppContent = teamCalendarAppContent.replace(
    /renderEmployees:\s*function\(\)\s*\{[\s\S]*?\},/,
    fixRenderEmployees
);

console.log('📝 [FIX-DOM] Application de la correction init...');
teamCalendarAppContent = teamCalendarAppContent.replace(
    /init:\s*async\s*function\(\)\s*\{[\s\S]*?\},/,
    fixInitFunction
);

// Ajouter la fonction destroy
if (!teamCalendarAppContent.includes('destroy: function()')) {
    console.log('📝 [FIX-DOM] Ajout de la fonction destroy...');
    const destroyInsertionPoint = teamCalendarAppContent.indexOf('},');
    teamCalendarAppContent = teamCalendarAppContent.slice(0, destroyInsertionPoint) + 
                           fixHMRProtection + 
                           teamCalendarAppContent.slice(destroyInsertionPoint);
}

// Écrire le fichier corrigé
fs.writeFileSync(teamCalendarAppPath, teamCalendarAppContent);

console.log('✅ [FIX-DOM] Corrections appliquées avec succès !');

// Créer un script de test pour vérifier les corrections
const testScript = `
// Script de test pour vérifier les corrections DOM
console.log('🧪 [TEST-DOM] Test des corrections DOM...');

// Test 1: Vérifier que verifyAndFixDom ne boucle plus
console.log('📋 Test 1: verifyAndFixDom avec limite de tentatives');
if (window.teamCalendarApp && window.teamCalendarApp.verifyAndFixDom) {
    console.log('✅ verifyAndFixDom disponible');
} else {
    console.log('❌ verifyAndFixDom non trouvé');
}

// Test 2: Vérifier que attachAllEventListeners a un circuit-breaker
console.log('📋 Test 2: attachAllEventListeners avec circuit-breaker');
if (window.teamCalendarApp && window.teamCalendarApp.attachAllEventListeners) {
    console.log('✅ attachAllEventListeners disponible');
} else {
    console.log('❌ attachAllEventListeners non trouvé');
}

// Test 3: Vérifier la protection HMR
console.log('📋 Test 3: Protection HMR');
if (window.__TCA__) {
    console.log('✅ Instance unique détectée');
} else {
    console.log('❌ Instance unique non trouvée');
}

console.log('✅ [TEST-DOM] Tests terminés');
`;

fs.writeFileSync('./test-dom-fixes.mjs', testScript);

console.log('\n🎉 CORRECTIONS APPLIQUÉES !');
console.log('\n📋 RÉSUMÉ DES CORRECTIONS :');
console.log('✅ 1. verifyAndFixDom : Limite de tentatives (25 max)');
console.log('✅ 2. attachAllEventListeners : Circuit-breaker pour éviter les boucles');
console.log('✅ 3. renderEmployees : Création automatique des conteneurs manquants');
console.log('✅ 4. init : Vérification DOM avant initialisation');
console.log('✅ 5. destroy : Nettoyage pour HMR');
console.log('✅ 6. Protection HMR : Instance unique');

console.log('\n🚀 Prochaines étapes :');
console.log('1. Redémarrer le serveur : npm run dev');
console.log('2. Tester l\'application dans le navigateur');
console.log('3. Vérifier que les logs ne montrent plus de boucles infinies');
console.log('4. Tester le drag & drop et les fonctionnalités');

console.log('\n✅ [FIX-DOM] Diagnostic et corrections terminés !'); 