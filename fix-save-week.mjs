import fs from 'fs';
import path from 'path';

const targetFile = path.resolve('src/teamCalendarApp.ts');

const searchBlock1 = `saveCurrentWeek: async function(options = {}) {
        const { force = false, silent = false } = options;`;

const replacementBlock1 = `saveCurrentWeek: async function(options = {}) {
        const { force = false, silent = false } = options;

        if (this._isSavingWeek) {
            console.log('🔄 [saveCurrentWeek] Sauvegarde déjà en cours, planification...');
            this._pendingSaveWeek = true;
            return;
        }`;

const searchBlock2 = `this.hideLoadingIndicator(); // Cacher l'indicateur
        }`;

const replacementBlock2 = `this.hideLoadingIndicator(); // Cacher l'indicateur
            this._isSavingWeek = false; // Libérer le verrou

            // ✅ NOUVEAU : Exécuter la sauvegarde en attente
            if (this._pendingSaveWeek) {
                console.log('⚙️ [saveCurrentWeek] Exécution de la sauvegarde en attente...');
                this._pendingSaveWeek = false;
                // Utiliser setTimeout pour éviter une récursion profonde et laisser le temps à l'UI de respirer
                setTimeout(() => this.saveCurrentWeek({ force: true, silent: true }), 100); 
            }
        }`;


try {
    let content = fs.readFileSync(targetFile, 'utf8');
    let changed = false;

    if (content.includes(searchBlock1)) {
        content = content.replace(searchBlock1, replacementBlock1);
        console.log('✅ Bloc 1 remplacé.');
        changed = true;
    } else if (!content.includes(replacementBlock1)) {
        console.error('❌ Le bloc 1 à remplacer n\'a pas été trouvé.');
    }

    if (content.includes(searchBlock2)) {
        content = content.replace(searchBlock2, replacementBlock2);
        console.log('✅ Bloc 2 remplacé.');
        changed = true;
    } else if (!content.includes(replacementBlock2)) {
         console.error('❌ Le bloc 2 à remplacer n\'a pas été trouvé.');
    }
    
    if(changed) {
        fs.writeFileSync(targetFile, content, 'utf8');
        console.log('✅ Correction de la persistance des postes appliquée avec succès.');
    } else {
        console.log('✅ Aucune modification nécessaire, le patch semble déjà appliqué.');
    }

} catch (error) {
    console.error('❌ Erreur lors de l\'application du patch :', error);
} 