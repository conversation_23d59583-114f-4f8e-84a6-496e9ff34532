import { query } from '../server/config/database.js';

console.log('🔍 [CHECK] Vérification de la structure de la table shifts...');

async function checkTableStructure() {
    try {
        // Vérifier la structure de la table shifts
        const structure = await query(`
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'shifts'
            ORDER BY ordinal_position
        `);
        
        console.log('📋 [STRUCTURE] Colonnes de la table shifts:');
        structure.rows.forEach((col, index) => {
            console.log(`  ${index + 1}. ${col.column_name} (${col.data_type})`);
        });
        
        // Vérifier spécifiquement date_key
        const dateKeyStructure = await query(`
            SELECT column_name, data_type, character_maximum_length
            FROM information_schema.columns 
            WHERE table_name = 'shifts' AND column_name = 'date_key'
        `);
        
        if (dateKeyStructure.rows.length > 0) {
            const col = dateKeyStructure.rows[0];
            console.log(`🎯 [DATE_KEY] Type: ${col.data_type}, Longueur max: ${col.character_maximum_length || 'N/A'}`);
        }
        
        // Échantillon de données actuelles
        const sample = await query(`
            SELECT date_key, pg_typeof(date_key) as type, date_key::text as text_value
            FROM shifts 
            LIMIT 5
        `);
        
        console.log('📊 [ÉCHANTILLON] Données actuelles:');
        sample.rows.forEach((row, index) => {
            console.log(`  ${index + 1}. "${row.date_key}" (type: ${row.type}, text: "${row.text_value}")`);
        });
        
    } catch (error) {
        console.error('❌ [ERREUR] Erreur lors de la vérification:', error);
    }
}

checkTableStructure().then(() => {
    console.log('✅ [TERMINÉ] Vérification terminée');
    process.exit(0);
}).catch(error => {
    console.error('💥 [ÉCHEC] Échec:', error);
    process.exit(1);
}); 