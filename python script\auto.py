import json
from pathlib import Path

# 📁 Dossier contenant tes fichiers .json (exportés avec cursor-export)
input_dir = Path(r"C:\Users\<USER>\Desktop\Cursor_Checkpoints\entriesjson")
output_dir = Path.home() / "Desktop" / "Checkpoint_Restaurés"
output_dir.mkdir(exist_ok=True)

count = 0

# 🔁 Parcours chaque .json
for json_file in input_dir.glob("*.json"):
    with open(json_file, encoding="utf-8") as f:
        data = json.load(f)

    messages = data.get("messages", [])
    version_index = 1

    for msg in messages:
        if msg.get("role") == "assistant":
            content = msg.get("content", "")
            if "```" in content:
                # Découpe en blocs de code
                parts = content.split("```")
                for i in range(1, len(parts), 2):
                    lang_and_code = parts[i].split("\n", 1)
                    if len(lang_and_code) == 2:
                        lang = lang_and_code[0].strip()
                        code = lang_and_code[1].strip()
                    else:
                        lang = "txt"
                        code = lang_and_code[0].strip()

                    filename = f"{json_file.stem}_checkpoint_{version_index}.{lang or 'txt'}"
                    with open(output_dir / filename, "w", encoding="utf-8") as out:
                        out.write(code)
                    version_index += 1
                    count += 1

print(f"{count} checkpoints exportés dans {output_dir}")