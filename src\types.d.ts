// Déclarations globales pour les types manquants

declare global {
  interface HTMLElement {
    dataset: DOMStringMap;
    checked?: boolean;
    value?: string;
    files?: FileList;
    _swapClickHandler?: (e: Event) => void;
    _sortable?: any;
  }

  interface Element {
    value?: string;
    checked?: boolean;
  }

  interface EventTarget {
    dataset?: DOMStringMap;
    checked?: boolean;
    value?: string;
    files?: FileList;
  }

  interface HTMLInputElement {
    files: FileList | null;
  }

  interface DOMStringMap {
    [name: string]: string | undefined;
  }

  interface Window {
    Sortable: any;
  }
}

// Types pour SortableJS
declare module 'sortablejs' {
  interface SortableEvent {
    item: HTMLElement;
    from: HTMLElement;
    to: HTMLElement;
    oldIndex: number;
    newIndex: number;
  }

  interface SortableOptions {
    group?: string | { name: string; pull?: boolean; put?: boolean };
    animation?: number;
    draggable?: string;
    onAdd?: (evt: SortableEvent) => void;
    onEnd?: (evt: SortableEvent) => void;
    onUpdate?: (evt: SortableEvent) => void;
  }

  class Sortable {
    constructor(el: HTMLElement, options?: SortableOptions);
    destroy(): void;
    static create(el: HTMLElement, options?: SortableOptions): Sortable;
  }

  export = Sortable;
}

export {}; 