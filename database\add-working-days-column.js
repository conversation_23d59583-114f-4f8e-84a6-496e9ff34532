const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'postgres',
  password: '', // Replace with your actual password if you have one
  port: 5432,
});

async function addWorkingDaysColumn() {
  try {
    const client = await pool.connect();
    const result = await client.query('ALTER TABLE standard_posts ADD COLUMN working_days JSONB;');
    console.log('Successfully added working_days column.');
    client.release();
  } catch (err) {
    console.error('Error adding working_days column:', err);
  } finally {
    pool.end();
  }
}

addWorkingDaysColumn();
