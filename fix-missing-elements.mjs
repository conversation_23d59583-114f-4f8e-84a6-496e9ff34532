#!/usr/bin/env node

/**
 * Correction des éléments DOM manquants
 */

import fs from 'fs';

console.log('🔧 [FIX-MISSING-ELEMENTS] Correction des éléments DOM manquants...\n');

const filePath = './src/teamCalendarApp.ts';
let content = fs.readFileSync(filePath, 'utf8');

// Rechercher la section elements et ajouter les initialisations manquantes
const elementsPattern = /(elements:\s*\{[\s\S]*?)(\s*\},)/;

if (elementsPattern.test(content)) {
    content = content.replace(elementsPattern, (match, before, after) => {
        let updated = before;
        
        // Ajouter addPostButton s'il n'existe pas
        if (!before.includes('addPostButton:')) {
            updated += '\n        addPostButton: document.getElementById(\'add-post-btn\'),\n        ';
        }
        
        // Ajouter availablePostsContainer s'il n'existe pas
        if (!before.includes('availablePostsContainer:')) {
            updated += '\n        availablePostsContainer: document.getElementById(\'available-posts-container\'),\n        ';
        }
        
        return updated + after;
    });
    
    console.log('✅ Éléments DOM ajoutés dans elements');
}

// S'assurer que setupEmployeeDragDrop est appelé dans attachAllEventListeners
if (!content.includes('this.setupEmployeeDragDrop()')) {
    const attachPattern = /(attachAllEventListeners:\s*function\(\)\s*\{[\s\S]*?)(\s*console\.log\('✅ \[attachAllEventListeners\] Écouteurs attachés avec succès'\);\s*\})/;
    if (attachPattern.test(content)) {
        content = content.replace(attachPattern, (match, before, after) => {
            return before + '\n        // ✅ CORRECTION : Configuration du drag & drop des employés\n        this.setupEmployeeDragDrop();\n        ' + after;
        });
        console.log('✅ setupEmployeeDragDrop() ajouté dans attachAllEventListeners');
    }
}

// S'assurer que createAvailablePostsContainer est appelé dans renderEmployees
if (!content.includes('this.createAvailablePostsContainer()')) {
    const renderPattern = /(renderEmployees:\s*function\(\)\s*\{[\s\S]*?)(\s*console\.log\('✅ \[renderEmployees\] Employés rendus avec succès'\);\s*\})/;
    if (renderPattern.test(content)) {
        content = content.replace(renderPattern, (match, before, after) => {
            return before + '\n        // ✅ CORRECTION : Créer le conteneur des postes disponibles\n        if (!this.elements.availablePostsContainer) {\n            this.elements.availablePostsContainer = this.createAvailablePostsContainer();\n        }\n        ' + after;
        });
        console.log('✅ createAvailablePostsContainer() ajouté dans renderEmployees');
    }
}

// S'assurer que ModalManager.init est appelé dans attachAllEventListeners
if (!content.includes('this.ModalManager.init(this)')) {
    const attachPattern = /(attachAllEventListeners:\s*function\(\)\s*\{[\s\S]*?)(\s*console\.log\('✅ \[attachAllEventListeners\] Écouteurs attachés avec succès'\);\s*\})/;
    if (attachPattern.test(content)) {
        content = content.replace(attachPattern, (match, before, after) => {
            return before + '\n        // ✅ CORRECTION : Initialiser le ModalManager\n        if (this.ModalManager && !this.ModalManager._initialized) {\n            this.ModalManager.init(this);\n            this.ModalManager._initialized = true;\n        }\n        ' + after;
        });
        console.log('✅ ModalManager.init() ajouté dans attachAllEventListeners');
    }
}

// Écrire les corrections
fs.writeFileSync(filePath, content);

console.log('\n✅ Corrections appliquées !');
console.log('\n📋 Éléments corrigés :');
console.log('✅ 1. addPostButton initialisé avec getElementById');
console.log('✅ 2. availablePostsContainer initialisé avec getElementById');
console.log('✅ 3. setupEmployeeDragDrop() ajouté dans attachAllEventListeners');
console.log('✅ 4. createAvailablePostsContainer() ajouté dans renderEmployees');
console.log('✅ 5. ModalManager.init() ajouté dans attachAllEventListeners');

console.log('\n🚀 Instructions pour tester :');
console.log('1. Redémarrer le serveur : npm run dev');
console.log('2. Ouvrir http://localhost:5173');
console.log('3. Vérifier que :');
console.log('   - Le drag & drop des employés fonctionne');
console.log('   - Les modales des paramètres s\'ouvrent');
console.log('   - Les postes disponibles sont visibles');

console.log('\n✅ [FIX-MISSING-ELEMENTS] Corrections terminées !'); 