# Guide de Test : Drag & Drop des Grips d'Attributions Régulières

## 🎯 Objectif
Tester le système de drag & drop pour déplacer les attributions régulières avec menu de confirmation.

## 🔧 Fonctionnalités à Tester

### 1. **Apparition des Grips**
- [ ] Les grips apparaissent sous les blocs récurrents au survol
- [ ] Les grips sont visibles uniquement sur les shifts avec `isRegular: true` et `assignmentId`
- [ ] Les grips ont une apparence distinctive (barre bleue-violette de 6px)

### 2. **Drag & Drop des Grips**
- [ ] Glisser un grip déclenche le système spécialisé (pas SortableJS)
- [ ] Les zones de drop d'employés se surlignent pendant le drag
- [ ] Le shift parent devient semi-transparent pendant le drag
- [ ] Le drop sur une ligne d'employé déclenche le menu de confirmation

### 3. **Menu de Confirmation**
- [ ] Le menu s'affiche avec les bonnes informations (employé source → employé cible)
- [ ] Deux options sont disponibles :
  - **Changement Permanent** : Modifie l'attribution régulière
  - **Remplacement Ponctuel** : Change seulement pour des dates spécifiques
- [ ] Le menu se ferme avec Escape ou clic extérieur
- [ ] Le bouton "Annuler" ferme le menu sans action

### 4. **Changement Permanent**
- [ ] Met à jour l'attribution régulière dans `this.data.regularAssignments`
- [ ] Déplace tous les shifts générés vers le nouvel employé
- [ ] Affiche un toast de succès
- [ ] Sauvegarde via l'API (PUT `/api/regular-assignments/:id`)

### 5. **Remplacement Ponctuel**
- [ ] Crée un shift individuel (non-régulier) pour l'employé cible
- [ ] Supprime le shift régulier pour la date spécifique
- [ ] Conserve l'attribution régulière intacte pour les autres dates
- [ ] Affiche un toast explicatif

## 🧪 Scénarios de Test

### Scénario 1 : Changement Permanent
1. Créer une attribution régulière pour un employé
2. Attendre que les shifts soient générés
3. Survoler un shift récurrent → grip apparaît
4. Glisser le grip vers un autre employé
5. Sélectionner "Changement Permanent"
6. ✅ Vérifier que tous les futurs shifts sont transférés

### Scénario 2 : Remplacement Ponctuel
1. Créer une attribution régulière pour un employé
2. Attendre que les shifts soient générés
3. Survoler un shift récurrent → grip apparaît
4. Glisser le grip vers un autre employé
5. Sélectionner "Remplacement Ponctuel"
6. ✅ Vérifier qu'un seul shift est modifié

### Scénario 3 : Annulation
1. Glisser un grip vers un autre employé
2. Cliquer "Annuler" ou appuyer Escape
3. ✅ Vérifier qu'aucun changement n'est effectué

## 🔍 Debug et Vérification

### Logs à Surveiller
```javascript
// Dans la console du navigateur
🎯 [GRIP] Début drag grip pour attribution {assignmentId}
🎯 [DROP] Attribution régulière {assignmentId} → employé {employeeName}
🎯 [showRegularAssignmentConfirmationMenu] Affichage menu pour: ...
🔄 [handlePermanentRegularAssignmentChange] Changement permanent ...
🔄 [executeReplacementForDate] Remplacement pour {dateKey}: ...
```

### Commandes Debug
```javascript
// Vérifier l'état des grips
document.querySelectorAll('.grip-regular').length

// Vérifier les attributions régulières
teamCalendarApp.data.regularAssignments

// Vérifier les shifts générés
teamCalendarApp.data.schedule
```

## 📋 Checklist de Validation

### Interface Utilisateur
- [ ] Grips visibles au survol des blocs récurrents
- [ ] Animation fluide du drag & drop
- [ ] Menu de confirmation moderne et accessible
- [ ] Toasts informatifs après les actions
- [ ] Pas d'interférence avec le drag & drop normal des shifts

### Fonctionnalité
- [ ] Changement permanent fonctionne
- [ ] Remplacement ponctuel fonctionne  
- [ ] Sauvegarde persistante des modifications
- [ ] Gestion d'erreur avec rollback
- [ ] Compatibilité avec les autres fonctionnalités

### Performance
- [ ] Pas de lag pendant le drag
- [ ] Rendu fluide après les modifications
- [ ] Pas de fuites mémoire avec les listeners

## 🚨 Problèmes Connus à Vérifier

1. **Conflit avec SortableJS** : Les grips ne doivent pas déclencher le drag des shifts
2. **Zones de drop manquantes** : S'assurer que les lignes d'employés sont détectées
3. **Menu modal** : Vérifier que le modal s'affiche au-dessus de tout

## 🎉 Validation Finale

Le système est considéré comme fonctionnel si :
- ✅ Les grips apparaissent et fonctionnent
- ✅ Le menu de confirmation s'affiche
- ✅ Les deux options (permanent/ponctuel) fonctionnent
- ✅ Les modifications sont persistantes
- ✅ L'UX est fluide et intuitive 