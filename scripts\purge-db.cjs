const { Pool } = require('pg');

const pool = new Pool({
  host: '*************',
  port: 5432,
  database: 'glive_db',
  user: 'postgres',
  password: 'SebbZ12342323!!'
});

// IDs des employés de base à conserver
const BASE_EMPLOYEES = [
  '26301449-caa1-46e4-a4f8-7aa1ef5e3166', // <PERSON>
  '41ff822b-baab-45b7-9d75-9b23d9ea3451', // <PERSON>
  '6e9837d6-69e4-4dfd-8daa-54717092c7c3', // <PERSON>
  '6194b40f-a5d8-4910-8c92-e8b0bba17931', // <PERSON>
  'fb361a02-afdf-400b-9c1a-12011cb8c67f', // <PERSON>
];

async function purgeDatabaseData() {
  console.log('🧹 PURGE COMPLÈTE DE LA BASE DE DONNÉES DISTANTE');
  console.log('🌐 Serveur: *************:5432 - Base: glive_db');
  console.log('⚠️  Suppression de TOUTES les données sauf 5 employés de base\n');
  
  let client;
  try {
    client = await pool.connect();
    console.log('✅ Connexion à PostgreSQL DISTANT établie');
    
    // État avant purge
    console.log('\n📊 ÉTAT INITIAL:');
    const beforeShifts = await client.query('SELECT COUNT(*) as total FROM shifts');
    const beforeEmployees = await client.query('SELECT COUNT(*) as total FROM employees');
    
    console.log(`   📈 Quarts: ${beforeShifts.rows[0].total}`);
    console.log(`   👥 Employés: ${beforeEmployees.rows[0].total}`);
    
    // 1. Supprimer TOUS les quarts de travail
    console.log('\n🗑️ Suppression de tous les quarts...');
    const deleteShifts = await client.query('DELETE FROM shifts');
    console.log(`   ✅ ${deleteShifts.rowCount} quarts supprimés`);
    
    // 2. Supprimer les assignements réguliers
    console.log('\n🗑️ Suppression des assignements réguliers...');
    try {
      const deleteAssignments = await client.query('DELETE FROM regular_assignments');
      console.log(`   ✅ ${deleteAssignments.rowCount} assignements supprimés`);
    } catch (err) {
      console.log('   ⚠️  Table regular_assignments non trouvée (OK)');
    }
    
    // 3. Supprimer les congés
    console.log('\n🗑️ Suppression des congés...');
    try {
      const deleteVacations = await client.query('DELETE FROM vacation_periods');
      console.log(`   ✅ ${deleteVacations.rowCount} congés supprimés`);
    } catch (err) {
      console.log('   ⚠️  Table vacation_periods non trouvée (OK)');
    }
    
    // 4. Analyser les doublons d'employés
    console.log('\n🔍 Analyse des doublons d\'employés...');
    const duplicates = await client.query(`
      SELECT name, COUNT(*) as count, array_agg(id::text) as ids
      FROM employees 
      GROUP BY name 
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `);
    
    let totalEmployeesDeleted = 0;
    
    if (duplicates.rows.length > 0) {
      console.log(`   🔍 ${duplicates.rows.length} groupes de doublons trouvés:`);
      
      for (const duplicate of duplicates.rows) {
        console.log(`   📝 "${duplicate.name}": ${duplicate.count} exemplaires`);
        
        // Garder le premier ID, supprimer les autres
        const idsToDelete = duplicate.ids.slice(1);
        
        if (idsToDelete.length > 0) {
          const deleteDuplicates = await client.query(
            'DELETE FROM employees WHERE id = ANY($1)',
            [idsToDelete]
          );
          totalEmployeesDeleted += deleteDuplicates.rowCount;
          console.log(`     ✅ ${deleteDuplicates.rowCount} doublons supprimés`);
        }
      }
    } else {
      console.log('   ✅ Aucun doublon trouvé');
    }
    
    // 5. Supprimer les employés qui ne sont PAS dans la liste de base
    console.log('\n🗑️ Suppression des employés non-essentiels...');
    const deleteNonBaseEmployees = await client.query(
      'DELETE FROM employees WHERE id != ALL($1)',
      [BASE_EMPLOYEES]
    );
    totalEmployeesDeleted += deleteNonBaseEmployees.rowCount;
    console.log(`   ✅ ${deleteNonBaseEmployees.rowCount} employés non-essentiels supprimés`);
    
    // 6. Reset des paramètres de l'application
    console.log('\n⚙️ Reset des paramètres de l\'application...');
    await client.query('DELETE FROM app_settings');
    await client.query(`
      INSERT INTO app_settings (setting_key, setting_value) VALUES
      ('weekStartsOn', 'monday'),
      ('weekStartDay', '1'),
      ('appVersion', '1.0.0'),
      ('currentWeekOffset', '0'),
      ('viewMode', 'week')
    `);
    console.log('   ✅ Paramètres réinitialisés');
    
    // État final
    console.log('\n📊 ÉTAT FINAL:');
    const afterShifts = await client.query('SELECT COUNT(*) as total FROM shifts');
    const afterEmployees = await client.query('SELECT COUNT(*) as total FROM employees');
    
    console.log(`   📈 Quarts: ${afterShifts.rows[0].total}`);
    console.log(`   👥 Employés: ${afterEmployees.rows[0].total}`);
    
    // Vérifier les employés restants
    const remainingEmployees = await client.query(
      'SELECT id, name, status FROM employees ORDER BY name'
    );
    
    console.log('\n✅ EMPLOYÉS CONSERVÉS:');
    if (remainingEmployees.rows.length > 0) {
      remainingEmployees.rows.forEach((emp, i) => {
        console.log(`   ${i+1}. ${emp.name} (${emp.status}) - ${emp.id.substring(0, 8)}...`);
      });
    } else {
      console.log('   ⚠️  AUCUN employé conservé! Vérifiez les IDs de base.');
    }
    
    // Résumé
    console.log('\n🎯 RÉSUMÉ DE LA PURGE:');
    console.log(`   🌐 Serveur distant: *************`);
    console.log(`   🗑️ ${beforeShifts.rows[0].total} quarts supprimés`);
    console.log(`   👥 ${totalEmployeesDeleted} employés supprimés`);
    console.log(`   ✅ ${remainingEmployees.rows.length} employés conservés`);
    console.log('   🛡️ 5 employés de base préservés');
    
    console.log('\n✅ PURGE TERMINÉE AVEC SUCCÈS!');
    
  } catch (error) {
    console.error('\n❌ ERREUR PENDANT LA PURGE:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
    console.log('🔌 Connexion fermée');
  }
}

// Exécuter immédiatement
console.log('🚀 Démarrage de la purge sur serveur distant...');
purgeDatabaseData(); 