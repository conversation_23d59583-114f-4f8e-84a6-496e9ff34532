/**
 * 🧪 SCRIPT DE TEST - SYSTÈME DE LOGS INTELLIGENT DRAG & DROP EMPLOYÉS
 * 
 * Ce script teste et démontre le nouveau système de logs intelligent
 * pour les déplacements d'employés avec session persistante.
 */

console.log('🧪 [TEST-DRAG-LOGGER] Démarrage des tests du système de logs intelligent...');

// Fonction pour tester le système de logs
function testEmployeeDragLogger() {
    console.log('🔬 [TEST] === TEST SYSTÈME LOGS INTELLIGENT ===');
    
    // Vérifier que TeamCalendarApp est disponible
    if (!window.TeamCalendarApp) {
        console.error('❌ [TEST] TeamCalendarApp non disponible');
        return false;
    }
    
    const app = window.TeamCalendarApp;
    
    // Vérifier que le logger est initialisé
    if (!app._employeeDragLogger) {
        console.error('❌ [TEST] Logger intelligent non trouvé');
        return false;
    }
    
    const logger = app._employeeDragLogger;
    
    // Test 1: Initialisation
    console.log('🧪 [TEST-1] Test d\'initialisation...');
    logger.init();
    
    if (!logger.sessionId || !logger.testId) {
        console.error('❌ [TEST-1] Échec initialisation');
        return false;
    }
    
    console.log('✅ [TEST-1] Initialisation réussie', {
        sessionId: logger.sessionId,
        testId: logger.testId,
        isInitialized: logger.isInitialized
    });
    
    // Test 2: Logging d'actions
    console.log('🧪 [TEST-2] Test logging d\'actions...');
    
    // Simuler un employé
    const fakeEmployee = {
        id: 'test-employee-123',
        name: 'Test Employee'
    };
    
    // Test logging drag start
    logger.logDragStart(fakeEmployee, {
        currentIndex: 0,
        employeeOrder: [fakeEmployee],
        trigger: 'test',
        totalEmployees: 1
    });
    
    // Test logging drag end
    logger.logDragEnd({
        success: true,
        employee: {
            id: fakeEmployee.id.substring(0, 8),
            name: fakeEmployee.name
        },
        oldIndex: 0,
        newIndex: 1,
        newOrder: ['Test Employee'],
        conflicts: []
    }, {
        duration: 500
    });
    
    // Test logging conflit
    logger.logConflict('TEST_CONFLICT', {
        testData: 'test conflict details'
    });
    
    // Test logging sauvegarde
    logger.logOrderSave({
        success: true,
        method: 'localStorage'
    }, {
        employeeCount: 1,
        orderChanged: true
    });
    
    if (logger.actions.length < 4) {
        console.error('❌ [TEST-2] Pas assez d\'actions loggées');
        return false;
    }
    
    console.log('✅ [TEST-2] Logging d\'actions réussi', {
        totalActions: logger.actions.length,
        lastAction: logger.actions[logger.actions.length - 1].actionType
    });
    
    // Test 3: Persistance localStorage
    console.log('🧪 [TEST-3] Test persistance localStorage...');
    
    const storageKey = `dragLogger_${logger.sessionId}`;
    const storedData = localStorage.getItem(storageKey);
    
    if (!storedData) {
        console.error('❌ [TEST-3] Données non persistées');
        return false;
    }
    
    try {
        const parsedData = JSON.parse(storedData);
        console.log('✅ [TEST-3] Persistance localStorage réussie', {
            storedActions: parsedData.length,
            firstAction: parsedData[0]?.actionType,
            lastAction: parsedData[parsedData.length - 1]?.actionType
        });
    } catch (error) {
        console.error('❌ [TEST-3] Erreur parsing données:', error);
        return false;
    }
    
    // Test 4: Anti-spam
    console.log('🧪 [TEST-4] Test anti-spam...');
    
    const actionsBefore = logger.actions.length;
    
    // Essayer de logger la même action rapidement (doit être ignorée)
    logger.logDragStart(fakeEmployee, {
        currentIndex: 0,
        employeeOrder: [fakeEmployee],
        trigger: 'test',
        totalEmployees: 1
    });
    
    const actionsAfter = logger.actions.length;
    
    if (actionsAfter > actionsBefore) {
        console.warn('⚠️ [TEST-4] Anti-spam pourrait ne pas fonctionner correctement');
    } else {
        console.log('✅ [TEST-4] Anti-spam fonctionne correctement');
    }
    
    // Test 5: Session ID persistante
    console.log('🧪 [TEST-5] Test session ID persistante...');
    
    const currentSessionId = logger.sessionId;
    
    // Simuler une réinitialisation
    logger.isInitialized = false;
    logger.init();
    
    if (logger.sessionId !== currentSessionId) {
        console.error('❌ [TEST-5] Session ID non persistante');
        return false;
    }
    
    console.log('✅ [TEST-5] Session ID persistante réussie', {
        sessionId: logger.sessionId
    });
    
    console.log('🎉 [TEST] Tous les tests réussis !');
    return true;
}

// Fonction pour afficher le statut du logger
function showLoggerStatus() {
    console.log('📊 [STATUS] === STATUT LOGGER INTELLIGENT ===');
    
    if (!window.TeamCalendarApp || !window.TeamCalendarApp._employeeDragLogger) {
        console.log('❌ [STATUS] Logger non disponible');
        return;
    }
    
    const logger = window.TeamCalendarApp._employeeDragLogger;
    
    console.log('📈 [STATUS] Informations générales:', {
        sessionId: logger.sessionId,
        testId: logger.testId,
        isInitialized: logger.isInitialized,
        totalActions: logger.actions.length
    });
    
    // Résumé des actions
    const actionsSummary = {};
    logger.actions.forEach(action => {
        actionsSummary[action.actionType] = (actionsSummary[action.actionType] || 0) + 1;
    });
    
    console.log('📊 [STATUS] Résumé des actions:', actionsSummary);
    
    // Dernières actions
    const recentActions = logger.actions.slice(-5);
    console.log('⏰ [STATUS] 5 dernières actions:', recentActions.map(a => ({
        type: a.actionType,
        time: new Date(a.timestamp).toLocaleTimeString(),
        seq: a.sequenceNumber
    })));
    
    // Vérifier localStorage
    try {
        const storageKey = `dragLogger_${logger.sessionId}`;
        const storedData = localStorage.getItem(storageKey);
        const parsedData = storedData ? JSON.parse(storedData) : [];
        
        console.log('💾 [STATUS] Persistance localStorage:', {
            key: storageKey,
            storedActions: parsedData.length,
            lastStored: parsedData[parsedData.length - 1]?.timestamp
        });
    } catch (error) {
        console.error('❌ [STATUS] Erreur localStorage:', error);
    }
}

// Fonction pour nettoyer les logs de test
function cleanupTestLogs() {
    console.log('🧹 [CLEANUP] Nettoyage des logs de test...');
    
    let cleaned = 0;
    
    // Nettoyer localStorage
    for (let i = localStorage.length - 1; i >= 0; i--) {
        const key = localStorage.key(i);
        if (key && key.startsWith('dragLogger_')) {
            localStorage.removeItem(key);
            cleaned++;
        }
    }
    
    // Nettoyer sessionStorage
    for (let i = sessionStorage.length - 1; i >= 0; i--) {
        const key = sessionStorage.key(i);
        if (key && (key.startsWith('dragLogger_') || key === 'dragLogger_refreshMarker')) {
            sessionStorage.removeItem(key);
            cleaned++;
        }
    }
    
    console.log(`✅ [CLEANUP] ${cleaned} entrées nettoyées`);
    
    // Réinitialiser le logger
    if (window.TeamCalendarApp && window.TeamCalendarApp._employeeDragLogger) {
        const logger = window.TeamCalendarApp._employeeDragLogger;
        logger.sessionId = null;
        logger.testId = null;
        logger.actions = [];
        logger.isInitialized = false;
        logger.duplicateCounter = {};
        
        console.log('🔄 [CLEANUP] Logger réinitialisé');
    }
}

// Fonction pour simuler une session de drag & drop
function simulateDragSession() {
    console.log('🎭 [SIMULATION] Simulation d\'une session de drag & drop...');
    
    if (!window.TeamCalendarApp || !window.TeamCalendarApp._employeeDragLogger) {
        console.error('❌ [SIMULATION] Logger non disponible');
        return;
    }
    
    const logger = window.TeamCalendarApp._employeeDragLogger;
    logger.init();
    
    const employees = [
        { id: 'emp-001', name: 'Alice Dupont' },
        { id: 'emp-002', name: 'Bob Martin' },
        { id: 'emp-003', name: 'Carol Smith' }
    ];
    
    // Simuler plusieurs opérations de drag & drop
    setTimeout(() => {
        console.log('🎭 [SIM] Drag 1: Alice de 0 vers 1');
        logger.logDragStart(employees[0], {
            currentIndex: 0,
            employeeOrder: employees,
            trigger: 'simulation',
            totalEmployees: 3
        });
        
        setTimeout(() => {
            logger.logDragEnd({
                success: true,
                employee: { id: 'emp-001', name: 'Alice Dupont' },
                oldIndex: 0,
                newIndex: 1,
                newOrder: ['Bob Martin', 'Alice Dupont', 'Carol Smith'],
                conflicts: []
            }, { duration: 750 });
        }, 750);
    }, 1000);
    
    setTimeout(() => {
        console.log('🎭 [SIM] Drag 2: Carol de 2 vers 0');
        logger.logDragStart(employees[2], {
            currentIndex: 2,
            employeeOrder: employees,
            trigger: 'simulation',
            totalEmployees: 3
        });
        
        setTimeout(() => {
            logger.logDragEnd({
                success: true,
                employee: { id: 'emp-003', name: 'Carol Smith' },
                oldIndex: 2,
                newIndex: 0,
                newOrder: ['Carol Smith', 'Bob Martin', 'Alice Dupont'],
                conflicts: []
            }, { duration: 420 });
        }, 420);
    }, 3000);
    
    setTimeout(() => {
        logger.logOrderSave({
            success: true,
            method: 'api'
        }, {
            employeeCount: 3,
            orderChanged: true
        });
        
        console.log('🎭 [SIMULATION] Session terminée');
        showLoggerStatus();
    }, 5000);
}

// Attendre que TeamCalendarApp soit prêt
function waitForTeamCalendarApp() {
    if (window.TeamCalendarApp && window.TeamCalendarApp._employeeDragLogger) {
        console.log('✅ [READY] TeamCalendarApp et logger prêts');
        
        // Exécuter les tests automatiquement
        setTimeout(() => {
            testEmployeeDragLogger();
        }, 1000);
        
        return true;
    } else {
        console.log('⏳ [WAIT] En attente de TeamCalendarApp...');
        setTimeout(waitForTeamCalendarApp, 1000);
    }
}

// Exposer les fonctions globalement pour utilisation manuelle
window.testEmployeeDragLogger = testEmployeeDragLogger;
window.showLoggerStatus = showLoggerStatus;
window.cleanupTestLogs = cleanupTestLogs;
window.simulateDragSession = simulateDragSession;

// Démarrer l'attente
waitForTeamCalendarApp();

console.log('💡 [INFO] Fonctions disponibles:');
console.log('  - testEmployeeDragLogger(): Exécuter tous les tests');
console.log('  - showLoggerStatus(): Afficher le statut du logger');
console.log('  - simulateDragSession(): Simuler une session de drag & drop');
console.log('  - cleanupTestLogs(): Nettoyer tous les logs de test'); 