#!/usr/bin/env node

/**
 * Script pour corriger la détection de source dans les logs existants
 */

import { query } from './server/config/database.js';

console.log('🔧 [FIX-SOURCE] Correction de la détection de source des logs');
console.log('='.repeat(70));

async function fixSourceDetection() {
  try {
    // 1. Analyser les logs mal classés
    console.log('1️⃣ [ANALYSE] Recherche des logs mal classés...');
    
    const wronglyClassified = await query(`
      SELECT id, source, message, ts
      FROM logs
      WHERE 
        -- Logs qui devraient être "frontend" mais sont "browser"
        (source = 'browser' AND (
          message LIKE '%[FRONTEND]%' OR
          message LIKE '%TeamCalendar%' OR 
          message LIKE '%ApiService%' OR
          message LIKE '%Employee%' OR
          message LIKE '%addSimplified%' OR
          message LIKE '%saveEmployeeOrder%' OR
          message LIKE '%[vite]%' OR
          message LIKE '%hot updated%' OR
          message LIKE '%React%'
        ))
        OR
        -- Logs qui devraient être "backend" mais sont "browser"
        (source = 'browser' AND (
          message LIKE '%[SYSTEM]%' OR
          message LIKE '%PostgreSQL%' OR
          message LIKE '%query%' OR
          message LIKE '%[Logger]%' OR
          message LIKE '%endpoint%'
        ))
      ORDER BY ts DESC
      LIMIT 1000
    `);
    
    console.log(`   📊 Logs mal classés trouvés: ${wronglyClassified.rows.length}`);
    
    // 2. Corriger les sources
    let frontendFixed = 0;
    let backendFixed = 0;
    
    for (const log of wronglyClassified.rows) {
      const message = log.message;
      let newSource = log.source;
      
      // Détection améliorée
      if (message.includes('[FRONTEND]') || 
          message.includes('TeamCalendar') || 
          message.includes('ApiService') ||
          message.includes('Employee') ||
          message.includes('addSimplified') ||
          message.includes('saveEmployeeOrder') ||
          message.includes('[vite]') ||
          message.includes('hot updated') ||
          message.includes('React')) {
        newSource = 'frontend';
        frontendFixed++;
      } else if (message.includes('[SYSTEM]') ||
                 message.includes('PostgreSQL') ||
                 message.includes('query') ||
                 message.includes('[Logger]') ||
                 message.includes('endpoint')) {
        newSource = 'backend';
        backendFixed++;
      }
      
      if (newSource !== log.source) {
        await query('UPDATE logs SET source = $1 WHERE id = $2', [newSource, log.id]);
      }
    }
    
    console.log(`   ✅ Logs frontend corrigés: ${frontendFixed}`);
    console.log(`   ✅ Logs backend corrigés: ${backendFixed}`);
    
    // 3. Vérifier la nouvelle répartition
    console.log('\n2️⃣ [VERIFICATION] Nouvelle répartition par source...');
    
    const distribution = await query(`
      SELECT source, COUNT(*) as count
      FROM logs
      WHERE session_id = (
        SELECT session_id 
        FROM logs 
        ORDER BY ts DESC 
        LIMIT 1
      )
      GROUP BY source
      ORDER BY count DESC
    `);
    
    distribution.rows.forEach(row => {
      const emoji = row.source === 'backend' ? '🟢' : 
                   row.source === 'frontend' ? '🟣' : '🟠';
      console.log(`   ${emoji} ${row.source}: ${row.count} logs`);
    });
    
    console.log('\n✅ [FIX-SOURCE] Correction terminée avec succès');
    
  } catch (error) {
    console.error('❌ [FIX-SOURCE] Erreur:', error.message);
  }
}

// Fonction pour améliorer la capture future
async function improveFutureCapture() {
  console.log('\n3️⃣ [AMELIORATION] Suggestions pour la capture future...');
  
  console.log('   📝 Modèles de détection améliorés:');
  console.log('   🟣 Frontend: [FRONTEND], TeamCalendar, ApiService, Employee, React, [vite]');
  console.log('   🟢 Backend: [SYSTEM], PostgreSQL, [Logger], query, endpoint');
  console.log('   🟠 Browser: Tout le reste');
  
  console.log('\n   💡 Recommandations:');
  console.log('   1. Préfixer les logs frontend avec [FRONTEND]');
  console.log('   2. Préfixer les logs backend avec [BACKEND]');
  console.log('   3. Utiliser des mots-clés distinctifs dans les messages');
}

// Exécution
async function main() {
  await fixSourceDetection();
  await improveFutureCapture();
  
  console.log('\n🎯 [RESULTAT] La détection de source a été améliorée !');
  console.log('   Testez avec: npm run logs:fix');
}

main().catch(console.error); 