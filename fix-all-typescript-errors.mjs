import fs from 'fs';
import path from 'path';

console.log('🔧 Correction complète des erreurs TypeScript...');

const filePath = path.join(process.cwd(), 'src', 'teamCalendarApp.ts');

// Lire le fichier
let content = fs.readFileSync(filePath, 'utf8');

// 1. Ajouter les propriétés manquantes dans l'interface TeamCalendarAppType
console.log('📝 Ajout des propriétés manquantes dans l\'interface...');

// Chercher la fin de l'interface TeamCalendarAppType (juste avant le }
const interfaceEndMatch = content.match(/interface TeamCalendarAppType\s*{[\s\S]*?(\n\s*)(}\n)/);
if (interfaceEndMatch) {
    const insertPosition = content.lastIndexOf(interfaceEndMatch[2], interfaceEndMatch.index + interfaceEndMatch[0].length);
    const indentation = interfaceEndMatch[1] || '  ';
    
    const missingProperties = `${indentation}// Propriétés de gestion d\'état ajoutées\n${indentation}_isSavingWeek?: boolean;\n${indentation}_pendingSaveWeek?: boolean;\n${indentation}_transactionLock?: boolean;\n${indentation}_savingState?: boolean;\n${indentation}_renderingInProgress?: boolean;\n${indentation}\n${indentation}// Méthodes de sécurité ajoutées\n${indentation}safeScheduleUpdate?: (employeeId: string, dateKey: string, updateFn: Function, operation: string) => any;\n${indentation}renderSafe?: () => void;\n${indentation}checkDataIntegrity?: () => number;\n${indentation}quickRepair?: () => void;\n`;
    
    content = content.slice(0, insertPosition) + missingProperties + content.slice(insertPosition);
    console.log('✅ Propriétés ajoutées à l\'interface');
} else {
    console.error('❌ Interface TeamCalendarAppType non trouvée');
}

// 2. Corriger les appels incorrects de apiService.saveShifts → apiService.saveWeekShifts
console.log('📝 Correction des appels de méthode incorrects...');

// Remplacer tous les appels incorrects
const saveShiftsRegex = /apiService\\.saveShifts\\(/g;
let replacements = 0;
content = content.replace(saveShiftsRegex, () => {
    replacements++;
    return 'apiService.saveWeekShifts(';
});

if (replacements > 0) {
    console.log(`✅ ${replacements} appels de saveShifts corrigés en saveWeekShifts`);
}

// 3. Corriger les appels avec window.apiService
const windowSaveShiftsRegex = /window\\.apiService\\.saveShifts\\(/g;
content = content.replace(windowSaveShiftsRegex, () => {
    replacements++;
    return 'window.apiService.saveWeekShifts(';
});

// 4. S\'assurer que les méthodes utilisent this correctement
console.log('📝 Vérification des références à this...');

// Corriger les références à ApiService qui devraient être apiService
content = content.replace(/this\\.ApiService\\.saveAllShifts/g, 'apiService.saveWeekShifts');

// 5. Corriger la logique d\'initialisation qui était problématique
console.log('📝 Amélioration de la logique d\'initialisation...');

const initFunctionRegex = /init:\s*async\s*function\s*\(\)\s*{[\\s\\S]*?^(\s*)}\s*(?=,)/m;
const initMatch = content.match(initFunctionRegex);

if (initMatch) {
    const newInitFunction = `init: async function() {
        console.log(\'🚀 [init] Initialisation de TeamCalendarApp...\');
        
        // Vérifier si déjà initialisé
        if (this.config._isInitialized) {
            console.log(\'⚠️ [init] Déjà initialisé, abandon\');
            return;
        }

        try {
            // 1. Charger les données
            console.log(\'📥 [init] Chargement des données...\');
            const loaded = await this.loadState();
            
            if (!loaded) {
                console.error(\'❌ [init] Échec du chargement des données\');
                window.toastSystem?.error(\'Erreur lors du chargement des données\');
                return;
            }

            // 2. Générer les dates de la semaine
            console.log(\'📅 [init] Génération des dates...\');
            this.generateWeekDates();

            // 3. Effectuer le rendu initial
            console.log(\'🎨 [init] Rendu initial...\');
            this.render();

            // 4. Attacher les écouteurs d\\'événements
            console.log(\'🔗 [init] Attachement des écouteurs...\');
            this.attachAllEventListeners();
            
            // 5. Initialiser les fonctionnalités
            console.log(\'⚙️ [init] Initialisation des fonctionnalités...\');
            this.ModalManager.init(this);
            this.initScrollIndicator();
            
            // 6. Appliquer les attributions régulières
            console.log(\'📋 [init] Application des attributions régulières...\');
            await this.applyRegularAssignmentsForCurrentWeek();

            // Marquer comme initialisé
            this.config._isInitialized = true;
            window.__TEAM_CALENDAR_READY__ = true;
            
            console.log(\'✅ [init] Initialisation terminée avec succès\');
            
        } catch (error) {
            console.error(\'❌ [init] Erreur lors de l\\\'initialisation:\', error);
            window.toastSystem?.error(\'Erreur lors de l\\\'initialisation de l\\\'application\');
            
            // Tentative de récupération
            this.quickRepair?.();
            this.render();
        }
    }`; // Notez l'échappement ici
    
    content = content.replace(initFunctionRegex, newInitFunction);
    console.log('✅ Fonction init améliorée');
}

// Écrire le fichier corrigé
fs.writeFileSync(filePath, content, 'utf8');

console.log('✅ Toutes les corrections TypeScript ont été appliquées');
console.log('');
console.log('📌 Corrections appliquées :');
console.log('   - Propriétés manquantes ajoutées à l\'interface');
console.log('   - Appels de méthode incorrects corrigés');
console.log('   - Logique d\'initialisation améliorée');
console.log('');
console.log('🚀 Redémarrez les serveurs pour appliquer les changements'); 