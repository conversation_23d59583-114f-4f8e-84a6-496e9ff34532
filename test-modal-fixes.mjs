#!/usr/bin/env node

/**
 * Script de test pour vérifier les corrections des modales
 */

import fs from 'fs/promises';
import path from 'path';

console.log('🔧 [TEST] Vérification des corrections des modales...\n');

let allChecks = true;
const issues = [];

try {
  // 1. Vérifier que l'erreur "modal is not defined" est corrigée
  console.log('📋 [ÉTAPE 1] Vérification de la correction "modal is not defined"...');
  const modalFunctionalitiesContent = await fs.readFile('src/modalFunctionalities.ts', 'utf8');

  // Vérifier que la structure corrigée est présente
  if (modalFunctionalitiesContent.includes('// ✅ CORRECTION : Ajouter l\'en-tête du modal seulement pour le modal externe') &&
      modalFunctionalitiesContent.includes('modal.appendChild(header);') &&
      modalFunctionalitiesContent.includes('modal.appendChild(tabsNav);') &&
      modalFunctionalitiesContent.includes('modal.appendChild(content);')) {
    console.log('✅ Erreur "modal is not defined" corrigée');
  } else {
    console.log('❌ Erreur "modal is not defined" toujours présente');
    allChecks = false;
    issues.push('Erreur "modal is not defined" non corrigée');
  }

  // 2. Vérifier que l'initialisation ne se fait plus automatiquement
  console.log('\n📋 [ÉTAPE 2] Vérification de l\'initialisation différée...');
  
  if (modalFunctionalitiesContent.includes('// this.initializeAll();')) {
    console.log('✅ Initialisation automatique désactivée');
  } else {
    console.log('❌ Initialisation automatique toujours active');
    allChecks = false;
    issues.push('Initialisation automatique non désactivée');
  }

  // 3. Vérifier que openSettingsModal initialise maintenant les fonctionnalités
  if (modalFunctionalitiesContent.includes('if (!this.isInitialized && this.elements.settingsModal)')) {
    console.log('✅ Initialisation différée dans openSettingsModal');
  } else {
    console.log('❌ Initialisation différée manquante dans openSettingsModal');
    allChecks = false;
    issues.push('Initialisation différée manquante');
  }

  // 4. Vérifier que settingsContent est défini pour le modal React
  if (modalFunctionalitiesContent.includes('this.elements.settingsContent = existingModal;')) {
    console.log('✅ settingsContent défini pour le modal React');
  } else {
    console.log('❌ settingsContent non défini pour le modal React');
    allChecks = false;
    issues.push('settingsContent non défini pour modal React');
  }

  // 5. Vérifier la structure du modal externe
  console.log('\n📋 [ÉTAPE 3] Vérification de la structure du modal externe...');
  
  if (modalFunctionalitiesContent.includes('// ✅ CORRECTION : Ajouter l\'en-tête du modal seulement pour le modal externe')) {
    console.log('✅ Structure du modal externe corrigée');
  } else {
    console.log('❌ Structure du modal externe non corrigée');
    allChecks = false;
    issues.push('Structure du modal externe non corrigée');
  }

  // 6. Vérifier que la variable inutilisée a été supprimée
  if (!modalFunctionalitiesContent.includes('let teamCalendarAppCheckInterval: number;')) {
    console.log('✅ Variable inutilisée supprimée');
  } else {
    console.log('❌ Variable inutilisée toujours présente');
    allChecks = false;
    issues.push('Variable inutilisée toujours présente');
  }

  // 7. Vérifier teamCalendarApp.ts
  console.log('\n📋 [ÉTAPE 4] Vérification de teamCalendarApp.ts...');
  const teamCalendarContent = await fs.readFile('src/teamCalendarApp.ts', 'utf8');
  
  if (teamCalendarContent.includes('// ✅ CORRECTION : Initialiser le gestionnaire de modales sans déclencher initializeAll()')) {
    console.log('✅ Commentaire de correction présent dans teamCalendarApp.ts');
  } else {
    console.log('❌ Correction manquante dans teamCalendarApp.ts');
    allChecks = false;
    issues.push('Correction manquante dans teamCalendarApp.ts');
  }

  // Résumé
  console.log('\n' + '='.repeat(60));
  if (allChecks) {
    console.log('🎉 SUCCÈS : Toutes les corrections des modales sont en place !');
    console.log('\n📋 Corrections appliquées :');
    console.log('  ✅ Erreur "modal is not defined" corrigée');
    console.log('  ✅ Initialisation automatique désactivée');
    console.log('  ✅ Initialisation différée dans openSettingsModal');
    console.log('  ✅ settingsContent défini pour modal React');
    console.log('  ✅ Structure du modal externe corrigée');
    console.log('  ✅ Variable inutilisée supprimée');
    console.log('  ✅ teamCalendarApp.ts corrigé');
    
    console.log('\n🚀 L\'application devrait maintenant fonctionner sans erreurs de modal !');
  } else {
    console.log('❌ ÉCHEC : Des problèmes subsistent');
    console.log('\n📋 Problèmes détectés :');
    issues.forEach(issue => console.log(`  ❌ ${issue}`));
  }

} catch (error) {
  console.error('❌ Erreur lors de la vérification:', error.message);
  allChecks = false;
}

process.exit(allChecks ? 0 : 1);
