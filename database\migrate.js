#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { query, closePool } from '../server/config/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Couleurs pour les logs
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

// Fonction utilitaire pour les logs colorés
const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.magenta}🚀 ${msg}${colors.reset}`)
};

// Fonction pour lire et exécuter un fichier SQL
async function executeSqlFile(filePath) {
  try {
    log.info(`Exécution du fichier: ${path.basename(filePath)}`);
    
    const sqlContent = fs.readFileSync(filePath, 'utf8');
    
    // Nettoyer le contenu SQL
    const cleanContent = sqlContent
      .replace(/--.*$/gm, '') // Supprimer les commentaires de ligne
      .replace(/\/\*[\s\S]*?\*\//g, '') // Supprimer les commentaires de bloc
      .trim();
    
    // Diviser le contenu en instructions séparées, en préservant les blocs de fonctions
    const statements = [];
    let currentStatement = '';
    let inFunction = false;
    
    for (const line of cleanContent.split('\n')) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;
      
      currentStatement += ' ' + trimmedLine;
      
      if (trimmedLine.includes('$$')) {
        inFunction = !inFunction;
      }
      
      if (!inFunction && trimmedLine.endsWith(';')) {
        statements.push(currentStatement.trim());
        currentStatement = '';
      }
    }
    
    if (currentStatement.trim()) {
      statements.push(currentStatement.trim());
    }
    
    // Filtrer les instructions vides
    const filteredStatements = statements.filter(statement => statement.length > 0 && !statement.startsWith('--'));
    
    for (const statement of filteredStatements) {
      const trimmedStatement = statement.trim();
      if (trimmedStatement) {
        console.log('Executing SQL:', trimmedStatement);
        await query(trimmedStatement);
      }
    }
    
    log.success(`Fichier ${path.basename(filePath)} exécuté avec succès`);
    return true;
  } catch (error) {
    log.error(`Erreur lors de l'exécution de ${path.basename(filePath)}: ${error.message}`);
    throw error;
  }
}

// Fonction pour créer la table des migrations si elle n'existe pas
async function createMigrationsTable() {
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS migrations (
      id SERIAL PRIMARY KEY,
      filename VARCHAR(255) UNIQUE NOT NULL,
      executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );
  `;
  
  try {
    await query(createTableSQL);
    log.success('Table des migrations initialisée');
  } catch (error) {
    log.error(`Erreur lors de la création de la table migrations: ${error.message}`);
    throw error;
  }
}

// Fonction pour vérifier si une migration a déjà été exécutée
async function isMigrationExecuted(filename) {
  try {
    const result = await query('SELECT filename FROM migrations WHERE filename = $1', [filename]);
    return result.rows.length > 0;
  } catch (error) {
    log.error(`Erreur lors de la vérification de la migration ${filename}: ${error.message}`);
    throw error;
  }
}

// Fonction pour marquer une migration comme exécutée
async function markMigrationExecuted(filename) {
  try {
    await query('INSERT INTO migrations (filename) VALUES ($1)', [filename]);
    log.success(`Migration ${filename} marquée comme exécutée`);
  } catch (error) {
    log.error(`Erreur lors du marquage de la migration ${filename}: ${error.message}`);
    throw error;
  }
}

// Fonction principale de migration
async function runMigrations() {
  try {
    log.title('DÉBUT DES MIGRATIONS');

    // Drop and recreate the database
    try {
      await query('DROP DATABASE IF EXISTS postgres;');
      log.success('Database dropped successfully.');
    } catch (error) {
      log.warning(`Could not drop database: ${error.message}`);
    }

    try {
      await query('CREATE DATABASE postgres;');
      log.success('Database created successfully.');
    } catch (error) {
      log.error(`Could not create database: ${error.message}`);
      throw error;
    }
    
    // Créer la table des migrations
    await createMigrationsTable();
    
    // Répertoires des migrations et seeds
    const migrationsDir = path.join(__dirname, 'migrations');
    const seedsDir = path.join(__dirname, 'seeds');
    
    // Lire et trier les fichiers de migration
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();
    
    log.info(`${migrationFiles.length} fichier(s) de migration trouvé(s)`);
    
    // Exécuter les migrations
    for (const file of migrationFiles) {
      const isExecuted = await isMigrationExecuted(file);
      
      if (isExecuted) {
        log.warning(`Migration ${file} déjà exécutée, ignorée`);
        continue;
      }
      
      const filePath = path.join(migrationsDir, file);
      await executeSqlFile(filePath);
      await markMigrationExecuted(file);
    }
    
    // Lire et trier les fichiers de seed
    if (fs.existsSync(seedsDir)) {
      try {
        const seedFiles = fs.readdirSync(seedsDir)
          .filter(file => file.endsWith('.sql') && file !== '002_initial_data.sql') // Skip problematic seed
          .sort();
        
        if (seedFiles.length > 0) {
          log.info(`${seedFiles.length} fichier(s) de seed trouvé(s)`);
          
          // Exécuter les seeds
          for (const file of seedFiles) {
            const isExecuted = await isMigrationExecuted(file);
            
            if (isExecuted) {
              log.warning(`Seed ${file} déjà exécuté, ignoré`);
              continue;
            }
            
            const filePath = path.join(seedsDir, file);
            await executeSqlFile(filePath);
            await markMigrationExecuted(file);
          }
        }
      } catch (error) {
        log.warning(`Erreur lors du traitement des seeds: ${error.message}`);
      }
    }
    
    log.success('TOUTES LES MIGRATIONS ONT ÉTÉ EXÉCUTÉES AVEC SUCCÈS');
    
  } catch (error) {
    log.error(`Erreur lors des migrations: ${error.message}`);
    throw error;
  }
}

// Fonction pour afficher l'aide
function showHelp() {
  console.log(`
${colors.cyan}📋 GESTIONNAIRE DE MIGRATIONS - TEAM CALENDAR APP${colors.reset}

Usage:
  node migrate.js [command]

Commandes:
  run       Exécuter toutes les migrations en attente (par défaut)
  status    Afficher le statut des migrations
  help      Afficher cette aide

Exemples:
  node migrate.js run
  node migrate.js status
  node migrate.js help
  `);
}

// Fonction pour afficher le statut des migrations
async function showMigrationStatus() {
  try {
    log.title('STATUT DES MIGRATIONS');
    
    await createMigrationsTable();
    
    const migrationsDir = path.join(__dirname, 'migrations');
    const seedsDir = path.join(__dirname, 'seeds');
    
    // Obtenir toutes les migrations exécutées
    const executedResult = await query('SELECT filename, executed_at FROM migrations ORDER BY executed_at');
    const executedMigrations = new Set(executedResult.rows.map(row => row.filename));
    
    // Vérifier les migrations
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();
    
    log.info('MIGRATIONS:');
    for (const file of migrationFiles) {
      const status = executedMigrations.has(file) ? '✅ Exécutée' : '⏳ En attente';
      const executed = executedResult.rows.find(row => row.filename === file);
      const date = executed ? new Date(executed.executed_at).toLocaleString() : '';
      console.log(`  ${file}: ${status} ${date}`);
    }
    
    // Vérifier les seeds
    if (fs.existsSync(seedsDir)) {
      const seedFiles = fs.readdirSync(seedsDir)
        .filter(file => file.endsWith('.sql'))
        .sort();
      
      log.info('SEEDS:');
      for (const file of seedFiles) {
        const status = executedMigrations.has(file) ? '✅ Exécuté' : '⏳ En attente';
        const executed = executedResult.rows.find(row => row.filename === file);
        const date = executed ? new Date(executed.executed_at).toLocaleString() : '';
        console.log(`  ${file}: ${status} ${date}`);
      }
    }
    
  } catch (error) {
    log.error(`Erreur lors de l'affichage du statut: ${error.message}`);
    throw error;
  }
}

// Point d'entrée principal
async function main() {
  const command = process.argv[2] || 'run';
  
  try {
    switch (command) {
      case 'run':
        await runMigrations();
        break;
      case 'status':
        await showMigrationStatus();
        break;
      case 'help':
        showHelp();
        break;
      default:
        log.error(`Commande inconnue: ${command}`);
        showHelp();
        process.exit(1);
    }
  } catch (error) {
    log.error(`Erreur fatale: ${error.message}`);
    process.exit(1);
  } finally {
    await closePool();
  }
}

// Toujours exécuter le script principal
main();

export {
  runMigrations,
  showMigrationStatus,
  executeSqlFile
};
