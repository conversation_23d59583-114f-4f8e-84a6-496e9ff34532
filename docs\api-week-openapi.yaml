openapi: 3.0.3
info:
  title: Week API - Architecture Lazy Loading
  description: |
    API robuste pour la gestion des semaines avec attributions régulières.
    Architecture lazy-loading avec cache intelligent et préchargement.
  version: 2.0.0
  contact:
    name: Team Calendar API
    email: <EMAIL>

servers:
  - url: http://localhost:3001/api
    description: Serveur de développement
  - url: https://api.teamcalendar.com/api
    description: Serveur de production

paths:
  /weeks/{isoWeek}:
    get:
      summary: Récupère la vue complète d'une semaine
      description: |
        Retourne la vue complète d'une semaine avec toutes les attributions régulières appliquées.
        Utilise un cache intelligent avec TTL de 3 heures.
      parameters:
        - name: isoWeek
          in: path
          required: true
          description: Semaine au format ISO (YYYY-WW)
          schema:
            type: string
            pattern: '^[0-9]{4}-W[0-9]{2}$'
            example: "2025-W25"
        - name: startDay
          in: query
          description: <PERSON><PERSON> de début de semaine (0=dimanche, 1=lundi, etc.)
          schema:
            type: integer
            minimum: 0
            maximum: 6
            default: 1
        - name: x-tenant-id
          in: header
          description: ID du tenant (pour multi-tenant futur)
          schema:
            type: string
            default: "default"
        - name: x-request-id
          in: header
          description: ID de requête pour traçabilité
          schema:
            type: string
      responses:
        '200':
          description: Vue de semaine générée avec succès
          headers:
            Cache-Control:
              description: Directives de cache
              schema:
                type: string
                example: "private, max-age=300"
            ETag:
              description: Tag d'entité pour validation cache
              schema:
                type: string
            X-Response-Time:
              description: Temps de réponse en millisecondes
              schema:
                type: string
                example: "45ms"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WeekView'
        '400':
          description: Paramètres invalides
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Erreur interne du serveur
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /weeks/{isoWeek}/assignments:
    get:
      summary: Récupère les attributions pour une semaine
      description: Retourne uniquement les attributions régulières applicables à une semaine
      parameters:
        - name: isoWeek
          in: path
          required: true
          schema:
            type: string
            pattern: '^[0-9]{4}-W[0-9]{2}$'
        - name: startDay
          in: query
          schema:
            type: integer
            minimum: 0
            maximum: 6
            default: 1
      responses:
        '200':
          description: Attributions récupérées avec succès
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WeekAssignments'

  /weeks/{isoWeek}/generate:
    post:
      summary: Force la régénération d'une semaine
      description: |
        Invalide le cache et force la régénération complète d'une semaine.
        Utile après modification d'attributions régulières.
      parameters:
        - name: isoWeek
          in: path
          required: true
          schema:
            type: string
            pattern: '^[0-9]{4}-W[0-9]{2}$'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                startDay:
                  type: integer
                  minimum: 0
                  maximum: 6
                  default: 1
      responses:
        '200':
          description: Semaine régénérée avec succès
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Semaine régénérée avec succès"
                  isoWeek:
                    type: string
                    example: "2025-W25"
                  weekView:
                    $ref: '#/components/schemas/WeekView'

  /weeks/range/{startWeek}/{endWeek}:
    get:
      summary: Récupère plusieurs semaines d'un coup
      description: |
        Retourne plusieurs semaines pour navigation rapide.
        Limité à 8 semaines maximum pour éviter la surcharge.
      parameters:
        - name: startWeek
          in: path
          required: true
          schema:
            type: string
            pattern: '^[0-9]{4}-W[0-9]{2}$'
        - name: endWeek
          in: path
          required: true
          schema:
            type: string
            pattern: '^[0-9]{4}-W[0-9]{2}$'
        - name: startDay
          in: query
          schema:
            type: integer
            minimum: 0
            maximum: 6
            default: 1
      responses:
        '200':
          description: Plage de semaines récupérée avec succès
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WeekRange'
        '400':
          description: Plage trop large (>8 semaines)
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Plage trop large"
                  maxWeeks:
                    type: integer
                    example: 8
                  requested:
                    type: integer
                    example: 12

  /weeks/cache/stats:
    get:
      summary: Statistiques du cache
      description: Retourne les métriques de performance du cache pour monitoring
      responses:
        '200':
          description: Statistiques récupérées avec succès
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CacheStats'

components:
  schemas:
    WeekView:
      type: object
      properties:
        isoWeek:
          type: string
          example: "2025-W25"
        startDate:
          type: string
          format: date
          example: "2025-06-16"
        endDate:
          type: string
          format: date
          example: "2025-06-22"
        dateKeys:
          type: array
          items:
            type: string
            format: date
          example: ["2025-06-16", "2025-06-17", "2025-06-18", "2025-06-19", "2025-06-20", "2025-06-21", "2025-06-22"]
        shifts:
          type: object
          additionalProperties:
            type: object
            additionalProperties:
              type: array
              items:
                $ref: '#/components/schemas/Shift'
        assignments:
          type: array
          items:
            $ref: '#/components/schemas/Assignment'
        generated:
          type: string
          format: date-time
          example: "2025-06-15T10:30:00.000Z"
        meta:
          $ref: '#/components/schemas/WeekMeta'

    Shift:
      type: object
      properties:
        id:
          type: string
          example: "generated-uuid-123"
        postId:
          type: string
          format: uuid
          example: "9b0e87f9-7942-42cb-a568-1441af17d62a"
        text:
          type: string
          example: "08:00-16:00"
        type:
          type: string
          enum: [sky, amber, emerald, red, purple]
          example: "sky"
        isRegular:
          type: boolean
          example: true
        isPunctual:
          type: boolean
          example: false
        assignmentId:
          type: string
          format: uuid
          example: "assignment-uuid-456"
        generated:
          type: boolean
          description: Indique si le shift a été généré automatiquement
          example: true

    Assignment:
      type: object
      properties:
        id:
          type: string
          format: uuid
        employeeId:
          type: string
          format: uuid
        postId:
          type: string
          format: uuid
        startDate:
          type: string
          format: date
          nullable: true
        endDate:
          type: string
          format: date
          nullable: true
        selectedDays:
          type: array
          items:
            type: integer
            minimum: 0
            maximum: 6
          example: [1, 2, 3, 4, 5]
        isLimited:
          type: boolean
          example: false
        employee:
          type: object
          properties:
            id:
              type: string
              format: uuid
            name:
              type: string
              example: "Sophie Leblanc"
        post:
          type: object
          properties:
            id:
              type: string
              format: uuid
            label:
              type: string
              example: "Poste WE2"
            hours:
              type: string
              example: "08:00-16:00"
            type:
              type: string
              example: "sky"
            category:
              type: string
              example: "weekend"

    WeekMeta:
      type: object
      properties:
        requestId:
          type: string
          example: "req-1671234567890-abc123"
        duration:
          type: integer
          description: Temps de génération en millisecondes
          example: 45
        cached:
          type: boolean
          description: Indique si la réponse vient du cache
          example: true
        tenantId:
          type: string
          example: "default"
        startDay:
          type: integer
          example: 1
        generatedAt:
          type: string
          format: date-time
          example: "2025-06-15T10:30:00.000Z"

    WeekAssignments:
      type: object
      properties:
        isoWeek:
          type: string
          example: "2025-W25"
        weekRange:
          type: object
          properties:
            start:
              type: string
              format: date
            end:
              type: string
              format: date
            dateKeys:
              type: array
              items:
                type: string
                format: date
        assignments:
          type: array
          items:
            $ref: '#/components/schemas/Assignment'
        total:
          type: integer
          example: 5

    WeekRange:
      type: object
      properties:
        startWeek:
          type: string
          example: "2025-W25"
        endWeek:
          type: string
          example: "2025-W27"
        weeks:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/WeekView'
        total:
          type: integer
          example: 3

    CacheStats:
      type: object
      properties:
        cache:
          type: object
          properties:
            totalEntries:
              type: integer
              example: 15
            validEntries:
              type: integer
              example: 12
            expiredEntries:
              type: integer
              example: 3
            hitRatio:
              type: number
              format: float
              minimum: 0
              maximum: 1
              example: 0.8
        timestamp:
          type: string
          format: date-time
          example: "2025-06-15T10:30:00.000Z"

    Error:
      type: object
      properties:
        error:
          type: string
          example: "Format de semaine invalide"
        message:
          type: string
          example: "Le format attendu est YYYY-WW"
        code:
          type: string
          example: "INVALID_WEEK_FORMAT"
        requestId:
          type: string
          example: "req-1671234567890-abc123"
        timestamp:
          type: string
          format: date-time
          example: "2025-06-15T10:30:00.000Z"

  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key

security:
  - ApiKeyAuth: []
