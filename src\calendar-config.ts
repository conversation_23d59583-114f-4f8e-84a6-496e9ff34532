// Configuration simplifiée pour le calendrier d'équipe
// Ce fichier centralise tous les paramètres configurables

export interface CalendarConfig {
    // Configuration des jours de la semaine
    weekStart: {
        day: number; // 0=dimanche, 1=lundi, etc.
        name: string;
    };
    
    // Postes standards
    standardPosts: StandardPost[];
    
    // Paramètres d'affichage
    display: {
        rowHeight: number;
        showWeekends: boolean;
        highlightToday: boolean;
    };
    
    // Paramètres de stockage
    storage: {
        key: string;
        autoSave: boolean;
    };
}

export interface StandardPost {
    id: string;
    label: string;
    hours: string;
    duration: number;
    type: 'sky' | 'amber' | 'emerald';
    category?: 'weekend' | 'night';
    workingDays?: number[];
    isWeekendOnly?: boolean;
}

// Configuration par défaut
export const DEFAULT_CONFIG: CalendarConfig = {
    weekStart: {
        day: 0, // Dimanche
        name: 'sunday'
    },
    
    standardPosts: [
        { 
            id: 'MORNING', 
            label: 'Poste Matin', 
            hours: '08:00-16:00', 
            duration: 8, 
            type: 'sky', 
            workingDays: [1, 2, 3, 4, 5] 
        },
        { 
            id: 'EVENING', 
            label: 'Poste Soir', 
            hours: '16:00-24:00', 
            duration: 8, 
            type: 'amber', 
            workingDays: [1, 2, 3, 4, 5] 
        },
        { 
            id: 'NIGHT', 
            label: 'Poste Nuit', 
            hours: '00:00-08:00', 
            duration: 8, 
            type: 'sky', 
            category: 'night', 
            workingDays: [1, 2, 3, 4, 5] 
        },
        { 
            id: 'WE1', 
            label: 'Poste WE1', 
            hours: '00:00-12:00', 
            duration: 12, 
            type: 'emerald', 
            category: 'weekend', 
            workingDays: [0, 6], 
            isWeekendOnly: true 
        },
        { 
            id: 'WE2', 
            label: 'Poste WE2', 
            hours: '12:00-24:00', 
            duration: 12, 
            type: 'emerald', 
            category: 'weekend', 
            workingDays: [0, 6], 
            isWeekendOnly: true 
        }
    ],
    
    display: {
        rowHeight: 60,
        showWeekends: true,
        highlightToday: true
    },
    
    storage: {
        key: 'teamCalendarState_v2',
        autoSave: true
    }
};

// Utilitaires pour la configuration
export class ConfigManager {
    private config: CalendarConfig;
    
    constructor(config: CalendarConfig = DEFAULT_CONFIG) {
        this.config = { ...config };
    }
    
    // Obtenir la configuration actuelle
    getConfig(): CalendarConfig {
        return { ...this.config };
    }
    
    // Mettre à jour le jour de début de semaine
    setWeekStart(day: number): void {
        if (day < 0 || day > 6) {
            throw new Error('Le jour doit être entre 0 (dimanche) et 6 (samedi)');
        }
        
        const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        this.config.weekStart = {
            day,
            name: dayNames[day]
        };
    }
    
    // Obtenir le jour de début de semaine
    getWeekStartDay(): number {
        return this.config.weekStart.day;
    }
    
    // Ajouter un poste standard
    addStandardPost(post: StandardPost): void {
        this.config.standardPosts.push(post);
    }
    
    // Supprimer un poste standard
    removeStandardPost(postId: string): void {
        this.config.standardPosts = this.config.standardPosts.filter(p => p.id !== postId);
    }
    
    // Obtenir un poste par ID
    getPostById(postId: string): StandardPost | null {
        return this.config.standardPosts.find(p => p.id === postId) || null;
    }
    
    // Valider la configuration
    validate(): boolean {
        // Vérifier que tous les postes ont des IDs uniques
        const postIds = this.config.standardPosts.map(p => p.id);
        const uniqueIds = new Set(postIds);
        
        if (postIds.length !== uniqueIds.size) {
            console.error('Configuration invalide: IDs de postes dupliqués');
            return false;
        }
        
        // Vérifier que le jour de début est valide
        if (this.config.weekStart.day < 0 || this.config.weekStart.day > 6) {
            console.error('Configuration invalide: jour de début de semaine invalide');
            return false;
        }
        
        return true;
    }
    
    // Sauvegarder la configuration
    save(): void {
        if (!this.validate()) {
            throw new Error('Configuration invalide, impossible de sauvegarder');
        }
        
        localStorage.setItem(this.config.storage.key + '_config', JSON.stringify(this.config));
    }
    
    // Charger la configuration
    load(): boolean {
        try {
            const saved = localStorage.getItem(this.config.storage.key + '_config');
            if (saved) {
                const loadedConfig = JSON.parse(saved);
                this.config = { ...DEFAULT_CONFIG, ...loadedConfig };
                return this.validate();
            }
        } catch (error) {
            console.error('Erreur lors du chargement de la configuration:', error);
        }
        return false;
    }
    
    // Réinitialiser à la configuration par défaut
    reset(): void {
        this.config = { ...DEFAULT_CONFIG };
    }
}
