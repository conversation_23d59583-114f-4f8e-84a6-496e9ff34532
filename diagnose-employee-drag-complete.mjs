import fs from 'fs';
import path from 'path';

console.log('🔍 [DIAGNOSTIC] Analyse complète du système de drag & drop des employés...');

const filePath = 'src/teamCalendarApp.ts';
const content = fs.readFileSync(filePath, 'utf8');

// 1. Analyser les appels multiples
console.log('\n📊 [ANALYSE] Recherche des appels multiples...');

const reorderCalls = content.match(/this\.reorderEmployees\(/g) || [];
const saveCalls = content.match(/this\.saveEmployeeOrder\(/g) || [];
const loadCalls = content.match(/this\.loadEmployeeOrder\(/g) || [];

console.log(`📈 [STATS] Appels détectés:`);
console.log(`   - reorderEmployees: ${reorderCalls.length}`);
console.log(`   - saveEmployeeOrder: ${saveCalls.length}`);
console.log(`   - loadEmployeeOrder: ${loadCalls.length}`);

// 2. Vérifier les protections
console.log('\n🛡️ [PROTECTION] Vérification des mécanismes de protection...');

const hasProtection = {
  reorderInProgress: content.includes('_reorderInProgress'),
  saveInProgress: content.includes('_saveOrderInProgress'),
  lastDragDropTime: content.includes('_lastDragDropTime'),
  loadInProgress: content.includes('_loadOrderInProgress')
};

console.log('🔒 [PROTECTION] Mécanismes détectés:');
Object.entries(hasProtection).forEach(([key, exists]) => {
  console.log(`   - ${key}: ${exists ? '✅' : '❌'}`);
});

// 3. Vérifier les logs de position
console.log('\n📋 [LOGS] Vérification des logs de position...');

const hasPositionLogs = content.includes('logEmployeePositions');
console.log(`📊 [LOGS] Fonction logEmployeePositions: ${hasPositionLogs ? '✅' : '❌'}`);

// 4. Créer une fonction utilitaire de logs de position
const positionLogFunction = `
// Fonction utilitaire pour logger les positions des employés
function logEmployeePositions(context: string) {
  const positions = this.data.employees.map((emp, index) => \`\${index + 1}. \${emp.name}\`);
  console.log(\`📊 [\${context}] Positions actuelles:\`, positions);
  console.log(\`📊 [\${context}] Total employés: \${this.data.employees.length}\`);
}`;

// 5. Ajouter les appels de logs dans les fonctions clés
const addPositionLogs = (content) => {
  let modified = content;
  
  // Ajouter la fonction si elle n'existe pas
  if (!content.includes('logEmployeePositions')) {
    // Insérer après les autres fonctions utilitaires
    const insertPoint = content.indexOf('// Fonction utilitaire pour logger les positions des employés');
    if (insertPoint === -1) {
      // Chercher un bon endroit pour insérer
      const functionStart = content.indexOf('quickRepair: function() {');
      if (functionStart !== -1) {
        const beforeFunction = content.substring(0, functionStart);
        const afterFunction = content.substring(functionStart);
        modified = beforeFunction + positionLogFunction + '\n\n' + afterFunction;
      }
    }
  }
  
  // Ajouter les appels dans les fonctions clés
  const callsToAdd = [
    { function: 'init', call: 'this.logEmployeePositions("INIT");' },
    { function: 'reorderEmployees', call: 'this.logEmployeePositions("APRÈS REORDER");' },
    { function: 'loadEmployeeOrder', call: 'this.logEmployeePositions("APRÈS LOAD");' },
    { function: 'saveEmployeeOrder', call: 'this.logEmployeePositions("AVANT SAVE");' }
  ];
  
  callsToAdd.forEach(({ function: funcName, call }) => {
    const functionPattern = new RegExp(`(${funcName}:\\s*function[^{]*{[^}]*})`, 'g');
    if (!content.includes(`${funcName}: function`)) {
      console.log(`⚠️ [WARNING] Fonction ${funcName} non trouvée`);
      return;
    }
    
    // Chercher la fin de la fonction pour ajouter l'appel
    const functionMatch = content.match(new RegExp(`${funcName}:\\s*function[^{]*{([^}]*(?:{[^}]*}[^}]*)*)}`, 'g'));
    if (functionMatch) {
      // Ajouter l'appel à la fin de la fonction
      const functionEndPattern = new RegExp(`(${funcName}:\\s*function[^{]*{[^}]*})`, 'g');
      modified = modified.replace(functionEndPattern, (match, funcStart) => {
        // Trouver la dernière accolade fermante de la fonction
        let braceCount = 0;
        let endIndex = -1;
        
        for (let i = funcStart.length; i < match.length; i++) {
          if (match[i] === '{') braceCount++;
          if (match[i] === '}') {
            braceCount--;
            if (braceCount === 0) {
              endIndex = i;
              break;
            }
          }
        }
        
        if (endIndex !== -1) {
          const beforeEnd = match.substring(0, endIndex);
          const afterEnd = match.substring(endIndex);
          return beforeEnd + '\n      ' + call + '\n    ' + afterEnd;
        }
        
        return match;
      });
    }
  });
  
  return modified;
};

// 6. Vérifier et corriger les protections
const fixProtections = (content) => {
  let modified = content;
  
  // Vérifier si les variables de protection existent dans la configuration
  const configPattern = /config:\s*{[\s\S]*?}/;
  const configMatch = content.match(configPattern);
  
  if (configMatch) {
    const config = configMatch[0];
    const missingProtections = [];
    
    if (!config.includes('_reorderInProgress')) missingProtections.push('_reorderInProgress: false');
    if (!config.includes('_saveOrderInProgress')) missingProtections.push('_saveOrderInProgress: false');
    if (!config.includes('_loadOrderInProgress')) missingProtections.push('_loadOrderInProgress: false');
    if (!config.includes('_lastDragDropTime')) missingProtections.push('_lastDragDropTime: 0');
    
    if (missingProtections.length > 0) {
      console.log('🔧 [FIX] Ajout des protections manquantes...');
      const newConfig = config.replace(/}$/, `,\n    ${missingProtections.join(',\n    ')}\n  }`);
      modified = content.replace(config, newConfig);
    }
  }
  
  return modified;
};

// 7. Appliquer les corrections
console.log('\n🔧 [CORRECTION] Application des corrections...');

let correctedContent = content;

// Ajouter les logs de position
correctedContent = addPositionLogs(correctedContent);

// Corriger les protections
correctedContent = fixProtections(correctedContent);

// 8. Vérifier les doublons dans les fonctions
console.log('\n🧹 [NETTOYAGE] Vérification des doublons...');

const duplicateFunctions = [
  'reorderEmployees',
  'saveEmployeeOrder', 
  'loadEmployeeOrder'
];

duplicateFunctions.forEach(funcName => {
  const matches = correctedContent.match(new RegExp(`${funcName}:\\s*function`, 'g')) || [];
  if (matches.length > 1) {
    console.log(`⚠️ [WARNING] Fonction ${funcName} dupliquée (${matches.length} occurrences)`);
  }
});

// 9. Sauvegarder les corrections
if (correctedContent !== content) {
  fs.writeFileSync(filePath, correctedContent, 'utf8');
  console.log('✅ [SAUVEGARDE] Corrections appliquées avec succès');
} else {
  console.log('ℹ️ [INFO] Aucune correction nécessaire');
}

// 10. Créer un script de test
const testScript = `
// Script de test pour vérifier le drag & drop
console.log('🧪 [TEST] Test du système de drag & drop...');

// Simuler un déplacement
if (window.TeamCalendarApp) {
  const app = window.TeamCalendarApp;
  
  console.log('📊 [TEST] Positions initiales:');
  app.logEmployeePositions?.('TEST INITIAL');
  
  // Simuler un reorder
  if (app.data.employees.length > 1) {
    console.log('🔄 [TEST] Simulation d\'un déplacement...');
    app.reorderEmployees(0, 1);
  }
}
`;

fs.writeFileSync('test-employee-drag.mjs', testScript);
console.log('✅ [TEST] Script de test créé: test-employee-drag.mjs');

console.log('\n🎯 [TERMINÉ] Diagnostic et corrections appliqués');
console.log('📋 [NEXT] Redémarrez l\'application et testez le drag & drop'); 