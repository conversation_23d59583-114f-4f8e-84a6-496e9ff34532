import pg from 'pg';
import fs from 'fs';
import readline from 'readline';

const { Pool } = pg;

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'team_calendar',
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT || 5432,
});

async function emergencyCleanup() {
  console.log('🚨 [URGENCE] Début du nettoyage des données dupliquées...');
  
  try {
    // 1. Analyser l'ampleur du problème
    console.log('\n📊 [Analyse] Vérification de l\'état actuel...');
    
    const shiftsCount = await pool.query('SELECT COUNT(*) as total FROM shifts');
    const employeesCount = await pool.query('SELECT COUNT(*) as total FROM employees');
    const duplicateShifts = await pool.query(`
      SELECT employee_id, date_key, shift_data, COUNT(*) as count
      FROM shifts 
      GROUP BY employee_id, date_key, shift_data
      HAVING COUNT(*) > 1
      ORDER BY count DESC
      LIMIT 10
    `);
    
    console.log(`📈 Total des quarts: ${shiftsCount.rows[0].total}`);
    console.log(`👥 Total des employés: ${employeesCount.rows[0].total}`);
    console.log(`🔍 Groupes de doublons détectés: ${duplicateShifts.rows.length}`);
    
    if (duplicateShifts.rows.length > 0) {
      console.log('\n🔍 Top 5 des doublons:');
      duplicateShifts.rows.slice(0, 5).forEach((row, i) => {
        console.log(`  ${i+1}. Employé ${row.employee_id}: ${row.count} copies identiques`);
      });
    }
    
    // 2. Sauvegarder avant nettoyage
    console.log('\n💾 [Sauvegarde] Création d\'une sauvegarde...');
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = `backup-emergency-${timestamp}.sql`;
    
    // Cette commande doit être exécutée manuellement
    console.log(`📝 Exécuter manuellement: pg_dump team_calendar > ${backupFile}`);
    
    // 3. Identifier les doublons exacts
    console.log('\n🔄 [Nettoyage] Suppression des doublons exacts...');
    
    const deleteQuery = `
      DELETE FROM shifts 
      WHERE id IN (
        SELECT id FROM (
          SELECT id, 
                 ROW_NUMBER() OVER (
                   PARTITION BY employee_id, date_key, 
                               (shift_data->>'text'), 
                               (shift_data->>'type'),
                               (shift_data->>'postId')
                   ORDER BY created_at DESC
                 ) as rn
          FROM shifts
        ) t 
        WHERE rn > 1
      )
    `;
    
    const deleteResult = await pool.query(deleteQuery);
    console.log(`✅ ${deleteResult.rowCount} doublons supprimés`);
    
    // 4. Nettoyer les assignmentId en double
    console.log('\n🔧 [Nettoyage] Assignements en double...');
    
    const duplicateAssignments = await pool.query(`
      SELECT (shift_data->>'assignmentId') as assignment_id, COUNT(*) as count
      FROM shifts 
      WHERE shift_data->>'assignmentId' IS NOT NULL
      GROUP BY (shift_data->>'assignmentId')
      HAVING COUNT(*) > 50
      ORDER BY count DESC
      LIMIT 10
    `);
    
    console.log(`🔍 Assignments avec >50 occurrences: ${duplicateAssignments.rows.length}`);
    
    for (const assignment of duplicateAssignments.rows) {
      if (assignment.count > 100) {
        console.log(`⚠️  Assignment ${assignment.assignment_id}: ${assignment.count} occurrences`);
        
        // Garder seulement les 7 premiers (une semaine normale)
        const cleanupAssignment = await pool.query(`
          DELETE FROM shifts 
          WHERE id IN (
            SELECT id FROM (
              SELECT id, 
                     ROW_NUMBER() OVER (ORDER BY created_at) as rn
              FROM shifts 
              WHERE shift_data->>'assignmentId' = $1
            ) t 
            WHERE rn > 7
          )
        `, [assignment.assignment_id]);
        
        console.log(`  ✅ ${cleanupAssignment.rowCount} quarts supprimés pour cet assignment`);
      }
    }
    
    // 5. Vérification finale
    console.log('\n📊 [Vérification] État après nettoyage...');
    
    const finalCount = await pool.query('SELECT COUNT(*) as total FROM shifts');
    const remainingDuplicates = await pool.query(`
      SELECT COUNT(*) as count
      FROM (
        SELECT employee_id, date_key, shift_data, COUNT(*) as count
        FROM shifts 
        GROUP BY employee_id, date_key, shift_data
        HAVING COUNT(*) > 1
      ) t
    `);
    
    console.log(`📈 Quarts restants: ${finalCount.rows[0].total}`);
    console.log(`🔍 Groupes de doublons restants: ${remainingDuplicates.rows[0].count}`);
    
    // 6. Générer un rapport
    const report = {
      timestamp: new Date().toISOString(),
      before: {
        shifts: parseInt(shiftsCount.rows[0].total),
        employees: parseInt(employeesCount.rows[0].total),
        duplicateGroups: duplicateShifts.rows.length
      },
      after: {
        shifts: parseInt(finalCount.rows[0].total),
        duplicateGroups: parseInt(remainingDuplicates.rows[0].count),
        deleted: parseInt(shiftsCount.rows[0].total) - parseInt(finalCount.rows[0].total)
      }
    };
    
    fs.writeFileSync(`cleanup-report-${timestamp}.json`, JSON.stringify(report, null, 2));
    
    console.log('\n✅ [TERMINÉ] Nettoyage d\'urgence complété!');
    console.log(`📄 Rapport sauvegardé: cleanup-report-${timestamp}.json`);
    console.log(`🗑️  ${report.after.deleted} quarts supprimés`);
    console.log(`📉 Réduction: ${Math.round((report.after.deleted / report.before.shifts) * 100)}%`);
    
  } catch (error) {
    console.error('❌ [ERREUR] Nettoyage échoué:', error);
  } finally {
    await pool.end();
  }
}

// Point d'entrée principal
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('⚠️  SCRIPT DE NETTOYAGE D\'URGENCE ⚠️');
  console.log('Ce script va supprimer les données dupliquées');
  console.log('Assurez-vous d\'avoir une sauvegarde !');
  console.log('');
  
  // Demander confirmation
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  rl.question('Voulez-vous continuer? (tapez "OUI" pour confirmer): ', (answer) => {
    if (answer.toUpperCase() === 'OUI') {
      emergencyCleanup().finally(() => rl.close());
    } else {
      console.log('❌ Annulé par l\'utilisateur');
      rl.close();
    }
  });
}

export { emergencyCleanup }; 