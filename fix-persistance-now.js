const fs = require('fs');

console.log('🚨 [PERSISTANCE-FIX] Correction immédiate des conflits...');

const file = 'src/teamCalendarApp.ts';
let content = fs.readFileSync(file, 'utf8');

// Ajouter système de verrouillage
const lockSystem = `
    _transactionLock: false,
    _savingState: false,
    _renderingInProgress: false,
    
    safeScheduleUpdate: function(employeeId, dateKey, updateFn, operation = 'update') {
        if (this._transactionLock) {
            console.warn('⚠️ Transaction en cours, ignoré');
            return;
        }
        
        this._transactionLock = true;
        try {
            if (!this.data.schedule[employeeId]) this.data.schedule[employeeId] = {};
            if (!this.data.schedule[employeeId][dateKey]) this.data.schedule[employeeId][dateKey] = [];
            
            const result = updateFn(this.data.schedule[employeeId][dateKey]);
            setTimeout(() => this.saveState(), 100);
            return result;
        } finally {
            this._transactionLock = false;
        }
    },
    
    renderSafe: function() {
        if (this._renderingInProgress) return;
        this._renderingInProgress = true;
        try {
            if (typeof this.actualRender === 'function') {
                this.actualRender();
            } else if (typeof this.renderUnifiedCalendar === 'function') {
                this.renderUnifiedCalendar();
            }
        } catch (error) {
            console.error('❌ Erreur render:', error);
        } finally {
            this._renderingInProgress = false;
        }
    },
    
    checkDataIntegrity: function() {
        let issues = 0;
        if (!this.data || !this.data.schedule) issues++;
        if (!Array.isArray(this.data.employees)) issues++;
        return issues;
    },
    
    quickRepair: function() {
        if (!this.data) this.data = {};
        if (!this.data.schedule) this.data.schedule = {};
        if (!Array.isArray(this.data.employees)) this.data.employees = [];
        console.log('✅ Réparation rapide terminée');
    },`;

// Insérer après la déclaration de l'objet
const insertPoint = content.indexOf('const TeamCalendarApp: TeamCalendarAppType = {');
if (insertPoint !== -1) {
    const nextLine = content.indexOf('\n', insertPoint);
    content = content.slice(0, nextLine) + lockSystem + content.slice(nextLine);
    console.log('✅ Système de verrouillage ajouté');
}

// Corriger saveState
const saveStatePattern = /saveState:\s*async\s*function\s*\([^)]*\)\s*{[^}]+(?:{[^}]*}[^}]*)*}/gs;
const newSaveState = `saveState: async function() {
    if (this._savingState) return;
    this._savingState = true;
    try {
        const state = {
            employees: this.data.employees || [],
            schedule: this.data.schedule || {},
            vacations: this.data.vacations || [],
            globalVacations: this.data.globalVacations || [],
            regularAssignments: this.data.regularAssignments || [],
            customPosts: this.data.customPosts || []
        };
        localStorage.setItem(this.config.storageKey, JSON.stringify(state));
        localStorage.setItem('teamCalendarAppSettings', JSON.stringify(this.config.appSettings || {}));
        console.log('✅ Sauvegarde sécurisée');
    } catch (error) {
        console.error('❌ Erreur sauvegarde:', error);
    } finally {
        this._savingState = false;
    }
}`;

if (saveStatePattern.test(content)) {
    content = content.replace(saveStatePattern, newSaveState);
    console.log('✅ SaveState sécurisé');
}

fs.writeFileSync(file, content);

// Créer script de test
const testScript = `
console.log('🧪 Test persistance...');
if (window.TeamCalendarApp) {
    const issues = window.TeamCalendarApp.checkDataIntegrity();
    console.log('Intégrité:', issues, 'problèmes');
    if (issues > 0) {
        window.TeamCalendarApp.quickRepair();
    }
    console.log('✅ Test terminé');
}
`;
fs.writeFileSync('test-fix.js', testScript);

console.log('🎉 CORRECTION TERMINÉE !');
console.log('📋 Actions :');
console.log('1. Recharger l\\'application');
console.log('2. Tester avec script test-fix.js dans console'); 