#!/usr/bin/env node

/**
 * TEST SYSTÈME DE LOGS AMÉLIORÉ
 * 
 * Validation complète de la capture de TOUS les logs :
 * - Backend (console.log serveur)
 * - Frontend (React, API calls)
 * - Déplacements (drag & drop employés)
 * - Interface /logs fonctionnelle
 * 
 * Date: 2025-07-02
 */

const API_BASE = 'http://localhost:3001';

console.log('🧪 [TEST-LOGS] Validation système de logs amélioré...');

// Test capture backend
async function testBackendCapture() {
  console.log('\n📡 [TEST] Capture logs backend...');
  
  try {
    const sessionResponse = await fetch(`${API_BASE}/api/debug/current-session`);
    const sessionData = await sessionResponse.json();
    const sessionId = sessionData.sessionId;
    
    // Trigger backend activity
    await fetch(`${API_BASE}/api/health`);
    await fetch(`${API_BASE}/api/employees`);
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const logsResponse = await fetch(`${API_BASE}/api/debug/sessions/${sessionId}?mode=chronological&max=50`);
    const logs = await logsResponse.json();
    const backendLogs = logs.filter(log => log.source === 'backend');
    
    console.log(`   📊 Backend logs: ${backendLogs.length} trouvés`);
    
    if (backendLogs.length > 0) {
      console.log('   ✅ Capture backend ACTIVE');
      return true;
    } else {
      console.log('   ❌ Capture backend INACTIVE');
      return false;
    }
  } catch (error) {
    console.log('   ❌ Erreur:', error.message);
    return false;
  }
}

// Test intégration logs déplacements
async function testDragLogsIntegration() {
  console.log('\n🎯 [TEST] Intégration logs déplacements...');
  
  try {
    const sessionResponse = await fetch(`${API_BASE}/api/debug/current-session`);
    const sessionData = await sessionResponse.json();
    const sessionId = sessionData.sessionId;
    
    // Simuler des logs de déplacement
    const dragLogs = [
      { level: 'info', message: '[DRAG-SYSTEM] Session initialisée', data: { test: true } },
      { level: 'debug', message: '[DRAG-SYSTEM] Drag start', data: { employee: 'Test', test: true } },
      { level: 'info', message: '[DRAG-SYSTEM] Drag end success', data: { success: true, test: true } }
    ];
    
    for (const log of dragLogs) {
      await fetch(`${API_BASE}/api/debug/browser`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId,
          level: log.level,
          message: log.message,
          data: log.data
        })
      });
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const logsResponse = await fetch(`${API_BASE}/api/debug/sessions/${sessionId}?mode=chronological&max=100`);
    const logs = await logsResponse.json();
    const dragSystemLogs = logs.filter(log => 
      log.message.includes('[DRAG-SYSTEM]') || log.source === 'drag-system'
    );
    
    console.log(`   📊 Logs déplacements: ${dragSystemLogs.length} trouvés`);
    
    if (dragSystemLogs.length >= 2) {
      console.log('   ✅ Logs déplacements INTÉGRÉS');
      return true;
    } else {
      console.log('   ❌ Logs déplacements NON INTÉGRÉS');
      return false;
    }
  } catch (error) {
    console.log('   ❌ Erreur:', error.message);
    return false;
  }
}

// Test interface complète
async function testCompleteInterface() {
  console.log('\n🖥️ [TEST] Interface complète...');
  
  try {
    const sessionResponse = await fetch(`${API_BASE}/api/debug/current-session`);
    const sessionData = await sessionResponse.json();
    const sessionId = sessionData.sessionId;
    
    // Test récupération avec différents modes
    const modes = ['chronological', 'ai', 'grouped'];
    let allModesOk = true;
    
    for (const mode of modes) {
      const response = await fetch(`${API_BASE}/api/debug/sessions/${sessionId}?mode=${mode}&max=100`);
      if (!response.ok) {
        console.log(`   ❌ Mode ${mode} échoué`);
        allModesOk = false;
      } else {
        const data = await response.json();
        console.log(`   ✅ Mode ${mode}: ${data.length} logs`);
      }
    }
    
    // Test SSE endpoint
    const sseResponse = await fetch(`${API_BASE}/api/debug/stream/${sessionId}`);
    if (sseResponse.ok) {
      console.log('   ✅ SSE endpoint OK');
      sseResponse.body?.cancel();
    } else {
      console.log('   ❌ SSE endpoint échoué');
      allModesOk = false;
    }
    
    return allModesOk;
  } catch (error) {
    console.log('   ❌ Erreur:', error.message);
    return false;
  }
}

// Test diversité des logs
async function testLogDiversity() {
  console.log('\n🌈 [TEST] Diversité des logs...');
  
  try {
    const sessionResponse = await fetch(`${API_BASE}/api/debug/current-session`);
    const sessionData = await sessionResponse.json();
    const sessionId = sessionData.sessionId;
    
    // Générer différents types de logs
    const testLogs = [
      { level: 'info', message: '[FRONTEND] Test React component', source: 'frontend' },
      { level: 'debug', message: '[BROWSER] Test console debug', source: 'browser' },
      { level: 'warn', message: '[API] Test warning', source: 'frontend' },
      { level: 'error', message: '[ERROR] Test error log', source: 'browser' }
    ];
    
    for (const log of testLogs) {
      await fetch(`${API_BASE}/api/debug/browser`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId,
          level: log.level,
          message: log.message,
          data: { testType: log.source }
        })
      });
    }
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const logsResponse = await fetch(`${API_BASE}/api/debug/sessions/${sessionId}?mode=chronological&max=100`);
    const logs = await logsResponse.json();
    
    // Analyser la diversité
    const sources = new Set(logs.map(log => log.source));
    const levels = new Set(logs.map(log => log.level));
    
    console.log(`   📊 Sources trouvées: ${Array.from(sources).join(', ')}`);
    console.log(`   📊 Niveaux trouvés: ${Array.from(levels).join(', ')}`);
    
    const hasBackend = sources.has('backend');
    const hasFrontend = sources.has('frontend');
    const hasBrowser = sources.has('browser');
    const hasMultipleLevels = levels.size >= 3;
    
    if (hasBackend && hasFrontend && hasBrowser && hasMultipleLevels) {
      console.log('   ✅ Capture COMPLÈTE et DIVERSIFIÉE');
      return true;
    } else {
      console.log('   ⚠️ Capture incomplète');
      console.log(`   Backend: ${hasBackend ? '✅' : '❌'}`);
      console.log(`   Frontend: ${hasFrontend ? '✅' : '❌'}`);
      console.log(`   Browser: ${hasBrowser ? '✅' : '❌'}`);
      console.log(`   Niveaux: ${hasMultipleLevels ? '✅' : '❌'}`);
      return false;
    }
  } catch (error) {
    console.log('   ❌ Erreur:', error.message);
    return false;
  }
}

// Exécution des tests
async function runTests() {
  console.log('\n=== VALIDATION SYSTÈME LOGS AMÉLIORÉ ===');
  
  const tests = [
    { name: 'Capture Backend', fn: testBackendCapture },
    { name: 'Logs Déplacements', fn: testDragLogsIntegration },
    { name: 'Interface Complète', fn: testCompleteInterface },
    { name: 'Diversité Logs', fn: testLogDiversity }
  ];
  
  let passed = 0;
  const total = tests.length;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
        console.log(`\n✅ ${test.name}: RÉUSSI`);
      } else {
        console.log(`\n❌ ${test.name}: ÉCHOUÉ`);
      }
    } catch (error) {
      console.log(`\n❌ ${test.name}: ERREUR - ${error.message}`);
    }
  }
  
  console.log('\n=== RÉSULTATS FINAUX ===');
  console.log(`📊 Tests réussis: ${passed}/${total} (${Math.round((passed/total)*100)}%)`);
  
  if (passed === total) {
    console.log('\n🎉 SYSTÈME DE LOGS COMPLÈTEMENT FONCTIONNEL !');
    console.log('✅ Backend + Frontend + Déplacements + Interface');
    console.log('🚀 Accédez à http://localhost:5173/logs pour voir tous les logs');
  } else {
    console.log('\n⚠️ Améliorations nécessaires');
    console.log('💡 Redémarrez le serveur: node server/app.js');
    console.log('💡 Rechargez l\'interface: http://localhost:5173/logs');
  }
  
  return passed === total;
}

// Lancement
runTests().catch(console.error); 