#!/usr/bin/env node

/**
 * CORRECTION CYCLES REPOSITIONNEMENT
 * 
 * Problème : Trop de sauvegardes/rechargements successifs
 * Solution : Transaction unique par drag & drop
 * 
 * Date: 2025-07-02
 */

import { readFileSync, writeFileSync } from 'fs';

console.log('🔧 [FIX-CYCLES] Correction des cycles de repositionnement...');

// Correction 1: reorderEmployees avec transaction unique
function fixReorderEmployees() {
  console.log('💾 [FIX] Correction reorderEmployees...');
  
  try {
    let content = readFileSync('src/teamCalendarApp.ts', 'utf8');
    
    // Remplacer la fonction reorderEmployees par une version avec transaction unique
    const oldReorderFunction = `// ✅ FONCTION CORRIGÉE : Réorganiser les employés avec marquage anti-écrasement
    reorderEmployees: function(oldIndex, newIndex) {
        if (oldIndex === newIndex) return;
        
        // ✅ NOUVEAU : Marquer qu'un drag & drop est en cours
        this._lastDragDropTime = Date.now();
        console.log('🛡️ [reorderEmployees] Marquage drag & drop pour protection anti-écrasement');`;
    
    const newReorderFunction = `// ✅ FONCTION CORRIGÉE : Réorganiser les employés avec UNE SEULE sauvegarde
    reorderEmployees: function(oldIndex, newIndex) {
        if (oldIndex === newIndex) return;
        
        // ✅ LOCK TRANSACTION : Éviter les appels multiples
        if (this._reorderInProgress) {
            console.log('⚠️ [reorderEmployees] Réorganisation déjà en cours, ignorée');
            return;
        }
        this._reorderInProgress = true;
        
        // ✅ ANNULATION : Supprimer toute sauvegarde en attente
        if (this._saveOrderTimeout) {
            clearTimeout(this._saveOrderTimeout);
            this._saveOrderTimeout = null;
        }
        
        // ✅ NOUVEAU : Marquer qu'un drag & drop est en cours
        this._lastDragDropTime = Date.now();
        console.log('🛡️ [reorderEmployees] TRANSACTION UNIQUE - Marquage drag & drop');`;
    
    if (content.includes(oldReorderFunction)) {
      content = content.replace(oldReorderFunction, newReorderFunction);
      console.log('✅ [FIX] reorderEmployees corrigé');
    }
    
    // Remplacer le timeout par sauvegarde immédiate
    const oldSaveTimeout = `        // Délai plus court pour une meilleure réactivité
        this._saveOrderTimeout = setTimeout(() => {
            this.saveEmployeeOrder();
        }, 200);`;
    
    const newSaveImmediate = `        // ✅ SAUVEGARDE IMMÉDIATE ET UNIQUE (sans timeout)
        console.log('💾 [reorderEmployees] Sauvegarde immédiate et unique');
        this.saveEmployeeOrder();
        
        // ✅ Libérer le lock après 1 seconde max
        setTimeout(() => {
            this._reorderInProgress = false;
        }, 1000);`;
    
    if (content.includes(oldSaveTimeout)) {
      content = content.replace(oldSaveTimeout, newSaveImmediate);
      console.log('✅ [FIX] Sauvegarde immédiate implémentée');
    }
    
    writeFileSync('src/teamCalendarApp.ts', content);
    console.log('✅ [FIX] reorderEmployees avec transaction unique appliqué');
    
  } catch (error) {
    console.error('❌ [FIX] Erreur:', error.message);
  }
}

// Correction 2: Désactiver les appels automatiques dans les tests
function disableAutoTests() {
  console.log('🚫 [FIX] Désactivation des tests automatiques...');
  
  try {
    // Désactiver les tests auto dans test-employee-order-persistence.js
    let testContent = readFileSync('public/test-employee-order-persistence.js', 'utf8');
    
    const autoTestTrigger = `// Auto-test après chargement
setTimeout(() => {
    if (window.teamCalendarApp?.data?.employees?.length > 0) {
        console.log('🚀 [AUTO-TEST] Lancement automatique du test de persistance...');
        window.testEmployeeOrderPersistence();
    } else {
        console.log('⏳ [AUTO-TEST] En attente du chargement des employés...');
        setTimeout(() => {
            if (window.teamCalendarApp?.data?.employees?.length > 0) {
                window.testEmployeeOrderPersistence();
            }
        }, 3000);
    }
}, 2000);`;

    const disabledAutoTest = `// ✅ CORRECTION : Tests automatiques désactivés pour éviter les cycles
// setTimeout(() => {
//     if (window.teamCalendarApp?.data?.employees?.length > 0) {
//         console.log('🚀 [AUTO-TEST] Lancement automatique du test de persistance...');
//         window.testEmployeeOrderPersistence();
//     } else {
//         console.log('⏳ [AUTO-TEST] En attente du chargement des employés...');
//         setTimeout(() => {
//             if (window.teamCalendarApp?.data?.employees?.length > 0) {
//                 window.testEmployeeOrderPersistence();
//             }
//         }, 3000);
//     }
// }, 2000);`;

    if (testContent.includes(autoTestTrigger)) {
      testContent = testContent.replace(autoTestTrigger, disabledAutoTest);
      writeFileSync('public/test-employee-order-persistence.js', testContent);
      console.log('✅ [FIX] Tests auto-persistance désactivés');
    }
    
    // Désactiver les tests auto dans test-drag-drop-source-fix.js
    let sourceTestContent = readFileSync('public/test-drag-drop-source-fix.js', 'utf8');
    
    if (sourceTestContent.includes('🚀 [AUTO-TEST-SOURCE] Lancement test correction à la source...')) {
      sourceTestContent = sourceTestContent.replace(
        'console.log(\'🚀 [AUTO-TEST-SOURCE] Lancement test correction à la source...\');',
        '// ✅ CORRECTION : Test auto-source désactivé pour éviter les cycles\n        // console.log(\'🚀 [AUTO-TEST-SOURCE] Lancement test correction à la source...\');'
      );
      
      sourceTestContent = sourceTestContent.replace(
        'window.testDragDropSourceFix();',
        '// window.testDragDropSourceFix();'
      );
      
      writeFileSync('public/test-drag-drop-source-fix.js', sourceTestContent);
      console.log('✅ [FIX] Tests auto-source désactivés');
    }
    
  } catch (error) {
    console.error('❌ [FIX] Erreur désactivation tests:', error.message);
  }
}

// Correction 3: Améliorer le debouncing dans saveEmployeeOrder
function improveSaveDebouncing() {
  console.log('⏱️ [FIX] Amélioration du debouncing...');
  
  try {
    let content = readFileSync('src/teamCalendarApp.ts', 'utf8');
    
    // Renforcer la protection dans saveEmployeeOrder
    const oldSaveStart = `    saveEmployeeOrder: async function() {
        // ✅ DEBOUNCING ROBUSTE : Éviter les sauvegardes en cascade
        if (this._saveOrderInProgress) {
            console.log('⚠️ [saveEmployeeOrder] Sauvegarde déjà en cours, ignorée');
            return;
        }
        
        this._saveOrderInProgress = true;`;
    
    const newSaveStart = `    saveEmployeeOrder: async function() {
        // ✅ SUPER-DEBOUNCING : Protection renforcée contre les cycles
        if (this._saveOrderInProgress) {
            console.log('⚠️ [saveEmployeeOrder] Sauvegarde déjà en cours, ignorée');
            return;
        }
        
        // ✅ NOUVEAU : Vérifier le delai depuis la dernière sauvegarde
        const now = Date.now();
        if (this._lastSaveTime && (now - this._lastSaveTime) < 1000) {
            console.log('⚠️ [saveEmployeeOrder] Sauvegarde trop rapprochée, ignorée');
            return;
        }
        
        this._saveOrderInProgress = true;
        this._lastSaveTime = now;`;
    
    if (content.includes(oldSaveStart)) {
      content = content.replace(oldSaveStart, newSaveStart);
      console.log('✅ [FIX] Super-debouncing ajouté');
    }
    
    writeFileSync('src/teamCalendarApp.ts', content);
    
  } catch (error) {
    console.error('❌ [FIX] Erreur debouncing:', error.message);
  }
}

// Correction 4: Limiter loadEmployeeOrder aux cas essentiels
function limitLoadEmployeeOrder() {
  console.log('🚫 [FIX] Limitation des appels loadEmployeeOrder...');
  
  try {
    let content = readFileSync('src/teamCalendarApp.ts', 'utf8');
    
    // Ajouter une condition pour éviter les appels inutiles
    const oldLoadStart = `    loadEmployeeOrder: async function() {
        try {
            // ✅ DEBUG : Afficher l'état de protection
            const now = Date.now();
            const GRACE_PERIOD = 5000; // 5 secondes de grâce après un drag & drop`;
    
    const newLoadStart = `    loadEmployeeOrder: async function() {
        try {
            // ✅ PROTECTION CYCLE : Éviter les appels trop fréquents
            const now = Date.now();
            if (this._lastLoadTime && (now - this._lastLoadTime) < 3000) {
                console.log('⚠️ [loadEmployeeOrder] Appel trop fréquent, ignoré');
                return;
            }
            this._lastLoadTime = now;
            
            // ✅ DEBUG : Afficher l'état de protection
            const GRACE_PERIOD = 5000; // 5 secondes de grâce après un drag & drop`;
    
    if (content.includes(oldLoadStart)) {
      content = content.replace(oldLoadStart, newLoadStart);
      console.log('✅ [FIX] Protection cycle loadEmployeeOrder ajoutée');
    }
    
    writeFileSync('src/teamCalendarApp.ts', content);
    
  } catch (error) {
    console.error('❌ [FIX] Erreur limitation loadEmployeeOrder:', error.message);
  }
}

// Exécution de toutes les corrections
async function fixAllCycles() {
  console.log('\n=== CORRECTION COMPLÈTE DES CYCLES ===\n');
  
  fixReorderEmployees();
  disableAutoTests();
  improveSaveDebouncing();
  limitLoadEmployeeOrder();
  
  console.log('\n=== RÉSULTATS ===');
  console.log('✅ 1. Transaction unique pour reorderEmployees');
  console.log('✅ 2. Tests automatiques désactivés');
  console.log('✅ 3. Super-debouncing pour sauvegardes');
  console.log('✅ 4. Protection cycles pour loadEmployeeOrder');
  
  console.log('\n🚀 REDÉMARREZ L\'APPLICATION POUR VOIR:');
  console.log('- 1 seule sauvegarde par drag & drop');
  console.log('- Plus de cycles infernaux');
  console.log('- Logs réduits à l\'essentiel');
  
  console.log('\n📊 ATTENDU : ~10 logs au lieu de 400+ pour un simple déplacement');
}

// Exécuter
fixAllCycles().catch(console.error); 