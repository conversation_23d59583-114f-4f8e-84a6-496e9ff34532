# 🎯 Fonctionnalité Drag & Drop pour Attributions Régulières

## 📋 Vue d'Ensemble

Cette fonctionnalité permet aux utilisateurs de re-assigner des attributions régulières en glissant-déposant une zone de "grip" spéciale sous chaque bloc récurrent dans l'agenda vers la ligne d'un autre employé.

## 🎮 Utilisation

### 1. **Identification des Blocs Réguliers**
- Les blocs d'horaires récurrents (générés par des attributions régulières) affichent une petite bande de 6px de hauteur sous le bloc au survol
- Cette bande est bleue semi-transparente avec un curseur de type "grab"

### 2. **Activation du Grip**
- **Survol du bloc** : Le grip apparaît automatiquement
- **Survol du grip** : Le grip reste visible et change de couleur
- **Délai de 100ms** : Transition fluide pour éviter les clignotements

### 3. **Glisser-Déposer**
- **Drag Start** : Cliquer et maintenir sur le grip
- **Fantôme visuel** : Un indicateur "Attribution régulière" suit le curseur
- **Zones de drop** : Les lignes d'employés se surlignent en vert
- **Drop** : Relâcher sur la ligne de l'employé cible

### 4. **Feedback Visuel**
- **Zones valides** : Surlignage vert des lignes d'employés
- **Hover sur zone** : Intensification du surlignage
- **Confirmation** : Message de succès/erreur via toast

## 🔧 Implémentation Technique

### **Frontend (TypeScript)**

#### **1. Création des Grips**
```typescript
// Dans createShiftElement()
if (shiftData.isRegular && shiftData.assignmentId) {
    const gripDiv = document.createElement('div');
    gripDiv.className = 'grip-regular hidden absolute inset-x-0 -bottom-[6px] h-[6px] cursor-grab bg-blue-500/30 hover:bg-blue-500/50';
    gripDiv.draggable = true;
    gripDiv.dataset.assignmentId = shiftData.assignmentId;
    gripDiv.dataset.regular = 'true';
    
    // Événements de drag
    gripDiv.addEventListener('dragstart', this.handleRegularDragStart.bind(this));
    gripDiv.addEventListener('dragend', this.handleRegularDragEnd.bind(this));
    
    shiftDiv.appendChild(gripDiv);
}
```

#### **2. Gestion du Drag & Drop**
```typescript
handleRegularDragStart(e: DragEvent) {
    const assignmentId = e.currentTarget.dataset.assignmentId;
    e.dataTransfer.setData('regularAssignmentId', assignmentId);
    e.dataTransfer.effectAllowed = 'move';
    
    // Fantôme personnalisé
    const ghost = document.createElement('div');
    ghost.textContent = 'Attribution régulière';
    e.dataTransfer.setDragImage(ghost, 0, 0);
    
    // Surligner les zones de drop
    this.highlightEmployeeDropZones(true);
}
```

#### **3. Re-assignation Optimiste**
```typescript
async reassignRegularAssignment(assignmentId: string, newEmployeeId: string) {
    // 1. Mise à jour optimiste de l'UI
    this.optimisticRegularAssignmentMove(assignmentId, newEmployeeId);
    
    // 2. Sauvegarder via API
    const result = await apiService.updateRegularAssignment(assignmentId, {
        employee_id: newEmployeeId
    });
    
    if (result.success) {
        // Recharger et afficher succès
        await this.loadState();
        this.render();
        window.toastSystem?.success('Attribution déplacée avec succès');
    } else {
        // Rollback en cas d'erreur
        this.rollbackRegularAssignmentMove(assignmentId);
        window.toastSystem?.error('Re-attribution impossible');
    }
}
```

### **Backend (API)**

#### **Route PATCH /api/regular-assignments/:id**
```javascript
// Dans server/app.js
app.put('/api/regular-assignments/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { employee_id } = req.body;
        
        // Validation
        if (!employee_id) {
            return res.status(400).json({ error: 'employee_id requis' });
        }
        
        // Mise à jour
        const result = await client.query(
            'UPDATE regular_assignments SET employee_id = $1 WHERE id = $2 RETURNING *',
            [employee_id, id]
        );
        
        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Attribution non trouvée' });
        }
        
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});
```

### **Styles CSS**

#### **Grips et Zones de Drop**
```css
/* Grip des attributions régulières */
.grip-regular {
    z-index: 10;
    transition: all 0.15s ease-in-out;
}

.grip-regular:hover {
    cursor: grab;
    background: rgba(29, 78, 216, 0.6) !important;
    transform: scaleY(1.2);
}

/* Zones de drop */
.regular-assignment-drop-zone::before {
    content: '';
    position: absolute;
    background: linear-gradient(90deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
    border: 2px dashed rgba(16, 185, 129, 0.3);
    border-radius: 8px;
}
```

## 🎯 Fonctionnalités Clés

### **1. Détection Intelligente**
- ✅ Seuls les blocs avec `isRegular: true` et `assignmentId` génèrent un grip
- ✅ Pas de logique basée sur la couleur, uniquement sur les propriétés de données

### **2. UX Optimisée**
- ✅ Apparition fluide du grip au survol (délai 100ms)
- ✅ Fantôme de drag personnalisé
- ✅ Feedback visuel immédiat sur les zones de drop
- ✅ Interface optimiste avec rollback automatique

### **3. Accessibilité**
- ✅ Support clavier (Space/Enter pour activer)
- ✅ Aria-labels appropriés
- ✅ Focus visible sur les grips
- ✅ Masquage automatique sur mobile

### **4. Robustesse**
- ✅ Gestion d'erreur complète avec rollback
- ✅ Validation côté backend
- ✅ Messages utilisateur appropriés
- ✅ Logs détaillés pour debug

## 📱 Responsive

### **Desktop**
- Grips visibles au survol
- Drag & drop complet

### **Mobile/Tablette**
- Grips masqués (éviter conflits tactiles)
- Indicateur alternatif : emoji 🔄 sur les blocs réguliers

## 🔮 Extensions Futures

### **Multi-sélection**
- Table `selectedAssignmentIds` prête pour sélection multiple
- Drag & drop de plusieurs attributions simultanément

### **Gestion par Lot**
- Re-assignation de toutes les attributions d'un employé
- Échange d'attributions entre deux employés

### **Historique**
- Suivi des modifications d'attributions
- Possibilité d'annuler les dernières actions

## 🐛 Debug & Monitoring

### **Logs Disponibles**
```javascript
// Activation du grip
console.log('🎯 [createShiftElement] Ajout du grip pour l\'attribution régulière');

// Début du drag
console.log('🚀 [handleRegularDragStart] Drag initié pour assignment');

// Drop réussi
console.log('✅ [reassignRegularAssignment] Attribution re-assignée avec succès');
```

### **Mode Debug**
```css
/* Afficher tous les grips */
.show-grips .grip-regular {
    display: block !important;
    background: rgba(29, 78, 216, 0.4) !important;
}
```

## ✅ Tests Recommandés

1. **Création d'attribution régulière** → Vérifier apparition du grip
2. **Survol du bloc** → Vérifier apparition/disparition fluide
3. **Drag vers employé valide** → Vérifier re-assignation
4. **Drag vers zone invalide** → Vérifier rejet
5. **Erreur réseau** → Vérifier rollback automatique
6. **Mobile** → Vérifier masquage des grips

## 🚀 Performance

- **Listeners optimisés** : Délégation d'événements centralisée
- **Rendu conditionnel** : Grips créés uniquement pour blocs réguliers
- **Cache intelligent** : Réutilisation des éléments DOM
- **Debouncing** : Éviter les re-renders excessifs

Cette fonctionnalité offre une expérience utilisateur moderne et intuitive pour la gestion des attributions régulières, tout en maintenant la robustesse et les performances de l'application. 