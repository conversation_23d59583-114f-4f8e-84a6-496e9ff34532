﻿import fs from 'fs';

console.log(' [TOOLTIP-FIX] Correction du système de tooltip...');

const teamCalendarPath = 'src/teamCalendarApp.ts';

try {
    let content = fs.readFileSync(teamCalendarPath, 'utf8');
    
    const oldCall = 'this.setupReplacementTooltip(shiftDiv, shiftData, employeeId);';
    const newCall = \// Utiliser le nouveau gestionnaire de tooltips amélioré
            const tooltipManager = (window as any).tooltipManager;
            if (tooltipManager) {
                tooltipManager.setupReplacementTooltip(shiftDiv, shiftData, employeeId);
            } else {
                this.setupReplacementTooltip(shiftDiv, shiftData, employeeId);
            }\;
    
    if (content.includes(oldCall)) {
        content = content.replace(oldCall, newCall);
        console.log(' [TOOLTIP-FIX] Appel setupReplacementTooltip remplacé');
        
        fs.writeFileSync(teamCalendarPath, content, 'utf8');
        console.log(' [TOOLTIP-FIX] Fichier mis à jour avec succès');
    } else {
        console.log(' [TOOLTIP-FIX] Appel setupReplacementTooltip non trouvé');
    }
    
} catch (error) {
    console.error(' [TOOLTIP-FIX] Erreur:', error.message);
}

console.log(' [TOOLTIP-FIX] Terminé !');
