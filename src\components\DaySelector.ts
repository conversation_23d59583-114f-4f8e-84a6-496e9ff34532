// 📅 Composant de sélection des jours pour les postes
// Interface unifiée pour remplacer la logique weekend/weekday

export interface DaySelectorConfig {
  selectedDays: number[];
  onChange: (selectedDays: number[]) => void;
  allowEmpty?: boolean;
  presets?: DayPreset[];
}

export interface DayPreset {
  name: string;
  days: number[];
  icon?: string;
}

// ✅ PRESETS PRÉDÉFINIS
export const DEFAULT_PRESETS: DayPreset[] = [
  {
    name: '<PERSON><PERSON><PERSON> (Lun-Ven)',
    days: [1, 2, 3, 4, 5],
    icon: '📅'
  },
  {
    name: 'Week-end (Sam-Dim)',
    days: [0, 6],
    icon: '🏖️'
  },
  {
    name: 'Tous les jours',
    days: [0, 1, 2, 3, 4, 5, 6],
    icon: '🗓️'
  },
  {
    name: 'Lun/Mer/Ven',
    days: [1, 3, 5],
    icon: '📋'
  },
  {
    name: 'Mar/Jeu',
    days: [2, 4],
    icon: '📝'
  }
];

export class DaySelector {
  private container: HTMLElement;
  private config: DaySelectorConfig;
  private dayNames = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'];
  private dayNamesLong = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];

  constructor(container: HTMLElement, config: DaySelectorConfig) {
    this.container = container;
    this.config = {
      allowEmpty: false,
      presets: DEFAULT_PRESETS,
      ...config
    };
    
    this.render();
  }

  // ✅ RENDU PRINCIPAL
  private render(): void {
    this.container.innerHTML = `
      <div class="day-selector">
        <!-- Titre -->
        <div class="day-selector-header">
          <h4 class="text-sm font-medium text-gray-700 mb-2">
            📅 Jours de travail
          </h4>
          <p class="text-xs text-gray-500 mb-3">
            Sélectionnez les jours où ce poste est actif
          </p>
        </div>

        <!-- Presets rapides -->
        <div class="day-selector-presets mb-4">
          <div class="flex flex-wrap gap-2">
            ${this.config.presets!.map(preset => `
              <button 
                type="button"
                class="preset-btn text-xs px-2 py-1 rounded border border-gray-300 hover:bg-gray-50 transition-colors"
                data-days="${preset.days.join(',')}"
                title="${preset.name}"
              >
                ${preset.icon || '📅'} ${preset.name}
              </button>
            `).join('')}
          </div>
        </div>

        <!-- Sélecteur de jours individuel -->
        <div class="day-selector-grid">
          <div class="grid grid-cols-7 gap-1 mb-2">
            ${this.dayNames.map((day, index) => `
              <div class="text-center">
                <label class="day-checkbox-container">
                  <input 
                    type="checkbox" 
                    class="day-checkbox hidden" 
                    value="${index}"
                    ${this.config.selectedDays.includes(index) ? 'checked' : ''}
                  />
                  <div class="day-checkbox-visual">
                    <span class="day-name">${day}</span>
                  </div>
                </label>
              </div>
            `).join('')}
          </div>
          
          <!-- Légende des jours longs -->
          <div class="day-legend text-xs text-gray-500 text-center">
            ${this.dayNamesLong.map((day, index) => `
              <span class="day-legend-item ${this.config.selectedDays.includes(index) ? 'selected' : ''}" data-day="${index}">
                ${day}
              </span>
            `).join(' • ')}
          </div>
        </div>

        <!-- Validation -->
        <div class="day-selector-validation mt-3">
          <div class="validation-message text-xs" id="day-validation-message"></div>
          <div class="selected-count text-xs text-gray-600" id="day-selected-count">
            ${this.getSelectedCountText()}
          </div>
        </div>
      </div>
    `;

    this.addStyles();
    this.attachEventListeners();
    this.updateValidation();
  }

  // ✅ STYLES CSS
  private addStyles(): void {
    if (document.getElementById('day-selector-styles')) return;

    const style = document.createElement('style');
    style.id = 'day-selector-styles';
    style.textContent = `
      .day-selector {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .day-checkbox-container {
        display: block;
        cursor: pointer;
        width: 100%;
      }

      .day-checkbox-visual {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border: 2px solid #d1d5db;
        border-radius: 6px;
        background: white;
        transition: all 0.2s ease;
        margin: 0 auto;
      }

      .day-checkbox:checked + .day-checkbox-visual {
        background: #3b82f6;
        border-color: #3b82f6;
        color: white;
        transform: scale(1.05);
      }

      .day-checkbox-visual:hover {
        border-color: #6b7280;
        background: #f9fafb;
      }

      .day-checkbox:checked + .day-checkbox-visual:hover {
        background: #2563eb;
        border-color: #2563eb;
      }

      .day-name {
        font-size: 11px;
        font-weight: 600;
        user-select: none;
      }

      .preset-btn {
        font-size: 11px;
        white-space: nowrap;
      }

      .preset-btn:hover {
        background: #f3f4f6;
        border-color: #9ca3af;
      }

      .preset-btn.active {
        background: #dbeafe;
        border-color: #3b82f6;
        color: #1d4ed8;
      }

      .day-legend-item {
        transition: color 0.2s ease;
      }

      .day-legend-item.selected {
        color: #3b82f6;
        font-weight: 500;
      }

      .validation-message.error {
        color: #dc2626;
      }

      .validation-message.success {
        color: #059669;
      }

      .selected-count {
        margin-top: 4px;
      }
    `;

    document.head.appendChild(style);
  }

  // ✅ ÉVÉNEMENTS
  private attachEventListeners(): void {
    // Checkboxes individuelles
    const checkboxes = this.container.querySelectorAll('.day-checkbox');
    checkboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        this.updateSelection();
      });
    });

    // Boutons presets
    const presetButtons = this.container.querySelectorAll('.preset-btn');
    presetButtons.forEach(button => {
      button.addEventListener('click', () => {
        const daysStr = button.getAttribute('data-days');
        if (daysStr) {
          const days = daysStr.split(',').map(Number);
          this.setSelectedDays(days);
        }
      });
    });
  }

  // ✅ MISE À JOUR SÉLECTION
  private updateSelection(): void {
    const checkboxes = this.container.querySelectorAll('.day-checkbox') as NodeListOf<HTMLInputElement>;
    const selectedDays: number[] = [];

    checkboxes.forEach(checkbox => {
      if (checkbox.checked) {
        selectedDays.push(parseInt(checkbox.value));
      }
    });

    this.config.selectedDays = selectedDays;
    this.config.onChange(selectedDays);
    
    this.updateValidation();
    this.updateLegend();
    this.updateSelectedCount();
    this.updatePresetButtons();
  }

  // ✅ DÉFINIR JOURS SÉLECTIONNÉS
  public setSelectedDays(days: number[]): void {
    this.config.selectedDays = days;
    
    // Mettre à jour les checkboxes
    const checkboxes = this.container.querySelectorAll('.day-checkbox') as NodeListOf<HTMLInputElement>;
    checkboxes.forEach(checkbox => {
      const dayValue = parseInt(checkbox.value);
      checkbox.checked = days.includes(dayValue);
    });

    this.config.onChange(days);
    this.updateValidation();
    this.updateLegend();
    this.updateSelectedCount();
    this.updatePresetButtons();
  }

  // ✅ VALIDATION
  private updateValidation(): void {
    const messageEl = this.container.querySelector('#day-validation-message');
    if (!messageEl) return;

    if (!this.config.allowEmpty && this.config.selectedDays.length === 0) {
      messageEl.textContent = '⚠️ Veuillez sélectionner au moins un jour';
      messageEl.className = 'validation-message error';
    } else {
      messageEl.textContent = '✅ Sélection valide';
      messageEl.className = 'validation-message success';
    }
  }

  // ✅ MISE À JOUR LÉGENDE
  private updateLegend(): void {
    const legendItems = this.container.querySelectorAll('.day-legend-item');
    legendItems.forEach((item, index) => {
      if (this.config.selectedDays.includes(index)) {
        item.classList.add('selected');
      } else {
        item.classList.remove('selected');
      }
    });
  }

  // ✅ COMPTEUR
  private updateSelectedCount(): void {
    const countEl = this.container.querySelector('#day-selected-count');
    if (countEl) {
      countEl.textContent = this.getSelectedCountText();
    }
  }

  private getSelectedCountText(): string {
    const count = this.config.selectedDays.length;
    if (count === 0) return 'Aucun jour sélectionné';
    if (count === 1) return '1 jour sélectionné';
    if (count === 7) return 'Tous les jours sélectionnés';
    return `${count} jours sélectionnés`;
  }

  // ✅ MISE À JOUR BOUTONS PRESETS
  private updatePresetButtons(): void {
    const presetButtons = this.container.querySelectorAll('.preset-btn');
    presetButtons.forEach(button => {
      const daysStr = button.getAttribute('data-days');
      if (daysStr) {
        const presetDays = daysStr.split(',').map(Number);
        const isActive = this.arraysEqual(presetDays, this.config.selectedDays);
        
        if (isActive) {
          button.classList.add('active');
        } else {
          button.classList.remove('active');
        }
      }
    });
  }

  // ✅ UTILITAIRES
  private arraysEqual(a: number[], b: number[]): boolean {
    if (a.length !== b.length) return false;
    const sortedA = [...a].sort();
    const sortedB = [...b].sort();
    return sortedA.every((val, index) => val === sortedB[index]);
  }

  // ✅ API PUBLIQUE
  public getSelectedDays(): number[] {
    return [...this.config.selectedDays];
  }

  public isValid(): boolean {
    return this.config.allowEmpty || this.config.selectedDays.length > 0;
  }

  public destroy(): void {
    this.container.innerHTML = '';
  }
}

// ✅ FONCTION HELPER POUR INTÉGRATION FACILE
export function createDaySelector(
  container: HTMLElement, 
  initialDays: number[] = [1, 2, 3, 4, 5],
  onChange?: (days: number[]) => void
): DaySelector {
  return new DaySelector(container, {
    selectedDays: initialDays,
    onChange: onChange || (() => {}),
    allowEmpty: false,
    presets: DEFAULT_PRESETS
  });
}

export default DaySelector;
