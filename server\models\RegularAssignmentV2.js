import { query } from '../config/database.js';
import { v4 as uuidv4 } from 'uuid';

// Mapping des anciens IDs vers les nouveaux UUIDs (conservé pour compatibilité)
const POST_ID_MAPPING = {
  'WE1': '0fb160a5-331f-4f8f-869e-fc8542b676d1',
  'WE2': '50b21dd0-3423-424b-b348-d6b910e9acdd',
  'MATIN': '12176c06-39de-4eff-b4d3-d07caa385831',
  'SOIR': '0bc24ccb-e6ab-41b1-9258-62a9da7f4ef0',
  'NUIT': 'e97f85e4-9094-400d-9037-5fb93104fa96'
};

async function validateAndConvertUuid(value, fieldName) {
  console.log(`🔍 [validateAndConvertUuid] Entrée: fieldName="${fieldName}", value="${value}", type=${typeof value}`);

  if (!value || value === "") {
    console.log(`⚠️ [validateAndConvertUuid] Valeur vide pour ${fieldName}`);
    return null;
  }

  // Si c'est déjà un UUID valide, le retourner
  if (typeof value === 'string' && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value)) {
    console.log(`✅ [validateAndConvertUuid] UUID valide détecté pour ${fieldName}: ${value}`);
    return value;
  }

  // Vérifier le mapping pour les anciens IDs
  if (POST_ID_MAPPING[value]) {
    console.log(`🔄 [validateAndConvertUuid] Mapping ${fieldName}: ${value} -> ${POST_ID_MAPPING[value]}`);
    return POST_ID_MAPPING[value];
  }

  console.log(`❌ [validateAndConvertUuid] Impossible de convertir ${fieldName}: ${value}`);
  return null;
}

class RegularAssignmentV2 {
  static async findAll() {
    const result = await query(`
      SELECT ra.*, ra.excluded_dates, e.name as employee_name, sp.label as post_label, sp.hours as post_hours, sp.type as post_type
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, ra.start_date
    `);
    return result.rows;
  }

  static async findById(id) {
    const result = await query(`
      SELECT ra.*, ra.excluded_dates, e.name as employee_name, sp.label as post_label, sp.hours as post_hours, sp.type as post_type
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.id = $1
    `, [id]);
    return result.rows[0];
  }

  static async findByEmployee(employeeId) {
    const result = await query(`
      SELECT ra.*, ra.excluded_dates, e.name as employee_name, sp.label as post_label, sp.hours as post_hours, sp.type as post_type
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.employee_id = $1 AND ra.is_active = true
      ORDER BY ra.start_date, array_length(ra.days_of_week, 1) DESC
    `, [employeeId]);
    return result.rows;
  }

  static async create(assignmentData) {
    console.log('📝 [RegularAssignmentV2.create] Données reçues:', assignmentData);
    
    // Gérer les deux formats de nommage (camelCase et snake_case)
    const { 
      id,
      employee_id, employeeId,
      post_id, postId,
      start_date, startDate,
      end_date, endDate,
      selected_days, selectedDays, days_of_week, daysOfWeek
    } = assignmentData;
    
    // Utiliser les valeurs appropriées selon le format
    const finalEmployeeId = employee_id || employeeId;
    const finalPostId = post_id || postId;
    const finalStartDate = start_date || startDate;
    const finalEndDate = end_date || endDate;
    
    // ✅ NOUVEAU : Support des jours multiples en une seule entrée
    const finalDaysOfWeek = days_of_week || daysOfWeek || selected_days || selectedDays || [];
    
    // Valider et convertir les UUID
    const validPostId = await validateAndConvertUuid(finalPostId, 'post_id');
    
    console.log('🔍 [RegularAssignmentV2.create] Post ID original:', finalPostId, 'Post ID validé:', validPostId);
    
    if (!validPostId) {
      throw new Error(`Post ID invalide: ${finalPostId}`);
    }

    if (!finalEmployeeId) {
      throw new Error(`Employee ID manquant`);
    }

    if (!Array.isArray(finalDaysOfWeek) || finalDaysOfWeek.length === 0) {
      throw new Error(`Jours sélectionnés manquants ou invalides`);
    }

    // Valider les jours de la semaine (0-6)
    const invalidDays = finalDaysOfWeek.filter(day => day < 0 || day > 6);
    if (invalidDays.length > 0) {
      throw new Error(`Jours invalides détectés: ${invalidDays.join(', ')}`);
    }

    // ✅ NOUVEAU : Créer UNE SEULE entrée pour tous les jours
    const assignmentId = uuidv4();
    
    console.log(`🔍 [RegularAssignmentV2.create] Création attribution unique pour jours: [${finalDaysOfWeek.join(', ')}]`);
    
    const result = await query(`
      INSERT INTO regular_assignments (id, employee_id, post_id, days_of_week, start_date, end_date, is_active)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [assignmentId, finalEmployeeId, validPostId, finalDaysOfWeek, finalStartDate, finalEndDate, true]);
    
    console.log(`✅ [RegularAssignmentV2.create] Attribution créée avec ${finalDaysOfWeek.length} jours`);
    return result.rows[0];
  }

  static async update(id, assignmentData) {
    console.log('📝 [RegularAssignmentV2.update] Données reçues:', assignmentData);
    
    // Gérer les deux formats de nommage (camelCase et snake_case)
    const { 
      employee_id, employeeId,
      post_id, postId,
      days_of_week, daysOfWeek, selected_days, selectedDays,
      start_date, startDate,
      end_date, endDate,
      is_active, isActive,
      excluded_dates, excludedDates // ✅ NOUVEAU : Support pour les dates exclues
    } = assignmentData;
    
    // Utiliser les valeurs appropriées selon le format
    const finalEmployeeId = employee_id || employeeId;
    const finalPostId = post_id || postId;
    const finalDaysOfWeek = days_of_week || daysOfWeek || selected_days || selectedDays;
    const finalStartDate = start_date || startDate;
    const finalEndDate = end_date || endDate;
    const finalIsActive = is_active !== undefined ? is_active : (isActive !== undefined ? isActive : true);
    const finalExcludedDates = excluded_dates || excludedDates || []; // ✅ NOUVEAU : Gestion des dates exclues
    
    // Valider et convertir les UUID
    const validPostId = await validateAndConvertUuid(finalPostId, 'post_id');
    
    if (!validPostId) {
      throw new Error(`Post ID invalide: ${finalPostId}`);
    }

    // Valider les jours si fournis
    if (finalDaysOfWeek && Array.isArray(finalDaysOfWeek)) {
      const invalidDays = finalDaysOfWeek.filter(day => day < 0 || day > 6);
      if (invalidDays.length > 0) {
        throw new Error(`Jours invalides détectés: ${invalidDays.join(', ')}`);
      }
    }
    
    const result = await query(`
      UPDATE regular_assignments 
      SET employee_id = $1, post_id = $2, days_of_week = $3, start_date = $4, 
          end_date = $5, is_active = $6, excluded_dates = $7, updated_at = CURRENT_TIMESTAMP
      WHERE id = $8
      RETURNING *
    `, [finalEmployeeId, validPostId, finalDaysOfWeek, finalStartDate, finalEndDate, finalIsActive, finalExcludedDates, id]);
    
    return result.rows[0];
  }

  static async delete(id) {
    const result = await query('DELETE FROM regular_assignments WHERE id = $1 RETURNING *', [id]);
    return result.rows[0];
  }

  // ✅ NOUVEAU : Méthode pour supprimer par groupe (employé + poste + dates)
  static async deleteByGroup(employeeId, postId, startDate, endDate = null) {
    const result = await query(`
      DELETE FROM regular_assignments 
      WHERE employee_id = $1 AND post_id = $2 AND start_date = $3 
      AND (end_date = $4 OR (end_date IS NULL AND $4 IS NULL))
      RETURNING *
    `, [employeeId, postId, startDate, endDate]);
    return result.rows;
  }

  static async deleteByEmployee(employeeId) {
    const result = await query('DELETE FROM regular_assignments WHERE employee_id = $1 RETURNING *', [employeeId]);
    return result.rows;
  }

  static async deleteByPost(postId) {
    const result = await query('DELETE FROM regular_assignments WHERE post_id = $1 RETURNING *', [postId]);
    return result.rows;
  }

  // ✅ NOUVEAU : Méthode optimisée pour trouver les attributions actives à une date
  static async findActiveAtDate(date) {
    const result = await query(`
      SELECT ra.*, e.name as employee_name, sp.label as post_label, sp.hours as post_hours, sp.type as post_type
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      AND ra.start_date <= $1
      AND (ra.end_date IS NULL OR ra.end_date >= $1)
      AND EXTRACT(DOW FROM $1::date) = ANY(ra.days_of_week)
      ORDER BY e.name, sp.label
    `, [date]);
    return result.rows;
  }

  // ✅ NOUVEAU : Méthode pour trouver par jour de la semaine avec optimisation
  static async findByDayOfWeek(dayOfWeek, date = null) {
    let whereClause = 'ra.is_active = true AND $1 = ANY(ra.days_of_week)';
    let params = [dayOfWeek];
    
    if (date) {
      whereClause += ' AND ra.start_date <= $2 AND (ra.end_date IS NULL OR ra.end_date >= $2)';
      params.push(date);
    }
    
    const result = await query(`
      SELECT ra.*, e.name as employee_name, sp.label as post_label, sp.hours as post_hours, sp.type as post_type
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ${whereClause}
      ORDER BY e.name, sp.label
    `, params);
    return result.rows;
  }

  // ✅ NOUVEAU : Méthode groupée optimisée pour l'affichage frontend
  static async findAllGrouped() {
    const result = await query(`
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
    `);
    
    return result.rows.map(row => ({
      ...row,
      selectedDays: row.days_of_week, // Compatibilité avec le frontend
      isLimited: row.end_date !== null,
      excludedDates: row.excluded_dates || []
    }));
  }

  // ✅ NOUVEAU : Méthode pour les statistiques et analyses
  static async getStatistics() {
    const result = await query(`
      SELECT 
        COUNT(*) as total_assignments,
        COUNT(DISTINCT employee_id) as employees_with_assignments,
        COUNT(DISTINCT post_id) as posts_with_assignments,
        AVG(array_length(days_of_week, 1)) as avg_days_per_assignment,
        MIN(array_length(days_of_week, 1)) as min_days_per_assignment,
        MAX(array_length(days_of_week, 1)) as max_days_per_assignment,
        COUNT(CASE WHEN end_date IS NULL THEN 1 END) as unlimited_assignments,
        COUNT(CASE WHEN end_date IS NOT NULL THEN 1 END) as limited_assignments
      FROM regular_assignments
      WHERE is_active = true
    `);
    return result.rows[0];
  }

  // Fonction utilitaire pour valider les UUIDs
  static isValidUUID(str) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  }

  // ✅ NOUVEAU : Méthode pour créer ou mettre à jour en masse (upsert optimisé)
  static async bulkUpsert(assignments) {
    const results = [];
    
    for (const assignment of assignments) {
      try {
        // Vérifier si une attribution similaire existe déjà
        const existing = await query(`
          SELECT id FROM regular_assignments 
          WHERE employee_id = $1 AND post_id = $2 AND start_date = $3 
          AND (end_date = $4 OR (end_date IS NULL AND $4 IS NULL))
          AND is_active = true
        `, [assignment.employeeId, assignment.postId, assignment.startDate, assignment.endDate]);
        
        if (existing.rows.length > 0) {
          // Mettre à jour l'attribution existante
          const updated = await this.update(existing.rows[0].id, assignment);
          results.push({ action: 'updated', data: updated });
        } else {
          // Créer une nouvelle attribution
          const created = await this.create(assignment);
          results.push({ action: 'created', data: created });
        }
      } catch (error) {
        console.error('Erreur lors du upsert:', error);
        results.push({ action: 'error', error: error.message, data: assignment });
      }
    }
    
    return results;
  }
}

export default RegularAssignmentV2; 