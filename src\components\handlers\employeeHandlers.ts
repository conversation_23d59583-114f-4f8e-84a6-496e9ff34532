/**
 * @fileoverview Gestionnaires d'événements pour les employés
 * @description Fonctions de gestion des événements liés aux employés
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import { Employee, ValidationResult } from '../types/interfaces';
import { validateEmployee, generateUUID } from '../utils/validationUtils';

/**
 * @description Gestionnaire pour l'ajout d'un employé
 * @param employeeData - Données de l'employé à ajouter
 * @param onSuccess - Callback en cas de succès
 * @param onError - Callback en cas d'erreur
 * @example
 * ```typescript
 * handleAddEmployee(
 *   { name: '<PERSON>', status: 'Actif', avatar: '' },
 *   (employee) => console.log('Employé ajouté:', employee),
 *   (error) => console.error('Erreur:', error)
 * );
 * ```
 */
export function handleAddEmployee(
  employeeData: Partial<Employee>,
  onSuccess: (employee: Employee) => void,
  onError: (error: string) => void
): void {
  try {
    // Générer un ID si manquant
    if (!employeeData.id) {
      employeeData.id = generateUUID();
    }

    // Valider les données
    const validation = validateEmployee(employeeData);
    if (!validation.isValid) {
      onError(`Données invalides: ${validation.errors.join(', ')}`);
      return;
    }

    // Créer l'employé complet
    const newEmployee: Employee = {
      id: employeeData.id!,
      name: employeeData.name!,
      status: employeeData.status || 'Actif',
      avatar: employeeData.avatar || '',
      templateId: employeeData.templateId,
      extraFields: employeeData.extraFields || {}
    };

    onSuccess(newEmployee);
  } catch (error) {
    onError(`Erreur lors de l'ajout: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
  }
}

/**
 * @description Gestionnaire pour la modification d'un employé
 * @param employeeId - ID de l'employé à modifier
 * @param updates - Modifications à apporter
 * @param currentEmployees - Liste actuelle des employés
 * @param onSuccess - Callback en cas de succès
 * @param onError - Callback en cas d'erreur
 */
export function handleUpdateEmployee(
  employeeId: string,
  updates: Partial<Employee>,
  currentEmployees: Employee[],
  onSuccess: (employee: Employee) => void,
  onError: (error: string) => void
): void {
  try {
    const employeeIndex = currentEmployees.findIndex(emp => emp.id === employeeId);
    if (employeeIndex === -1) {
      onError('Employé non trouvé');
      return;
    }

    const currentEmployee = currentEmployees[employeeIndex];
    const updatedEmployee = { ...currentEmployee, ...updates };

    // Valider les données mises à jour
    const validation = validateEmployee(updatedEmployee);
    if (!validation.isValid) {
      onError(`Données invalides: ${validation.errors.join(', ')}`);
      return;
    }

    onSuccess(updatedEmployee);
  } catch (error) {
    onError(`Erreur lors de la modification: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
  }
}

/**
 * @description Gestionnaire pour la suppression d'un employé
 * @param employeeId - ID de l'employé à supprimer
 * @param currentEmployees - Liste actuelle des employés
 * @param checkUsage - Fonction pour vérifier si l'employé est utilisé
 * @param onSuccess - Callback en cas de succès
 * @param onError - Callback en cas d'erreur
 */
export function handleDeleteEmployee(
  employeeId: string,
  currentEmployees: Employee[],
  checkUsage: (id: string) => boolean,
  onSuccess: (employeeId: string) => void,
  onError: (error: string) => void
): void {
  try {
    const employee = currentEmployees.find(emp => emp.id === employeeId);
    if (!employee) {
      onError('Employé non trouvé');
      return;
    }

    // Vérifier si l'employé est utilisé dans le planning
    if (checkUsage(employeeId)) {
      onError('Impossible de supprimer cet employé car il est utilisé dans le planning');
      return;
    }

    onSuccess(employeeId);
  } catch (error) {
    onError(`Erreur lors de la suppression: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
  }
}

/**
 * @description Gestionnaire pour la réorganisation des employés
 * @param draggedEmployeeId - ID de l'employé déplacé
 * @param targetEmployeeId - ID de l'employé cible
 * @param currentEmployees - Liste actuelle des employés
 * @param onSuccess - Callback en cas de succès
 * @param onError - Callback en cas d'erreur
 */
export function handleReorderEmployees(
  draggedEmployeeId: string,
  targetEmployeeId: string,
  currentEmployees: Employee[],
  onSuccess: (reorderedEmployees: Employee[]) => void,
  onError: (error: string) => void
): void {
  try {
    const draggedIndex = currentEmployees.findIndex(emp => emp.id === draggedEmployeeId);
    const targetIndex = currentEmployees.findIndex(emp => emp.id === targetEmployeeId);

    if (draggedIndex === -1 || targetIndex === -1) {
      onError('Employé non trouvé pour la réorganisation');
      return;
    }

    const reorderedEmployees = [...currentEmployees];
    const [draggedEmployee] = reorderedEmployees.splice(draggedIndex, 1);
    reorderedEmployees.splice(targetIndex, 0, draggedEmployee);

    onSuccess(reorderedEmployees);
  } catch (error) {
    onError(`Erreur lors de la réorganisation: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
  }
}

/**
 * @description Gestionnaire pour l'upload d'avatar
 * @param file - Fichier image à uploader
 * @param maxSize - Taille maximale en bytes (défaut: 2MB)
 * @param onSuccess - Callback en cas de succès avec l'URL/base64
 * @param onError - Callback en cas d'erreur
 */
export function handleAvatarUpload(
  file: File,
  maxSize: number = 2 * 1024 * 1024, // 2MB
  onSuccess: (avatarData: string) => void,
  onError: (error: string) => void
): void {
  try {
    // Vérifier le type de fichier
    if (!file.type.startsWith('image/')) {
      onError('Le fichier doit être une image');
      return;
    }

    // Vérifier la taille
    if (file.size > maxSize) {
      onError(`Le fichier est trop volumineux (max: ${Math.round(maxSize / 1024 / 1024)}MB)`);
      return;
    }

    // Lire le fichier en base64
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        onSuccess(e.target.result as string);
      } else {
        onError('Erreur lors de la lecture du fichier');
      }
    };
    reader.onerror = () => {
      onError('Erreur lors de la lecture du fichier');
    };
    reader.readAsDataURL(file);
  } catch (error) {
    onError(`Erreur lors de l'upload: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
  }
}

/**
 * @description Gestionnaire pour l'échange de postes entre employés
 * @param employee1Id - ID du premier employé
 * @param employee2Id - ID du deuxième employé
 * @param dateKey - Clé de date pour l'échange
 * @param getEmployeeShifts - Fonction pour obtenir les shifts d'un employé
 * @param onSuccess - Callback en cas de succès
 * @param onError - Callback en cas d'erreur
 */
export function handleEmployeeSwap(
  employee1Id: string,
  employee2Id: string,
  dateKey: string,
  getEmployeeShifts: (employeeId: string, dateKey: string) => any[],
  onSuccess: (swapData: { employee1Id: string; employee2Id: string; dateKey: string }) => void,
  onError: (error: string) => void
): void {
  try {
    if (employee1Id === employee2Id) {
      onError('Impossible d\'échanger un employé avec lui-même');
      return;
    }

    const shifts1 = getEmployeeShifts(employee1Id, dateKey);
    const shifts2 = getEmployeeShifts(employee2Id, dateKey);

    // Vérifier s'il y a des shifts à échanger
    if (shifts1.length === 0 && shifts2.length === 0) {
      onError('Aucun poste à échanger pour cette date');
      return;
    }

    onSuccess({ employee1Id, employee2Id, dateKey });
  } catch (error) {
    onError(`Erreur lors de l'échange: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
  }
}

/**
 * @description Utilitaire pour filtrer les employés
 * @param employees - Liste des employés
 * @param searchTerm - Terme de recherche
 * @param statusFilter - Filtre par statut (optionnel)
 * @returns Liste des employés filtrés
 */
export function filterEmployees(
  employees: Employee[],
  searchTerm: string = '',
  statusFilter?: string
): Employee[] {
  return employees.filter(employee => {
    const matchesSearch = !searchTerm || 
      employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.id.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !statusFilter || employee.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });
}

/**
 * @description Utilitaire pour trier les employés
 * @param employees - Liste des employés
 * @param sortBy - Critère de tri ('name' | 'status' | 'id')
 * @param sortOrder - Ordre de tri ('asc' | 'desc')
 * @returns Liste des employés triés
 */
export function sortEmployees(
  employees: Employee[],
  sortBy: 'name' | 'status' | 'id' = 'name',
  sortOrder: 'asc' | 'desc' = 'asc'
): Employee[] {
  return [...employees].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'status':
        comparison = a.status.localeCompare(b.status);
        break;
      case 'id':
        comparison = a.id.localeCompare(b.id);
        break;
    }
    
    return sortOrder === 'asc' ? comparison : -comparison;
  });
}
