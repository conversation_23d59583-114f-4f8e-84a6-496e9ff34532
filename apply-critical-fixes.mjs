#!/usr/bin/env node

/**
 * Application des corrections critiques de manière précise
 */

import fs from 'fs';

console.log('🔧 [APPLY-CRITICAL-FIXES] Application des corrections critiques...\n');

const filePath = './src/teamCalendarApp.ts';
let content = fs.readFileSync(filePath, 'utf8');

// Correction 1: Initialiser addPostButton et availablePostsContainer
content = content.replace(
    'addPostButton: null,',
    'addPostButton: document.getElementById(\'add-post-btn\'),'
);

content = content.replace(
    'availablePostsContainer: null,',
    'availablePostsContainer: document.getElementById(\'available-posts-container\'),'
);

console.log('✅ Éléments DOM initialisés avec getElementById');

// Correction 2: Ajouter setupEmployeeDragDrop dans attachAllEventListeners
if (!content.includes('this.setupEmployeeDragDrop()')) {
    const attachPattern = /(attachAllEventListeners:\s*function\(\)\s*\{[\s\S]*?)(\s*console\.log\('✅ \[attachAllEventListeners\] Écouteurs attachés avec succès'\);\s*\})/;
    if (attachPattern.test(content)) {
        content = content.replace(attachPattern, (match, before, after) => {
            return before + '\n        // ✅ CORRECTION : Configuration du drag & drop des employés\n        this.setupEmployeeDragDrop();\n        ' + after;
        });
        console.log('✅ setupEmployeeDragDrop() ajouté dans attachAllEventListeners');
    }
}

// Correction 3: Ajouter createAvailablePostsContainer dans renderEmployees
if (!content.includes('this.createAvailablePostsContainer()')) {
    const renderPattern = /(renderEmployees:\s*function\(\)\s*\{[\s\S]*?)(\s*console\.log\('✅ \[renderEmployees\] Employés rendus avec succès'\);\s*\})/;
    if (renderPattern.test(content)) {
        content = content.replace(renderPattern, (match, before, after) => {
            return before + '\n        // ✅ CORRECTION : Créer le conteneur des postes disponibles\n        if (!this.elements.availablePostsContainer) {\n            this.elements.availablePostsContainer = this.createAvailablePostsContainer();\n        }\n        ' + after;
        });
        console.log('✅ createAvailablePostsContainer() ajouté dans renderEmployees');
    }
}

// Correction 4: S'assurer que ModalManager.init est appelé
if (!content.includes('this.ModalManager.init(this)')) {
    const attachPattern = /(attachAllEventListeners:\s*function\(\)\s*\{[\s\S]*?)(\s*console\.log\('✅ \[attachAllEventListeners\] Écouteurs attachés avec succès'\);\s*\})/;
    if (attachPattern.test(content)) {
        content = content.replace(attachPattern, (match, before, after) => {
            return before + '\n        // ✅ CORRECTION : Initialiser le ModalManager\n        if (this.ModalManager && !this.ModalManager._initialized) {\n            this.ModalManager.init(this);\n            this.ModalManager._initialized = true;\n        }\n        ' + after;
        });
        console.log('✅ ModalManager.init() ajouté dans attachAllEventListeners');
    }
}

// Écrire les corrections
fs.writeFileSync(filePath, content);

console.log('\n✅ Corrections critiques appliquées !');
console.log('\n📋 Corrections appliquées :');
console.log('✅ 1. addPostButton initialisé avec getElementById');
console.log('✅ 2. availablePostsContainer initialisé avec getElementById');
console.log('✅ 3. setupEmployeeDragDrop() ajouté dans attachAllEventListeners');
console.log('✅ 4. createAvailablePostsContainer() ajouté dans renderEmployees');
console.log('✅ 5. ModalManager.init() ajouté dans attachAllEventListeners');

console.log('\n🚀 Instructions pour tester :');
console.log('1. Redémarrer le serveur : npm run dev');
console.log('2. Ouvrir http://localhost:5173');
console.log('3. Vérifier que :');
console.log('   - Le drag & drop des employés fonctionne');
console.log('   - Les modales des paramètres s\'ouvrent');
console.log('   - Les postes disponibles sont visibles');

console.log('\n✅ [APPLY-CRITICAL-FIXES] Corrections terminées !'); 