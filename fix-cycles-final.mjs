#!/usr/bin/env node

import { readFileSync, writeFileSync } from 'fs';

console.log('🔄 CORRECTION FINALE DES CYCLES DE REPOSITIONNEMENT');

// Correction principale: remplacer reorderEmployees par version sans cycles
function fixCycles() {
  try {
    let content = readFileSync('src/teamCalendarApp.ts', 'utf8');
    
    // 1. Corriger reorderEmployees - version simple sans timeout
    const newReorderEmployees = `    reorderEmployees: function(oldIndex, newIndex) {
        if (oldIndex === newIndex) return;
        
        // Verrou pour éviter les appels multiples
        if (this._reorderInProgress) {
            console.log('🔒 [reorderEmployees] Déjà en cours, ignoré');
            return;
        }
        
        this._reorderInProgress = true;
        this._lastDragDropTime = Date.now();
        
        console.log('🔄 [reorderEmployees] ' + oldIndex + ' → ' + newIndex);
        
        try {
            // Réorganiser
            const employees = [...this.data.employees];
            const moved = employees.splice(oldIndex, 1)[0];
            employees.splice(newIndex, 0, moved);
            this.data.employees = employees;
            this.renderEmployeeRows();
            
            // Sauvegarder immédiatement
            this.saveEmployeeOrder();
            
        } finally {
            // Libérer après 1 seconde
            setTimeout(() => { this._reorderInProgress = false; }, 1000);
        }
    },`;
    
    // 2. Corriger saveEmployeeOrder - debouncing strict
    const newSaveEmployeeOrder = `    saveEmployeeOrder: async function() {
        const now = Date.now();
        
        // Débouncing: 3 secondes minimum entre sauvegardes
        if (this._lastSaveTime && (now - this._lastSaveTime) < 3000) {
            console.log('⚠️ [saveEmployeeOrder] Trop rapide, ignoré');
            return;
        }
        
        if (this._saveOrderInProgress) {
            console.log('⚠️ [saveEmployeeOrder] Déjà en cours, ignoré');
            return;
        }
        
        this._saveOrderInProgress = true;
        this._lastSaveTime = now;
        
        try {
            const order = this.data.employees.map((emp, i) => ({
                id: emp.id, name: emp.name, order: i
            }));
            
            await fetch('/api/employee-order', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ employeeOrder: order })
            });
            
            localStorage.setItem('employeeOrder', JSON.stringify(order));
            console.log('✅ [saveEmployeeOrder] Sauvegardé');
            
        } catch (error) {
            console.error('❌ [saveOrderOrder] Erreur:', error);
        } finally {
            this._saveOrderInProgress = false;
        }
    },`;
    
    // 3. Corriger loadEmployeeOrder - bloquage automatique
    const newLoadEmployeeOrder = `    loadEmployeeOrder: async function() {
        const now = Date.now();
        
        // Bloquer si drag & drop récent (5 secondes)
        if (this._lastDragDropTime && (now - this._lastDragDropTime) < 5000) {
            console.log('🛡️ [loadEmployeeOrder] Drag récent, bloqué');
            return;
        }
        
        // Bloquer appels trop fréquents (3 secondes)
        if (this._lastLoadTime && (now - this._lastLoadTime) < 3000) {
            console.log('🛡️ [loadEmployeeOrder] Trop fréquent, bloqué');
            return;
        }
        
        this._lastLoadTime = now;
        
        try {
            const cached = localStorage.getItem('employeeOrder');
            if (cached) {
                console.log('✅ [loadEmployeeOrder] Cache utilisé');
                this.applyEmployeeOrder(JSON.parse(cached));
                return;
            }
            
            const response = await fetch('/api/employee-order');
            if (response.ok) {
                const data = await response.json();
                if (data.employeeOrder) {
                    console.log('✅ [loadEmployeeOrder] API chargé');
                    this.applyEmployeeOrder(data.employeeOrder);
                }
            }
        } catch (error) {
            console.warn('⚠️ [loadEmployeeOrder]', error.message);
        }
    },`;
    
    // Remplacements
    content = content.replace(
      /reorderEmployees:\s*function\([^}]*\}[^}]*\}[^}]*\},/s,
      newReorderEmployees
    );
    
    content = content.replace(
      /saveEmployeeOrder:\s*async\s*function\(\)[^}]*\{[^}]*\}[^}]*\}[^}]*\}[^}]*\},/s,
      newSaveEmployeeOrder
    );
    
    content = content.replace(
      /loadEmployeeOrder:\s*async\s*function\(\)[^}]*\{[^}]*\}[^}]*\}[^}]*\}[^}]*\},/s,
      newLoadEmployeeOrder
    );
    
    // Supprimer les appels automatiques
    content = content.replace(/this\.loadEmployeeOrder\(\);/g, '// Auto-load supprimé');
    
    writeFileSync('src/teamCalendarApp.ts', content);
    
    console.log('✅ Corrections appliquées:');
    console.log('  - reorderEmployees: verrou + sauvegarde immédiate');
    console.log('  - saveEmployeeOrder: debouncing 3s');
    console.log('  - loadEmployeeOrder: blocage drag récent');
    console.log('  - Appels automatiques supprimés');
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

fixCycles();

console.log('\n🎉 FINI LES CYCLES DE REPOSITIONNEMENT !');
console.log('📋 Résultat attendu: 1 seule sauvegarde par drag & drop');
console.log('🔗 Testez maintenant: http://localhost:5173/logs'); 