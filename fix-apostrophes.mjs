#!/usr/bin/env node

/**
 * Script pour corriger les erreurs d'apostrophes dans les messages de log
 */

import fs from 'fs';

console.log('🔧 [FIX-APOSTROPHES] Correction des erreurs d\'apostrophes...');

const filePath = './src/teamCalendarApp.ts';
let content = fs.readFileSync(filePath, 'utf8');

// Corrections des apostrophes dans les messages de log
const corrections = [
    {
        from: "console.log('🚀 [init] Démarrage de l'initialisation...');",
        to: "console.log('🚀 [init] Démarrage de l\\'initialisation...');"
    },
    {
        from: "console.warn('⚠️ [init] Impossible d'initialiser _employeeDragLogger:', e);",
        to: "console.warn('⚠️ [init] Impossible d\\'initialiser _employeeDragLogger:', e);"
    },
    {
        from: "console.warn('⚠️ [init] Impossible d'initialiser employeeService:', e);",
        to: "console.warn('⚠️ [init] Impossible d\\'initialiser employeeService:', e);"
    },
    {
        from: "window.toastSystem?.error('Erreur d'initialisation du DOM');",
        to: "window.toastSystem?.error('Erreur d\\'initialisation du DOM');"
    },
    {
        from: "console.error('❌ [init] Erreur lors de l'initialisation:', error);",
        to: "console.error('❌ [init] Erreur lors de l\\'initialisation:', error);"
    },
    {
        from: "window.toastSystem?.error('Erreur lors de l'initialisation');",
        to: "window.toastSystem?.error('Erreur lors de l\\'initialisation');"
    }
];

console.log('📝 [FIX-APOSTROPHES] Application des corrections...');

let correctionsApplied = 0;
corrections.forEach((correction, index) => {
    if (content.includes(correction.from)) {
        content = content.replace(correction.from, correction.to);
        console.log(`✅ ${index + 1}. Correction appliquée`);
        correctionsApplied++;
    } else {
        console.log(`⚠️ ${index + 1}. Pattern non trouvé`);
    }
});

// Écrire le fichier corrigé
fs.writeFileSync(filePath, content);

console.log(`✅ [FIX-APOSTROPHES] ${correctionsApplied} corrections appliquées !`);

console.log('\n🚀 Prochaines étapes :');
console.log('1. Redémarrer le serveur : npm run dev');
console.log('2. Vérifier qu\'il n\'y a plus d\'erreurs de syntaxe');
console.log('3. Tester l\'application dans le navigateur');

console.log('\n✅ [FIX-APOSTROPHES] Script terminé !'); 