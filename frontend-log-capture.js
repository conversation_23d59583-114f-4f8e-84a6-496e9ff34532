/**
 * Capture des logs Frontend/React vers l'API
 * À injecter dans les composants React pour capturer les logs frontend
 */

let frontendSessionId = null;
let isInitialized = false;

// Initialiser la capture frontend
async function initFrontendLogCapture() {
  if (isInitialized) return;
  
  try {
    // Récupérer la session du serveur
    const response = await fetch('/api/debug/current-session');
    if (response.ok) {
      const data = await response.json();
      frontendSessionId = data.sessionId;
      console.log(`🟣 [Frontend] Session: ${frontendSessionId.substring(0, 8)}...`);
      isInitialized = true;
    }
  } catch (error) {
    console.warn('Frontend log capture non disponible:', error.message);
  }
}

// Fonction d'envoi des logs frontend
async function sendFrontendLog(level, message, data = {}) {
  if (!frontendSessionId) {
    await initFrontendLogCapture();
  }
  
  if (!frontendSessionId) return; // Pas de session disponible
  
  try {
    await fetch('/api/debug/browser', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        sessionId: frontendSessionId,
        level,
        message: `[FRONTEND] ${message}`,
        data
      })
    });
  } catch (error) {
    // Échec silencieux
  }
}

// API publique pour les composants React
window.frontendLogger = {
  debug: (msg, data) => sendFrontendLog('debug', msg, data),
  info: (msg, data) => sendFrontendLog('info', msg, data),
  warn: (msg, data) => sendFrontendLog('warn', msg, data),
  error: (msg, data) => sendFrontendLog('error', msg, data),
  init: initFrontendLogCapture
};

// Auto-initialisation
if (typeof window !== 'undefined') {
  initFrontendLogCapture();
  
  // Capturer les erreurs React
  window.addEventListener('error', (event) => {
    if (event.error && event.error.stack && event.error.stack.includes('react')) {
      sendFrontendLog('error', `React Error: ${event.message}`, {
        stack: event.error.stack,
        filename: event.filename,
        lineno: event.lineno
      });
    }
  });
  
  // Hook dans les warnings React (si disponible)
  if (window.React && window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
    const originalWarn = console.warn;
    console.warn = (...args) => {
      originalWarn.apply(console, args);
      const message = args.join(' ');
      if (message.includes('React') || message.includes('Warning:')) {
        sendFrontendLog('warn', `React Warning: ${message}`);
      }
    };
  }
}

export { sendFrontendLog, initFrontendLogCapture }; 