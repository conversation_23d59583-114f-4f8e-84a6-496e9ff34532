module.exports = {
  root: true,
  env: { browser: true, es2020: true },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react-hooks/recommended',
  ],
  ignorePatterns: ['dist', '.eslintrc.js'],
  parser: '@typescript-eslint/parser',
  plugins: ['react-refresh'],
  rules: {
    'react-refresh/only-export-components': [
      'warn',
      { allowConstantExport: true },
    ],
    
    // ✅ NOUVELLE RÈGLE : Interdire les URLs vides ou null
    'no-empty-href': 'error',
    'no-null-urls': 'error',
    
    // ✅ RÈGLES PERSONNALISÉES : URLs et sécurité
    'no-restricted-syntax': [
      'error',
      {
        selector: 'Literal[value="#"]',
        message: 'Évitez href="#" - utilisez un bouton ou une URL valide'
      },
      {
        selector: 'Literal[value="null"]',
        message: '<PERSON><PERSON><PERSON>z les URLs "null" - utilisez une URL valide ou undefined'
      },
      {
        selector: 'Literal[value=""]',
        message: '<PERSON>vitez les URLs vides - utilisez une URL valide ou undefined'
      },
      {
        selector: 'JSXAttribute[name.name="src"][value.value="null"]',
        message: 'src="null" détecté - utilisez un placeholder ou une URL valide'
      },
      {
        selector: 'JSXAttribute[name.name="href"][value.value="null"]',
        message: 'href="null" détecté - utilisez un bouton ou une URL valide'
      },
      {
        selector: 'CallExpression[callee.name="fetch"][arguments.0.value="null"]',
        message: 'fetch(null) détecté - vérifiez la construction de l\'URL'
      }
    ],

    // ✅ NOUVELLE RÈGLE : Interdire les tokens en clair dans les URLs
    'no-url-tokens': 'error',
    
    // Règles pour les promesses
    'no-async-promise-executor': 'error',
    'prefer-promise-reject-errors': 'error',
    
    // Désactiver les règles trop strictes pour notre projet
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/no-unused-vars': 'warn',
    'no-console': 'off', // Nous utilisons beaucoup console.log pour le debug
  },
  
  // Configuration spécifique pour les fichiers TypeScript
  overrides: [
    {
      files: ['*.ts', '*.tsx'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        '@typescript-eslint/ban-ts-comment': 'off',
      }
    }
  ]
};
