import fs from 'fs';
import path from 'path';

const targetFile = path.resolve('server/app.js');
const correctQuery = `const queryText = \`
            SELECT id, label, hours, duration, type, category, is_custom, color, working_days
            FROM standard_posts
            ORDER BY label;
        \`;
        const result = await query(queryText);`;

try {
    let content = fs.readFileSync(targetFile, 'utf8');

    // Regex pour trouver l'ancienne requête, qu'elle soit sur une ou plusieurs lignes.
    const incorrectQueryRegex = /const result = await query\((['"`])SELECT \* FROM standard_posts ORDER BY label\1\);/m;
    const incorrectQueryRegex2 = /const queryText = \`([\s\S]*?)COALESCE\(working_days_v2, working_days\)([\s\S]*?)\`;/m

    if (incorrectQueryRegex2.test(content)) {
        content = content.replace(incorrectQueryRegex2, `const queryText = \`$1 working_days $2\`;`);
        fs.writeFileSync(targetFile, content, 'utf8');
        console.log('✅ Requête SQL avec COALESCE corrigée avec succès.');
    } else if (incorrectQueryRegex.test(content)) {
        content = content.replace(incorrectQueryRegex, correctQuery);
        fs.writeFileSync(targetFile, content, 'utf8');
        console.log('✅ Requête SQL "SELECT *" remplacée par la version explicite et correcte.');
    } else {
        console.log('🟡 Avertissement: Aucune requête SQL à corriger n\'a été trouvée. Le code est peut-être déjà à jour.');
    }
} catch (error) {
    console.error('❌ Erreur lors de l\'application du patch SQL :', error);
} 