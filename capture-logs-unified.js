/**
 * 🔄 SYSTÈME DE CAPTURE UNIFIÉ - CORRECTION COMPLÈTE SESSIONS MULTIPLES
 * Assure une session unique pour backend, frontend et browser
 * + MODE DEBUG INTENSIF pour diagnostic complet
 */

// ✅ ÉTAPE 1 : Configuration mode debug intensif
const DEBUG_MODE = {
  enabled: localStorage.getItem('ULTRA_DEBUG_MODE') === 'true',
  level: localStorage.getItem('DEBUG_LEVEL') || 'normal', // normal, verbose, insane
  captureAll: localStorage.getItem('CAPTURE_ALL') === 'true'
};

// ✅ FONCTIONS DEBUG DÉFINIES EN PREMIER (avant unifiedLogger)
const enableUltraDebug = (level = 'insane') => {
  localStorage.setItem('ULTRA_DEBUG_MODE', 'true');
  localStorage.setItem('DEBUG_LEVEL', level);
  localStorage.setItem('CAPTURE_ALL', 'true');
  console.log('🚨 MODE DEBUG ULTRA-INTENSIF ACTIVÉ - NIVEAU:', level);
  console.log('🔧 Redémarrage recommandé pour activation complète');
  location.reload();
};

const disableUltraDebug = () => {
  localStorage.removeItem('ULTRA_DEBUG_MODE');
  localStorage.removeItem('DEBUG_LEVEL'); 
  localStorage.removeItem('CAPTURE_ALL');
  console.log('✅ Mode debug désactivé');
  location.reload();
};

// Exposer globalement immédiatement
window.enableUltraDebug = enableUltraDebug;
window.disableUltraDebug = disableUltraDebug;

// ✅ ÉTAPE 1 : Récupérer la session serveur unique
let unifiedSessionId = null;
let isInitialized = false;

async function initializeUnifiedSession() {
  if (isInitialized) return unifiedSessionId;
  
  try {
    console.log('🔄 [UNIFIED] Initialisation session unique...');
    
    const response = await fetch('/api/debug/current-session');
    if (!response.ok) throw new Error('API non disponible');
    
    const data = await response.json();
    unifiedSessionId = data.sessionId;
    
    // Forcer dans localStorage pour cohérence
    localStorage.setItem('logs_session_id', unifiedSessionId);
    localStorage.setItem('logs_server_start', data.serverStarted);
    
    console.log(`✅ [UNIFIED] Session unique: ${unifiedSessionId.substring(0, 8)}...`);
    isInitialized = true;
    
    return unifiedSessionId;
    
  } catch (error) {
    console.error('❌ [UNIFIED] Erreur initialisation:', error);
    // Fallback sur localStorage
    unifiedSessionId = localStorage.getItem('logs_session_id') || 'fallback-' + Date.now();
    return unifiedSessionId;
  }
}

// ✅ SYSTÈME ANTI-SPAM ADAPTATIF
const logQueue = [];
const recentLogs = new Set();
let lastSend = 0;
let batchTimer = null;

// Configuration anti-spam selon le mode debug
const getThrottleConfig = () => {
  if (DEBUG_MODE.enabled) {
    switch (DEBUG_MODE.level) {
      case 'insane':
        return { 
          batchDelay: 50,     // Envoi ultra-rapide 
          maxPerSecond: 1000, // 1000 logs/seconde
          batchSize: 50,      // Gros batches
          chunkSize: 20,      // Gros chunks
          chunkDelay: 10      // Délai minimal
        };
      case 'verbose':
        return {
          batchDelay: 100,
          maxPerSecond: 500,
          batchSize: 30,
          chunkSize: 15,
          chunkDelay: 50
        };
      default:
        return {
          batchDelay: 200,
          maxPerSecond: 100,
          batchSize: 20,
          chunkSize: 10,
          chunkDelay: 100
        };
    }
  }
  
  // Mode normal (anti-spam standard)
  return {
    batchDelay: 1000,
    maxPerSecond: 10,
    batchSize: 10,
    chunkSize: 3,
    chunkDelay: 300
  };
};

// ✅ ÉTAPE 2 : Logger unifié pour frontend et browser - VERSION DEBUG INTENSIF
async function sendUnifiedLog(source, level, message, data = {}) {
  const sessionId = await initializeUnifiedSession();
  if (!sessionId) return;
  
  const config = getThrottleConfig();
  const now = Date.now();
  
  // Créer le message unique pour déduplication
  const messageKey = `${source}_${level}_${message}`;
  
  // En mode debug intensif, désactiver la déduplication
  if (!DEBUG_MODE.enabled) {
    if (recentLogs.has(messageKey)) {
      return; // Skip doublons en mode normal
    }
    recentLogs.add(messageKey);
    setTimeout(() => recentLogs.delete(messageKey), 5000);
  }
  
  // Vérifier le throttling (sauf en mode debug intensif)
  if (!DEBUG_MODE.enabled) {
    if (now - lastSend < 1000/config.maxPerSecond) {
      return; // Skip si trop rapide
    }
  }
  
  try {
    // ✅ PAYLOAD ENRICHI pour mode debug
    const payload = {
      sessionId,
      level,
      message: `[${source.toUpperCase()}] ${message}`,
      data: DEBUG_MODE.enabled ? {
        ...data,
        // Métadonnées enrichies en mode debug
        debugLevel: DEBUG_MODE.level,
        timestamp: now,
        stack: DEBUG_MODE.level === 'insane' ? new Error().stack : undefined,
        userAgent: navigator.userAgent,
        url: window.location.href,
        memory: performance?.memory ? {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit
        } : undefined,
        timing: performance?.timing ? {
          loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
          domReady: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
        } : undefined
      } : (typeof data === 'object' ? data : { value: data })
    };
    
    // En mode debug intensif, envoyer immédiatement
    if (DEBUG_MODE.enabled && DEBUG_MODE.level === 'insane') {
      await fetch('/api/debug/browser', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });
      
      // Log local pour debug
      console.log(`📤 [${source.toUpperCase()}] ${message}`, data);
      return;
    }
    
    // Mode normal : utiliser la queue
    logQueue.push(payload);
    
    // Limiter la taille de la queue
    if (logQueue.length > config.batchSize) {
      logQueue.splice(0, logQueue.length - config.batchSize);
    }
    
    // Déclencher l'envoi par batch
    if (!batchTimer) {
      batchTimer = setTimeout(processBatch, config.batchDelay);
    }
    
    // Log local pour debug
    console.log(`📤 [${source.toUpperCase()}] ${message}`, data);
    
  } catch (error) {
    // Échec silencieux pour ne pas casser l'application
    console.warn(`❌ [${source.toUpperCase()}] Échec envoi log:`, error.message);
  }
}

// ✅ TRAITEMENT BATCH OPTIMISÉ
async function processBatch() {
  batchTimer = null;
  
  if (logQueue.length === 0) return;
  
  const config = getThrottleConfig();
  const batch = logQueue.splice(0, config.batchSize);
  
  try {
    // Envoyer par chunks pour éviter la surcharge
    for (let i = 0; i < batch.length; i += config.chunkSize) {
      const chunk = batch.slice(i, i + config.chunkSize);
      
      await Promise.all(chunk.map(async (payload) => {
        try {
          await fetch('/api/debug/browser', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
          });
        } catch (error) {
          console.warn('❌ [BATCH] Échec envoi chunk:', error.message);
        }
      }));
      
      // Délai entre chunks (sauf en mode debug)
      if (i + config.chunkSize < batch.length && !DEBUG_MODE.enabled) {
        await new Promise(resolve => setTimeout(resolve, config.chunkDelay));
      }
    }
    
    lastSend = Date.now();
    
  } catch (error) {
    console.error('❌ [BATCH] Erreur traitement batch:', error);
  }
  
  // Si il reste des logs en queue, programmer le prochain batch
  if (logQueue.length > 0) {
    batchTimer = setTimeout(processBatch, config.batchDelay);
  }
}

// ✅ ÉTAPE 3 : Remplacer capture-logs.js existant
if (typeof window !== 'undefined') {
  
  // Empêcher double initialisation
  if (window.__unifiedLogCaptureInitialized) {
    console.warn('⚠️ [UNIFIED] Système déjà initialisé');
  } else {
    window.__unifiedLogCaptureInitialized = true;
    
    console.log('🚀 [UNIFIED] Démarrage capture logs unifiée...');
    if (DEBUG_MODE.enabled) {
      console.log(`🚨 [DEBUG] Mode debug activé - Niveau: ${DEBUG_MODE.level}`);
    }
    
    // Initialiser immédiatement
    initializeUnifiedSession().then(() => {
      console.log('✅ [UNIFIED] Session synchronisée avec serveur');
      
      // Log initial
      sendUnifiedLog('browser', 'info', 'Système de capture unifié activé', {
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date().toISOString(),
        debugMode: DEBUG_MODE
      });
    });
    
    // 🔴 CAPTURE CONSOLE - REMPLACEMENT COMPLET
    const originalConsole = {
      log: console.log,
      info: console.info,
      warn: console.warn,
      error: console.error,
      debug: console.debug
    };
    
    // Détection intelligente source
    function detectSource(args) {
      const message = args.join(' ');
      
      // Logs frontend React/Vite
      if (message.includes('[vite]') || message.includes('hot updated')) {
        return 'frontend';
      }
      
      // Logs application métier
      if (message.includes('TeamCalendar') || message.includes('ApiService') || 
          message.includes('[TEST]') || message.includes('Employee')) {
        return 'frontend';
      }
      
      // Par défaut : browser
      return 'browser';
    }
    
    // ✅ CAPTURE ÉTENDUE en mode debug
    const wrapConsoleMethod = (method, level) => {
      return (...args) => {
        originalConsole[method](...args);
        const source = detectSource(args);
        
        // En mode debug, capturer TOUT avec stack trace
        if (DEBUG_MODE.enabled) {
          const stack = DEBUG_MODE.level === 'insane' ? new Error().stack : undefined;
          sendUnifiedLog(source, level, args.join(' '), { 
            args, 
            method,
            stack: stack?.split('\n').slice(1, 6) // 5 premières lignes de stack
          });
        } else {
          sendUnifiedLog(source, level, args.join(' '), { args });
        }
      };
    };
    
    console.log = wrapConsoleMethod('log', 'info');
    console.info = wrapConsoleMethod('info', 'info');
    console.warn = wrapConsoleMethod('warn', 'warn');
    console.error = wrapConsoleMethod('error', 'error');
    console.debug = wrapConsoleMethod('debug', 'debug');
    
    // ✅ CAPTURE SUPPLÉMENTAIRE en mode debug intensif
    if (DEBUG_MODE.enabled) {
      
      // Capture des appels fetch
      const originalFetch = window.fetch;
      window.fetch = async (...args) => {
        const startTime = performance.now();
        const url = typeof args[0] === 'string' ? args[0] : args[0]?.url;
        
        sendUnifiedLog('browser', 'debug', `🌐 FETCH START: ${url}`, {
          url,
          method: args[1]?.method || 'GET',
          headers: args[1]?.headers
        });
        
        try {
          const response = await originalFetch(...args);
          const endTime = performance.now();
          
          sendUnifiedLog('browser', 'debug', `✅ FETCH SUCCESS: ${url} (${Math.round(endTime - startTime)}ms)`, {
            url,
            status: response.status,
            statusText: response.statusText,
            duration: Math.round(endTime - startTime)
          });
          
          return response;
        } catch (error) {
          const endTime = performance.now();
          
          sendUnifiedLog('browser', 'error', `❌ FETCH ERROR: ${url} (${Math.round(endTime - startTime)}ms)`, {
            url,
            error: error.message,
            duration: Math.round(endTime - startTime)
          });
          
          throw error;
        }
      };
      
      // Capture des mutations DOM
      if (DEBUG_MODE.level === 'insane') {
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            sendUnifiedLog('browser', 'debug', `🔄 DOM MUTATION: ${mutation.type}`, {
              type: mutation.type,
              target: mutation.target?.tagName,
              addedNodes: mutation.addedNodes?.length,
              removedNodes: mutation.removedNodes?.length
            });
          });
        });
        
        observer.observe(document.body, {
          childList: true,
          subtree: true,
          attributes: true,
          attributeOldValue: true
        });
      }
      
      // Capture des événements de performance
      if (window.PerformanceObserver) {
        try {
          const perfObserver = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
              sendUnifiedLog('browser', 'debug', `⚡ PERFORMANCE: ${entry.entryType}`, {
                name: entry.name,
                entryType: entry.entryType,
                startTime: entry.startTime,
                duration: entry.duration
              });
            });
          });
          
          perfObserver.observe({ entryTypes: ['navigation', 'resource', 'measure', 'mark'] });
        } catch (e) {
          console.warn('Performance Observer non supporté');
        }
      }
    }
    
    // 🔴 CAPTURE ERREURS GLOBALES - VERSION ENRICHIE
    window.addEventListener('error', (event) => {
      sendUnifiedLog('browser', 'error', `Erreur globale: ${event.message}`, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        url: window.location.href,
        source: event.target?.tagName || 'unknown'
      });
    });
    
    window.addEventListener('unhandledrejection', (event) => {
      sendUnifiedLog('browser', 'error', `Promise rejetée: ${event.reason}`, {
        reason: event.reason,
        url: window.location.href,
        stack: event.reason?.stack
      });
    });
    
    // 🔴 NAVIGATION EVENTS ENRICHIS
    window.addEventListener('beforeunload', () => {
      sendUnifiedLog('browser', 'info', 'Navigation - Page fermée', {
        duration: performance.now(),
        url: window.location.href
      });
    });
    
    // Capture du focus/blur en mode debug
    if (DEBUG_MODE.enabled) {
      window.addEventListener('focus', () => {
        sendUnifiedLog('browser', 'debug', '👁️ Page focus gained');
      });
      
      window.addEventListener('blur', () => {
        sendUnifiedLog('browser', 'debug', '👁️ Page focus lost');
      });
      
      // Capture des clics en mode insane
      if (DEBUG_MODE.level === 'insane') {
        document.addEventListener('click', (event) => {
          sendUnifiedLog('browser', 'debug', `🖱️ CLICK: ${event.target.tagName}`, {
            tagName: event.target.tagName,
            className: event.target.className,
            id: event.target.id,
            x: event.clientX,
            y: event.clientY
          });
        });
      }
    }
    
    // 🔴 API PUBLIQUE ENRICHIE avec protection renforcée
    const unifiedLoggerAPI = {
      frontend: {
        debug: (msg, data) => sendUnifiedLog('frontend', 'debug', msg, data),
        info: (msg, data) => sendUnifiedLog('frontend', 'info', msg, data),
        warn: (msg, data) => sendUnifiedLog('frontend', 'warn', msg, data),
        error: (msg, data) => sendUnifiedLog('frontend', 'error', msg, data),
      },
      browser: {
        debug: (msg, data) => sendUnifiedLog('browser', 'debug', msg, data),
        info: (msg, data) => sendUnifiedLog('browser', 'info', msg, data),
        warn: (msg, data) => sendUnifiedLog('browser', 'warn', msg, data),
        error: (msg, data) => sendUnifiedLog('browser', 'error', msg, data),
      },
      getSessionId: () => unifiedSessionId,
      reinit: initializeUnifiedSession,
      // ✅ NOUVELLES FONCTIONS DEBUG avec protection
      enableDebug: enableUltraDebug,
      disableDebug: disableUltraDebug,
      getDebugMode: () => {
        try {
          return {
            enabled: DEBUG_MODE.enabled,
            level: DEBUG_MODE.level,
            captureAll: DEBUG_MODE.captureAll
          };
        } catch (error) {
          console.warn('Erreur getDebugMode:', error);
          return { enabled: false, level: 'normal', captureAll: false };
        }
      },
      flushQueue: () => {
        try {
          if (batchTimer) {
            clearTimeout(batchTimer);
            processBatch();
          }
        } catch (error) {
          console.warn('Erreur flushQueue:', error);
        }
      },
      // ✅ Fonction de diagnostic
      status: () => {
        console.log('🔍 [UnifiedLogger] Status:', {
          initialized: isInitialized,
          sessionId: unifiedSessionId,
          debugMode: DEBUG_MODE,
          queueLength: logQueue.length,
          recentLogsCount: recentLogs.size
        });
      }
    };
    
    // S'assurer que l'API est toujours accessible
    window.unifiedLogger = unifiedLoggerAPI;
    
    // Définir également une référence globale de secours
    if (typeof globalThis !== 'undefined') {
      globalThis.unifiedLogger = unifiedLoggerAPI;
    }
    
    const modeText = DEBUG_MODE.enabled ? `MODE DEBUG ${DEBUG_MODE.level.toUpperCase()}` : 'MODE NORMAL';
    console.log(`✅ [UNIFIED] Capture logs complète activée (${modeText})`);
  }
}

// Export pour usage dans les modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { sendUnifiedLog, initializeUnifiedSession };
} 