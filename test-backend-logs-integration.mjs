#!/usr/bin/env node

/**
 * 🧪 TEST INTÉGRATION LOGS BACKEND
 * Vérifier que tous les logs backend sont capturés
 */

import fetch from 'node-fetch';

const API_BASE = 'http://localhost:3001';

async function testBackendLogsIntegration() {
  console.log('🧪 [TEST] Test intégration logs backend...');
  
  try {
    // 1. Test connexion serveur
    console.log('1️⃣ Test connexion serveur...');
    const healthResponse = await fetch(`${API_BASE}/api/health`);
    if (!healthResponse.ok) {
      throw new Error('Serveur non accessible');
    }
    console.log('✅ Serveur accessible');
    
    // 2. Test route de test backend
    console.log('2️⃣ Test route logs backend...');
    const testResponse = await fetch(`${API_BASE}/api/debug/test-backend-logs`);
    if (!testResponse.ok) {
      throw new Error('Route test backend non accessible');
    }
    const testData = await testResponse.json();
    console.log('✅ Logs de test envoyés:', testData.message);
    
    // 3. Attendre un peu pour que les logs soient traités
    console.log('3️⃣ Attente traitement logs...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 4. Récupérer les logs récents
    console.log('4️⃣ Récupération logs récents...');
    const sessionResponse = await fetch(`${API_BASE}/api/debug/current-session`);
    const sessionData = await sessionResponse.json();
    
    const logsResponse = await fetch(`${API_BASE}/api/debug/sessions/${sessionData.sessionId}?mode=chronological&max=50`);
    const logs = await logsResponse.json();
    
    // 5. Analyser les logs backend
    const backendLogs = logs.filter(log => 
      log.source === 'backend' || 
      log.message.includes('[BACKEND]') ||
      log.message.includes('[LOGGER]')
    );
    
    console.log(`✅ ${backendLogs.length} logs backend trouvés`);
    
    // Afficher les logs backend récents
    const recentBackendLogs = backendLogs.slice(-5);
    console.log('📋 Logs backend récents:');
    recentBackendLogs.forEach(log => {
      console.log(`   [${log.ts}] [${log.level}] ${log.message}`);
    });
    
    // 6. Vérifier l'ordre chronologique
    const timestamps = logs.map(log => new Date(log.ts).getTime());
    const isChronological = timestamps.every((ts, i) => i === 0 || ts >= timestamps[i - 1]);
    
    console.log(`✅ Ordre chronologique: ${isChronological ? 'CORRECT' : 'INCORRECT'}`);
    
    console.log('\n🎉 TEST RÉUSSI - Intégration logs backend opérationnelle');
    
  } catch (error) {
    console.error('❌ Test échoué:', error.message);
    process.exit(1);
  }
}

testBackendLogsIntegration();
