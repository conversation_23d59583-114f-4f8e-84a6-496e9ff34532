import { readFileSync, writeFileSync } from 'fs';
import { resolve } from 'path';

const filePath = resolve('./src/teamCalendarApp.ts');

try {
    let content = readFileSync(filePath, 'utf8');
    console.log('🔍 [fix-syntax-errors] Lecture du fichier teamCalendarApp.ts...');
    
    // Corrections des erreurs de syntaxe courantes
    let fixes = 0;
    
    // 1. Corriger les fonctions mal fermées
    console.log('🔧 [fix-syntax-errors] Correction des fonctions mal fermées...');
    
    // Correction 1: Fermer correctement les fonctions qui finissent par un setTimeout
    content = content.replace(
        /}, 200\); \/\/ 200ms au lieu de 300ms pour meilleure réactivité\s*\/\/ ✅ OPTIMISATION : Réorganiser seulement les lignes d'employés sans recréer tout le calendrier\s*this\.reorderEmployeeRowsOnlyOptimized\(\);/g,
        `}, 200); // 200ms au lieu de 300ms pour meilleure réactivité
        
        // ✅ OPTIMISATION : Réorganiser seulement les lignes d'employés sans recréer tout le calendrier
        this.reorderEmployeeRowsOnlyOptimized();
    },`
    );
    
    // Correction 2: Ajouter des fermetures de fonction manquantes
    content = content.replace(
        /this\.reorderEmployeeRowsOnlyOptimized\(\);\s*\},\s*\/\/ ✅ NOUVELLE FONCTION : Réorganiser uniquement les lignes d'employés/g,
        `this.reorderEmployeeRowsOnlyOptimized();
    },

    // ✅ NOUVELLE FONCTION : Réorganiser uniquement les lignes d'employés`
    );
    
    // Correction 3: Corriger les lignes qui ne sont pas dans un contexte de fonction
    content = content.replace(
        /\s*\/\/ ✅ OPTIMISATION : Réorganiser seulement les lignes d'employés sans recréer tout le calendrier\s*this\.reorderEmployeeRowsOnlyOptimized\(\);\s*\},\s*\/\/ ✅ NOUVELLE FONCTION/g,
        `
    },

    // ✅ NOUVELLE FONCTION`
    );
    
    // Correction 4: Réparer les structures de fonctions incomplètes
    content = content.replace(
        /this\.reorderEmployeeRowsOnlyOptimized\(\);\s*\},\s*\/\/ ✅ NOUVELLE FONCTION : Réorganiser uniquement les lignes d'employés/g,
        `this.reorderEmployeeRowsOnlyOptimized();
    },

    // ✅ NOUVELLE FONCTION : Réorganiser uniquement les lignes d'employés`
    );
    
    // Correction 5: Corriger les déclarations de fonction
    content = content.replace(
        /reorderEmployeeRowsOnly: function\(\) \{/g,
        `reorderEmployeeRowsOnly: function() {`
    );
    
    fixes++;
    console.log(`✅ [fix-syntax-errors] ${fixes} corrections appliquées`);
    
    // Écrire le fichier corrigé
    writeFileSync(filePath, content, 'utf8');
    console.log('✅ [fix-syntax-errors] Fichier corrigé avec succès');
    
} catch (error) {
    console.error('❌ [fix-syntax-errors] Erreur:', error);
    process.exit(1);
} 