// ========================================
// SCRIPT DE VALIDATION DE LA CORRECTION MODALE
// ========================================

console.log('🔍 [VALIDATION] Script de validation de la correction modale chargé');

// Fonction pour valider que la correction principale a été appliquée
function validateMainFix() {
    console.log('🔍 [VALIDATION] Validation de la correction principale...');
    
    // Vérifier que ModalManager existe et a setupAssignmentContextModal
    if (!window.TeamCalendarApp) {
        console.error('❌ [VALIDATION] TeamCalendarApp non disponible');
        return false;
    }
    
    if (!window.TeamCalendarApp.ModalManager) {
        console.error('❌ [VALIDATION] ModalManager non disponible');
        return false;
    }
    
    if (typeof window.TeamCalendarApp.ModalManager.setupAssignmentContextModal !== 'function') {
        console.error('❌ [VALIDATION] setupAssignmentContextModal non disponible dans ModalManager');
        return false;
    }
    
    console.log('✅ [VALIDATION] ModalManager.setupAssignmentContextModal disponible');
    
    // Vérifier que ModalManager a été initialisé avec app
    if (!window.TeamCalendarApp.ModalManager.app) {
        console.error('❌ [VALIDATION] ModalManager.app non initialisé');
        return false;
    }
    
    console.log('✅ [VALIDATION] ModalManager.app initialisé');
    
    // Vérifier que handleAssignmentContextConfirm existe dans l'app
    if (typeof window.TeamCalendarApp.handleAssignmentContextConfirm !== 'function') {
        console.error('❌ [VALIDATION] handleAssignmentContextConfirm non disponible dans TeamCalendarApp');
        return false;
    }
    
    console.log('✅ [VALIDATION] handleAssignmentContextConfirm disponible');
    
    return true;
}

// Fonction pour valider que les éléments DOM existent
function validateDOMElements() {
    console.log('🔍 [VALIDATION] Validation des éléments DOM...');
    
    const elements = {
        modal: document.getElementById('assignment-context-modal'),
        closeBtn: document.getElementById('assignment-context-close'),
        cancelBtn: document.getElementById('assignment-context-cancel'),
        confirmBtn: document.getElementById('assignment-context-confirm'),
        radioButtons: document.querySelectorAll('input[name="assignment-type"]')
    };
    
    const results = {
        modal: !!elements.modal,
        closeBtn: !!elements.closeBtn,
        cancelBtn: !!elements.cancelBtn,
        confirmBtn: !!elements.confirmBtn,
        radioButtons: elements.radioButtons.length > 0
    };
    
    console.log('📋 [VALIDATION] Éléments DOM:', results);
    
    const allPresent = Object.values(results).every(Boolean);
    
    if (allPresent) {
        console.log('✅ [VALIDATION] Tous les éléments DOM sont présents');
    } else {
        console.error('❌ [VALIDATION] Éléments DOM manquants');
    }
    
    return allPresent;
}

// Fonction pour valider que les listeners sont attachés
function validateListeners() {
    console.log('🔍 [VALIDATION] Validation des listeners...');
    
    try {
        // Forcer la reconfiguration des listeners
        window.TeamCalendarApp.ModalManager.setupAssignmentContextModal();
        console.log('✅ [VALIDATION] setupAssignmentContextModal exécuté sans erreur');
        return true;
    } catch (error) {
        console.error('❌ [VALIDATION] Erreur lors de setupAssignmentContextModal:', error);
        return false;
    }
}

// Fonction pour effectuer un test complet de validation
function runCompleteValidation() {
    console.log('🚀 [VALIDATION] Lancement de la validation complète...');
    
    const results = {
        mainFix: validateMainFix(),
        domElements: validateDOMElements(),
        listeners: validateListeners()
    };
    
    console.log('📊 [VALIDATION] Résultats de validation:', results);
    
    const allPassed = Object.values(results).every(Boolean);
    
    if (allPassed) {
        console.log('🎉 [VALIDATION] Toutes les validations sont passées avec succès !');
        console.log('✅ [VALIDATION] La correction de la modale devrait maintenant fonctionner');
        
        // Suggérer un test pratique
        console.log('🧪 [VALIDATION] Pour tester pratiquement:');
        console.log('1. Effectuez un drag & drop d\'un poste vers un employé');
        console.log('2. Vérifiez que la modale s\'ouvre');
        console.log('3. Cliquez sur "Confirmer" et vérifiez les logs');
        console.log('4. Ou utilisez testButtonClicks() pour un test automatique');
        
    } else {
        console.error('❌ [VALIDATION] Certaines validations ont échoué');
        console.log('🔧 [VALIDATION] Actions recommandées:');
        
        if (!results.mainFix) {
            console.log('- Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés');
        }
        if (!results.domElements) {
            console.log('- Vérifier que la modale assignment-context-modal existe dans le DOM');
        }
        if (!results.listeners) {
            console.log('- Vérifier les erreurs dans setupAssignmentContextModal');
        }
    }
    
    return allPassed;
}

// Fonction pour tester la fonctionnalité end-to-end
function testEndToEnd() {
    console.log('🧪 [E2E] Test end-to-end de la modale...');
    
    if (!runCompleteValidation()) {
        console.error('❌ [E2E] Validation échouée, arrêt du test');
        return false;
    }
    
    // Simuler des données de contexte
    window.TeamCalendarApp.currentContextAssignment = {
        postId: 'e0332f5d-23e2-40fa-bf2d-d9271f03100a',
        employeeId: '59f5df3a-33ef-425c-bfa2-adca818cf94f',
        preselectedDay: null
    };
    
    // Ouvrir la modale
    const modal = document.getElementById('assignment-context-modal');
    modal.classList.remove('hidden');
    console.log('✅ [E2E] Modale ouverte');
    
    // Attendre un peu puis tester le bouton confirmer
    setTimeout(() => {
        console.log('🧪 [E2E] Test du bouton confirmer...');
        const confirmBtn = document.getElementById('assignment-context-confirm');
        
        if (confirmBtn) {
            confirmBtn.click();
            console.log('✅ [E2E] Clic sur confirmer effectué - vérifiez les logs');
        } else {
            console.error('❌ [E2E] Bouton confirmer non trouvé');
        }
    }, 1000);
    
    return true;
}

// Exposer les fonctions globalement
window.validateMainFix = validateMainFix;
window.validateDOMElements = validateDOMElements;
window.validateListeners = validateListeners;
window.runCompleteValidation = runCompleteValidation;
window.testEndToEnd = testEndToEnd;

// Auto-validation au chargement
setTimeout(() => {
    console.log('🚀 [VALIDATION] Lancement de la validation automatique...');
    runCompleteValidation();
}, 3000);

console.log('✅ [VALIDATION] Fonctions de validation disponibles:');
console.log('- validateMainFix() : Valider la correction principale');
console.log('- validateDOMElements() : Valider les éléments DOM');
console.log('- validateListeners() : Valider les listeners');
console.log('- runCompleteValidation() : Validation complète');
console.log('- testEndToEnd() : Test end-to-end complet');
