/**
 * 🧠 SYSTÈME DE LOGS INTELLIGENT POUR DRAG & DROP EMPLOYÉS
 * 
 * Ce module trace les déplacements d'employés de manière intelligente :
 * - Session persistante entre refresh du navigateur
 * - Logs groupés pour éviter le spam
 * - Métadonnées contextuelles pour l'IA
 * - Historique des sessions pour analyse
 */

class EmployeeDragLogger {
    constructor() {
        this.sessionId = null;
        this.testId = null;
        this.actions = [];
        this.isInitialized = false;
        this.lastLogTime = 0;
        this.duplicateCounter = {};
        
        // Configuration anti-spam
        this.DUPLICATE_THRESHOLD = 500; // 500ms pour considérer comme doublon
        this.MAX_ACTIONS_PER_SESSION = 100;
    }

    // Initialiser le système de logging
    init() {
        if (this.isInitialized) return;
        
        // Générer ou récupérer les IDs de session
        this.sessionId = this._getOrCreateSessionId();
        this.testId = this._generateTestId();
        
        // Récupérer l'historique des sessions précédentes
        this._loadSessionHistory();
        
        console.log(`🧠 [DRAG-LOGGER] Session initialisée: ${this.sessionId}`);
        console.log(`🧪 [DRAG-LOGGER] Test ID: ${this.testId}`);
        
        this.isInitialized = true;
        
        // Log d'initialisation pour l'IA
        this._logAction('SESSION_START', {
            sessionId: this.sessionId,
            testId: this.testId,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            browserRefresh: this._detectBrowserRefresh(),
            url: window.location.href
        });
        
        // Nettoyage périodique des anciens logs
        this._scheduleCleanup();
    }
    
    // Générer ou récupérer l'ID de session (persiste entre refresh)
    _getOrCreateSessionId() {
        let sessionId = sessionStorage.getItem('dragLogger_sessionId');
        if (!sessionId) {
            sessionId = 'drag-session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            sessionStorage.setItem('dragLogger_sessionId', sessionId);
        }
        return sessionId;
    }
    
    // Générer un ID de test unique pour chaque initialisation
    _generateTestId() {
        return 'test-' + Date.now() + '-' + Math.random().toString(36).substr(2, 6);
    }
    
    // Détecter si le navigateur a été refreshé
    _detectBrowserRefresh() {
        const refreshMarker = sessionStorage.getItem('dragLogger_refreshMarker');
        const wasRefreshed = refreshMarker === 'true';
        sessionStorage.setItem('dragLogger_refreshMarker', 'true');
        return wasRefreshed;
    }
    
    // Charger l'historique des sessions
    _loadSessionHistory() {
        try {
            const history = localStorage.getItem('dragLogger_sessionHistory');
            if (history) {
                const parsedHistory = JSON.parse(history);
                console.log(`📚 [DRAG-LOGGER] Historique chargé: ${parsedHistory.length} sessions précédentes`);
                
                // Afficher un résumé des sessions récentes
                const recentSessions = parsedHistory.slice(-3);
                recentSessions.forEach(session => {
                    console.log(`📊 [DRAG-LOGGER] Session ${session.sessionId.split('-').pop()}: ${session.totalActions} actions, ${session.conflicts} conflits`);
                });
            }
        } catch (error) {
            console.warn('⚠️ [DRAG-LOGGER] Erreur lors du chargement de l\'historique:', error);
        }
    }
    
    // Logger une action avec anti-spam et métadonnées
    _logAction(actionType, data) {
        const now = Date.now();
        const actionKey = `${actionType}_${JSON.stringify(data)}`;
        
        // Anti-spam : éviter les doublons récents
        if (this.duplicateCounter[actionKey] && 
            (now - this.duplicateCounter[actionKey]) < this.DUPLICATE_THRESHOLD) {
            return; // Ignorer ce doublon
        }
        
        this.duplicateCounter[actionKey] = now;
        
        const logEntry = {
            sessionId: this.sessionId,
            testId: this.testId,
            actionType,
            timestamp: new Date().toISOString(),
            timestampMs: now,
            data,
            id: 'action-' + now + '-' + Math.random().toString(36).substr(2, 4),
            sequenceNumber: this.actions.length + 1
        };
        
        this.actions.push(logEntry);
        
        // Sauvegarder dans localStorage pour persistance
        this._saveActionToStorage(logEntry);
        
        // Log pour l'IA avec format spécial (condensé pour lisibilité)
        console.log(`🤖 [AI-DRAG-LOG] ${actionType}:`, {
            session: this.sessionId.split('-').pop(),
            test: this.testId.split('-').pop(),
            seq: logEntry.sequenceNumber,
            ...this._condenseDataForAI(data)
        });
        
        // ✅ TEMPORAIREMENT DÉSACTIVÉ : Intégration backend (pour éviter spam)
        // this._sendToBackendLogger(actionType, logEntry).catch(error => {
        //     // Échec silencieux pour ne pas perturber l'application
        // });
        
        this.lastLogTime = now;
    }
    
    // Condenser les données pour l'affichage IA (éviter le spam) - CORRIGÉ SÉRIALISATION
    _condenseDataForAI(data) {
        const condensed = {};
        
        // Condenser les employés avec sérialisation sécurisée
        if (data.employee) {
            condensed.emp = {
                name: data.employee.name || 'Inconnu',
                id: (data.employee.id || '').substring(0, 8)
            };
        }
        
        // Condenser les ordres d'employés avec noms explicites
        if (data.employeeOrder && Array.isArray(data.employeeOrder)) {
            condensed.order = data.employeeOrder.slice(0, 3).map(emp => {
                if (typeof emp === 'string') return emp.split(' ')[0];
                if (emp && emp.name) return emp.name.split(' ')[0];
                return 'Inconnu';
            });
            if (data.employeeOrder.length > 3) {
                condensed.order.push(`+${data.employeeOrder.length - 3} autres`);
            }
        }
        
        // Condenser les nouveaux ordres avec noms explicites
        if (data.newOrder && Array.isArray(data.newOrder)) {
            condensed.newOrder = data.newOrder.slice(0, 3).map(name => {
                if (typeof name === 'string') return name.split(' ')[0];
                return 'Inconnu';
            });
            if (data.newOrder.length > 3) {
                condensed.newOrder.push(`+${data.newOrder.length - 3} autres`);
            }
        }
        
        // Garder les données importantes avec sérialisation sécurisée
        ['oldIndex', 'newIndex', 'success', 'method', 'duration', 'conflicts', 'error'].forEach(key => {
            if (data[key] !== undefined) {
                // Sérialiser les objets complexes pour éviter [object Object]
                if (typeof data[key] === 'object' && data[key] !== null) {
                    try {
                        condensed[key] = JSON.stringify(data[key]);
                    } catch {
                        condensed[key] = `[Object ${Object.keys(data[key] || {}).length} keys]`;
                    }
                } else {
                    condensed[key] = data[key];
                }
            }
        });
        
        return condensed;
    }
    
    // Sauvegarder l'action dans le localStorage
    _saveActionToStorage(logEntry) {
        try {
            const storageKey = `dragLogger_${this.sessionId}`;
            const existingActions = JSON.parse(localStorage.getItem(storageKey) || '[]');
            existingActions.push(logEntry);
            
            // Limiter le nombre d'actions par session
            if (existingActions.length > this.MAX_ACTIONS_PER_SESSION) {
                existingActions.splice(0, existingActions.length - this.MAX_ACTIONS_PER_SESSION);
            }
            
            localStorage.setItem(storageKey, JSON.stringify(existingActions));
        } catch (error) {
            console.warn('⚠️ [DRAG-LOGGER] Erreur sauvegarde localStorage:', error);
        }
    }
    
    // Programmer un nettoyage périodique
    _scheduleCleanup() {
        // Nettoyer les anciens logs toutes les 5 minutes
        setInterval(() => {
            this._cleanupOldLogs();
        }, 5 * 60 * 1000);
        
        // Nettoyage initial après 30 secondes
        setTimeout(() => {
            this._cleanupOldLogs();
        }, 30000);
    }
    
    // Nettoyer les anciens logs (plus de 24h)
    _cleanupOldLogs() {
        try {
            const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
            const keysToRemove = [];
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('dragLogger_drag-session-')) {
                    const sessionTimestamp = parseInt(key.split('-')[2]);
                    if (sessionTimestamp && sessionTimestamp < oneDayAgo) {
                        keysToRemove.push(key);
                    }
                }
            }
            
            keysToRemove.forEach(key => localStorage.removeItem(key));
            
            if (keysToRemove.length > 0) {
                console.log(`🧹 [DRAG-LOGGER] ${keysToRemove.length} sessions anciennes nettoyées`);
            }
        } catch (error) {
            console.warn('⚠️ [DRAG-LOGGER] Erreur nettoyage:', error);
        }
    }
    
    // ✅ NOUVEAU : Envoyer les logs vers le système backend unifié
    async _sendToBackendLogger(actionType, logEntry) {
        try {
            // Vérifier si le système unifié est disponible
            const unifiedLogger = window.unifiedLogger;
            if (!unifiedLogger || typeof unifiedLogger.frontend !== 'object') {
                return; // Système non disponible
            }
            
            // Préparer le message pour le backend
            const message = `[DRAG-SYSTEM] ${actionType}`;
            const data = {
                sessionId: this.sessionId,
                testId: this.testId,
                actionType,
                sequenceNumber: logEntry.sequenceNumber,
                timestamp: logEntry.timestamp,
                data: logEntry.data,
                condensedData: this._condenseDataForAI(logEntry.data)
            };
            
            // Envoyer via le système unifié (niveau adapté selon le type d'action)
            const logLevel = this._getLogLevelForAction(actionType);
            await unifiedLogger.frontend[logLevel](message, data);
            
        } catch (error) {
            // Échec silencieux - ne pas perturber l'application
        }
    }
    
    // Déterminer le niveau de log approprié selon le type d'action
    _getLogLevelForAction(actionType) {
        const levelMap = {
            'SESSION_START': 'info',
            'DRAG_START': 'debug',
            'DRAG_END': 'info',
            'ORDER_SAVE': 'info',
            'CONFLICT_DETECTED': 'warn',
            'DIAGNOSTIC': 'debug'
        };
        return levelMap[actionType] || 'debug';
    }

    // === MÉTHODES PUBLIQUES DE LOGGING ===
    
    // Logger le début d'un drag
    logDragStart(employeeData, context = {}) {
        this._logAction('DRAG_START', {
            employee: {
                id: employeeData.id,
                name: employeeData.name,
                currentIndex: context.currentIndex
            },
            employeeOrder: context.employeeOrder || [],
            trigger: context.trigger || 'unknown',
            totalEmployees: context.totalEmployees || 0
        });
    }
    
    // Logger la fin d'un drag avec le résultat
    logDragEnd(result, context = {}) {
        this._logAction('DRAG_END', {
            success: result.success,
            employee: result.employee,
            oldIndex: result.oldIndex,
            newIndex: result.newIndex,
            newOrder: result.newOrder,
            conflicts: result.conflicts || [],
            duration: context.duration || 0,
            actualChange: result.oldIndex !== result.newIndex
        });
    }
    
    // Logger une sauvegarde d'ordre
    logOrderSave(saveResult, context = {}) {
        this._logAction('ORDER_SAVE', {
            success: saveResult.success,
            method: saveResult.method, // 'api' ou 'localStorage'
            employeeCount: context.employeeCount,
            orderChanged: context.orderChanged,
            error: saveResult.error || null,
            duration: context.duration || 0
        });
    }
    
    // Logger un conflit détecté
    logConflict(conflictType, details) {
        this._logAction('CONFLICT_DETECTED', {
            type: conflictType,
            details,
            timestamp: new Date().toISOString(),
            severity: this._getConflictSeverity(conflictType)
        });
    }
    
    // Logger un événement de diagnostic
    logDiagnostic(diagnosticType, data) {
        this._logAction('DIAGNOSTIC', {
            type: diagnosticType,
            data,
            timestamp: new Date().toISOString()
        });
    }
    
    // Déterminer la sévérité d'un conflit
    _getConflictSeverity(conflictType) {
        const severityMap = {
            'CONTAINER_NOT_FOUND': 'HIGH',
            'SORTABLE_UNAVAILABLE': 'MEDIUM',
            'INVALID_DROP_TARGET': 'LOW',
            'DATA_TRANSFER_ERROR': 'MEDIUM',
            'EMPLOYEE_NOT_FOUND': 'HIGH'
        };
        return severityMap[conflictType] || 'LOW';
    }
    
    // === MÉTHODES D'ANALYSE ===
    
    // Générer un rapport complet pour l'IA
    generateAIReport() {
        const report = {
            sessionId: this.sessionId,
            testId: this.testId,
            totalActions: this.actions.length,
            sessionDuration: this._calculateSessionDuration(),
            actionsSummary: this._summarizeActions(),
            conflictsDetected: this._countConflicts(),
            dragOperations: this._analyzeDragOperations(),
            saveOperations: this._analyzeSaveOperations(),
            recommendations: this._generateRecommendations(),
            timeline: this._generateTimeline()
        };
        
        console.log('🤖 [AI-REPORT] Rapport complet de session drag & drop:', report);
        return report;
    }
    
    // Analyser les opérations de drag
    _analyzeDragOperations() {
        const dragStarts = this.actions.filter(a => a.actionType === 'DRAG_START');
        const dragEnds = this.actions.filter(a => a.actionType === 'DRAG_END');
        const successfulDrags = dragEnds.filter(a => a.data.success);
        
        return {
            totalDragStarts: dragStarts.length,
            totalDragEnds: dragEnds.length,
            successfulDrags: successfulDrags.length,
            abandonedDrags: dragStarts.length - dragEnds.length,
            averageDuration: this._calculateAverageDragDuration(dragEnds)
        };
    }
    
    // Analyser les opérations de sauvegarde
    _analyzeSaveOperations() {
        const saves = this.actions.filter(a => a.actionType === 'ORDER_SAVE');
        const successfulSaves = saves.filter(a => a.data.success);
        
        return {
            totalSaves: saves.length,
            successfulSaves: successfulSaves.length,
            failedSaves: saves.length - successfulSaves.length,
            methods: this._groupSavesByMethod(saves)
        };
    }
    
    // Calculer la durée moyenne des drags
    _calculateAverageDragDuration(dragEnds) {
        if (dragEnds.length === 0) return 0;
        const durations = dragEnds.map(a => a.data.duration || 0);
        return durations.reduce((sum, d) => sum + d, 0) / durations.length;
    }
    
    // Grouper les sauvegardes par méthode
    _groupSavesByMethod(saves) {
        const methods = {};
        saves.forEach(save => {
            const method = save.data.method || 'unknown';
            methods[method] = (methods[method] || 0) + 1;
        });
        return methods;
    }
    
    // Générer une timeline condensée
    _generateTimeline() {
        return this.actions.slice(-10).map(action => ({
            time: new Date(action.timestamp).toLocaleTimeString(),
            action: action.actionType,
            summary: this._getActionSummary(action)
        }));
    }
    
    // Résumer une action pour la timeline
    _getActionSummary(action) {
        switch (action.actionType) {
            case 'DRAG_START':
                return `Début drag ${action.data.employee?.name}`;
            case 'DRAG_END':
                return `Fin drag ${action.data.success ? 'réussi' : 'échoué'}`;
            case 'ORDER_SAVE':
                return `Sauvegarde ${action.data.success ? 'réussie' : 'échouée'} (${action.data.method})`;
            case 'CONFLICT_DETECTED':
                return `Conflit: ${action.data.type}`;
            default:
                return action.actionType;
        }
    }
    
    // Résumer les actions pour l'IA
    _summarizeActions() {
        const summary = {};
        this.actions.forEach(action => {
            summary[action.actionType] = (summary[action.actionType] || 0) + 1;
        });
        return summary;
    }
    
    // Calculer la durée de la session
    _calculateSessionDuration() {
        if (this.actions.length < 2) return 0;
        const start = new Date(this.actions[0].timestamp);
        const end = new Date(this.actions[this.actions.length - 1].timestamp);
        return end.getTime() - start.getTime();
    }
    
    // Compter les conflits
    _countConflicts() {
        return this.actions.filter(action => action.actionType === 'CONFLICT_DETECTED').length;
    }
    
    // Générer des recommandations pour l'IA
    _generateRecommendations() {
        const recommendations = [];
        const dragOps = this._analyzeDragOperations();
        const saveOps = this._analyzeSaveOperations();
        const conflicts = this._countConflicts();
        
        if (dragOps.abandonedDrags > 0) {
            recommendations.push({
                type: 'INCOMPLETE_DRAGS',
                message: `${dragOps.abandonedDrags} drags non terminés détectés`,
                severity: 'MEDIUM',
                suggestion: 'Vérifier les event listeners dragend'
            });
        }
        
        if (conflicts > 0) {
            recommendations.push({
                type: 'CONFLICTS',
                message: `${conflicts} conflits détectés`,
                severity: 'HIGH',
                suggestion: 'Analyser les types de conflits et corriger les causes'
            });
        }
        
        if (saveOps.failedSaves > 0) {
            recommendations.push({
                type: 'SAVE_FAILURES',
                message: `${saveOps.failedSaves} sauvegardes échouées`,
                severity: 'HIGH',
                suggestion: 'Vérifier la connectivité API et les fallbacks localStorage'
            });
        }
        
        if (dragOps.totalDragStarts > 20) {
            recommendations.push({
                type: 'HIGH_ACTIVITY',
                message: 'Session très active détectée',
                severity: 'INFO',
                suggestion: 'Tests intensifs en cours, surveiller les performances'
            });
        }
        
        return recommendations;
    }
    
    // Exporter les données de session pour analyse externe
    exportSessionData() {
        return {
            sessionInfo: {
                sessionId: this.sessionId,
                testId: this.testId,
                startTime: this.actions[0]?.timestamp,
                endTime: this.actions[this.actions.length - 1]?.timestamp,
                duration: this._calculateSessionDuration()
            },
            actions: this.actions,
            analysis: {
                dragOperations: this._analyzeDragOperations(),
                saveOperations: this._analyzeSaveOperations(),
                conflicts: this.actions.filter(a => a.actionType === 'CONFLICT_DETECTED'),
                recommendations: this._generateRecommendations()
            }
        };
    }

    // Méthode pour envoyer des logs au serveur
    async _sendToLogsServer(actionType, data) {
        try {
            // Obtenir la session actuelle
            const sessionResponse = await fetch('/api/debug/current-session');
            if (!sessionResponse.ok) return;
            
            const sessionData = await sessionResponse.json();
            const sessionId = sessionData.sessionId;
            
            // Formater le message pour la page /logs
            const message = `[DRAG-SYSTEM] ${actionType}: ${JSON.stringify(data)}`;
            
            // Envoyer directement au serveur de logs
            const response = await fetch('/api/debug/browser', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    sessionId,
                    level: actionType.includes('ERROR') ? 'error' : 'info',
                    message,
                    data: {
                        actionType,
                        dragData: data,
                        source: 'DRAG-SYSTEM',
                        priority: 90
                    }
                })
            });
            
            if (!response.ok) {
                console.error('[DRAG-LOGGER] Erreur envoi serveur:', response.status);
            } else {
                console.log(`[DRAG-LOGGER] ✅ Log envoyé au serveur: ${actionType}`);
            }
        } catch (error) {
            console.error('[DRAG-LOGGER] Erreur serveur:', error);
        }
    }
}

// Instance globale du logger
window.employeeDragLogger = new EmployeeDragLogger();

// Export pour utilisation en module
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EmployeeDragLogger;
}

export default EmployeeDragLogger;