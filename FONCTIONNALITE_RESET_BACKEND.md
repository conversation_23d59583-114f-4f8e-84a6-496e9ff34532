# 🧹 Fonctionnalité de Reset Backend

## 📋 Description

Cette fonctionnalité permet d'exécuter une purge complète de la base de données directement depuis l'interface utilisateur, en cliquant sur le bouton "Réinitialiser toutes les données" dans les paramètres.

## ✅ Corrections Apportées

### 1. **Configuration du Premier Jour de la Semaine**
- **Configuration** : Le script de purge configure `weekStartsOn: "sunday"` et `weekStartDay: 0`
- **Choix** : Dimanche comme premier jour de la semaine selon les préférences utilisateur
- **Impact** : Calendrier commence par Dimanche comme souhaité

### 2. **Route API Backend**
- **Nouvelle route** : `POST /api/database/purge`
- **Fonctionnalité** : Exécute le script de purge complet côté serveur
- **Réponse** : Retourne un résumé détaillé de la purge

### 3. **Interface Utilisateur Améliorée**
- **Bouton intelligent** : Affiche l'état de progression
- **Sans confirmation** : Exécution directe comme demandé
- **Messages informatifs** : Toast de succès/erreur détaillés
- **Nettoyage complet** : localStorage + base de données

## 🚀 Utilisation

### Via l'Interface
1. Ouvrir les paramètres de l'application
2. Cliquer sur "Réinitialiser toutes les données"
3. Le bouton affiche "Purge en cours..." pendant l'exécution
4. Message de succès et rechargement automatique

### Via les Scripts
```bash
# Purge manuelle locale
npm run db:purge

# Test de l'API de purge
npm run db:purge:test
```

## 🔧 Fonctionnement Technique

### 1. **Côté Frontend** (`resetData()`)
```javascript
// Appel API backend
const response = await fetch('/api/database/purge', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' }
});

// Nettoyage localStorage
localStorage.removeItem('teamCalendarState');
localStorage.removeItem('teamCalendarAppSettings');
localStorage.removeItem('workingDaysDebugLogs');
localStorage.removeItem('modificationHistory');

// Rechargement
location.reload();
```

### 2. **Côté Backend** (Route API)
```javascript
app.post('/api/database/purge', async (req, res) => {
    const { purgeDatabaseData } = await import('../scripts/database-purge.cjs');
    const summary = await purgeDatabaseData();
    res.json({ success: true, summary });
});
```

### 3. **Script de Purge** (Corrigé)
- Supprime tous les quarts
- Supprime les attributions régulières  
- Supprime les congés
- Nettoie les doublons d'employés
- Conserve les 5 employés de base
- Recrée les postes standards
- **✅ Configure `weekStartsOn: "sunday"`** (Dimanche premier jour)

## 📊 Données Conservées

### Employés de Base (5)
- Jean Dupont
- Marie Martin
- Pierre Durand
- Sophie Leblanc
- Lucas Bernard

### Postes Standards (5)
- Poste Matin (08:00-16:00)
- Poste Soir (16:00-00:00)
- Poste Nuit (00:00-08:00)
- Poste WE1 (00:00-12:00)
- Poste WE2 (12:00-24:00)

### Paramètres d'Application
- `weekStartsOn: "sunday"` ✅ (Dimanche premier jour)
- `weekStartDay: 0` ✅ (Dimanche = 0)
- `viewMode: "week"`
- `currentWeekOffset: 0`
- `appVersion: "1.0.0"`

## 🛡️ Sécurité

- **Exécution sans confirmation** : Comme demandé par l'utilisateur
- **Gestion d'erreurs robuste** : Messages d'erreur détaillés
- **Rollback d'interface** : Restaure le bouton en cas d'échec
- **Logs détaillés** : Traçabilité complète dans la console

## 🧪 Tests

### Test de l'API
```bash
npm run db:purge:test
```

### Vérification Manuel
1. Créer quelques quarts/attributions
2. Cliquer sur "Réinitialiser toutes les données"
3. Vérifier que seules les données de base restent
4. Confirmer que `weekStartsOn` est configuré sur "sunday"

## 📝 Logs de Purge

Chaque purge génère un rapport JSON :
```json
{
  "timestamp": "2025-01-19T...",
  "initial": { "shifts": 50, "employees": 10 },
  "final": { "shifts": 0, "employees": 5 },
  "deleted": { "shifts": 50, "employees": 5 }
}
```

## ✅ Résultat Final

Le bouton "Réinitialiser toutes les données" dans les paramètres exécute maintenant :

1. ✅ **Purge backend complète** via l'API
2. ✅ **Configuration premier jour** (Dimanche comme souhaité)
3. ✅ **Nettoyage localStorage complet**
4. ✅ **Exécution sans confirmation** comme demandé
5. ✅ **Interface utilisateur informative**
6. ✅ **Gestion d'erreurs robuste** 