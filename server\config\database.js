import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

// Configuration de la connexion PostgreSQL
const config = {
  host: process.env.DB_HOST || '*************',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'glive_db',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'SebbZ12342323!!', // Mot de passe en dur pour simplifier
  max: 20, // Maximum number of clients in the pool
  connectionTimeoutMillis: 0,
  idleTimeoutMillis: 10000,
  ssl: false // Changez en true si SSL est requis
};

// Créer le pool de connexions
const pool = new Pool(config);

// Gestion des erreurs de connexion
pool.on('error', (err, client) => {
  console.error('Erreur de connexion PostgreSQL:', err);
});

// Test de connexion
pool.on('connect', () => {
  console.log('🔗 Connexion PostgreSQL établie avec succès');
});

// Fonction utilitaire pour exécuter des requêtes
const query = async (text, params) => {
  const start = Date.now();
  try {
    console.log(`[query] Executing query: ${text}`, { params });
    const res = await pool.query(text, params);
    const duration = Date.now() - start;
    console.log(`✅ Requête exécutée en ${duration}ms:`, text.substring(0, 50), { params });
    return res;
  } catch (error) {
    console.error('❌ Erreur lors de l\'exécution de la requête:', text, params, error);
    throw error;
  }
};

// Fonction pour obtenir un client
const getClient = async () => {
  try {
    const client = await pool.connect();
    return client;
  } catch (error) {
    console.error('❌ Erreur lors de l\'obtention du client:', error);
    throw error;
  }
};

// Fonction pour fermer toutes les connexions
const closePool = async () => {
  try {
    await pool.end();
    console.log('🔒 Pool de connexions fermé');
  } catch (error) {
    console.error('❌ Erreur lors de la fermeture du pool:', error);
  }
};

export {
  pool,
  query,
  getClient,
  closePool,
  config
};
