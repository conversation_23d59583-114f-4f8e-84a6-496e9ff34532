#!/usr/bin/env node

/**
 * 🔄 DIAGNOSTIC ET TEST SYSTÈME TEMPS RÉEL CORRIGÉ
 * Test de la robustesse du nouveau système SSE avec reconnexion
 */

const API_BASE = 'http://localhost:3001';
const FRONTEND_BASE = 'http://localhost:5173';

console.log('🔄 [REALTIME-DIAGNOSTIC] Test système temps réel corrigé');
console.log('='.repeat(60));

// Test 1: Vérifier que les serveurs sont actifs
async function testServersAlive() {
  console.log('\n🏃 [TEST-1] Vérification serveurs...');
  
  try {
    // Backend
    const backendResponse = await fetch(`${API_BASE}/health`);
    console.log(`   🟢 Backend: ${backendResponse.ok ? 'ACTIF' : 'INACTIF'}`);
    
    // Frontend (vérifier que Vite répond)
    const frontendResponse = await fetch(`${FRONTEND_BASE}/`);
    console.log(`   🟣 Frontend: ${frontendResponse.ok ? 'ACTIF' : 'INACTIF'}`);
    
    return backendResponse.ok && frontendResponse.ok;
  } catch (error) {
    console.log('   ❌ Erreur connexion serveurs:', error.message);
    return false;
  }
}

// Test 2: Vérifier l'endpoint SSE
async function testSSEEndpoint() {
  console.log('\n🔄 [TEST-2] Test endpoint SSE robuste...');
  
  try {
    // Récupérer session courante
    const sessionResponse = await fetch(`${API_BASE}/api/debug/current-session`);
    if (!sessionResponse.ok) {
      throw new Error('Session API indisponible');
    }
    const sessionData = await sessionResponse.json();
    const sessionId = sessionData.sessionId;
    
    console.log(`   🔑 Session test: ${sessionId.substring(0, 8)}...`);
    
    // Test connexion SSE basique (sans EventSource car on est en Node.js)
    const sseResponse = await fetch(`${API_BASE}/api/debug/stream/${sessionId}`);
    console.log(`   📡 Headers SSE:`, {
      contentType: sseResponse.headers.get('content-type'),
      cacheControl: sseResponse.headers.get('cache-control'),
      connection: sseResponse.headers.get('connection')
    });
    
    console.log(`   ✅ Endpoint SSE répond: ${sseResponse.ok ? 'OK' : 'ERREUR'}`);
    
    // Fermer la connexion
    if (sseResponse.body) {
      sseResponse.body.cancel();
    }
    
    return sseResponse.ok;
  } catch (error) {
    console.log('   ❌ Erreur test SSE:', error.message);
    return false;
  }
}

// Test 3: Générer des logs de test pour voir si le temps réel fonctionne
async function generateTestLogs() {
  console.log('\n📝 [TEST-3] Génération logs de test...');
  
  try {
    const sessionResponse = await fetch(`${API_BASE}/api/debug/current-session`);
    const sessionData = await sessionResponse.json();
    const sessionId = sessionData.sessionId;
    
    const testLogs = [
      { level: 'info', message: '[TEST-REALTIME] Log de test temps réel #1' },
      { level: 'warn', message: '[TEST-REALTIME] Log de test temps réel #2' },
      { level: 'error', message: '[TEST-REALTIME] Log de test temps réel #3' },
      { level: 'debug', message: '[TEST-REALTIME] Log de test temps réel #4' },
    ];
    
    for (const [index, testLog] of testLogs.entries()) {
      const response = await fetch(`${API_BASE}/api/debug/browser`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId,
          level: testLog.level,
          message: testLog.message,
          data: { 
            test: true, 
            timestamp: new Date().toISOString(),
            sequence: index + 1
          }
        })
      });
      
      if (response.ok) {
        console.log(`   ✅ Log ${index + 1}/4 envoyé (${testLog.level})`);
      } else {
        console.log(`   ❌ Échec log ${index + 1}/4`);
      }
      
      // Délai entre les logs pour voir l'effet temps réel
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    return true;
  } catch (error) {
    console.log('   ❌ Erreur génération logs:', error.message);
    return false;
  }
}

// Test 4: Vérifier la récupération des logs
async function testLogRetrieval() {
  console.log('\n📋 [TEST-4] Test récupération logs...');
  
  try {
    const sessionResponse = await fetch(`${API_BASE}/api/debug/current-session`);
    const sessionData = await sessionResponse.json();
    const sessionId = sessionData.sessionId;
    
    const logsResponse = await fetch(`${API_BASE}/api/debug/sessions/${sessionId}?mode=chronological&max=50`);
    if (!logsResponse.ok) {
      throw new Error('Erreur récupération logs');
    }
    
    const logs = await logsResponse.json();
    console.log(`   📊 Logs récupérés: ${logs.length}`);
    
    // Vérifier si nos logs de test sont présents
    const testLogsFound = logs.filter(log => log.message.includes('[TEST-REALTIME]'));
    console.log(`   🧪 Logs de test trouvés: ${testLogsFound.length}/4`);
    
    // Répartition par source
    const sources = {
      backend: logs.filter(l => l.source === 'backend').length,
      frontend: logs.filter(l => l.source === 'frontend').length,
      browser: logs.filter(l => l.source === 'browser').length
    };
    console.log(`   📊 Répartition: Backend=${sources.backend}, Frontend=${sources.frontend}, Browser=${sources.browser}`);
    
    return logs.length > 0;
  } catch (error) {
    console.log('   ❌ Erreur récupération logs:', error.message);
    return false;
  }
}

// Exécution des tests
async function runDiagnostic() {
  let score = 0;
  const totalTests = 4;
  
  if (await testServersAlive()) score++;
  if (await testSSEEndpoint()) score++;
  if (await generateTestLogs()) score++;
  if (await testLogRetrieval()) score++;
  
  console.log('\n' + '='.repeat(60));
  console.log(`🏆 [RÉSULTAT] Score: ${score}/${totalTests} tests réussis`);
  
  if (score === totalTests) {
    console.log('✅ [SUCCESS] Système temps réel complètement fonctionnel !');
    console.log('');
    console.log('📋 [INSTRUCTIONS] Pour tester l\'interface:');
    console.log('   1. Ouvrir http://localhost:5173/logs');
    console.log('   2. Sélectionner la session courante');
    console.log('   3. ✅ Cocher "Temps réel"');
    console.log('   4. Observer le point vert (connecté)');
    console.log('   5. Générer des actions dans l\'app principale');
    console.log('   6. Les logs apparaissent automatiquement !');
    console.log('');
    console.log('🔄 [NOUVELLES FONCTIONNALITÉS]:');
    console.log('   • Reconnexion automatique en cas de perte');
    console.log('   • Heartbeat toutes les 15s pour maintenir connexion');
    console.log('   • Timeout protection SQL (10s max)');
    console.log('   • Basculement auto vers snapshot si échec');
    console.log('   • Gestion robuste des erreurs réseau');
  } else {
    console.log('⚠️ [WARNING] Système partiellement fonctionnel');
    console.log('   Vérifier les logs serveur pour plus de détails');
  }
  
  console.log('='.repeat(60));
}

// Point d'entrée
runDiagnostic().catch(error => {
  console.error('💥 [FATAL] Erreur diagnostic:', error);
  process.exit(1);
}); 