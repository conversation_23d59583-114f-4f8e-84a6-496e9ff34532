// 🔄 Service Worker pour cache offline et performances
// Version: 1.0.0

const CACHE_NAME = 'team-calendar-v1';
const STATIC_CACHE = 'static-v1';
const API_CACHE = 'api-v1';
const FONTS_CACHE = 'fonts-v1';

// ✅ RESSOURCES À METTRE EN CACHE
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/src/main.tsx',
  '/src/index.css',
  '/src/App.tsx',
  '/favicon.svg',
  // Ajout dynamique des assets Vite
];

const FONT_URLS = [
  'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans:wght@400;500;700;900&display=swap',
  'https://fonts.googleapis.com/icon?family=Material+Icons+Outlined',
  // URLs des fonts qui seront détectées dynamiquement
];

const API_ENDPOINTS = [
  '/api/employees',
  '/api/posts',
  '/api/shifts',
  '/api/regular-assignments',
  '/api/health'
];

// ✅ INSTALLATION DU SERVICE WORKER
self.addEventListener('install', (event) => {
  console.log('🔧 [SW] Installation du Service Worker');
  
  event.waitUntil(
    Promise.all([
      // Cache des ressources statiques
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('📦 [SW] Cache des ressources statiques');
        return cache.addAll(STATIC_ASSETS);
      }),
      
      // Cache des fonts
      caches.open(FONTS_CACHE).then((cache) => {
        console.log('🔤 [SW] Cache des fonts');
        return cache.addAll(FONT_URLS);
      })
    ]).then(() => {
      console.log('✅ [SW] Installation terminée');
      // Forcer l'activation immédiate
      return self.skipWaiting();
    })
  );
});

// ✅ ACTIVATION DU SERVICE WORKER
self.addEventListener('activate', (event) => {
  console.log('🚀 [SW] Activation du Service Worker');
  
  event.waitUntil(
    Promise.all([
      // Nettoyer les anciens caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && 
                cacheName !== API_CACHE && 
                cacheName !== FONTS_CACHE) {
              console.log(`🗑️ [SW] Suppression ancien cache: ${cacheName}`);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      
      // Prendre le contrôle immédiatement
      self.clients.claim()
    ]).then(() => {
      console.log('✅ [SW] Activation terminée');
    })
  );
});

// ✅ INTERCEPTION DES REQUÊTES
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Ignorer les requêtes non-HTTP
  if (!request.url.startsWith('http')) {
    return;
  }
  
  // Stratégie selon le type de ressource
  if (url.pathname.startsWith('/api/')) {
    // API : Network First avec fallback cache
    event.respondWith(handleApiRequest(request));
  } else if (isFontRequest(request)) {
    // Fonts : Cache First avec fallback network
    event.respondWith(handleFontRequest(request));
  } else if (isStaticAsset(request)) {
    // Assets statiques : Cache First
    event.respondWith(handleStaticRequest(request));
  } else {
    // Autres : Network First
    event.respondWith(handleNetworkFirst(request));
  }
});

// ✅ GESTION DES REQUÊTES API
async function handleApiRequest(request) {
  const cache = await caches.open(API_CACHE);
  
  try {
    // Essayer le réseau d'abord
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Mettre en cache si succès
      cache.put(request, networkResponse.clone());
      console.log(`📡 [SW] API mise en cache: ${request.url}`);
    }
    
    return networkResponse;
  } catch (error) {
    // Fallback vers le cache
    console.log(`📱 [SW] Mode offline, utilisation du cache: ${request.url}`);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Réponse d'erreur offline
    return new Response(
      JSON.stringify({ 
        error: 'Offline', 
        message: 'Données non disponibles hors ligne' 
      }),
      { 
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// ✅ GESTION DES FONTS
async function handleFontRequest(request) {
  const cache = await caches.open(FONTS_CACHE);
  
  // Cache First pour les fonts
  const cachedResponse = await cache.match(request);
  if (cachedResponse) {
    console.log(`🔤 [SW] Font depuis cache: ${request.url}`);
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
      console.log(`🔤 [SW] Font mise en cache: ${request.url}`);
    }
    return networkResponse;
  } catch (error) {
    console.log(`❌ [SW] Font non disponible: ${request.url}`);
    return new Response('', { status: 404 });
  }
}

// ✅ GESTION DES ASSETS STATIQUES
async function handleStaticRequest(request) {
  const cache = await caches.open(STATIC_CACHE);
  
  // Cache First pour les assets
  const cachedResponse = await cache.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    // Fallback vers page offline si disponible
    return cache.match('/offline.html') || new Response('Offline', { status: 503 });
  }
}

// ✅ NETWORK FIRST GÉNÉRIQUE
async function handleNetworkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    return networkResponse;
  } catch (error) {
    const cache = await caches.open(STATIC_CACHE);
    const cachedResponse = await cache.match(request);
    return cachedResponse || new Response('Offline', { status: 503 });
  }
}

// ✅ UTILITAIRES DE DÉTECTION
function isFontRequest(request) {
  return request.url.includes('fonts.googleapis.com') ||
         request.url.includes('fonts.gstatic.com') ||
         request.destination === 'font';
}

function isStaticAsset(request) {
  const url = new URL(request.url);
  return url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf)$/);
}

// ✅ GESTION DES MESSAGES
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_CACHE_STATUS') {
    getCacheStatus().then((status) => {
      event.ports[0].postMessage(status);
    });
  }
});

// ✅ STATUT DU CACHE
async function getCacheStatus() {
  const cacheNames = await caches.keys();
  const status = {};
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    status[cacheName] = keys.length;
  }
  
  return status;
}

console.log('🔧 [SW] Service Worker chargé et prêt');
