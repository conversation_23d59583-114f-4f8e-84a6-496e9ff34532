{"name": "tempapp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext ts,tsx,js,jsx --format json --output-file eslint-report.json", "lint:fix": "eslint . --ext ts,tsx,js,jsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,css,md}\"", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage --reporter=verbose --reporter=json --outputFile=test-results.xml", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "preview": "vite preview", "analyze": "node scripts/analyze-bundle.js", "analyze:no-build": "node scripts/analyze-bundle.js --no-build", "server": "node server/app.js", "server:stable": "node server/app-simple.js", "server:safe": "node scripts/start-safe.js", "cleanup:legacy": "node scripts/cleanup-legacy-data.js", "emergency:cleanup": "node scripts/emergency-cleanup.js", "db:help": "node scripts/db-tools.cjs", "db:start": "node scripts/start-postgres.cjs", "db:purge": "node scripts/database-purge.cjs", "db:purge:auto": "node scripts/database-purge-auto.cjs", "db:purge:test": "node scripts/test-database-purge-api.js", "db:check": "node scripts/check-database.cjs", "db:test": "node scripts/test-db.cjs", "db:logs:migrate": "node migrate-logs-remote.mjs", "db:logs:test": "node -e \"import('./migrate-logs-remote.mjs').then(()=>console.log('Migration logs terminée'))\"", "env:load": "node load-env.mjs", "start:clean": "node clean-logs-start.mjs", "logs:clean": "node clean-session-storage.mjs", "logs:test": "node test-sessions-fixes.mjs", "logs:fix": "node fix-logs-display.mjs", "logs:fix-sources": "node fix-source-detection.mjs", "logs:test-realtime": "node fix-realtime-logs.mjs", "migrate": "node database/run-migration.js", "db:fix": "node database/add-missing-columns.js", "db:verify": "node database/verify.js", "db:state": "node scripts/verify-database-state.js", "backup": "node scripts/backup-data.js", "restore": "node scripts/restore-data.js", "db:status": "node database/migrate.js status", "test:integration": "node test-integration.js", "test:frontend-backend": "node test-frontend-backend-integration.js", "test:connection": "node test-connection.mjs", "test:grace-period": "node test-connection-grace-period.js", "test:server-stability": "node test-server-stability.js", "dev:full": "concurrently \"npm run server\" \"npm run dev\"", "dev:system": "node start-full-system.mjs", "dev:stable": "node start-stable-server.js", "dev:clean": "node kill-all-node.mjs && timeout /t 3 >nul 2>&1 && node start-full-system.mjs", "start": "npm run server", "dev:ssr": "vite --config ssr-renderer/vite.ssr.config.ts", "build:ssr": "vite build --config ssr-renderer/vite.ssr.config.ts", "docs:generate": "node scripts/generate-docs.js", "docs:serve": "npx http-server docs -p 8080", "quality:check": "npm run lint && npm run type-check && npm run format:check", "quality:fix": "npm run lint:fix && npm run format", "ci:test": "npm run quality:check && npm run test:coverage", "ci:build": "npm run quality:check && npm run build", "optimize:bundle": "npm run build && npm run analyze", "health:check": "node scripts/health-check.js"}, "dependencies": {"@tanstack/react-query": "^5.80.7", "@tanstack/react-query-devtools": "^5.80.7", "@types/react-router-dom": "^5.3.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "framer-motion": "^12.16.0", "helmet": "^8.1.0", "lucide-react": "^0.514.0", "node-fetch": "^3.3.2", "pg": "^8.16.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "sortablejs": "^1.15.6", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@lhci/cli": "^0.12.0", "@playwright/test": "^1.40.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^22.15.30", "@types/pg": "^8.15.4", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^1.0.0", "@vitest/ui": "^1.0.0", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "eslint": "^9.25.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^23.0.0", "playwright": "^1.40.0", "postcss": "^8.5.4", "prettier": "^3.0.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^1.0.0"}}