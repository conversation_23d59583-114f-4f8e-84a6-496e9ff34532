-- Migration : Ajout des colonnes pour les indicateurs de réintégration
-- Date : 2025-01-19
-- Description : Ajoute les colonnes nécessaires pour la persistance des remplacements ponctuels

-- Ajouter les colonnes de base pour les remplacements
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS is_punctual BOOLEAN DEFAULT FALSE;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS is_replacement BOOLEAN DEFAULT FALSE;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS original_assignment_id VARCHAR(36);

-- Ajouter les colonnes de propriétés visuelles
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS visual_style VARCHAR(50);
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS color_override VARCHAR(50);
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS shape VARCHAR(50);
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS border_style VARCHAR(50);

-- Ajouter les colonnes de métadonnées
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS is_temporary BOOLEAN DEFAULT FALSE;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS replacement_date DATE;
ALTER TABLE shifts ADD COLUMN IF NOT EXISTS replacement_reason TEXT;

-- Ajouter les index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_shifts_is_replacement ON shifts(is_replacement);
CREATE INDEX IF NOT EXISTS idx_shifts_is_punctual ON shifts(is_punctual);
CREATE INDEX IF NOT EXISTS idx_shifts_is_temporary ON shifts(is_temporary);
CREATE INDEX IF NOT EXISTS idx_shifts_original_assignment_id ON shifts(original_assignment_id);
CREATE INDEX IF NOT EXISTS idx_shifts_replacement_date ON shifts(replacement_date);

-- Mettre à jour les données existantes pour détecter les remplacements ponctuels
UPDATE shifts 
SET 
    is_punctual = true,
    is_replacement = true,
    visual_style = 'orange-replacement',
    color_override = 'orange'
WHERE 
    (shift_data::text LIKE '%isPunctual%' OR 
     shift_data::text LIKE '%isReplacement%' OR
     shift_data::text LIKE '%replacement%' OR
     shift_data::text LIKE '%originalAssignmentId%') AND
    is_regular = false;

-- Essayer de retrouver les originalAssignmentId manquants
UPDATE shifts 
SET original_assignment_id = (
    SELECT ra.id 
    FROM regular_assignments ra 
    WHERE ra.employee_id = shifts.employee_id 
    AND ra.post_id = shifts.post_id 
    AND ra.excluded_dates @> to_jsonb(ARRAY[shifts.date_key])
    LIMIT 1
)
WHERE is_replacement = true 
AND original_assignment_id IS NULL;

-- Afficher le résumé des changements
SELECT 
    'Migration terminée' as status,
    COUNT(*) as total_shifts,
    COUNT(CASE WHEN is_punctual = true THEN 1 END) as punctual_shifts,
    COUNT(CASE WHEN is_replacement = true THEN 1 END) as replacement_shifts,
    COUNT(CASE WHEN is_replacement = true AND original_assignment_id IS NOT NULL THEN 1 END) as complete_replacements
FROM shifts; 