
// ✅ SYSTÈME DE LOGS OPTIMISÉ
const LogLevel = {
    ERROR: 0,
    WARN: 1, 
    INFO: 2,
    DEBUG: 3
};

// Configuration par défaut : moins verbeux
let currentLogLevel = LogLevel.INFO;

// Fonction helper pour les logs conditionnels
function logConditional(level, message, ...args) {
    if (level <= currentLogLevel) {
        const prefix = ['❌', '⚠️', 'ℹ️', '🔍'][level];
        console.log(prefix + ' ' + message, ...args);
    }
}

// Remplacer les console.log verbeux par des logs conditionnels
// Exemple d'utilisation :
// logConditional(LogLevel.DEBUG, '[DRAG&DROP] Détails:', shiftData);
// logConditional(LogLevel.INFO, '[REINTEGRATION] Succès');
// logConditional(LogLevel.ERROR, '[ERROR] Erreur critique:', error);

// Fonction pour changer le niveau depuis la console
window.setLogLevel = function(level) {
    currentLogLevel = level;
    console.log('📊 Niveau de logs défini à:', Object.keys(LogLevel)[level]);
};

// Raccourcis pour la console
window.logQuiet = () => window.setLogLevel(LogLevel.ERROR);
window.logNormal = () => window.setLogLevel(LogLevel.INFO);
window.logVerbose = () => window.setLogLevel(LogLevel.DEBUG);
