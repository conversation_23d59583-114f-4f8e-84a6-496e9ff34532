import fs from 'fs';

console.log('🔧 Correction robuste de la déclaration saveFixedShifts dans teamCalendarApp.ts...');

const file = './src/teamCalendarApp.ts';
let content = fs.readFileSync(file, 'utf8');

// Découper en lignes
const lines = content.split('\n');
let fixed = false;
for (let i = 0; i < lines.length; i++) {
  if (lines[i].includes('return fixedCount;')) {
    // Chercher la prochaine déclaration saveFixedShifts
    let j = i + 1;
    while (j < lines.length && !lines[j].includes('saveFixedShifts: async function')) {
      j++;
    }
    if (j < lines.length) {
      // On a trouvé la déclaration, on remplace tout l'intervalle
      lines.splice(i + 1, j - i - 1, '    },');
      fixed = true;
      break;
    }
  }
}
if (fixed) {
  content = lines.join('\n');
  fs.writeFileSync(file, content);
  console.log('✅ Correction appliquée : saveFixedShifts bien déclarée dans l\'objet.');
} else {
  console.log('❌ Pattern non trouvé, aucune modification.');
}
console.log('🎯 Script terminé. Redémarrez le serveur pour appliquer les changements.'); 