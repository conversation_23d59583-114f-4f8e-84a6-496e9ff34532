import fs from 'fs';
import path from 'path';

const FILE = process.argv[2] || 'src/teamCalendarApp.ts';
const OUTPUT = process.argv[3] || 'DOCS_teamCalendarApp.md';

const content = fs.readFileSync(FILE, 'utf-8');
const lines = content.split('\n');

let docs = `# Documentation extraite de ${FILE}\n\n`;

let inComment = false;
let currentComment = [];
let currentSignature = '';

for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // Détection des commentaires JSDoc ou simples
    if (line.trim().startsWith('/**')) {
        inComment = true;
        currentComment = [line.trim()];
        continue;
    }
    if (inComment) {
        currentComment.push(line.trim());
        if (line.trim().endsWith('*/')) {
            inComment = false;
        }
        continue;
    }

    // Détection des signatures de fonction (méthodes d'objet ou fonctions libres)
    const functionRegex = /^\s*(?:async\s*)?(?:function\s+)?([a-zA-Z0-9_]+)\s*[:=]\s*(?:async\s*)?function\s*\(([^)]*)\)/;
    const methodRegex = /^\s*([a-zA-Z0-9_]+)\s*\(([^)]*)\)\s*\{/;
    const match = line.match(functionRegex) || line.match(methodRegex);

    if (match) {
        const name = match[1];
        const params = match[2];
        currentSignature = `### \`${name}(${params})\`\n`;
        if (currentComment.length > 0) {
            docs += currentComment.join('\n') + '\n';
            currentComment = [];
        }
        docs += currentSignature + '\n';
    }
}

// Sauvegarde du résultat
fs.writeFileSync(OUTPUT, docs, 'utf-8');
console.log(`✅ Documentation extraite dans ${OUTPUT}`); 