-- Migration pour corriger la table standard_posts
-- Ajouter une valeur par défaut pour la colonne duration et la rendre nullable

BEGIN;

-- 1. Vérifier la structure actuelle
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'standard_posts' 
ORDER BY ordinal_position;

-- 2. Modifier la colonne duration pour permettre NULL
ALTER TABLE standard_posts 
ALTER COLUMN duration DROP NOT NULL;

-- 3. Ajouter une valeur par défaut
ALTER TABLE standard_posts 
ALTER COLUMN duration SET DEFAULT '8:00';

-- 4. Mettre à jour les enregistrements existants qui ont duration NULL
UPDATE standard_posts 
SET duration = '8:00' 
WHERE duration IS NULL;

-- 5. Vérifier que la table a les colonnes nécessaires
DO $$
BEGIN
    -- Ajouter la colonne id si elle n'existe pas
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'standard_posts' AND column_name = 'id') THEN
        ALTER TABLE standard_posts ADD COLUMN id UUID PRIMARY KEY DEFAULT gen_random_uuid();
    END IF;
    
    -- Ajouter la colonne label si elle n'existe pas
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'standard_posts' AND column_name = 'label') THEN
        ALTER TABLE standard_posts ADD COLUMN label VARCHAR(255) NOT NULL;
    END IF;
    
    -- Ajouter la colonne hours si elle n'existe pas
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'standard_posts' AND column_name = 'hours') THEN
        ALTER TABLE standard_posts ADD COLUMN hours VARCHAR(50);
    END IF;
    
    -- Ajouter la colonne type si elle n'existe pas
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'standard_posts' AND column_name = 'type') THEN
        ALTER TABLE standard_posts ADD COLUMN type VARCHAR(50) DEFAULT 'sky';
    END IF;
    
    -- Ajouter la colonne category si elle n'existe pas
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'standard_posts' AND column_name = 'category') THEN
        ALTER TABLE standard_posts ADD COLUMN category VARCHAR(100);
    END IF;
    
    -- Ajouter les colonnes de timestamp si elles n'existent pas
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'standard_posts' AND column_name = 'created_at') THEN
        ALTER TABLE standard_posts ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'standard_posts' AND column_name = 'updated_at') THEN
        ALTER TABLE standard_posts ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
END $$;

-- 6. Insérer quelques postes par défaut si la table est vide
INSERT INTO standard_posts (label, hours, duration, type, category, created_at, updated_at)
SELECT 'Réception', '08:00-16:00', '8:00', 'sky', 'Administration', NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM standard_posts WHERE label = 'Réception');

INSERT INTO standard_posts (label, hours, duration, type, category, created_at, updated_at)
SELECT 'Cuisine', '06:00-14:00', '8:00', 'amber', 'Restauration', NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM standard_posts WHERE label = 'Cuisine');

INSERT INTO standard_posts (label, hours, duration, type, category, created_at, updated_at)
SELECT 'Ménage', '09:00-17:00', '8:00', 'emerald', 'Entretien', NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM standard_posts WHERE label = 'Ménage');

INSERT INTO standard_posts (label, hours, duration, type, category, created_at, updated_at)
SELECT 'Sécurité', '22:00-06:00', '8:00', 'red', 'Sécurité', NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM standard_posts WHERE label = 'Sécurité');

INSERT INTO standard_posts (label, hours, duration, type, category, created_at, updated_at)
SELECT 'Maintenance', '08:00-16:00', '8:00', 'purple', 'Technique', NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM standard_posts WHERE label = 'Maintenance');

-- 7. Afficher le résultat final
SELECT 'Migration terminée - Postes disponibles:' as message;
SELECT id, label, hours, duration, type, category FROM standard_posts ORDER BY label;

COMMIT;

-- Vérification finale
SELECT 'Structure finale de la table standard_posts:' as message;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'standard_posts' 
ORDER BY ordinal_position;
