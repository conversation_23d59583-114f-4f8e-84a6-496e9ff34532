/**
 * @fileoverview Export centralisé des composants
 * @description Point d'entrée unique pour tous les composants de l'application
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

// ✅ Composants principaux
export { default as TeamCalendarApp } from '../teamCalendarApp';
export { default as modalFunctionalities } from '../modalFunctionalities';

// ✅ Services
export { apiService } from '../api';
export { logger, uiLogger, dbLogger, apiLogger } from '../logger';

// ✅ Utilitaires (à créer)
export * from './utils/dateUtils';
export * from './utils/dragDropUtils';
export * from './utils/validationUtils';

// ✅ Gestionnaires d'événements
export * from './handlers/employeeHandlers';

// ✅ Composants UI (à développer selon les besoins)
// export * from './ui/modals';
// export * from './ui/calendar';
// export * from './ui/employees';

// ✅ Types et interfaces (à créer)
export * from './types/interfaces';
export * from './types/enums';

/**
 * @description Configuration par défaut des composants
 */
export const ComponentsConfig = {
  version: '1.0.0',
  namespace: 'TeamCalendar',
  debug: process.env.NODE_ENV === 'development'
};

/**
 * @description Initialisation globale des composants
 * @example
 * ```typescript
 * import { initializeComponents } from './components';
 * await initializeComponents();
 * ```
 */
export async function initializeComponents(): Promise<void> {
  console.log('🚀 [Components] Initialisation des composants...');
  
  // Initialisation des services
  // TODO: Ajouter l'initialisation des services si nécessaire
  
  console.log('✅ [Components] Composants initialisés');
}
