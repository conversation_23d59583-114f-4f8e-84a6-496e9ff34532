/**
 * 🚨 VALIDATION CRITIQUE DES CORRECTIONS
 * Script pour valider que toutes les erreurs critiques ont été corrigées
 */

console.log('🚨 [VALIDATION-CRITIQUE] Démarrage de la validation des corrections...');

// ✅ CONFIGURATION DE VALIDATION
const VALIDATION_CONFIG = {
    TIMEOUT: 5000,
    RETRY_COUNT: 3,
    LOG_LEVEL: 'CRITICAL'
};

// ✅ SYSTÈME DE VALIDATION CRITIQUE
const CriticalValidator = {
    errors: [],
    warnings: [],
    successes: [],
    
    logError(message, details = null) {
        const error = { message, details, timestamp: new Date().toISOString() };
        this.errors.push(error);
        console.error(`❌ [VALIDATION-CRITIQUE] ${message}`, details || '');
    },
    
    logWarning(message, details = null) {
        const warning = { message, details, timestamp: new Date().toISOString() };
        this.warnings.push(warning);
        console.warn(`⚠️ [VALIDATION-CRITIQUE] ${message}`, details || '');
    },
    
    logSuccess(message, details = null) {
        const success = { message, details, timestamp: new Date().toISOString() };
        this.successes.push(success);
        console.log(`✅ [VALIDATION-CRITIQUE] ${message}`, details || '');
    },
    
    getSummary() {
        return {
            errors: this.errors.length,
            warnings: this.warnings.length,
            successes: this.successes.length,
            total: this.errors.length + this.warnings.length + this.successes.length,
            criticalIssues: this.errors.length,
            status: this.errors.length === 0 ? 'SUCCESS' : 'FAILED'
        };
    }
};

// ✅ VALIDATION 1 : DISPONIBILITÉ GLOBALE DE TEAMCALENDARAPP
function validateTeamCalendarAppAvailability() {
    CriticalValidator.logSuccess('=== VALIDATION 1: Disponibilité TeamCalendarApp ===');
    
    // Test 1.1: window.teamCalendarApp
    if (window.teamCalendarApp) {
        CriticalValidator.logSuccess('window.teamCalendarApp disponible');
    } else {
        CriticalValidator.logError('window.teamCalendarApp NON DISPONIBLE - CRITIQUE');
        return false;
    }
    
    // Test 1.2: window.TeamCalendarApp
    if (window.TeamCalendarApp) {
        CriticalValidator.logSuccess('window.TeamCalendarApp disponible');
    } else {
        CriticalValidator.logWarning('window.TeamCalendarApp non disponible (non critique)');
    }
    
    // Test 1.3: Fonctions critiques
    const criticalFunctions = [
        'detectDropDateFromPosition',
        'showConfirmationMenu', 
        'handleRegularAssignmentDrop'
    ];
    
    let missingFunctions = 0;
    criticalFunctions.forEach(funcName => {
        if (window.teamCalendarApp[funcName]) {
            CriticalValidator.logSuccess(`Fonction ${funcName} disponible`);
        } else {
            CriticalValidator.logError(`Fonction ${funcName} MANQUANTE - CRITIQUE`);
            missingFunctions++;
        }
    });
    
    return missingFunctions === 0;
}

// ✅ VALIDATION 2 : MODALFUNCTIONALITIES
function validateModalFunctionalities() {
    CriticalValidator.logSuccess('=== VALIDATION 2: modalFunctionalities ===');
    
    // Test 2.1: Disponibilité
    if (window.modalFunctionalities) {
        CriticalValidator.logSuccess('window.modalFunctionalities disponible');
    } else {
        CriticalValidator.logError('window.modalFunctionalities NON DISPONIBLE - CRITIQUE');
        return false;
    }
    
    // Test 2.2: Fonction openAssignmentContextModal
    if (window.modalFunctionalities.openAssignmentContextModal) {
        CriticalValidator.logSuccess('openAssignmentContextModal disponible');
    } else {
        CriticalValidator.logError('openAssignmentContextModal MANQUANTE - CRITIQUE');
        return false;
    }
    
    return true;
}

// ✅ VALIDATION 3 : DONNÉES DE L'APPLICATION
function validateApplicationData() {
    CriticalValidator.logSuccess('=== VALIDATION 3: Données de l\'application ===');
    
    const app = window.teamCalendarApp;
    if (!app || !app.data) {
        CriticalValidator.logError('Données de l\'application NON DISPONIBLES - CRITIQUE');
        return false;
    }
    
    // Test 3.1: Postes
    if (app.data.posts && app.data.posts.length > 0) {
        CriticalValidator.logSuccess(`${app.data.posts.length} poste(s) disponible(s)`);
    } else {
        CriticalValidator.logError('Aucun poste disponible - CRITIQUE pour drag & drop');
        return false;
    }
    
    // Test 3.2: Employés
    if (app.data.employees && app.data.employees.length > 0) {
        CriticalValidator.logSuccess(`${app.data.employees.length} employé(s) disponible(s)`);
    } else {
        CriticalValidator.logError('Aucun employé disponible - CRITIQUE pour drag & drop');
        return false;
    }
    
    // Test 3.3: Attributions régulières
    if (app.data.regularAssignments) {
        CriticalValidator.logSuccess(`${app.data.regularAssignments.length} attribution(s) régulière(s)`);
    } else {
        CriticalValidator.logWarning('Aucune attribution régulière (normal au démarrage)');
    }
    
    return true;
}

// ✅ VALIDATION 4 : ÉLÉMENTS DOM CRITIQUES
function validateDOMElements() {
    CriticalValidator.logSuccess('=== VALIDATION 4: Éléments DOM critiques ===');
    
    // Test 4.1: Cellules de calendrier
    const calendarCells = document.querySelectorAll('[data-date], [data-day-key]');
    if (calendarCells.length > 0) {
        CriticalValidator.logSuccess(`${calendarCells.length} cellule(s) de calendrier trouvée(s)`);
    } else {
        CriticalValidator.logError('Aucune cellule de calendrier trouvée - CRITIQUE pour drag & drop');
        return false;
    }
    
    // Test 4.2: Bouton paramètres
    const settingsBtn = document.getElementById('sidebar-settings-btn');
    if (settingsBtn) {
        CriticalValidator.logSuccess('Bouton paramètres trouvé');
    } else {
        CriticalValidator.logWarning('Bouton paramètres non trouvé (non critique)');
    }
    
    return true;
}

// ✅ VALIDATION 5 : TEST FONCTIONNEL DRAG & DROP
async function validateDragDropFunctionality() {
    CriticalValidator.logSuccess('=== VALIDATION 5: Fonctionnalité drag & drop ===');
    
    const app = window.teamCalendarApp;
    if (!app) {
        CriticalValidator.logError('TeamCalendarApp non disponible pour test fonctionnel');
        return false;
    }
    
    try {
        // Test 5.1: Détection de date
        const testResult = app.detectDropDateFromPosition(100, 100);
        if (testResult !== undefined) {
            CriticalValidator.logSuccess('Fonction detectDropDateFromPosition fonctionne');
        } else {
            CriticalValidator.logWarning('detectDropDateFromPosition retourne undefined (peut être normal)');
        }
        
        // Test 5.2: Données de test pour modal
        if (app.data.posts.length > 0 && app.data.employees.length > 0) {
            const testData = {
                postData: app.data.posts[0],
                employeeId: app.data.employees[0].id,
                employeeName: app.data.employees[0].name,
                dateKey: '2024-01-15',
                position: { x: 100, y: 100 }
            };
            
            // Test 5.3: Ouverture du modal (sans l'afficher réellement)
            if (window.modalFunctionalities.openAssignmentContextModal) {
                CriticalValidator.logSuccess('Modal d\'attribution peut être ouvert');
            }
        }
        
        return true;
        
    } catch (error) {
        CriticalValidator.logError('Erreur lors du test fonctionnel', error.message);
        return false;
    }
}

// ✅ FONCTION PRINCIPALE DE VALIDATION
async function runCriticalValidation() {
    CriticalValidator.logSuccess('🚨 DÉMARRAGE DE LA VALIDATION CRITIQUE');
    
    const validationResults = {
        startTime: new Date(),
        tests: {},
        summary: null
    };
    
    try {
        // Validation 1: TeamCalendarApp
        validationResults.tests.teamCalendarApp = validateTeamCalendarAppAvailability();
        
        // Validation 2: modalFunctionalities
        validationResults.tests.modalFunctionalities = validateModalFunctionalities();
        
        // Validation 3: Données
        validationResults.tests.applicationData = validateApplicationData();
        
        // Validation 4: DOM
        validationResults.tests.domElements = validateDOMElements();
        
        // Validation 5: Fonctionnalité
        validationResults.tests.dragDropFunctionality = await validateDragDropFunctionality();
        
        // Calcul du résumé
        const testResults = Object.values(validationResults.tests);
        const passedTests = testResults.filter(result => result === true).length;
        const totalTests = testResults.length;
        
        validationResults.summary = {
            ...CriticalValidator.getSummary(),
            testsTotal: totalTests,
            testsPassed: passedTests,
            testsFailed: totalTests - passedTests,
            endTime: new Date(),
            duration: new Date() - validationResults.startTime
        };
        
        // Log final
        if (validationResults.summary.criticalIssues === 0) {
            CriticalValidator.logSuccess(`🎉 VALIDATION RÉUSSIE: ${passedTests}/${totalTests} tests passés, 0 erreur critique`);
        } else {
            CriticalValidator.logError(`❌ VALIDATION ÉCHOUÉE: ${validationResults.summary.criticalIssues} erreur(s) critique(s)`);
        }
        
    } catch (error) {
        CriticalValidator.logError('Erreur critique pendant la validation', error.message);
        validationResults.error = error.message;
    }
    
    // Afficher le résumé détaillé
    console.log('📊 [VALIDATION-CRITIQUE] RÉSUMÉ FINAL:', validationResults.summary);
    console.log('📋 [VALIDATION-CRITIQUE] ERREURS:', CriticalValidator.errors);
    console.log('⚠️ [VALIDATION-CRITIQUE] AVERTISSEMENTS:', CriticalValidator.warnings);
    
    return validationResults;
}

// ✅ EXPORT POUR UTILISATION MANUELLE
window.validateCriticalFixes = {
    runCriticalValidation,
    validateTeamCalendarAppAvailability,
    validateModalFunctionalities,
    validateApplicationData,
    validateDOMElements,
    validateDragDropFunctionality,
    CriticalValidator
};

// ✅ AUTO-EXÉCUTION
console.log('✅ [VALIDATION-CRITIQUE] Script chargé. Utilisez runCriticalValidation() pour valider.');

// Attendre que l'application soit chargée puis lancer la validation
setTimeout(async () => {
    if (window.teamCalendarApp) {
        console.log('🚀 [VALIDATION-CRITIQUE] Lancement automatique de la validation...');
        const results = await runCriticalValidation();
        
        // Stocker les résultats pour inspection
        window._lastCriticalValidationResults = results;
        
        // Afficher le statut final
        if (results.summary && results.summary.status === 'SUCCESS') {
            console.log('🎉 [VALIDATION-CRITIQUE] TOUTES LES CORRECTIONS SONT VALIDÉES !');
        } else {
            console.log('❌ [VALIDATION-CRITIQUE] DES ERREURS CRITIQUES PERSISTENT !');
        }
    } else {
        console.log('⏳ [VALIDATION-CRITIQUE] En attente du chargement de TeamCalendarApp...');
    }
}, 4000);
