// 🔧 SCRIPT DE RÉPARATION AUTOMATIQUE
// Coller dans la console du navigateur

console.log('🔧 Début réparation automatique...');

// Nettoyer localStorage
localStorage.removeItem('ULTRA_DEBUG_MODE');
localStorage.removeItem('DEBUG_LEVEL');
localStorage.removeItem('CAPTURE_ALL');
console.log('✅ localStorage nettoyé');

// Vérifier unifiedLogger
if (window.unifiedLogger) {
  console.log('✅ unifiedLogger disponible');
  if (typeof window.unifiedLogger.getDebugMode === 'function') {
    console.log('✅ getDebugMode disponible');
    console.log('📊 Mode actuel:', window.unifiedLogger.getDebugMode());
  } else {
    console.log('❌ getDebugMode manquant');
  }
} else {
  console.log('❌ unifiedLogger non disponible');
}

// Recharger la page
console.log('🔄 Rechargement de la page...');
setTimeout(() => location.reload(), 1000);