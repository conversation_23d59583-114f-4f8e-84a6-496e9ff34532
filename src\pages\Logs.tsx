import React, { useState, useEffect, useRef } from 'react';

// Interface pour un enregistrement de log
interface LogEntry {
  id: number;
  session_id: string;
  timestamp: string;
  source: 'backend' | 'frontend';
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  data: any;
  priority: number;
}

// Couleurs pour les différents niveaux de log
const levelColors = {
  error: 'text-red-400',
  warn: 'text-yellow-400',
  info: 'text-blue-400',
  debug: 'text-gray-500',
  backend: 'bg-gray-700 text-white',
  frontend: 'bg-blue-800 text-white',
};

const LogRow: React.FC<{ log: LogEntry }> = ({ log }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const hasData = log.data && Object.keys(log.data).length > 0;

  return (
    <div className="border-b border-gray-800 hover:bg-gray-800/50 transition-colors duration-150 font-mono text-sm">
      <div className="flex items-center p-2 cursor-pointer" onClick={() => setIsExpanded(!isExpanded)}>
        <span className="w-48 text-gray-500">{new Date(log.timestamp).toLocaleString('fr-FR')}</span>
        <span className={`w-24 px-2 py-0.5 rounded-full text-center text-xs font-semibold ${levelColors[log.source]}`}>{log.source.toUpperCase()}</span>
        <span className={`w-24 text-center font-bold ${levelColors[log.level]}`}>{log.level.toUpperCase()}</span>
        <span className="flex-1 ml-4 text-gray-200 truncate">{log.message}</span>
        {hasData && (
          <span className="ml-2 text-gray-500">{isExpanded ? '▼' : '▶'}</span>
        )}
      </div>
      {isExpanded && hasData && (
        <div className="p-4 bg-gray-900/70 m-2 rounded">
          <pre className="text-xs text-green-400 whitespace-pre-wrap">
            {JSON.stringify(log.data, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

const LogsPage: React.FC = () => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [isCopied, setIsCopied] = useState(false);
  const logsEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Connexion au nouveau flux SSE simplifié
    const eventSource = new EventSource('http://localhost:3001/api/logs/stream');
    console.log('[LogsPage] Connexion au flux /api/logs/stream...');

    eventSource.onmessage = (event) => {
      const newLog = JSON.parse(event.data);
      setLogs(prevLogs => [...prevLogs, newLog]);
    };

    eventSource.onerror = (err) => {
      console.error('[LogsPage] Erreur SSE:', err);
      eventSource.close();
    };

    // Nettoyage à la fermeture du composant
    return () => {
      console.log('[LogsPage] Fermeture de la connexion SSE.');
      eventSource.close();
    };
  }, []); // Se connecte une seule fois au montage

  useEffect(() => {
    // Scroll automatique vers le bas
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [logs]);

  const copyLogsToClipboard = () => {
    const logText = logs.map(log => {
      const timestamp = new Date(log.timestamp).toLocaleString('fr-FR');
      const data = log.data && Object.keys(log.data).length > 0 ? `\n  Data: ${JSON.stringify(log.data, null, 2)}` : '';
      return `[${timestamp}] [${log.source.toUpperCase()}] [${log.level.toUpperCase()}] ${log.message}${data}`;
    }).join('\n');

    navigator.clipboard.writeText(logText).then(() => {
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000); // Réinitialiser après 2 secondes
    });
  };

  return (
    <div className="bg-gray-900 text-white h-screen flex flex-col p-4">
      <div className="flex justify-between items-center mb-4 border-b border-gray-700 pb-2">
        <h1 className="text-2xl font-bold">Journal des événements en temps réel</h1>
        <button
          onClick={copyLogsToClipboard}
          className={`px-4 py-2 rounded-lg text-sm font-semibold transition-colors duration-200 ${
            isCopied 
              ? 'bg-green-500 text-white' 
              : 'bg-blue-600 hover:bg-blue-500 text-white'
          }`}
        >
          {isCopied ? 'Copié !' : 'Copier les logs'}
        </button>
      </div>
      
      <div className="flex-1 overflow-y-auto bg-gray-800/30 rounded-lg shadow-inner">
        {/* Header */}
        <div className="sticky top-0 bg-gray-800 flex items-center p-2 font-bold z-10">
            <span className="w-48">Timestamp</span>
            <span className="w-24 text-center">Source</span>
            <span className="w-24 text-center">Level</span>
            <span className="flex-1 ml-4">Message</span>
        </div>
        
        {/* Log entries */}
        <div className="p-2">
            {logs.map((log, idx) => <LogRow key={`${log.id}-${idx}`} log={log} />)}
            <div ref={logsEndRef} />
        </div>
        {logs.length === 0 && (
            <div className="text-center text-gray-500 p-8">
                En attente de nouveaux logs...
            </div>
        )}
      </div>
    </div>
  );
};

export default LogsPage; 