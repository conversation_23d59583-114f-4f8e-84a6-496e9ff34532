/**
 * @fileoverview Utilitaires de validation
 * @description Fonctions de validation pour l'application
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

/**
 * @description Valide un UUID v4
 * @param str - Chaîne à valider
 * @returns true si c'est un UUID valide
 * @example
 * ```typescript
 * const isValid = isValidUUID('123e4567-e89b-12d3-a456-************'); // true
 * ```
 */
export function isValidUUID(str: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
}

/**
 * @description Génère un UUID v4
 * @returns UUID généré
 * @example
 * ```typescript
 * const id = generateUUID(); // "123e4567-e89b-12d3-a456-************"
 * ```
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * @description Valide un nom d'employé
 * @param name - Nom à valider
 * @returns true si le nom est valide
 */
export function isValidEmployeeName(name: string): boolean {
  if (!name || typeof name !== 'string') {
    return false;
  }
  
  const trimmed = name.trim();
  
  // Vérifications de base
  if (trimmed.length < 2 || trimmed.length > 50) {
    return false;
  }
  
  // Caractères autorisés : lettres, espaces, tirets, apostrophes
  const validNameRegex = /^[a-zA-ZÀ-ÿ\s\-']+$/;
  if (!validNameRegex.test(trimmed)) {
    return false;
  }
  
  // Pas plus de 2 espaces consécutifs
  if (/\s{3,}/.test(trimmed)) {
    return false;
  }
  
  // Ne doit pas commencer ou finir par un espace, tiret ou apostrophe
  if (/^[\s\-']|[\s\-']$/.test(trimmed)) {
    return false;
  }
  
  return true;
}

/**
 * @description Valide une adresse email
 * @param email - Email à valider
 * @returns true si l'email est valide
 */
export function isValidEmail(email: string): boolean {
  if (!email || typeof email !== 'string') {
    return false;
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
}

/**
 * @description Valide un numéro de téléphone
 * @param phone - Numéro à valider
 * @returns true si le numéro est valide
 */
export function isValidPhone(phone: string): boolean {
  if (!phone || typeof phone !== 'string') {
    return false;
  }
  
  // Supprimer tous les caractères non numériques sauf + au début
  const cleaned = phone.replace(/[^\d+]/g, '');
  
  // Vérifier le format
  const phoneRegex = /^(\+33|0)[1-9](\d{8})$/;
  return phoneRegex.test(cleaned);
}

/**
 * @description Valide une date au format YYYY-MM-DD
 * @param dateStr - Date à valider
 * @returns true si la date est valide
 */
export function isValidDateString(dateStr: string): boolean {
  if (!dateStr || typeof dateStr !== 'string') {
    return false;
  }
  
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(dateStr)) {
    return false;
  }
  
  const date = new Date(dateStr);
  return !isNaN(date.getTime()) && date.toISOString().split('T')[0] === dateStr;
}

/**
 * @description Valide un format d'heure HH:MM
 * @param timeStr - Heure à valider
 * @returns true si l'heure est valide
 */
export function isValidTimeString(timeStr: string): boolean {
  if (!timeStr || typeof timeStr !== 'string') {
    return false;
  }
  
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(timeStr);
}

/**
 * @description Valide un format d'heures de poste HH:MM-HH:MM
 * @param hoursStr - Heures à valider
 * @returns true si le format est valide
 */
export function isValidPostHours(hoursStr: string): boolean {
  if (!hoursStr || typeof hoursStr !== 'string') {
    return false;
  }
  
  const hoursRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]-([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  if (!hoursRegex.test(hoursStr)) {
    return false;
  }
  
  const [startTime, endTime] = hoursStr.split('-');
  
  // Vérifier que l'heure de fin est après l'heure de début
  const [startHour, startMin] = startTime.split(':').map(Number);
  const [endHour, endMin] = endTime.split(':').map(Number);
  
  const startMinutes = startHour * 60 + startMin;
  let endMinutes = endHour * 60 + endMin;
  
  // Gérer le passage à minuit (ex: 22:00-06:00)
  if (endMinutes < startMinutes) {
    endMinutes += 24 * 60;
  }
  
  return endMinutes > startMinutes;
}

/**
 * @description Échappe les caractères HTML dangereux
 * @param unsafe - Chaîne non sécurisée
 * @returns Chaîne sécurisée
 */
export function escapeHtml(unsafe: string): string {
  if (!unsafe || typeof unsafe !== 'string') {
    return '';
  }
  
  return unsafe
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

/**
 * @description Valide les jours de travail d'un poste
 * @param workingDays - Tableau des jours (0-6)
 * @returns true si les jours sont valides
 */
export function isValidWorkingDays(workingDays: any): boolean {
  if (!Array.isArray(workingDays)) {
    return false;
  }
  
  if (workingDays.length === 0) {
    return false;
  }
  
  // Vérifier que tous les éléments sont des nombres entre 0 et 6
  return workingDays.every(day => 
    typeof day === 'number' && 
    Number.isInteger(day) && 
    day >= 0 && 
    day <= 6
  );
}

/**
 * @description Valide une couleur hexadécimale
 * @param color - Couleur à valider
 * @returns true si la couleur est valide
 */
export function isValidHexColor(color: string): boolean {
  if (!color || typeof color !== 'string') {
    return false;
  }
  
  const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  return hexRegex.test(color);
}

/**
 * @description Valide les données d'un employé
 * @param employee - Données de l'employé
 * @returns Objet de validation avec erreurs
 */
export function validateEmployee(employee: any): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!employee) {
    errors.push('Données employé manquantes');
    return { isValid: false, errors };
  }
  
  if (!isValidEmployeeName(employee.name)) {
    errors.push('Nom d\'employé invalide');
  }
  
  if (employee.email && !isValidEmail(employee.email)) {
    errors.push('Adresse email invalide');
  }
  
  if (employee.phone && !isValidPhone(employee.phone)) {
    errors.push('Numéro de téléphone invalide');
  }
  
  if (!employee.id || !isValidUUID(employee.id)) {
    errors.push('ID employé invalide');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * @description Valide les données d'un poste
 * @param post - Données du poste
 * @returns Objet de validation avec erreurs
 */
export function validatePost(post: any): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!post) {
    errors.push('Données poste manquantes');
    return { isValid: false, errors };
  }
  
  if (!post.label || post.label.trim().length < 2) {
    errors.push('Nom du poste trop court');
  }
  
  if (!isValidPostHours(post.hours)) {
    errors.push('Format d\'heures invalide');
  }
  
  if (post.workingDays && !isValidWorkingDays(post.workingDays)) {
    errors.push('Jours de travail invalides');
  }
  
  if (!post.id || !isValidUUID(post.id)) {
    errors.push('ID poste invalide');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
