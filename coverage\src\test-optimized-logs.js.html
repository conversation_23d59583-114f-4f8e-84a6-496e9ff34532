
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/test-optimized-logs.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> / <a href="index.html">src</a> test-optimized-logs.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/115</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/115</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" >/**</span></span></span>
<span class="cstat-no" title="statement not covered" > * Script de test pour le système de logs optimisé IA</span>
<span class="cstat-no" title="statement not covered" > * À exécuter dans la console du navigateur pour tester les fonctionnalités</span>
<span class="cstat-no" title="statement not covered" > */</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >console.log('🧪 [TEST-LOGS] Démarrage des tests du système de logs optimisé...');</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// Test 1: Vérifier que le logger est disponible</span>
<span class="cstat-no" title="statement not covered" >if (typeof window !== 'undefined' &amp;&amp; window.logger) {</span>
<span class="cstat-no" title="statement not covered" >  console.log('✅ [TEST-LOGS] Logger global disponible');</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  // Test 2: Logs basiques avec différents niveaux</span>
<span class="cstat-no" title="statement not covered" >  console.log('\n📝 [TEST-LOGS] Test des niveaux de logs...');</span>
<span class="cstat-no" title="statement not covered" >  window.logger.debug('TestSystem', 'Message de debug');</span>
<span class="cstat-no" title="statement not covered" >  window.logger.info('TestSystem', 'Message d\'information');</span>
<span class="cstat-no" title="statement not covered" >  window.logger.warn('TestSystem', 'Message d\'avertissement');</span>
<span class="cstat-no" title="statement not covered" >  window.logger.error('TestSystem', 'Message d\'erreur');</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  // Test 3: Compression des logs répétés</span>
<span class="cstat-no" title="statement not covered" >  console.log('\n🔄 [TEST-LOGS] Test de compression des logs répétés...');</span>
<span class="cstat-no" title="statement not covered" >  for (let i = 0; i &lt; 10; i++) {</span>
<span class="cstat-no" title="statement not covered" >    window.logger.info('TestCompression', 'Message répété pour test de compression', { iteration: i });</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  // Test 4: Logs avec données complexes</span>
<span class="cstat-no" title="statement not covered" >  console.log('\n📊 [TEST-LOGS] Test avec données complexes...');</span>
<span class="cstat-no" title="statement not covered" >  window.logger.info('TestData', 'Log avec données', {</span>
<span class="cstat-no" title="statement not covered" >    user: 'TestUser',</span>
<span class="cstat-no" title="statement not covered" >    action: 'test_logs',</span>
<span class="cstat-no" title="statement not covered" >    timestamp: new Date().toISOString(),</span>
<span class="cstat-no" title="statement not covered" >    metadata: {</span>
<span class="cstat-no" title="statement not covered" >      browser: navigator.userAgent,</span>
<span class="cstat-no" title="statement not covered" >      url: window.location.href</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  });</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >  // Test 5: Package IA</span>
<span class="cstat-no" title="statement not covered" >  setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    console.log('\n🤖 [TEST-LOGS] Test du package IA...');</span>
<span class="cstat-no" title="statement not covered" >    if (window.getAIDebugPackage) {</span>
<span class="cstat-no" title="statement not covered" >      const aiPackage = window.getAIDebugPackage();</span>
<span class="cstat-no" title="statement not covered" >      console.log('📦 Package IA généré:', aiPackage);</span>
<span class="cstat-no" title="statement not covered" >      console.log('📊 Résumé:', aiPackage.summary);</span>
<span class="cstat-no" title="statement not covered" >      console.log('🔍 Analyse des patterns:', aiPackage.patternAnalysis);</span>
<span class="cstat-no" title="statement not covered" >      console.log('📈 Estimation tokens:', aiPackage.tokenEstimate);</span>
<span class="cstat-no" title="statement not covered" >      </span>
<span class="cstat-no" title="statement not covered" >      // Test 6: Export pour IA</span>
<span class="cstat-no" title="statement not covered" >      if (window.exportLogsForAI) {</span>
<span class="cstat-no" title="statement not covered" >        console.log('\n📤 [TEST-LOGS] Test d\'export pour IA...');</span>
<span class="cstat-no" title="statement not covered" >        const exportedLogs = window.exportLogsForAI();</span>
<span class="cstat-no" title="statement not covered" >        console.log('✅ Logs exportés (longueur):', exportedLogs.length, 'caractères');</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        // Afficher un aperçu</span>
<span class="cstat-no" title="statement not covered" >        console.log('👁️ Aperçu des logs exportés:');</span>
<span class="cstat-no" title="statement not covered" >        console.log(exportedLogs.substring(0, 500) + '...');</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >      console.log('❌ [TEST-LOGS] getAIDebugPackage non disponible');</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    // Test 7: Historique</span>
<span class="cstat-no" title="statement not covered" >    console.log('\n📚 [TEST-LOGS] Test de l\'historique...');</span>
<span class="cstat-no" title="statement not covered" >    const history = window.logger.getHistory();</span>
<span class="cstat-no" title="statement not covered" >    console.log('📜 Historique total:', history.length, 'entrées');</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    const errors = window.logger.getHistory(3); // LogLevel.ERROR</span>
<span class="cstat-no" title="statement not covered" >    console.log('❌ Erreurs dans l\'historique:', errors.length);</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    // Test 8: Nettoyage</span>
<span class="cstat-no" title="statement not covered" >    console.log('\n🧹 [TEST-LOGS] Test de nettoyage...');</span>
<span class="cstat-no" title="statement not covered" >    const beforeCount = window.logger.getHistory().length;</span>
<span class="cstat-no" title="statement not covered" >    window.logger.clearHistory();</span>
<span class="cstat-no" title="statement not covered" >    const afterCount = window.logger.getHistory().length;</span>
<span class="cstat-no" title="statement not covered" >    console.log('📊 Avant nettoyage:', beforeCount, '| Après nettoyage:', afterCount);</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    console.log('\n✅ [TEST-LOGS] Tous les tests terminés avec succès !');</span>
<span class="cstat-no" title="statement not covered" >    console.log('💡 [TEST-LOGS] Utilisez window.exportLogsForAI() pour récupérer les logs pour l\'IA');</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >  }, 2000);</span>
<span class="cstat-no" title="statement not covered" >  </span>
<span class="cstat-no" title="statement not covered" >} else {</span>
<span class="cstat-no" title="statement not covered" >  console.log('❌ [TEST-LOGS] Logger non disponible. Vérifiez que logger.ts est bien chargé.');</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// Test des fonctions utilitaires</span>
<span class="cstat-no" title="statement not covered" >console.log('\n🔧 [TEST-LOGS] Test des fonctions utilitaires...');</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// Test du timer de performance</span>
<span class="cstat-no" title="statement not covered" >if (window.logger) {</span>
<span class="cstat-no" title="statement not covered" >  const timer = window.logger.time('TestOperation');</span>
<span class="cstat-no" title="statement not covered" >  setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const duration = timer.end();</span>
<span class="cstat-no" title="statement not covered" >    console.log('⏱️ Durée de l\'opération test:', duration, 'ms');</span>
<span class="cstat-no" title="statement not covered" >  }, 100);</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// Test des loggers spécialisés</span>
<span class="cstat-no" title="statement not covered" >if (window.apiLogger) {</span>
<span class="cstat-no" title="statement not covered" >  console.log('✅ [TEST-LOGS] API Logger disponible');</span>
<span class="cstat-no" title="statement not covered" >  window.apiLogger.info('Test du logger API spécialisé');</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >if (window.uiLogger) {</span>
<span class="cstat-no" title="statement not covered" >  console.log('✅ [TEST-LOGS] UI Logger disponible');</span>
<span class="cstat-no" title="statement not covered" >  window.uiLogger.info('Test du logger UI spécialisé');</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// Instructions pour l'utilisateur</span>
<span class="cstat-no" title="statement not covered" >console.log('\n📋 [TEST-LOGS] Instructions pour récupérer les logs pour l\'IA:');</span>
<span class="cstat-no" title="statement not covered" >console.log('1. Attendez que l\'application génère quelques logs');</span>
<span class="cstat-no" title="statement not covered" >console.log('2. Utilisez: window.exportLogsForAI()');</span>
<span class="cstat-no" title="statement not covered" >console.log('3. Copiez le résultat pour l\'analyse IA');</span>
<span class="cstat-no" title="statement not covered" >console.log('4. Ou utilisez le bouton "Export IA" dans la barre latérale');</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >export {}; </span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-07T17:35:30.510Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    