import fs from 'fs';
import path from 'path';

const targetFile = path.resolve('src/teamCalendarApp.ts');
const searchString = `_pendingSaveWeek?: boolean;`;
const newProperty = `_transactionLock?: any;`;
const replacementString = `${searchString}\n  ${newProperty}`;

try {
    let content = fs.readFileSync(targetFile, 'utf8');
    
    if (content.includes(searchString) && !content.includes(newProperty)) {
        content = content.replace(searchString, replacementString);
        fs.writeFileSync(targetFile, content, 'utf8');
        console.log('✅ Correction finale de l\'interface appliquée avec succès.');
    } else {
        console.log('🟡 Avertissement: Le correctif semble déjà appliqué ou le point d\'insertion est introuvable.');
    }

} catch (error) {
    console.error('❌ Erreur lors de l\'application du patch final de l\'interface :', error);
} 