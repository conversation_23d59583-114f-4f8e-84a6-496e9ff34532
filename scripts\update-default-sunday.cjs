#!/usr/bin/env node

/**
 * Script de mise à jour des paramètres par défaut
 * Change la configuration pour que le dimanche soit le jour par défaut
 * lors de la réinitialisation des données
 */

const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  host: process.env.DB_HOST || '*************',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'team_calendar',
  user: process.env.DB_USER || 'team_calendar_user',
  password: process.env.DB_PASSWORD || 'SecurePassword123!',
  ssl: false,
  connectionTimeoutMillis: 10000,
  idleTimeoutMillis: 30000,
  max: 10
});

async function query(text, params) {
  const client = await pool.connect();
  try {
    const result = await client.query(text, params);
    return result;
  } finally {
    client.release();
  }
}

async function updateDefaultSundaySettings() {
  console.log('🔧 [UPDATE] Mise à jour des paramètres par défaut vers Dimanche...');
  
  try {
    // 1. Vérifier les paramètres actuels
    console.log('📋 Vérification des paramètres actuels...');
    const currentSettings = await query('SELECT * FROM app_settings WHERE setting_key IN ($1, $2)', 
      ['weekStartsOn', 'weekStartDay']);
    
    console.log('Paramètres actuels:');
    currentSettings.rows.forEach(row => {
      console.log(`   ${row.setting_key}: ${row.setting_value} - ${row.description}`);
    });
    
    // 2. Mettre à jour weekStartsOn vers 'sunday'
    console.log('\n🔄 Mise à jour de weekStartsOn vers "sunday"...');
    await query(`
      INSERT INTO app_settings (setting_key, setting_value, description)
      VALUES ($1, $2, $3)
      ON CONFLICT (setting_key) 
      DO UPDATE SET 
        setting_value = $2,
        description = $3,
        updated_at = CURRENT_TIMESTAMP
    `, ['weekStartsOn', '"sunday"', 'Premier jour de la semaine - Dimanche par défaut']);
    
    // 3. Mettre à jour weekStartDay vers 0
    console.log('🔄 Mise à jour de weekStartDay vers 0...');
    await query(`
      INSERT INTO app_settings (setting_key, setting_value, description)
      VALUES ($1, $2, $3)
      ON CONFLICT (setting_key) 
      DO UPDATE SET 
        setting_value = $2,
        description = $3,
        updated_at = CURRENT_TIMESTAMP
    `, ['weekStartDay', '0', 'Premier jour de la semaine (numérique) - 0=Dimanche par défaut']);
    
    // 4. Vérifier les nouveaux paramètres
    console.log('\n✅ Vérification des nouveaux paramètres...');
    const newSettings = await query('SELECT * FROM app_settings WHERE setting_key IN ($1, $2)', 
      ['weekStartsOn', 'weekStartDay']);
    
    console.log('Nouveaux paramètres:');
    newSettings.rows.forEach(row => {
      console.log(`   ${row.setting_key}: ${row.setting_value} - ${row.description}`);
    });
    
    console.log('\n✅ [UPDATE] Mise à jour terminée avec succès !');
    console.log('📝 Note: Ces paramètres seront maintenant utilisés par défaut lors de la réinitialisation');
    
  } catch (error) {
    console.error('❌ [UPDATE] Erreur lors de la mise à jour:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Exécuter le script
if (require.main === module) {
  updateDefaultSundaySettings()
    .then(() => {
      console.log('\n🎯 [UPDATE] Script terminé avec succès');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 [UPDATE] Échec du script:', error.message);
      process.exit(1);
    });
}

module.exports = { updateDefaultSundaySettings }; 