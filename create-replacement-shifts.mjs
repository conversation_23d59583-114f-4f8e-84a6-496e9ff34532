#!/usr/bin/env node

/**
 * 🔧 SCRIPT DE DÉVELOPPEMENT : Créateur de Remplacements Ponctuels Aléatoires
 * 
 * Ce script génère automatiquement des remplacements ponctuels aléatoires
 * pour tester et développer le système de tooltips et de réintégration.
 * 
 * UTILISATION AUTONOME - Ne dépend pas de l'application principale
 */

import fs from 'fs';
import crypto from 'crypto';

console.log('🎯 [REPLACEMENT-CREATOR] Générateur de remplacements ponctuels pour développement');

// Configuration du générateur
const CONFIG = {
    // Nombre de remplacements à créer
    replacementCount: 5,
    
    // Plage de dates (à partir d'aujourd'hui)
    dateRangeStart: 0, // jours à partir d'aujourd'hui
    dateRangeEnd: 14,  // jours à partir d'aujourd'hui
    
    // Employés fictifs pour les tests
    employees: [
        { id: 'emp_001', name: '<PERSON>', avatar: 'https://i.pravatar.cc/150?img=1' },
        { id: 'emp_002', name: '<PERSON>', avatar: 'https://i.pravatar.cc/150?img=2' },
        { id: 'emp_003', name: 'Marie Martin', avatar: 'https://i.pravatar.cc/150?img=3' },
        { id: 'emp_004', name: '<PERSON>pont', avatar: 'https://i.pravatar.cc/150?img=4' },
        { id: 'emp_005', name: 'Claire Bernard', avatar: 'https://i.pravatar.cc/150?img=5' }
    ],
    
    // Postes fictifs
    posts: [
        { id: 'post_matin', label: 'Poste Matin', hours: '08:00-16:00', color: '#3b82f6' },
        { id: 'post_aprem', label: 'Poste Après-midi', hours: '14:00-22:00', color: '#10b981' },
        { id: 'post_nuit', label: 'Poste Nuit', hours: '22:00-06:00', color: '#8b5cf6' },
        { id: 'post_weekend', label: 'Poste Weekend', hours: '09:00-17:00', color: '#f59e0b' }
    ],
    
    // Motifs de remplacement possibles
    replacementReasons: [
        'Congé maladie',
        'Formation',
        'Congé personnel',
        'Urgence familiale',
        'Congé sans solde',
        'Mission temporaire',
        'Remplacement planifié',
        'Absence exceptionnelle'
    ]
};

/**
 * Génère un UUID v4 simple
 */
function generateUUID() {
    return crypto.randomUUID();
}

/**
 * Génère une date aléatoire dans la plage configurée
 */
function generateRandomDate() {
    const today = new Date();
    const randomDays = Math.floor(Math.random() * (CONFIG.dateRangeEnd - CONFIG.dateRangeStart + 1)) + CONFIG.dateRangeStart;
    const targetDate = new Date(today);
    targetDate.setDate(today.getDate() + randomDays);
    return targetDate.toISOString().split('T')[0]; // Format YYYY-MM-DD
}

/**
 * Sélectionne un élément aléatoire d'un tableau
 */
function randomChoice(array) {
    return array[Math.floor(Math.random() * array.length)];
}

/**
 * Génère une attribution régulière fictive
 */
function generateRegularAssignment(employeeId, postId) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30); // Commencé il y a 30 jours
    
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 90); // Se termine dans 90 jours
    
    // Jours de la semaine aléatoires (1-5 pour lundi-vendredi)
    const selectedDays = [];
    const dayCount = Math.floor(Math.random() * 3) + 2; // 2-4 jours par semaine
    while (selectedDays.length < dayCount) {
        const day = Math.floor(Math.random() * 5) + 1; // 1-5 (lundi-vendredi)
        if (!selectedDays.includes(day)) {
            selectedDays.push(day);
        }
    }
    
    return {
        id: generateUUID(),
        employeeId: employeeId,
        postId: postId,
        selectedDays: selectedDays,
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        isActive: true,
        createdAt: new Date().toISOString(),
        createdBy: 'system'
    };
}

/**
 * Génère un remplacement ponctuel
 */
function generateReplacementShift(originalAssignment, replacementEmployeeId) {
    const post = CONFIG.posts.find(p => p.id === originalAssignment.postId);
    const originalEmployee = CONFIG.employees.find(e => e.id === originalAssignment.employeeId);
    const replacementEmployee = CONFIG.employees.find(e => e.id === replacementEmployeeId);
    const reason = randomChoice(CONFIG.replacementReasons);
    
    const createdAt = new Date();
    const dateKey = generateRandomDate();
    
    // Vérifier que la date correspond aux jours sélectionnés de l'attribution
    const targetDate = new Date(dateKey + 'T12:00:00');
    const dayOfWeek = targetDate.getDay();
    
    // Si le jour ne correspond pas, ajuster
    if (!originalAssignment.selectedDays.includes(dayOfWeek)) {
        const validDay = randomChoice(originalAssignment.selectedDays);
        const daysToAdd = (validDay - dayOfWeek + 7) % 7;
        targetDate.setDate(targetDate.getDate() + daysToAdd);
    }
    
    return {
        id: generateUUID(),
        postId: originalAssignment.postId,
        postLabel: post.label,
        hours: post.hours,
        dateKey: targetDate.toISOString().split('T')[0],
        employeeId: replacementEmployeeId,
        
        // Propriétés spécifiques au remplacement ponctuel
        isPunctual: true,
        isReplacement: true,
        originalAssignmentId: originalAssignment.id,
        originalEmployeeId: originalAssignment.employeeId,
        
        // Métadonnées
        createdAt: createdAt.toISOString(),
        createdBy: 'dev-script',
        reason: reason,
        
        // Style visuel (forme de losange orange)
        visualStyle: 'replacement',
        colorOverride: '#f59e0b',
        
        // Informations pour le tooltip
        metadata: {
            originalEmployeeName: originalEmployee.name,
            replacementEmployeeName: replacementEmployee.name,
            postLabel: post.label,
            hours: post.hours,
            reason: reason,
            createdAt: createdAt.toISOString(),
            createdBy: 'Système de développement'
        }
    };
}

/**
 * Génère le dataset complet
 */
function generateReplacementDataset() {
    console.log('📊 [GENERATOR] Génération du dataset de remplacements...');
    
    const dataset = {
        timestamp: new Date().toISOString(),
        generatedBy: 'create-replacement-shifts.mjs',
        config: CONFIG,
        employees: CONFIG.employees,
        posts: CONFIG.posts,
        regularAssignments: [],
        replacementShifts: [],
        summary: {
            employeeCount: CONFIG.employees.length,
            postCount: CONFIG.posts.length,
            replacementCount: 0,
            assignmentCount: 0
        }
    };
    
    // Créer des attributions régulières pour chaque employé
    CONFIG.employees.forEach(employee => {
        // Chaque employé peut avoir 1-2 attributions régulières
        const assignmentCount = Math.floor(Math.random() * 2) + 1;
        
        for (let i = 0; i < assignmentCount; i++) {
            const post = randomChoice(CONFIG.posts);
            const assignment = generateRegularAssignment(employee.id, post.id);
            dataset.regularAssignments.push(assignment);
        }
    });
    
    dataset.summary.assignmentCount = dataset.regularAssignments.length;
    console.log(`✅ [GENERATOR] ${dataset.regularAssignments.length} attributions régulières créées`);
    
    // Créer les remplacements ponctuels
    for (let i = 0; i < CONFIG.replacementCount; i++) {
        const originalAssignment = randomChoice(dataset.regularAssignments);
        
        // Choisir un employé différent pour le remplacement
        const availableEmployees = CONFIG.employees.filter(e => e.id !== originalAssignment.employeeId);
        const replacementEmployee = randomChoice(availableEmployees);
        
        const replacementShift = generateReplacementShift(originalAssignment, replacementEmployee.id);
        dataset.replacementShifts.push(replacementShift);
        
        console.log(`🔄 [GENERATOR] Remplacement ${i + 1}/${CONFIG.replacementCount}: ${replacementEmployee.name} → ${originalAssignment.postId} (${replacementShift.dateKey})`);
    }
    
    dataset.summary.replacementCount = dataset.replacementShifts.length;
    
    return dataset;
}

/**
 * Sauvegarde le dataset
 */
function saveDataset(dataset) {
    const filename = `replacement-dataset-${Date.now()}.json`;
    
    try {
        fs.writeFileSync(filename, JSON.stringify(dataset, null, 2), 'utf8');
        console.log(`💾 [SAVE] Dataset sauvegardé: ${filename}`);
        return filename;
    } catch (error) {
        console.error('❌ [SAVE] Erreur lors de la sauvegarde:', error);
        return null;
    }
}

/**
 * Génère un rapport de synthèse
 */
function generateReport(dataset) {
    console.log('\n📋 [RAPPORT] Synthèse du dataset généré:');
    console.log('=' .repeat(50));
    console.log(`📅 Timestamp: ${dataset.timestamp}`);
    console.log(`👥 Employés: ${dataset.summary.employeeCount}`);
    console.log(`📍 Postes: ${dataset.summary.postCount}`);
    console.log(`📋 Attributions régulières: ${dataset.summary.assignmentCount}`);
    console.log(`🔄 Remplacements ponctuels: ${dataset.summary.replacementCount}`);
    
    console.log('\n👥 Employés:');
    dataset.employees.forEach((emp, index) => {
        console.log(`  ${index + 1}. ${emp.name} (${emp.id})`);
    });
    
    console.log('\n📍 Postes:');
    dataset.posts.forEach((post, index) => {
        console.log(`  ${index + 1}. ${post.label} - ${post.hours}`);
    });
    
    console.log('\n🔄 Remplacements générés:');
    dataset.replacementShifts.forEach((replacement, index) => {
        const originalEmp = dataset.employees.find(e => e.id === replacement.originalEmployeeId);
        const replacementEmp = dataset.employees.find(e => e.id === replacement.employeeId);
        const post = dataset.posts.find(p => p.id === replacement.postId);
        
        console.log(`  ${index + 1}. ${replacement.dateKey}: ${originalEmp.name} → ${replacementEmp.name} (${post.label})`);
        console.log(`     Motif: ${replacement.reason}`);
    });
    
    console.log('\n💡 [UTILISATION] Pour tester dans votre application:');
    console.log('1. Copiez les données du fichier JSON généré');
    console.log('2. Importez-les dans votre base de données de test');
    console.log('3. Les remplacements apparaîtront avec des tooltips');
    console.log('4. Testez le drag & drop vers les employés d\'origine');
    console.log('=' .repeat(50));
}

/**
 * Fonction principale
 */
function main() {
    console.log('🚀 [START] Démarrage du générateur de remplacements ponctuels');
    console.log(`📊 [CONFIG] ${CONFIG.replacementCount} remplacements sur ${CONFIG.dateRangeEnd - CONFIG.dateRangeStart + 1} jours`);
    
    try {
        // Générer le dataset
        const dataset = generateReplacementDataset();
        
        // Sauvegarder
        const filename = saveDataset(dataset);
        
        if (filename) {
            // Générer le rapport
            generateReport(dataset);
            
            console.log(`\n✅ [SUCCESS] Génération terminée avec succès!`);
            console.log(`📄 Fichier créé: ${filename}`);
        } else {
            console.log('❌ [ERROR] Échec de la sauvegarde');
        }
        
    } catch (error) {
        console.error('❌ [ERROR] Erreur lors de la génération:', error);
        process.exit(1);
    }
}

// Exécuter le script
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export { generateReplacementDataset, CONFIG }; 