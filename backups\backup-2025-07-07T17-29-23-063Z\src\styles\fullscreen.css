/* =================================================================
   INTERFACE FULLSCREEN RESPONSIVE - SANS SCROLLBARS
   Conception compartimentée adaptée à toutes les résolutions
   ================================================================= */

/* ===== VARIABLES CSS GLOBALES ===== */
:root {
  --sidebar-width-collapsed: 4rem;
  --sidebar-width-expanded: 16rem;
  --header-height: 4rem;
  --controls-height: 5rem;
  --stats-height: 4rem;
  --transition-duration: 300ms;
  --transition-easing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== RESET ET BASE ===== */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  overflow: hidden; /* Empêche les scrollbars globales */
  height: 100vh;
  width: 100vw;
}

/* ===== CONTENEUR PRINCIPAL FULLSCREEN ===== */
.fullscreen-app {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  display: flex;
  flex-direction: row;
}

/* ===== SIDEBAR FIXE RÉTRACTABLE ===== */
.sidebar-fixed {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: var(--sidebar-width-collapsed);
  z-index: 50;
  transition: width var(--transition-duration) var(--transition-easing);
  overflow: hidden;
}

.sidebar-fixed:hover {
  width: var(--sidebar-width-expanded);
}

.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-brand {
  height: var(--header-height);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 1rem;
}

.sidebar-nav {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0.5rem;
}

.sidebar-link {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem;
  margin-bottom: 0.25rem;
  border-radius: 0.75rem;
  transition: all var(--transition-duration) var(--transition-easing);
  text-decoration: none;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
}

.sidebar-link:hover {
  transform: translateY(-1px);
}

.sidebar-link.active {
  background: rgba(59, 130, 246, 0.1);
  color: rgb(29, 78, 216);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.sidebar-footer {
  flex-shrink: 0;
  padding: 1rem;
  border-top: 1px solid rgba(148, 163, 184, 0.2);
}

/* ===== CONTENU PRINCIPAL ===== */
.main-content {
  margin-left: var(--sidebar-width-collapsed);
  height: 100vh;
  width: calc(100vw - var(--sidebar-width-collapsed));
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: margin-left var(--transition-duration) var(--transition-easing);
}

/* ===== HEADER FIXE ===== */
.header-fixed {
  height: var(--header-height);
  flex-shrink: 0;
  border-bottom: 1px solid rgba(148, 163, 184, 0.3);
  z-index: 40;
}

.header-btn {
  transition: all var(--transition-duration) var(--transition-easing);
}

.header-btn:hover {
  transform: scale(1.05);
}

/* ===== ZONE DE CONTENU ===== */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; /* Important pour flexbox */
}

/* ===== BARRE DE CONTRÔLES ===== */
.controls-bar {
  height: var(--controls-height);
  flex-shrink: 0;
  border-bottom: 1px solid rgba(148, 163, 184, 0.2);
}

.control-btn {
  transition: all var(--transition-duration) var(--transition-easing);
}

.control-btn:hover {
  transform: scale(1.1);
}

.action-btn {
  transition: all var(--transition-duration) var(--transition-easing);
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* ===== GRILLE CALENDRIER ===== */
.calendar-grid {
  flex: 1;
  overflow: hidden;
  min-height: 0;
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.calendar-container {
  height: 100%;
  overflow: hidden;
}

.calendar-content {
  height: 100%;
  display: grid;
  grid-template-columns: 240px 1fr;
  overflow: hidden;
}

/* ===== COLONNE EMPLOYÉS ===== */
.employees-column {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-right: 1px solid rgba(148, 163, 184, 0.4);
}

.employees-header {
  height: 4rem;
  flex-shrink: 0;
  border-bottom: 1px solid rgba(148, 163, 184, 0.4);
}

.employees-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
}

.posts-section {
  flex-shrink: 0;
  border-top: 1px solid rgba(148, 163, 184, 0.2);
}

.posts-header {
  height: 3.5rem;
  flex-shrink: 0;
  border-bottom: 1px solid rgba(148, 163, 184, 0.4);
}

.posts-list {
  max-height: 8rem;
  overflow-y: auto;
  overflow-x: hidden;
}

/* ===== GRILLE JOURS ===== */
.days-grid {
  overflow: hidden;
}

/* ===== BARRE DE STATUTS ===== */
.stats-bar {
  height: var(--stats-height);
  flex-shrink: 0;
  border-top: 1px solid rgba(148, 163, 184, 0.2);
}

/* ===== SCROLLBARS PERSONNALISÉES ===== */
/* Webkit (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.4);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.6);
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.4) rgba(148, 163, 184, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Écrans très petits (mobiles) */
@media (max-width: 640px) {
  :root {
    --sidebar-width-collapsed: 0;
    --sidebar-width-expanded: 16rem;
    --header-height: 3.5rem;
    --controls-height: 4rem;
    --stats-height: 3rem;
  }

  .main-content {
    margin-left: 0;
    width: 100vw;
  }

  .sidebar-fixed {
    transform: translateX(-100%);
    transition: transform var(--transition-duration) var(--transition-easing);
  }

  .sidebar-fixed:hover,
  .sidebar-fixed.mobile-open {
    transform: translateX(0);
  }

  .calendar-content {
    grid-template-columns: 200px 1fr;
  }

  .nav-btn {
    display: none;
  }
}

/* Écrans moyens (tablettes) */
@media (max-width: 1024px) {
  .calendar-content {
    grid-template-columns: 220px 1fr;
  }

  .week-display {
    min-width: 250px;
    font-size: 1rem;
  }
}

/* Écrans très larges */
@media (min-width: 1920px) {
  .calendar-content {
    grid-template-columns: 280px 1fr;
  }
}

/* ===== HAUTEURS ADAPTATIVES ===== */
@media (max-height: 720px) {
  :root {
    --header-height: 3rem;
    --controls-height: 4rem;
    --stats-height: 3rem;
  }

  .posts-list {
    max-height: 6rem;
  }
}

@media (max-height: 600px) {
  :root {
    --header-height: 2.5rem;
    --controls-height: 3rem;
    --stats-height: 2.5rem;
  }

  .posts-list {
    max-height: 4rem;
  }

  .sidebar-nav {
    padding: 0.25rem;
  }

  .sidebar-link {
    padding: 0.5rem;
    margin-bottom: 0.125rem;
  }
}

/* ===== UTILITAIRES POUR DÉBORDEMENT ===== */
.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.overflow-hidden {
  overflow: hidden !important;
}

.overflow-clip {
  overflow: clip !important;
}

/* ===== ANIMATIONS ===== */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-in {
  animation: slideIn var(--transition-duration) var(--transition-easing);
}

.fade-in {
  animation: fadeIn var(--transition-duration) var(--transition-easing);
}

/* ===== ÉTATS DE CHARGEMENT ===== */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* ===== FOCUS ET ACCESSIBILITÉ ===== */
*:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ===== STYLES MODAL PARAMÈTRES ===== */
.tab-btn {
  background: rgba(148, 163, 184, 0.1);
  color: rgb(100, 116, 139);
  border: 1px solid rgba(148, 163, 184, 0.2);
  transition: all var(--transition-duration) var(--transition-easing);
  cursor: pointer;
}

.tab-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  color: rgb(59, 130, 246);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.tab-btn.active {
  background: rgb(59, 130, 246);
  color: white;
  border-color: rgb(59, 130, 246);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.tab-content {
  transition: opacity var(--transition-duration) var(--transition-easing);
}

.tab-content.hidden {
  display: none !important;
}

/* Styles pour les boutons du modal */
#settings-modal button:not(.tab-btn) {
  transition: all var(--transition-duration) var(--transition-easing);
}

#settings-modal button:not(.tab-btn):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Animations pour le modal */
#settings-modal:not(.hidden) {
  animation: fadeIn var(--transition-duration) var(--transition-easing);
}

#settings-modal:not(.hidden) > div {
  animation: slideIn var(--transition-duration) var(--transition-easing);
}

/* Responsive pour le modal */
@media (max-width: 1024px) {
  #settings-modal .w-full.max-w-6xl {
    max-width: 95vw;
  }
}

@media (max-width: 640px) {
  #settings-modal .h-90vh {
    height: 95vh;
  }
  
  #settings-modal .grid.grid-cols-4 {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .sidebar-fixed,
  .header-fixed,
  .controls-bar,
  .stats-bar {
    display: none !important;
  }

  .main-content {
    margin-left: 0 !important;
    width: 100% !important;
  }

  .fullscreen-app {
    position: relative !important;
    height: auto !important;
  }
} 