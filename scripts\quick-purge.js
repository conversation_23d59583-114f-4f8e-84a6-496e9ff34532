import pg from 'pg';
const { Pool } = pg;

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'team_calendar',
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT || 5432,
});

// IDs des employés de base à conserver
const BASE_EMPLOYEES = [
  '26301449-caa1-46e4-a4f8-7aa1ef5e3166', // <PERSON>
  '41ff822b-baab-45b7-9d75-9b23d9ea3451', // <PERSON>
  '6e9837d6-69e4-4dfd-8daa-54717092c7c3', // <PERSON>
  '6194b40f-a5d8-4910-8c92-e8b0bba17931', // <PERSON>
  'fb361a02-afdf-400b-9c1a-12011cb8c67f', // <PERSON>
];

async function quickPurge() {
  console.log('🧹 PURGE RAPIDE DE LA BASE DE DONNÉES');
  
  try {
    const client = await pool.connect();
    
    // Compter avant
    const beforeShifts = await client.query('SELECT COUNT(*) as total FROM shifts');
    const beforeEmployees = await client.query('SELECT COUNT(*) as total FROM employees');
    
    console.log(`📊 AVANT: ${beforeShifts.rows[0].total} quarts, ${beforeEmployees.rows[0].total} employés`);
    
    // 1. Supprimer TOUS les quarts
    console.log('🗑️ Suppression de tous les quarts...');
    await client.query('DELETE FROM shifts');
    
    // 2. Supprimer les doublons d'employés (garder un exemplaire de chaque nom)
    console.log('🗑️ Suppression des doublons d\'employés...');
    await client.query(`
      DELETE FROM employees 
      WHERE id NOT IN (
        SELECT DISTINCT ON (name) id 
        FROM employees 
        ORDER BY name, created_at
      )
    `);
    
    // 3. Supprimer les employés non-essentiels (garder seulement les 5 de base)
    console.log('🗑️ Suppression des employés non-essentiels...');
    await client.query('DELETE FROM employees WHERE id != ALL($1)', [BASE_EMPLOYEES]);
    
    // 4. Reset des paramètres
    console.log('⚙️ Reset des paramètres...');
    await client.query('DELETE FROM app_settings');
    await client.query(`
      INSERT INTO app_settings (setting_key, setting_value) VALUES
      ('weekStartsOn', 'monday'),
      ('weekStartDay', '1'),
      ('appVersion', '1.0.0'),
      ('currentWeekOffset', '0'),
      ('viewMode', 'week')
    `);
    
    // Compter après
    const afterShifts = await client.query('SELECT COUNT(*) as total FROM shifts');
    const afterEmployees = await client.query('SELECT COUNT(*) as total FROM employees');
    
    console.log(`📊 APRÈS: ${afterShifts.rows[0].total} quarts, ${afterEmployees.rows[0].total} employés`);
    
    // Afficher les employés restants
    const remaining = await client.query('SELECT name, status FROM employees ORDER BY name');
    console.log('✅ Employés conservés:');
    remaining.rows.forEach((emp, i) => {
      console.log(`   ${i+1}. ${emp.name} (${emp.status})`);
    });
    
    client.release();
    console.log('✅ PURGE TERMINÉE!');
    
  } catch (error) {
    console.error('❌ ERREUR:', error.message);
  } finally {
    await pool.end();
  }
}

quickPurge(); 