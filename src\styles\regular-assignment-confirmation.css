/* ========================================
   STYLES POUR LE MENU DE CONFIRMATION 
   DES ATTRIBUTIONS RÉGULIÈRES
   ======================================== */

/* Modal de confirmation principal */
#regular-assignment-confirmation-modal {
    animation: fadeIn 0.2s ease-out;
}

#regular-assignment-confirmation-modal .bg-white {
    animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        transform: translateY(-20px);
        opacity: 0;
    }
    to { 
        transform: translateY(0);
        opacity: 1;
    }
}

/* Boutons d'options */
#regular-assignment-permanent,
#regular-assignment-replacement {
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

#regular-assignment-permanent:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

#regular-assignment-replacement:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.15);
}

/* Icônes dans les boutons */
#regular-assignment-permanent .w-5,
#regular-assignment-replacement .w-5 {
    transition: transform 0.2s ease;
}

#regular-assignment-permanent:hover .w-5 {
    transform: rotate(180deg);
}

#regular-assignment-replacement:hover .w-5 {
    transform: scale(1.1);
}

/* Modal de sélection de date */
#replacement-date-selection-modal {
    animation: fadeIn 0.2s ease-out;
}

#replacement-date-selection-modal .bg-white {
    animation: slideIn 0.3s ease-out;
}

/* Options de date */
input[type="radio"]:checked + div {
    color: #059669;
    font-weight: 600;
}

input[type="radio"]:checked + div .text-gray-500 {
    color: #047857 !important;
}

/* Boutons de confirmation */
#replacement-date-confirm {
    transition: all 0.2s ease;
}

#replacement-date-confirm:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.25);
}

/* Responsive */
@media (max-width: 640px) {
    #regular-assignment-confirmation-modal .max-w-md,
    #replacement-date-selection-modal .max-w-md {
        max-width: 95%;
        margin: 0 auto;
    }
    
    #regular-assignment-confirmation-modal .p-6,
    #replacement-date-selection-modal .p-6 {
        padding: 1rem;
    }
    
    #regular-assignment-permanent,
    #regular-assignment-replacement {
        padding: 0.75rem;
    }
    
    #regular-assignment-permanent .text-sm,
    #regular-assignment-replacement .text-sm {
        font-size: 0.75rem;
    }
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
    #regular-assignment-confirmation-modal .bg-white,
    #replacement-date-selection-modal .bg-white {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    #regular-assignment-confirmation-modal .text-gray-900,
    #replacement-date-selection-modal .text-gray-900 {
        color: #f9fafb;
    }
    
    #regular-assignment-confirmation-modal .text-gray-700,
    #replacement-date-selection-modal .text-gray-700 {
        color: #d1d5db;
    }
    
    #regular-assignment-confirmation-modal .text-gray-600,
    #replacement-date-selection-modal .text-gray-600 {
        color: #9ca3af;
    }
    
    #regular-assignment-confirmation-modal .text-gray-500,
    #replacement-date-selection-modal .text-gray-500 {
        color: #6b7280;
    }
    
    #regular-assignment-confirmation-modal .bg-gray-50,
    #replacement-date-selection-modal .bg-gray-50 {
        background-color: #374151;
    }
    
    #regular-assignment-confirmation-modal .border-gray-200,
    #replacement-date-selection-modal .border-gray-200 {
        border-color: #4b5563;
    }
    
    #regular-assignment-permanent:hover,
    #regular-assignment-replacement:hover {
        background-color: #374151;
    }
    
    input[type="radio"]:checked + div {
        color: #10b981;
    }
    
    input[type="radio"]:checked + div .text-gray-500 {
        color: #059669 !important;
    }
}

/* Accessibilité */
#regular-assignment-permanent:focus,
#regular-assignment-replacement:focus,
#replacement-date-confirm:focus,
#regular-assignment-cancel:focus,
#replacement-date-cancel:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* États de chargement */
.loading-state {
    opacity: 0.6;
    pointer-events: none;
}

.loading-state::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
} 