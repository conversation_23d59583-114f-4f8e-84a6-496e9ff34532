#!/usr/bin/env node

import { pool, closePool } from '../server/config/database.js';

// Couleurs pour les logs
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.magenta}🚀 ${msg}${colors.reset}`)
};

async function runMigration() {
  const client = await pool.connect();
  
  try {
    log.title('🏗️  CRÉATION DU SCHÉMA DE BASE DE DONNÉES');
    
    // 1. Créer l'extension UUID
    log.info('Création de l\'extension UUID...');
    await client.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
    log.success('Extension UUID créée');
    
    // 2. Créer les tables une par une
    log.info('Création de la table employee_templates...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS employee_templates (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name VARCHAR(255) NOT NULL,
          description TEXT,
          fields JSONB NOT NULL DEFAULT '[]',
          is_default BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    log.success('Table employee_templates créée');
    
    log.info('Création de la table employees...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS employees (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name VARCHAR(255) NOT NULL,
          status VARCHAR(100) NOT NULL DEFAULT 'Temps Plein',
          avatar TEXT,
          avatar_url TEXT,
          template_id UUID REFERENCES employee_templates(id) ON DELETE SET NULL,
          extra_fields JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    log.success('Table employees créée');
    
    log.info('Création de la table standard_posts...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS standard_posts (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          label VARCHAR(255) NOT NULL,
          color VARCHAR(50),
          hours VARCHAR(50) NOT NULL,
          duration INTEGER NOT NULL,
          type VARCHAR(50) NOT NULL,
          category VARCHAR(100),
          is_custom BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    log.success('Table standard_posts créée');
    
    log.info('Création de la table shifts...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS shifts (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
          post_id UUID REFERENCES standard_posts(id) ON DELETE SET NULL,
          date_key DATE NOT NULL,
          start_time TIME,
          end_time TIME,
          break_duration INTEGER DEFAULT 0,
          shift_data JSONB NOT NULL,
          notes TEXT,
          status VARCHAR(50) DEFAULT 'scheduled',
          is_regular BOOLEAN DEFAULT FALSE,
          is_punctual BOOLEAN DEFAULT FALSE,
          assignment_id UUID,
          week_offset INTEGER DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    log.success('Table shifts créée');
    
    log.info('Création de la table regular_assignments...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS regular_assignments (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
          post_id UUID NOT NULL REFERENCES standard_posts(id) ON DELETE CASCADE,
          is_limited BOOLEAN DEFAULT FALSE,
          start_date DATE NOT NULL,
          end_date DATE,
          selected_days INTEGER[] DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    log.success('Table regular_assignments créée');
    
    log.info('Création de la table vacation_periods...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS vacation_periods (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
          start_date DATE NOT NULL,
          end_date DATE NOT NULL,
          type VARCHAR(50) NOT NULL DEFAULT 'CP',
          description TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    log.success('Table vacation_periods créée');
    
    log.info('Création de la table global_vacations...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS global_vacations (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name VARCHAR(255) NOT NULL,
          start_date DATE NOT NULL,
          end_date DATE NOT NULL,
          description TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    log.success('Table global_vacations créée');
    
    log.info('Création de la table app_settings...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS app_settings (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          setting_key VARCHAR(255) UNIQUE NOT NULL,
          setting_value JSONB NOT NULL,
          description TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    log.success('Table app_settings créée');
    
    // 3. Créer les index
    log.info('Création des index...');
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_employee_templates_name ON employee_templates(name)',
      'CREATE INDEX IF NOT EXISTS idx_employee_templates_default ON employee_templates(is_default)',
      'CREATE INDEX IF NOT EXISTS idx_employees_name ON employees(name)',
      'CREATE INDEX IF NOT EXISTS idx_employees_status ON employees(status)',
      'CREATE INDEX IF NOT EXISTS idx_employees_template ON employees(template_id)',
      'CREATE INDEX IF NOT EXISTS idx_standard_posts_type ON standard_posts(type)',
      'CREATE INDEX IF NOT EXISTS idx_standard_posts_category ON standard_posts(category)',
      'CREATE INDEX IF NOT EXISTS idx_shifts_employee_date ON shifts(employee_id, date_key)',
      'CREATE INDEX IF NOT EXISTS idx_shifts_date ON shifts(date_key)',
      'CREATE INDEX IF NOT EXISTS idx_shifts_post ON shifts(post_id)',
      'CREATE INDEX IF NOT EXISTS idx_shifts_assignment ON shifts(assignment_id)',
      'CREATE INDEX IF NOT EXISTS idx_regular_assignments_employee ON regular_assignments(employee_id)',
      'CREATE INDEX IF NOT EXISTS idx_regular_assignments_post ON regular_assignments(post_id)',
      'CREATE INDEX IF NOT EXISTS idx_regular_assignments_dates ON regular_assignments(start_date, end_date)',
      'CREATE INDEX IF NOT EXISTS idx_vacation_periods_employee ON vacation_periods(employee_id)',
      'CREATE INDEX IF NOT EXISTS idx_vacation_periods_dates ON vacation_periods(start_date, end_date)',
      'CREATE INDEX IF NOT EXISTS idx_vacation_periods_type ON vacation_periods(type)',
      'CREATE INDEX IF NOT EXISTS idx_global_vacations_dates ON global_vacations(start_date, end_date)',
      'CREATE INDEX IF NOT EXISTS idx_global_vacations_name ON global_vacations(name)',
      'CREATE UNIQUE INDEX IF NOT EXISTS idx_app_settings_key ON app_settings(setting_key)'
    ];
    
    for (const indexQuery of indexes) {
      await client.query(indexQuery);
    }
    log.success('Index créés');
    
    // 4. Créer la fonction de mise à jour des timestamps
    log.info('Création de la fonction de mise à jour...');
    await client.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = CURRENT_TIMESTAMP;
          RETURN NEW;
      END;
      $$ language 'plpgsql'
    `);
    log.success('Fonction de mise à jour créée');
    
    // 5. Créer les triggers
    log.info('Création des triggers...');
    const triggers = [
      'CREATE TRIGGER update_employee_templates_updated_at BEFORE UPDATE ON employee_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()',
      'CREATE TRIGGER update_employees_updated_at BEFORE UPDATE ON employees FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()',
      'CREATE TRIGGER update_standard_posts_updated_at BEFORE UPDATE ON standard_posts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()',
      'CREATE TRIGGER update_shifts_updated_at BEFORE UPDATE ON shifts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()',
      'CREATE TRIGGER update_regular_assignments_updated_at BEFORE UPDATE ON regular_assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()',
      'CREATE TRIGGER update_vacation_periods_updated_at BEFORE UPDATE ON vacation_periods FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()',
      'CREATE TRIGGER update_global_vacations_updated_at BEFORE UPDATE ON global_vacations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()',
      'CREATE TRIGGER update_app_settings_updated_at BEFORE UPDATE ON app_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()'
    ];
    
    for (const triggerQuery of triggers) {
      try {
        await client.query(triggerQuery);
      } catch (error) {
        if (!error.message.includes('already exists')) {
          throw error;
        }
      }
    }
    log.success('Triggers créés');
    
    log.success('🎉 SCHÉMA DE BASE CRÉÉ AVEC SUCCÈS !');
    
  } catch (error) {
    log.error(`Erreur lors de la migration: ${error.message}`);
    throw error;
  } finally {
    client.release();
  }
}

async function insertInitialData() {
  const client = await pool.connect();
  
  try {
    log.title('📝 INSERTION DES DONNÉES INITIALES');
    
    // 1. Modèle par défaut
    log.info('Insertion du modèle de fiche par défaut...');
    const templateResult = await client.query(`
      INSERT INTO employee_templates (name, description, fields, is_default) VALUES 
      ($1, $2, $3, $4)
      ON CONFLICT DO NOTHING
      RETURNING id
    `, [
      'Modèle Standard',
      'Modèle de fiche employé par défaut avec les champs essentiels',
      JSON.stringify([
        {"id": "name", "label": "Nom complet", "type": "text", "required": true, "order": 1, "placeholder": "ex: Jean Dupont"},
        {"id": "status", "label": "Statut", "type": "select", "required": true, "order": 2, "options": ["Temps Plein", "Temps Partiel", "Stagiaire", "Freelance"]},
        {"id": "email", "label": "Email professionnel", "type": "email", "required": false, "order": 3, "placeholder": "ex: <EMAIL>"},
        {"id": "phone", "label": "Téléphone", "type": "tel", "required": false, "order": 4, "placeholder": "ex: 01 23 45 67 89"},
        {"id": "department", "label": "Département", "type": "select", "required": false, "order": 5, "options": ["Administration", "Production", "Maintenance", "Qualité", "Logistique"]},
        {"id": "position", "label": "Poste", "type": "text", "required": false, "order": 6, "placeholder": "ex: Opérateur de production"},
        {"id": "hire_date", "label": "Date d'embauche", "type": "date", "required": false, "order": 7},
        {"id": "notes", "label": "Notes", "type": "textarea", "required": false, "order": 8, "placeholder": "Informations complémentaires..."}
      ]),
      true
    ]);
    
    // Récupérer l'ID du template créé ou existant
    let templateId;
    if (templateResult.rows.length > 0) {
      templateId = templateResult.rows[0].id;
    } else {
      const existingTemplate = await client.query("SELECT id FROM employee_templates WHERE is_default = true LIMIT 1");
      templateId = existingTemplate.rows[0].id;
    }
    
    log.success('Modèle par défaut inséré');
    
    // 2. Postes standards
    log.info('Insertion des postes standards...');
    const posts = [
      ['Poste Matin', '#0ea5e9', '08:00-16:00', 8, 'sky', null, false],
      ['Poste Soir', '#f59e0b', '16:00-24:00', 8, 'amber', null, false],
      ['Poste Nuit', '#0ea5e9', '00:00-08:00', 8, 'sky', 'night', false],
      ['Poste WE1', '#10b981', '00:00-12:00', 12, 'emerald', 'weekend', false],
      ['Poste WE2', '#10b981', '12:00-24:00', 12, 'emerald', 'weekend', false]
    ];
    
    for (const post of posts) {
      await client.query(`
        INSERT INTO standard_posts (label, color, hours, duration, type, category, is_custom) 
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT DO NOTHING
      `, post);
    }
    log.success('Postes standards insérés');
    
    // 3. Employés d'exemple
    log.info('Insertion des employés d\'exemple...');
    const employees = [
      ['Jean Dupont', 'Temps Plein', JSON.stringify({"email": "<EMAIL>", "department": "Production"})],
      ['Marie Martin', 'Temps Plein', JSON.stringify({"email": "<EMAIL>", "department": "Administration"})],
      ['Pierre Durand', 'Temps Partiel', JSON.stringify({"email": "<EMAIL>", "department": "Maintenance"})],
      ['Sophie Leblanc', 'Temps Plein', JSON.stringify({"email": "<EMAIL>", "department": "Qualité"})],
      ['Lucas Bernard', 'Stagiaire', JSON.stringify({"email": "<EMAIL>", "department": "Logistique"})]
    ];
    
    for (const employee of employees) {
      await client.query(`
        INSERT INTO employees (name, status, template_id, extra_fields) 
        VALUES ($1, $2, $3, $4)
        ON CONFLICT DO NOTHING
      `, [...employee.slice(0, 2), templateId, employee[2]]);
    }
    log.success('Employés d\'exemple insérés');
    
    // 4. Paramètres d'application
    log.info('Insertion des paramètres d\'application...');
    const settings = [
      ['weekStartsOn', '"sunday"', 'Premier jour de la semaine - Dimanche par défaut'],
      ['weekStartDay', '0', 'Premier jour de la semaine (numérique) - 0=Dimanche par défaut'],
      ['viewMode', '"week"', 'Mode d\'affichage par défaut'],
      ['currentWeekOffset', '0', 'Décalage de semaine actuel'],
      ['appVersion', '"1.0.0"', 'Version de l\'application'],
      ['lastBackup', 'null', 'Timestamp de la dernière sauvegarde']
    ];
    
    for (const setting of settings) {
      await client.query(`
        INSERT INTO app_settings (setting_key, setting_value, description) 
        VALUES ($1, $2, $3)
        ON CONFLICT (setting_key) DO NOTHING
      `, setting);
    }
    log.success('Paramètres d\'application insérés');
    
    // 5. Vacances globales
    log.info('Insertion des vacances globales...');
    const holidays = [
      ['Jour de l\'An', '2025-01-01', '2025-01-01', 'Jour férié'],
      ['Fête du Travail', '2025-05-01', '2025-05-01', 'Jour férié'],
      ['Fête de la Victoire', '2025-05-08', '2025-05-08', 'Jour férié'],
      ['Ascension', '2025-05-29', '2025-05-29', 'Jour férié'],
      ['Lundi de Pentecôte', '2025-06-09', '2025-06-09', 'Jour férié'],
      ['Fête Nationale', '2025-07-14', '2025-07-14', 'Jour férié'],
      ['Assomption', '2025-08-15', '2025-08-15', 'Jour férié'],
      ['Toussaint', '2025-11-01', '2025-11-01', 'Jour férié'],
      ['Armistice', '2025-11-11', '2025-11-11', 'Jour férié'],
      ['Noël', '2025-12-25', '2025-12-25', 'Jour férié']
    ];
    
    for (const holiday of holidays) {
      await client.query(`
        INSERT INTO global_vacations (name, start_date, end_date, description) 
        VALUES ($1, $2, $3, $4)
        ON CONFLICT DO NOTHING
      `, holiday);
    }
    log.success('Vacances globales insérées');
    
    log.success('🎉 DONNÉES INITIALES INSÉRÉES AVEC SUCCÈS !');
    
  } catch (error) {
    log.error(`Erreur lors de l'insertion: ${error.message}`);
    throw error;
  } finally {
    client.release();
  }
}

async function main() {
  try {
    await runMigration();
    await insertInitialData();
    log.success('✨ MIGRATION TERMINÉE AVEC SUCCÈS !');
  } catch (error) {
    log.error(`Erreur fatale: ${error.message}`);
    process.exit(1);
  } finally {
    await closePool();
  }
}

// Exécuter le script
main(); 