/* Styles personnalisés pour la barre latérale avec effets 3D */

.sidebar-container {
  /* Effet de profondeur avec box-shadow progressive */
  box-shadow: 
    4px 0 12px rgba(0, 0, 0, 0.05),
    8px 0 24px rgba(0, 0, 0, 0.03),
    12px 0 36px rgba(0, 0, 0, 0.02);
  
  /* Transition fluide pour tous les états */
  transition: 
    width 0.5s cubic-bezier(0.34, 1.56, 0.64, 1),
    box-shadow 0.3s ease-out,
    transform 0.3s ease-out;
}

.sidebar-container:hover {
  /* Ombre plus prononcée au survol */
  box-shadow: 
    6px 0 20px rgba(0, 0, 0, 0.08),
    12px 0 40px rgba(0, 0, 0, 0.05),
    18px 0 60px rgba(0, 0, 0, 0.03);
}

/* Effet de rebond pour les liens de navigation */
.sidebar-link {
  transform-origin: left center;
  transition: 
    all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275),
    background-color 0.2s ease-out,
    color 0.2s ease-out;
}

.sidebar-link:hover {
  transform: translateX(4px) scale(1.02);
  transition: 
    all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.sidebar-link.active {
  transform: translateX(2px) scale(1.01);
  position: relative;
}

.sidebar-link.active::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60%;
  background: linear-gradient(to bottom, #3B82F6, #1D4ED8);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

/* Animation d'entrée pour les textes */
.group:hover .sidebar-link span:last-child {
  animation: slideInText 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes slideInText {
  0% {
    opacity: 0;
    transform: translateX(-10px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Effet de glow pour l'icône principale */
.sidebar-header .size-8 {
  filter: drop-shadow(0 2px 8px rgba(59, 130, 246, 0.2));
  transition: filter 0.3s ease-out;
}

.group:hover .sidebar-header .size-8 {
  filter: drop-shadow(0 4px 16px rgba(59, 130, 246, 0.4));
}

/* Séparateur avec effet de gradient */
.sidebar-divider {
  background: linear-gradient(
    90deg, 
    transparent 0%, 
    rgba(148, 163, 184, 0.3) 20%, 
    rgba(148, 163, 184, 0.6) 50%, 
    rgba(148, 163, 184, 0.3) 80%, 
    transparent 100%
  );
  border: none;
  height: 1px;
  margin: 1rem 0.5rem;
  transition: opacity 0.3s ease-out;
}

.group:hover .sidebar-divider {
  opacity: 0.8;
}

/* Avatar avec effet de pulse au survol */
.sidebar-footer .rounded-full {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.group:hover .sidebar-footer .rounded-full {
  box-shadow: 
    0 0 0 3px rgba(59, 130, 246, 0.2),
    0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Effet de breathing pour la barre latérale */
@keyframes breathe {
  0%, 100% { 
    transform: scale(1); 
  }
  50% { 
    transform: scale(1.005); 
  }
}

.sidebar-container:not(:hover) {
  animation: breathe 4s ease-in-out infinite;
}

/* Style pour les écrans plus petits */
@media (max-width: 768px) {
  .sidebar-container {
    width: 4rem !important;
  }
  
  .sidebar-container:hover {
    width: 16rem !important;
  }
}

/* Amélioration de l'effet de backdrop-blur */
.sidebar-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 50%,
    rgba(255, 255, 255, 0.95) 100%
  );
  backdrop-filter: blur(12px) saturate(1.2);
  border-radius: inherit;
  z-index: -1;
}

/* Effet de reflet lumineux sur les liens actifs */
.sidebar-link.active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  border-radius: inherit;
  pointer-events: none;
}

/* Transition douce pour le contenu principal */
.ml-16 {
  transition: margin-left 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Micro-interactions pour les icônes */
.sidebar-link .material-icons-outlined {
  transition: transform 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.sidebar-link:hover .material-icons-outlined {
  transform: rotate(5deg) scale(1.1);
}

.sidebar-link.active .material-icons-outlined {
  transform: scale(1.05);
}

/* Améliorations pour l'espacement des employés */
.employee-row {
  min-height: 95px !important;
  margin-bottom: 8px;
}

.employee-info-cell-content {
  padding: 16px !important;
  gap: 16px !important;
}

/* Effet de drag pour les postes vers employés */
.drag-target {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(59, 130, 246, 0.1) 50%,
    rgba(59, 130, 246, 0.05) 100%
  );
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  transform: scale(1.02);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Animation pour les champs extra des employés */
.employee-row:hover .text-slate-600 {
  color: rgb(51, 65, 85);
  transition: color 0.2s ease-out;
}

/* Message pour postes non attribués */
.empty-posts-message {
  transition: opacity 0.3s ease-in-out;
}

.empty-posts-message:hover {
  opacity: 0.8;
}

/* Amélioration des avatars employés */
.employee-avatar {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.employee-row:hover .employee-avatar {
  transform: scale(1.1);
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
} 