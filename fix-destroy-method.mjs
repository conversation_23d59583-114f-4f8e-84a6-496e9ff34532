#!/usr/bin/env node

/**
 * Script pour corriger la mauvaise insertion de la méthode destroy dans _preloadedRange
 */

import fs from 'fs';

console.log('🔧 [FIX-DESTROY] Correction de la méthode destroy...');

const filePath = './src/teamCalendarApp.ts';
let content = fs.readFileSync(filePath, 'utf8');

// 1. Supprimer la méthode destroy de l'intérieur de _preloadedRange
console.log('📝 [FIX-DESTROY] Suppression de destroy de _preloadedRange...');

const destroyMethodPattern = /\/\/ ✅ NOUVEAU : Protection HMR pour éviter les doubles initialisations\s+destroy: function\(\) \{[\s\S]*?console\.log\('✅ \[destroy\] Nettoyage terminé'\);\s+\},\s*\},/;

if (destroyMethodPattern.test(content)) {
    content = content.replace(destroyMethodPattern, '');
    console.log('✅ [FIX-DESTROY] Méthode destroy supprimée de _preloadedRange');
} else {
    console.log('⚠️ [FIX-DESTROY] Pattern destroy dans _preloadedRange non trouvé');
}

// 2. Corriger la déclaration de _preloadedRange
console.log('📝 [FIX-DESTROY] Correction de la déclaration _preloadedRange...');

const preloadedRangePattern = /_preloadedRange:\s*\{\s*start:\s*Date\s*\|\s*null,\s*end:\s*Date\s*\|\s*null\s*\n\s*\/\/ ✅ NOUVEAU : Protection HMR pour éviter les doubles initialisations\s*\n\s*destroy:\s*function\(\)\s*\{[\s\S]*?\},\s*\},/;

if (preloadedRangePattern.test(content)) {
    content = content.replace(preloadedRangePattern, '_preloadedRange: { start: null, end: null },');
    console.log('✅ [FIX-DESTROY] _preloadedRange corrigé');
} else {
    console.log('⚠️ [FIX-DESTROY] Pattern _preloadedRange non trouvé');
}

// 3. Ajouter la méthode destroy au niveau principal de l'objet
console.log('📝 [FIX-DESTROY] Ajout de destroy au niveau principal...');

const destroyMethod = `
  destroy: function() {
    console.log('🧹 [destroy] Nettoyage de l\'instance TeamCalendarApp...');
    // Supprimer les listeners
    if (this._centralizedDragOverHandler) {
      document.removeEventListener('dragover', this._centralizedDragOverHandler);
    }
    if (this._centralizedDropHandler) {
      document.removeEventListener('drop', this._centralizedDropHandler);
    }
    // Nettoyer les éléments DOM
    if (this.elements.employeeListContainer) {
      this.elements.employeeListContainer.innerHTML = '';
    }
    // Réinitialiser les flags
    this._eventListenersAttached = false;
    this._attachRetryCount = 0;
    this.config._isInitialized = false;
    console.log('✅ [destroy] Nettoyage terminé');
  },
`;

// Chercher un bon endroit pour insérer destroy (après une méthode existante)
const insertAfterPattern = /attachAllEventListeners:\s*function\(\)\s*\{[\s\S]*?\},/;

if (insertAfterPattern.test(content)) {
    content = content.replace(insertAfterPattern, (match) => {
        return match + '\n' + destroyMethod;
    });
    console.log('✅ [FIX-DESTROY] Méthode destroy ajoutée après attachAllEventListeners');
} else {
    // Fallback: chercher après render
    const renderPattern = /render:\s*function\(\)\s*\{[\s\S]*?\},/;
    if (renderPattern.test(content)) {
        content = content.replace(renderPattern, (match) => {
            return match + '\n' + destroyMethod;
        });
        console.log('✅ [FIX-DESTROY] Méthode destroy ajoutée après render');
    } else {
        console.log('❌ [FIX-DESTROY] Impossible de trouver un endroit pour insérer destroy');
    }
}

// 4. Écrire le fichier corrigé
fs.writeFileSync(filePath, content);

console.log('✅ [FIX-DESTROY] Correction terminée !');
console.log('\n📋 RÉSUMÉ DES CORRECTIONS :');
console.log('✅ 1. Méthode destroy supprimée de _preloadedRange');
console.log('✅ 2. _preloadedRange corrigé : { start: null, end: null }');
console.log('✅ 3. Méthode destroy ajoutée au niveau principal');

console.log('\n🚀 Prochaines étapes :');
console.log('1. Redémarrer le serveur : npm run dev');
console.log('2. Recharger la page (Ctrl+F5)');
console.log('3. Tester les fonctions de diagnostic DOM');

console.log('\n✅ [FIX-DESTROY] Script terminé !'); 