/// <reference types="vitest/globals" />
// Setup global pour les tests Vitest
import { vi } from 'vitest';

// ✅ MOCKS GLOBAUX

// Mock de fetch pour les tests API
global.fetch = vi.fn();

// Mock de localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock de console pour les tests (optionnel)
global.console = {
  ...console,
  // Désactiver les logs pendant les tests si nécessaire
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  info: vi.fn(),
  debug: vi.fn()
};

// ✅ CORRECTION : Mock propre des timers sans récursion
// Sauvegarder les fonctions natives AVANT de les mocker
const _realSetTimeout = global.setTimeout;
const _realClearTimeout = global.clearTimeout;

// Utiliser les faux timers de Vitest (recommandé)
vi.useFakeTimers();

// Optionnel : Espionner les appels si nécessaire
vi.spyOn(global, 'setTimeout');
vi.spyOn(global, 'clearTimeout');

// ✅ HELPERS DE TEST

// Helper pour créer des dates de test
export const createTestDate = (dateString: string) => {
  return new Date(dateString);
};

// Helper pour créer un employé de test
export const createTestEmployee = (overrides = {}) => {
  return {
    id: 'test-employee-1',
    name: 'Test Employee',
    status: 'active',
    avatar: null,
    extraFields: {},
    ...overrides
  };
};

// Helper pour créer un poste de test
export const createTestPost = (overrides = {}) => {
  return {
    id: 'test-post-1',
    label: 'Test Post',
    color: 'emerald',
    workingDays: [1, 2, 3, 4, 5], // Lundi à Vendredi
    isCustom: false,
    ...overrides
  };
};

// Helper pour créer une assignation de test
export const createTestAssignment = (overrides = {}) => {
  return {
    id: 'test-assignment-1',
    employeeId: 'test-employee-1',
    postId: 'test-post-1',
    type: 'regular',
    dayOfWeek: 1, // Lundi
    startTime: '08:00',
    endTime: '16:00',
    startDate: '2025-01-01',
    endDate: '2025-12-31',
    isActive: true,
    ...overrides
  };
};

// Helper pour créer une semaine de test
export const createTestWeek = (startDate: string) => {
  const start = new Date(startDate);
  const days = [];
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(start);
    date.setDate(start.getDate() + i);
    
    days.push({
      date,
      dateKey: date.toISOString().split('T')[0],
      dayOfWeek: date.getDay(),
      isToday: false,
      isWeekend: date.getDay() === 0 || date.getDay() === 6
    });
  }
  
  return days;
};

// ✅ MATCHERS PERSONNALISÉS

// Matcher pour vérifier qu'une date est dans une plage
expect.extend({
  toBeInDateRange(received: Date, start: Date, end: Date) {
    const pass = received >= start && received <= end;
    
    if (pass) {
      return {
        message: () => `Expected ${received} not to be between ${start} and ${end}`,
        pass: true
      };
    } else {
      return {
        message: () => `Expected ${received} to be between ${start} and ${end}`,
        pass: false
      };
    }
  }
});

// Matcher pour vérifier qu'un assignment est valide
expect.extend({
  toBeValidAssignment(received: any) {
    const hasRequiredFields = received && 
      received.employeeId && 
      received.postId && 
      typeof received.dayOfWeek === 'number';
    
    if (hasRequiredFields) {
      return {
        message: () => `Expected assignment to be invalid`,
        pass: true
      };
    } else {
      return {
        message: () => `Expected assignment to have employeeId, postId, and dayOfWeek`,
        pass: false
      };
    }
  }
});

// ✅ CONFIGURATION GLOBALE

// Augmenter le timeout par défaut pour les tests lents
vi.setConfig({
  testTimeout: 10000
});

// ✅ CORRECTION : Nettoyer les mocks et timers après chaque test
afterEach(() => {
  vi.clearAllMocks();
  vi.clearAllTimers();
  // Remettre les timers réels si nécessaire
  // vi.useRealTimers(); // Décommenté si problèmes persistent
});

console.log('🧪 [Test Setup] Configuration des tests initialisée');

export {
  localStorageMock
};
