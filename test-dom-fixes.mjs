
// Script de test pour vérifier les corrections DOM
console.log('🧪 [TEST-DOM] Test des corrections DOM...');

// Test 1: Vérifier que verifyAndFixDom ne boucle plus
console.log('📋 Test 1: verifyAndFixDom avec limite de tentatives');
if (window.teamCalendarApp && window.teamCalendarApp.verifyAndFixDom) {
    console.log('✅ verifyAndFixDom disponible');
} else {
    console.log('❌ verifyAndFixDom non trouvé');
}

// Test 2: Vérifier que attachAllEventListeners a un circuit-breaker
console.log('📋 Test 2: attachAllEventListeners avec circuit-breaker');
if (window.teamCalendarApp && window.teamCalendarApp.attachAllEventListeners) {
    console.log('✅ attachAllEventListeners disponible');
} else {
    console.log('❌ attachAllEventListeners non trouvé');
}

// Test 3: Vérifier la protection HMR
console.log('📋 Test 3: Protection HMR');
if (window.__TCA__) {
    console.log('✅ Instance unique détectée');
} else {
    console.log('❌ Instance unique non trouvée');
}

console.log('✅ [TEST-DOM] Tests terminés');
