import { query } from '../server/config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runMigration005() {
    try {
        console.log('🚀 [Migration 005] Début de la migration - Ajout colonne excluded_dates...');
        
        // Lire le fichier de migration
        const migrationPath = path.join(__dirname, 'migrations', '005_add_excluded_dates_column.sql');
        const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
        
        // Vérifier si la colonne existe déjà
        const columnExists = await query(`
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'regular_assignments' 
            AND column_name = 'excluded_dates'
        `);
        
        if (columnExists.rows.length > 0) {
            console.log('✅ [Migration 005] Colonne excluded_dates existe déjà');
            return;
        }
        
        // Exécuter la migration
        await query(migrationSQL);
        
        console.log('✅ [Migration 005] Migration terminée avec succès');
        console.log('📊 [Migration 005] Colonne excluded_dates ajoutée à regular_assignments');
        
        // Vérifier que la colonne a été créée
        const verification = await query(`
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'regular_assignments' 
            AND column_name = 'excluded_dates'
        `);
        
        if (verification.rows.length > 0) {
            console.log('✅ [Migration 005] Vérification réussie:', verification.rows[0]);
        } else {
            console.error('❌ [Migration 005] Erreur: colonne non créée');
        }
        
    } catch (error) {
        console.error('❌ [Migration 005] Erreur:', error);
        throw error;
    }
}

// Exécuter si appelé directement
if (import.meta.url === `file://${process.argv[1]}`) {
    runMigration005()
        .then(() => {
            console.log('🎉 [Migration 005] Migration terminée');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 [Migration 005] Échec:', error);
            process.exit(1);
        });
}

export { runMigration005 }; 