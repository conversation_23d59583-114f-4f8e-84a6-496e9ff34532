import { query } from '../config/database.js';
import { v4 as uuidv4 } from 'uuid';

// Mapping des anciens IDs de postes vers les UUID réels
const POST_ID_MAPPING = {
  'WE1': '1406c294-9678-4189-82b6-f4a121fcfc1d',
  'WE2': 'eade7d42-7f13-4d67-bbda-81c776e97af2', 
  'MATIN': 'ce7a07ed-29e7-4556-8665-a04b0ae5317f',
  'SOIR': '8f2b7172-d20f-42c7-9c05-f32135ab2e23',
  'NUIT': '360f25ff-e2cf-4752-b952-ab828c7cd3ba',
  'MORNING': 'ce7a07ed-29e7-4556-8665-a04b0ae5317f',
  'EVENING': '8f2b7172-d20f-42c7-9c05-f32135ab2e23',
  'NIGHT': '360f25ff-e2cf-4752-b952-ab828c7cd3ba'
};

// Fonction pour valider et convertir les UUID
function validateAndConvertUuid(value, fieldName) {
  if (!value) return null;
  
  // Si c'est déjà un UUID valide, le retourner
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (uuidRegex.test(value)) {
    return value;
  }
  
  // Pour les post_id, essayer le mapping
  if (fieldName === 'post_id' && POST_ID_MAPPING[value]) {
    console.log(`🔄 [UUID Mapping] ${value} -> ${POST_ID_MAPPING[value]}`);
    return POST_ID_MAPPING[value];
  }
  
  // Pour les assignment_id non-UUID, retourner null
  if (fieldName === 'assignment_id') {
    console.log(`⚠️ [UUID Warning] Invalid assignment_id ignored: ${value}`);
    return null;
  }
  
  console.log(`❌ [UUID Error] Invalid ${fieldName}: ${value}`);
  return null;
}

class Shift {
  static async findAll() {
    const result = await query(`
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
    `);
    return result.rows;
  }

  static async findByDateRange(startDate, endDate) {
    const result = await query(`
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      WHERE s.date_key >= $1 AND s.date_key <= $2
      ORDER BY s.date_key, e.name
    `, [startDate, endDate]);
    return result.rows;
  }

  static async findByEmployee(employeeId) {
    const result = await query(`
      SELECT s.*, sp.label as post_label
      FROM shifts s
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      WHERE s.employee_id = $1
      ORDER BY s.date_key
    `, [employeeId]);
    return result.rows;
  }

  static async findByDate(date) {
    const result = await query(`
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      WHERE s.date_key = $1
      ORDER BY e.name
    `, [date]);
    return result.rows;
  }

  static async create(shiftData) {
    const { employee_id, post_id, date_key, shift_data, is_regular, is_punctual, assignment_id, week_offset } = shiftData;
    
    // Valider et convertir les UUID
    const validPostId = validateAndConvertUuid(post_id, 'post_id');
    const validAssignmentId = validateAndConvertUuid(assignment_id, 'assignment_id');
    
    const result = await query(`
      INSERT INTO shifts (employee_id, post_id, date_key, shift_data, is_regular, is_punctual, assignment_id, week_offset)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [employee_id, validPostId, date_key, shift_data || '{}', is_regular || false, is_punctual || false, validAssignmentId, week_offset || 0]);
    
    return result.rows[0];
  }

  static async update(id, shiftData) {
    const { employee_id, post_id, date_key, shift_data, is_regular, is_punctual, assignment_id, week_offset } = shiftData;
    
    // Valider et convertir les UUID
    const validPostId = validateAndConvertUuid(post_id, 'post_id');
    const validAssignmentId = validateAndConvertUuid(assignment_id, 'assignment_id');
    
    const result = await query(`
      UPDATE shifts 
      SET employee_id = $1, post_id = $2, date_key = $3, shift_data = $4, 
          is_regular = $5, is_punctual = $6, assignment_id = $7, week_offset = $8
      WHERE id = $9
      RETURNING *
    `, [employee_id, validPostId, date_key, shift_data || '{}', is_regular || false, is_punctual || false, validAssignmentId, week_offset || 0, id]);
    
    return result.rows[0];
  }

  static async delete(id) {
    const result = await query('DELETE FROM shifts WHERE id = $1 RETURNING *', [id]);
    return result.rows[0];
  }

  static async deleteByEmployeeAndDate(employeeId, date) {
    const result = await query('DELETE FROM shifts WHERE employee_id = $1 AND date_key = $2 RETURNING *', [employeeId, date]);
    return result.rows;
  }

  static async deleteByAssignmentId(assignmentId) {
    const result = await query('DELETE FROM shifts WHERE assignment_id = $1 RETURNING *', [assignmentId]);
    return result.rows;
  }

  static async findConflicts(employeeId, date) {
    const result = await query(`
      SELECT s.*, sp.label as post_label
      FROM shifts s
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      WHERE s.employee_id = $1 AND s.date_key = $2
    `, [employeeId, date]);
    return result.rows;
  }

  static async bulkCreate(shiftsData) {
    // Filtrer et valider les données avant insertion
    const validShifts = shiftsData
      .map(shiftData => ({
        ...shiftData,
        post_id: validateAndConvertUuid(shiftData.post_id, 'post_id'),
        assignment_id: validateAndConvertUuid(shiftData.assignment_id, 'assignment_id')
      }))
      .filter(shiftData => {
        // Garder seulement les shifts avec des données valides
        if (!shiftData.employee_id || !shiftData.date_key) {
          console.log(`⚠️ [Shift Filter] Shift ignoré - données manquantes:`, shiftData);
          return false;
        }
        return true;
      });

    console.log(`📊 [Bulk Create] ${validShifts.length}/${shiftsData.length} shifts valides`);

    if (validShifts.length === 0) {
      console.log('⚠️ [Bulk Create] Aucun shift valide à insérer');
      return [];
    }

    // Pour l'insertion en lots, utiliser une transaction
    const { getClient } = await import('../config/database.js');
    const client = await getClient();
    try {
      await client.query('BEGIN');
      
      const createdShifts = [];
      for (const shiftData of validShifts) {
        const result = await client.query(`
          INSERT INTO shifts (employee_id, post_id, date_key, shift_data, is_regular, is_punctual, assignment_id, week_offset)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          RETURNING *
        `, [
          shiftData.employee_id, 
          shiftData.post_id, 
          shiftData.date_key, 
          shiftData.shift_data || '{}', 
          shiftData.is_regular || false, 
          shiftData.is_punctual || false, 
          shiftData.assignment_id, 
          shiftData.week_offset || 0
        ]);
        createdShifts.push(result.rows[0]);
      }
      
      await client.query('COMMIT');
      console.log(`✅ [Bulk Create] ${createdShifts.length} shifts créés avec succès`);
      return createdShifts;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ [Bulk Create] Erreur lors de l\'insertion:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // ✅ NOUVEAU : Méthode bulkUpsert (insert ou update)
  static async bulkUpsert(shiftsData) {
    console.log(`🔄 [Bulk Upsert] Traitement de ${shiftsData.length} shifts`);
    
    // Filtrer et valider les données avant insertion
    const validShifts = shiftsData
      .map(shiftData => ({
        ...shiftData,
        post_id: validateAndConvertUuid(shiftData.post_id, 'post_id'),
        assignment_id: validateAndConvertUuid(shiftData.assignment_id, 'assignment_id')
      }))
      .filter(shiftData => {
        // Garder seulement les shifts avec des données valides
        if (!shiftData.employee_id || !shiftData.date_key) {
          console.log(`⚠️ [Bulk Upsert Filter] Shift ignoré - données manquantes:`, shiftData);
          return false;
        }
        return true;
      });

    console.log(`📊 [Bulk Upsert] ${validShifts.length}/${shiftsData.length} shifts valides`);

    if (validShifts.length === 0) {
      console.log('⚠️ [Bulk Upsert] Aucun shift valide à traiter');
      return [];
    }

    // Utiliser une transaction pour garantir la cohérence
    const { getClient } = await import('../config/database.js');
    const client = await getClient();
    
    try {
      await client.query('BEGIN');
      
      const results = [];
      for (const shiftData of validShifts) {
        // Utiliser ON CONFLICT pour faire un upsert
        const result = await client.query(`
          INSERT INTO shifts (id, employee_id, post_id, date_key, shift_data, is_regular, is_punctual, assignment_id, week_offset)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          ON CONFLICT (id) DO UPDATE SET
            employee_id = EXCLUDED.employee_id,
            post_id = EXCLUDED.post_id,
            date_key = EXCLUDED.date_key,
            shift_data = EXCLUDED.shift_data,
            is_regular = EXCLUDED.is_regular,
            is_punctual = EXCLUDED.is_punctual,
            assignment_id = EXCLUDED.assignment_id,
            week_offset = EXCLUDED.week_offset,
            updated_at = CURRENT_TIMESTAMP
          RETURNING *
        `, [
          shiftData.id || uuidv4(), // Générer un UUID si pas fourni
          shiftData.employee_id, 
          shiftData.post_id, 
          shiftData.date_key, 
          shiftData.shift_data || '{}', // ✅ CORRECTION: Valeur par défaut pour shift_data
          shiftData.is_regular || false, 
          shiftData.is_punctual || false, 
          shiftData.assignment_id, 
          shiftData.week_offset || 0
        ]);
        results.push(result.rows[0]);
      }
      
      await client.query('COMMIT');
      console.log(`✅ [Bulk Upsert] ${results.length} shifts traités avec succès`);
      return results;
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ [Bulk Upsert] Erreur lors du traitement:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // ✅ NOUVEAU : Méthode de remplacement complet (supprime tout puis recrée)
  static async replaceAll(shiftsData) {
    console.log(`🔄 [Replace All] Remplacement complet avec ${shiftsData.length} shifts`);
    
    // Filtrer et valider les données avant insertion
    const validShifts = shiftsData
      .map(shiftData => ({
        ...shiftData,
        post_id: validateAndConvertUuid(shiftData.post_id, 'post_id'),
        assignment_id: validateAndConvertUuid(shiftData.assignment_id, 'assignment_id')
      }))
      .filter(shiftData => {
        // Garder seulement les shifts avec des données valides
        if (!shiftData.employee_id || !shiftData.date_key) {
          console.log(`⚠️ [Replace All Filter] Shift ignoré - données manquantes:`, shiftData);
          return false;
        }
        return true;
      });

    console.log(`📊 [Replace All] ${validShifts.length}/${shiftsData.length} shifts valides`);

    // Utiliser une transaction pour garantir la cohérence
    const { getClient } = await import('../config/database.js');
    const client = await getClient();
    
    try {
      await client.query('BEGIN');
      
      // 1. Supprimer tous les shifts existants
      const deleteResult = await client.query('DELETE FROM shifts');
      const deletedCount = deleteResult.rowCount;
      console.log(`🗑️ [Replace All] ${deletedCount} shifts supprimés`);
      
      // 2. Insérer les nouveaux shifts
      const createdShifts = [];
      for (const shiftData of validShifts) {
        const result = await client.query(`
          INSERT INTO shifts (employee_id, post_id, date_key, shift_data, is_regular, is_punctual, assignment_id, week_offset)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          RETURNING *
        `, [
          shiftData.employee_id, 
          shiftData.post_id, 
          shiftData.date_key, 
          shiftData.shift_data || '{}', 
          shiftData.is_regular || false, 
          shiftData.is_punctual || false, 
          shiftData.assignment_id, 
          shiftData.week_offset || 0
        ]);
        createdShifts.push(result.rows[0]);
      }
      
      await client.query('COMMIT');
      
      console.log(`✅ [Replace All] Remplacement terminé: ${deletedCount} supprimés, ${createdShifts.length} créés`);
      
      return {
        deleted: deletedCount,
        created: createdShifts.length,
        shifts: createdShifts
      };
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ [Replace All] Erreur lors du remplacement:', error);
      throw error;
    } finally {
      client.release();
    }
  }
}

export default Shift; 