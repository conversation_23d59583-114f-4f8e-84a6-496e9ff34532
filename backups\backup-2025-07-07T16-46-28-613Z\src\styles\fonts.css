/* 🔤 Fonts auto-hébergées pour améliorer les performances
   Remplace les Google Fonts pour éviter les requêtes externes bloquantes */

/* ✅ POLICES SYSTÈME - Temporairement désactivées pour éviter les erreurs 404 */
/*
Les polices auto-hébergées sont commentées car les fichiers de polices ne sont pas présents.
L'application utilise maintenant les polices système par défaut.
*/

/* ✅ CLASSES UTILITAIRES POUR FONTS - POLICES SYSTÈME */
.font-inter {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.font-noto {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.font-icons {
  /* Utilisation d'icônes textuelles en attendant les vraies polices d'icônes */
  font-family: -apple-system, BlinkMacSystemFont, 'Sego<PERSON> UI', <PERSON><PERSON>, sans-serif;
  font-weight: normal;
  font-style: normal;
  font-size: 18px;
  line-height: 1;
  text-rendering: optimizeLegibility;
}

/* ✅ OPTIMISATIONS GLOBALES */
* {
  /* Améliore le rendu des fonts */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* ✅ POLICES SYSTÈME UNIQUEMENT */
:root {
  --font-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-secondary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* ✅ CLASSES DE POIDS */
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-black { font-weight: 900; }

/* ✅ RESPONSIVE FONT SIZES */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }

/* ✅ PRELOAD HINTS (à ajouter dans index.html) */
/*
<link rel="preload" href="/fonts/inter-regular.woff2" as="font" type="font/woff2" crossorigin>
<link rel="preload" href="/fonts/inter-medium.woff2" as="font" type="font/woff2" crossorigin>
<link rel="preload" href="/fonts/inter-semibold.woff2" as="font" type="font/woff2" crossorigin>
<link rel="preload" href="/fonts/material-icons-outlined.woff2" as="font" type="font/woff2" crossorigin>
*/
