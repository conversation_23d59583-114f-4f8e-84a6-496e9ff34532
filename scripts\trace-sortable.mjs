// TRACEUR SORTABLEJS – instrumentation légère pour diagnostiquer les problèmes de drag & drop
// -----------------------------------------------------------------------------
// Usage (console navigateur) :
//   import('/scripts/trace-sortable.mjs').then(m => m.startTrace())
// Arrêter :
//   import('/scripts/trace-sortable.mjs').then(m => m.stopTrace())
// -----------------------------------------------------------------------------

let active = false;
let subscriptions = [];

function style(str, color) {
  return `%c${str}`, `color:${color}`;
}

function attach(instance, label = 'EMPLOYEE') {
  const events = ['choose', 'start', 'move', 'change', 'end', 'update', 'unchoose'];
  events.forEach(evt => {
    const handler = e => {
      console.log(...style(`[Sortable:${evt}]`, evt === 'end' ? '#22c55e' : '#0ea5e9'), {
        oldIdx: e.oldIndex,
        newIdx: e.newIndex,
        itemText: e.item?.textContent?.trim(),
        from: e.from?.id,
        to: e.to?.id,
        label
      });
    };
    instance.el.addEventListener(evt, handler);
    subscriptions.push({ el: instance.el, evt, handler });
  });
}

export function startTrace() {
  if (active) {
    console.warn('Trace déjà actif');
    return;
  }
  if (typeof window === 'undefined' || !window.Sortable) {
    console.error('SortableJS non présent sur window');
    return;
  }

  const app = window.TeamCalendarApp;
  if (app?.employeeSortable) {
    attach(app.employeeSortable, 'EMPLOYEE_SORTABLE');
    console.log('🎯 Trace attaché à employeeSortable');
  }

  // Fallback : attacher à tous les sortables connus
  if (window.Sortable.utils && Array.isArray(window.Sortable.utils._domSelectors)) {
    window.Sortable.utils._domSelectors.forEach(el => {
      const inst = el && el[window.Sortable.expando];
      if (inst) attach(inst, 'AUTO');
    });
  }

  active = true;
  console.log('🟢 Trace SortableJS activé');
}

export function stopTrace() {
  subscriptions.forEach(({ el, evt, handler }) => el.removeEventListener(evt, handler));
  subscriptions = [];
  active = false;
  console.log('🔴 Trace SortableJS désactivé');
} 