import { writeFileSync } from 'fs';
import { EOL } from 'os';

const targetFile = 'public/capture-logs-unified.js';

const newContent = [
    '// =====================================================',
    '// SYSTÈME DE CAPTURE DE LOGS UNIFIÉ (Version Simplifiée)',
    '// =====================================================',
    '(function() {',
    '    \'use strict\';',
    '',
    '    let sessionId = sessionStorage.getItem(\'log_session_id\');',
    '    if (!sessionId) {',
    '        sessionId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;',
    '        sessionStorage.setItem(\'log_session_id\', sessionId);',
    '    }',
    '',
    '    console.log(`[CAPTURE-SIMPLIFIÉE] Démarrage. Session ID: ${sessionId}`);',
    '',
    '    const originalConsole = {',
    '        log: console.log,',
    '        warn: console.warn,',
    '        error: console.error,',
    '        info: console.info,',
    '        debug: console.debug,',
    '    };',
    '',
    '    const sendLogToServer = (level, args) => {',
    '        if (args.some(arg => typeof arg === \'string\' && arg.includes(\'[CAPTURE-SIMPLIFIÉE]\'))) {',
    '            return;',
    '        }',
    '',
    '        const message = args.map(arg => {',
    '             if (arg instanceof Error) {',
    '                return `Error: ${arg.message}\\n${arg.stack}`;',
    '            }',
    '            if (typeof arg === \'object\' && arg !== null) {',
    '                try {',
    '                    return JSON.stringify(arg);',
    '                } catch (e) {',
    '                    return \'[Objet non sérialisable]\';',
    '                }',
    '            }',
    '            return String(arg);',
    '        }).join(\' \');',
    '',
    '        fetch(\'/api/logs\', {',
    '            method: \'POST\',',
    '            headers: {',
    '                \'Content-Type\': \'application/json\',',
    '            },',
    '            body: JSON.stringify({',
    '                sessionId,',
    '                source: \'frontend\',',
    '                level,',
    '                message,',
    '                data: {},',
    '                priority: level === \'error\' ? 3 : (level === \'warn\' ? 2 : 0),',
    '            }),',
    '        }).catch(error => {',
    '            originalConsole.error(\'[CAPTURE-SIMPLIFIÉE] Échec de l\\\'envoi du log:\', error);',
    '        });',
    '    };',
    '',
    '    Object.keys(originalConsole).forEach(level => {',
    '        console[level] = function(...args) {',
    '            originalConsole[level].apply(console, args);',
    '            sendLogToServer(level, args);',
    '        };',
    '    });',
    '',
    '    window.addEventListener(\'error\', event => {',
    '        sendLogToServer(\'error\', [event.message, event.filename, event.lineno, event.colno, event.error]);',
    '    });',
    '',
    '    window.addEventListener(\'unhandledrejection\', event => {',
    '        sendLogToServer(\'error\', [\'Promise non gérée\', event.reason]);',
    '    });',
    '',
    '})();',
].join(EOL);

try {
    writeFileSync(targetFile, newContent, 'utf8');
    console.log(`✅ ${targetFile} a été simplifié avec succès.`);
} catch (error) {
    console.error(`❌ Erreur lors de la simplification de ${targetFile}:`, error);
} 