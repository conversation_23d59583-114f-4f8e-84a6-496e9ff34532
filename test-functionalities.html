<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Fonctionnalités - Tutoriel et Reset</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 10px;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #4338ca;
            transform: translateY(-2px);
        }
        .btn.danger {
            background: #ef4444;
        }
        .btn.danger:hover {
            background: #dc2626;
        }
        .status {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 600;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .status.loading {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        .hidden {
            display: none;
        }
        pre {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid #e2e8f0;
            font-size: 12px;
        }
        h1 {
            color: #1f2937;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        .info {
            background: #eff6ff;
            border: 1px solid #3b82f6;
            color: #1e40af;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test des Fonctionnalités Corrigées</h1>
        
        <div class="info">
            <strong>Objectif :</strong> Vérifier que le système de tutoriel et la fonctionnalité de réinitialisation sont maintenant opérationnels.
        </div>

        <!-- Test du Tutoriel -->
        <div class="test-section">
            <h2>🎓 Test du Système de Tutoriel</h2>
            <p>Le système de tutoriel interactif devrait maintenant être réactivé avec l'icône d'aide flottante.</p>
            
            <button class="btn" onclick="testTutorial()">
                🎓 Lancer le Tutoriel
            </button>
            
            <button class="btn" onclick="checkTutorialIcon()">
                👀 Vérifier l'Icône d'Aide
            </button>
            
            <div id="tutorial-status" class="status hidden"></div>
        </div>

        <!-- Test de la Réinitialisation -->
        <div class="test-section">
            <h2>🧹 Test de la Réinitialisation</h2>
            <p><strong>⚠️ ATTENTION :</strong> Cette action supprimera toutes les données et réinitialisera l'application !</p>
            
            <button class="btn" onclick="testResetAPI()">
                🌐 Tester l'API de Reset
            </button>
            
            <div id="reset-status" class="status hidden"></div>
            <pre id="reset-result" class="hidden"></pre>
        </div>

        <!-- Test de Connectivité -->
        <div class="test-section">
            <h2>🌐 Test de Connectivité Serveur</h2>
            <p>Vérification que le serveur backend répond correctement.</p>
            
            <button class="btn" onclick="testServerHealth()">
                ❤️ Test Health Check
            </button>
            
            <div id="server-status" class="status hidden"></div>
        </div>
    </div>

    <script>
        // Test de l'API de reset
        async function testResetAPI() {
            const status = document.getElementById('reset-status');
            const result = document.getElementById('reset-result');
            
            status.className = 'status loading';
            status.textContent = '🌐 Test de l\'API de purge...';
            status.classList.remove('hidden');

            try {
                const response = await fetch('http://localhost:3001/api/database/purge', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    status.className = 'status success';
                    status.textContent = '✅ API de purge fonctionnelle !';
                    result.textContent = JSON.stringify(data, null, 2);
                    result.classList.remove('hidden');
                } else {
                    status.className = 'status error';
                    status.textContent = `❌ Erreur API: ${data.error || data.message}`;
                    result.textContent = JSON.stringify(data, null, 2);
                    result.classList.remove('hidden');
                }

            } catch (error) {
                status.className = 'status error';
                status.textContent = `❌ Erreur de connexion: ${error.message}`;
                result.textContent = error.stack;
                result.classList.remove('hidden');
            }
        }

        // Test de santé du serveur
        async function testServerHealth() {
            const status = document.getElementById('server-status');
            status.className = 'status loading';
            status.textContent = '🔍 Vérification de la santé du serveur...';
            status.classList.remove('hidden');

            try {
                const response = await fetch('http://localhost:3001/api/health');
                const data = await response.json();

                if (response.ok) {
                    status.className = 'status success';
                    status.textContent = `✅ Serveur opérationnel ! Status: ${data.status}`;
                } else {
                    status.className = 'status error';
                    status.textContent = `❌ Serveur en erreur: ${response.status}`;
                }

            } catch (error) {
                status.className = 'status error';
                status.textContent = `❌ Serveur inaccessible: ${error.message}`;
            }
        }

        function testTutorial() {
            alert('Ouvrez l\'application principale (http://localhost:5173) pour tester le tutoriel !');
        }

        function checkTutorialIcon() {
            alert('Vérifiez l\'icône d\'aide flottante en bas à droite de l\'application principale !');
        }
    </script>
</body>
</html> 
</html> 