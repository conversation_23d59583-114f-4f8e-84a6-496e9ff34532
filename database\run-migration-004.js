import { query } from '../server/config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runMigration004() {
  console.log('🚀 [Migration 004] Début de la refactorisation des attributions régulières...');
  
  try {
    // Lire le fichier de migration
    const migrationPath = path.join(__dirname, 'migrations', '004_refactor_regular_assignments.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Vérifier l'état actuel de la table
    console.log('🔍 [Migration 004] Vérification de l\'état actuel...');
    const currentStructure = await query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'regular_assignments' 
      ORDER BY ordinal_position
    `);
    
    console.log('📊 [Migration 004] Structure actuelle:', currentStructure.rows);
    
    // Compter les données existantes
    const countResult = await query('SELECT COUNT(*) as count FROM regular_assignments');
    const existingCount = parseInt(countResult.rows[0].count);
    console.log(`📈 [Migration 004] ${existingCount} attributions existantes à migrer`);
    
    if (existingCount > 0) {
      // Analyser les doublons potentiels
      const duplicatesAnalysis = await query(`
        SELECT employee_id, post_id, start_date, end_date, COUNT(*) as count
        FROM regular_assignments 
        WHERE is_active = true
        GROUP BY employee_id, post_id, start_date, end_date
        HAVING COUNT(*) > 1
        ORDER BY count DESC
      `);
      
      console.log(`🔍 [Migration 004] ${duplicatesAnalysis.rows.length} groupes de doublons détectés`);
      if (duplicatesAnalysis.rows.length > 0) {
        console.log('📋 [Migration 004] Aperçu des doublons:', duplicatesAnalysis.rows.slice(0, 5));
      }
    }
    
    // Exécuter la migration
    console.log('⚡ [Migration 004] Exécution de la migration...');
    await query(migrationSQL);
    
    // Vérifier le résultat
    console.log('✅ [Migration 004] Vérification du résultat...');
    const newStructure = await query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'regular_assignments' 
      ORDER BY ordinal_position
    `);
    
    console.log('📊 [Migration 004] Nouvelle structure:', newStructure.rows);
    
    const newCount = await query('SELECT COUNT(*) as count FROM regular_assignments');
    const finalCount = parseInt(newCount.rows[0].count);
    console.log(`📈 [Migration 004] ${finalCount} attributions après migration`);
    
    // Analyser les résultats
    const sampleData = await query(`
      SELECT id, employee_id, post_id, days_of_week, start_date, end_date
      FROM regular_assignments 
      LIMIT 5
    `);
    
    console.log('📋 [Migration 004] Échantillon des nouvelles données:');
    sampleData.rows.forEach(row => {
      console.log(`  - Employé: ${row.employee_id}, Poste: ${row.post_id}, Jours: [${row.days_of_week.join(',')}]`);
    });
    
    // Statistiques finales
    const stats = await query(`
      SELECT 
        COUNT(*) as total_assignments,
        AVG(array_length(days_of_week, 1)) as avg_days_per_assignment,
        MIN(array_length(days_of_week, 1)) as min_days,
        MAX(array_length(days_of_week, 1)) as max_days
      FROM regular_assignments
    `);
    
    console.log('📊 [Migration 004] Statistiques finales:', stats.rows[0]);
    console.log('🎉 [Migration 004] Migration terminée avec succès !');
    
    return {
      success: true,
      originalCount: existingCount,
      finalCount: finalCount,
      reduction: existingCount - finalCount,
      stats: stats.rows[0]
    };
    
  } catch (error) {
    console.error('❌ [Migration 004] Erreur lors de la migration:', error);
    throw error;
  }
}

// Exécuter si appelé directement
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigration004()
    .then(result => {
      console.log('✅ Migration 004 réussie:', result);
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Migration 004 échouée:', error);
      process.exit(1);
    });
}

export { runMigration004 }; 