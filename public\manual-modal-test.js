// 🧪 Test manuel simple pour la modale
console.log('🚀 [MANUAL-TEST] Script de test manuel de la modale chargé');

// Fonction de test simple
function testModalManually() {
    console.log('🧪 [MANUAL-TEST] === TEST MANUEL DE LA MODALE ===');
    
    // 1. Vérifier la disponibilité
    console.log('📋 [MANUAL-TEST] 1. Vérification de la disponibilité...');
    console.log('TeamCalendarApp:', !!window.TeamCalendarApp);
    console.log('teamCalendarApp:', !!window.teamCalendarApp);
    console.log('modalFunctionalities:', !!window.modalFunctionalities);
    
    // 2. Forcer l'intégration si nécessaire
    if (!window.modalFunctionalities && typeof modalFunctionalities !== 'undefined') {
        window.modalFunctionalities = modalFunctionalities;
        console.log('✅ [MANUAL-TEST] modalFunctionalities rendu disponible globalement');
    }
    
    // 3. Test d'ouverture simple
    if (window.modalFunctionalities && typeof window.modalFunctionalities.openAssignmentContextModal === 'function') {
        console.log('🧪 [MANUAL-TEST] Test d\'ouverture de la modale...');
        
        const testData = {
            postData: { id: 'test-post', label: 'Poste de Test' },
            employeeId: 'test-employee',
            employeeName: 'Employé Test',
            dateKey: '2025-07-18',
            position: { x: 100, y: 100 }
        };
        
        try {
            window.modalFunctionalities.openAssignmentContextModal(testData);
            console.log('✅ [MANUAL-TEST] Modale ouverte avec succès !');
            
            // Vérifier que la modale est visible
            setTimeout(() => {
                const modal = document.getElementById('assignment-context-modal');
                if (modal) {
                    console.log('✅ [MANUAL-TEST] Modale trouvée dans le DOM');
                    console.log('Visible:', !modal.classList.contains('hidden'));
                    console.log('Style display:', modal.style.display);
                    
                    // Test des boutons
                    const buttons = modal.querySelectorAll('[data-action]');
                    console.log(`✅ [MANUAL-TEST] ${buttons.length} boutons trouvés`);
                    
                    buttons.forEach(btn => {
                        console.log(`- Bouton: ${btn.dataset.action}`);
                    });
                } else {
                    console.error('❌ [MANUAL-TEST] Modale non trouvée dans le DOM');
                }
            }, 500);
            
        } catch (error) {
            console.error('❌ [MANUAL-TEST] Erreur lors de l\'ouverture:', error);
        }
    } else {
        console.error('❌ [MANUAL-TEST] Fonction openAssignmentContextModal non disponible');
    }
}

// Fonction pour fermer la modale
function closeModal() {
    const modal = document.getElementById('assignment-context-modal');
    if (modal) {
        modal.classList.add('hidden');
        modal.style.display = 'none';
        console.log('✅ [MANUAL-TEST] Modale fermée');
    } else {
        console.log('⚠️ [MANUAL-TEST] Aucune modale à fermer');
    }
}

// Fonction pour tester un bouton spécifique
function testButton(action) {
    const modal = document.getElementById('assignment-context-modal');
    if (!modal) {
        console.error('❌ [MANUAL-TEST] Modale non trouvée');
        return;
    }
    
    const button = modal.querySelector(`[data-action="${action}"]`);
    if (button) {
        console.log(`🧪 [MANUAL-TEST] Test du bouton ${action}...`);
        button.click();
        console.log(`✅ [MANUAL-TEST] Bouton ${action} cliqué`);
    } else {
        console.error(`❌ [MANUAL-TEST] Bouton ${action} non trouvé`);
    }
}

// Rendre les fonctions disponibles globalement
window.testModalManually = testModalManually;
window.closeModal = closeModal;
window.testButton = testButton;

console.log('✅ [MANUAL-TEST] Fonctions disponibles:');
console.log('- testModalManually() : Test complet');
console.log('- closeModal() : Fermer la modale');
console.log('- testButton(action) : Tester un bouton spécifique');
console.log('  Actions disponibles: "regular", "temporary", "replacement", "cancel", "close"');

// Test automatique après 2 secondes
setTimeout(() => {
    console.log('🚀 [MANUAL-TEST] Lancement automatique du test...');
    testModalManually();
}, 2000);
