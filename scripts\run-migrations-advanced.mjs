import { pool, closePool } from '../server/config/database.js';
import { readdirSync, readFileSync } from 'fs';
import { join } from 'path';

// Couleurs pour les logs
const colors = {
    red: '\x1b[31m', green: '\x1b[32m', yellow: '\x1b[33m',
    blue: '\x1b[34m', magenta: '\x1b[35m', cyan: '\x1b[36m', reset: '\x1b[0m'
};
const log = {
    info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
    success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
    warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
    error: (msg) => console.error(`${colors.red}❌ ${msg}${colors.reset}`),
    title: (msg) => console.log(`${colors.magenta}🚀 ${msg}${colors.reset}`)
};

const MIGRATIONS_DIR = join(process.cwd(), 'database', 'migrations');

async function getExecutedMigrations(client) {
    await client.query(`
        CREATE TABLE IF NOT EXISTS schema_migrations (
            version VARCHAR(255) PRIMARY KEY,
            applied_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        );
    `);
    const result = await client.query('SELECT version FROM schema_migrations');
    return result.rows.map(r => r.version);
}

async function run() {
    const client = await pool.connect();
    log.title('Démarrage du système de migration avancé');

    try {
        await client.query('BEGIN');

        const executedMigrations = await getExecutedMigrations(client);
        log.info(`Migrations déjà exécutées: ${executedMigrations.length > 0 ? executedMigrations.join(', ') : 'Aucune'}`);

        const migrationFiles = readdirSync(MIGRATIONS_DIR)
            .filter(file => file.endsWith('.sql'))
            .sort();

        let appliedCount = 0;
        for (const file of migrationFiles) {
            if (executedMigrations.includes(file)) {
                continue;
            }

            log.info(`Application de la migration: ${file}...`);
            const sql = readFileSync(join(MIGRATIONS_DIR, file), 'utf-8');
            await client.query(sql);
            await client.query('INSERT INTO schema_migrations (version) VALUES ($1)', [file]);
            log.success(`Migration ${file} appliquée avec succès.`);
            appliedCount++;
        }

        if (appliedCount === 0) {
            log.success('La base de données est déjà à jour.');
        } else {
            log.success(`${appliedCount} nouvelle(s) migration(s) appliquée(s).`);
        }

        await client.query('COMMIT');
    } catch (err) {
        await client.query('ROLLBACK');
        log.error('Erreur durant la migration. Annulation des changements.');
        log.error(err.stack);
        process.exit(1);
    } finally {
        client.release();
        await closePool();
        log.title('Migration terminée.');
    }
}

run(); 