#!/usr/bin/env node
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import pg from 'pg';

const { Client } = pg;
const __dirname = dirname(fileURLToPath(import.meta.url));

// Configuration flexible de la base de données
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'team_calendar',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'admin123'
};

async function migrateLogs() {
  console.log('🔄 [Migration Logs] Démarrage...');
  
  // Test de connexion PostgreSQL
  const client = new Client(dbConfig);
  
  try {
    console.log('📡 [Migration] Test de connexion PostgreSQL...');
    await client.connect();
    console.log('✅ [Migration] Connexion PostgreSQL réussie');
    
    // Vérifier si la table existe déjà
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'logs'
      );
    `);
    
    if (tableCheck.rows[0].exists) {
      console.log('ℹ️  [Migration] Table logs déjà existante');
      
      // Vérifier si la fonction existe
      const functionCheck = await client.query(`
        SELECT EXISTS (
          SELECT FROM pg_proc WHERE proname = 'ai_ranked_logs'
        );
      `);
      
      if (!functionCheck.rows[0].exists) {
        console.log('⚡ [Migration] Création de la fonction ai_ranked_logs...');
        await client.query(getFunctionSQL());
        console.log('✅ [Migration] Fonction ai_ranked_logs créée');
      }
      
      console.log('✅ [Migration] Système de logs déjà opérationnel');
      return;
    }
    
    console.log('📋 [Migration] Création de la table logs...');
    
    // Créer la table logs
    await client.query(`
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
      
      CREATE TABLE logs (
          id           BIGSERIAL PRIMARY KEY,
          session_id   UUID      NOT NULL,
          source       TEXT      NOT NULL,
          level        TEXT      NOT NULL,
          message      TEXT      NOT NULL,
          data         JSONB     DEFAULT '{}'::jsonb,
          ts           TIMESTAMPTZ DEFAULT now(),
          capture_mode TEXT      DEFAULT 'full'
      );
      
      CREATE INDEX logs_session_idx ON logs(session_id);
      CREATE INDEX logs_level_idx ON logs(level);
      CREATE INDEX logs_ts_idx ON logs(ts);
      CREATE INDEX logs_source_idx ON logs(source);
    `);
    
    console.log('✅ [Migration] Table logs créée');
    
    // Créer la fonction ai_ranked_logs
    console.log('⚡ [Migration] Création de la fonction ai_ranked_logs...');
    await client.query(getFunctionSQL());
    console.log('✅ [Migration] Fonction ai_ranked_logs créée');
    
    // Créer la fonction de nettoyage
    await client.query(`
      CREATE OR REPLACE FUNCTION cleanup_old_logs(days_to_keep INTEGER DEFAULT 7)
      RETURNS VOID AS $$
      BEGIN
          DELETE FROM logs WHERE ts < NOW() - INTERVAL '1 day' * days_to_keep;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    console.log('✅ [Migration] Fonction cleanup_old_logs créée');
    
    // Insérer quelques logs de test
    console.log('📝 [Test] Ajout de logs de test...');
    const sessionId = '12345678-1234-1234-1234-123456789abc';
    
    await client.query(`
      INSERT INTO logs (session_id, source, level, message, data) VALUES
      ($1, 'backend', 'info', 'Serveur démarré avec succès', '{"port": 3001}'),
      ($1, 'frontend', 'warn', 'Composant non monté', '{"component": "TestComponent"}'),
      ($1, 'browser', 'error', 'Erreur de réseau', '{"url": "/api/test"}'),
      ($1, 'backend', 'debug', 'Requête API reçue', '{"endpoint": "/api/debug"}'),
      ($1, 'frontend', 'info', 'Page chargée', '{"route": "/logs"}')
    `, [sessionId]);
    
    console.log('✅ [Test] Logs de test ajoutés');
    
    // Test de la fonction de scoring
    console.log('🎯 [Test] Test du scoring IA...');
    const scoredLogs = await client.query('SELECT * FROM ai_ranked_logs($1, $2)', [sessionId, 'full']);
    console.log(`✅ [Test] ${scoredLogs.rows.length} logs scorés et triés`);
    
    // Afficher les logs triés
    console.log('📊 [Résultat] Logs triés par score:');
    scoredLogs.rows.slice(0, 5).forEach(log => {
      console.log(`   ${log.level.toUpperCase().padEnd(5)} [${log.source.padEnd(8)}] Score: ${log.score} - ${log.message}`);
    });
    
    console.log('\n🎉 [Succès] Système de logs complètement opérationnel!');
    console.log('📍 [Info] Interface accessible via: http://localhost:5173/logs');
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.error('\n❌ [Erreur] PostgreSQL non accessible!');
      console.error('🔧 [Solution] Démarrez PostgreSQL avec:');
      console.error('   • Windows: services.msc → Démarrer PostgreSQL');
      console.error('   • ou: pg_ctl start -D "C:\\Program Files\\PostgreSQL\\[version]\\data"');
      console.error('   • ou: net start postgresql-[version]');
    } else if (error.code === '3D000') {
      console.error('\n❌ [Erreur] Base de données "team_calendar" non trouvée!');
      console.error('🔧 [Solution] Créez la base avec: createdb team_calendar');
    } else {
      console.error('❌ [Erreur] Échec de la migration:', error.message);
    }
    process.exit(1);
  } finally {
    await client.end();
  }
}

function getFunctionSQL() {
  return `
    CREATE OR REPLACE FUNCTION ai_ranked_logs(
            p_session  UUID,
            p_mode     TEXT DEFAULT 'ai'
    ) RETURNS TABLE(
            id     BIGINT,
            ts     TIMESTAMPTZ,
            level  TEXT,
            source TEXT,
            message TEXT,
            score  INT
    ) AS $$
    WITH base AS (
       SELECT *,
              CASE level
                WHEN 'fatal' THEN 100
                WHEN 'error' THEN 80
                WHEN 'warn'  THEN 60
                WHEN 'info'  THEN 30
                ELSE 10
              END                              AS lvl_score,
              ROW_NUMBER() OVER (PARTITION BY message ORDER BY id)
                                               AS dup_rank
       FROM   logs
       WHERE  session_id   = p_session
       AND    capture_mode = p_mode
    )
    SELECT id, ts, level, source, message,
           lvl_score - GREATEST(dup_rank-1,0)*5 AS score
    FROM   base
    ORDER  BY score DESC, id DESC;
    $$ LANGUAGE sql STABLE;
  `;
}

// Exécuter la migration
migrateLogs().catch(console.error); 