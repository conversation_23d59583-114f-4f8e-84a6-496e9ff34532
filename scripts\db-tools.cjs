console.log('🛠️  OUTILS DE GESTION BASE DE DONNÉES PostgreSQL DISTANTE');
console.log('🌐 Serveur: *************:5432 - Base: glive_db');
console.log('='.repeat(60));

console.log('\n📋 SCRIPTS DISPONIBLES:');
console.log('-'.repeat(30));

const tools = [
  {
    command: 'npm run db:check',
    description: '🔍 Diagnostic complet: connexion, tables, colonnes, données',
    usage: 'Vérification de l\'état de la base de données distante'
  },
  {
    command: 'npm run db:test',
    description: '🔗 Test rapide de connexion PostgreSQL + latence',
    usage: 'Vérification simple si le serveur distant répond'
  },
  {
    command: 'npm run db:purge',
    description: '🧹 Purge complète: garde seulement 5 employés de base',
    usage: '⚠️  DANGEREUX - Nettoie toutes les données sur serveur distant'
  },
  {
    command: 'npm run migrate',
    description: '🏗️  Exécute les migrations pour créer/modifier les tables',
    usage: 'Création ou mise à jour de la structure sur serveur distant'
  },
  {
    command: 'npm run db:verify',
    description: '✅ Vérifie l\'intégrité de la base après migration',
    usage: 'Validation post-migration sur serveur distant'
  }
];

tools.forEach((tool, i) => {
  console.log(`\n${i+1}. ${tool.command}`);
  console.log(`   ${tool.description}`);
  console.log(`   💡 ${tool.usage}`);
});

console.log('\n🔄 ORDRE RECOMMANDÉ POUR DIAGNOSTIC:');
console.log('-'.repeat(40));
console.log('1. npm run db:test     (Test connexion rapide)');
console.log('2. npm run db:check    (Diagnostic complet)');
console.log('3. npm run migrate     (Créer tables si manquantes)');
console.log('4. npm run server      (Lancer l\'API)');

console.log('\n🆘 EN CAS DE PROBLÈME:');
console.log('-'.repeat(25));
console.log('📊 Données corrompues  → npm run db:purge');
console.log('🔌 Connexion échouée   → Vérifier réseau/firewall');
console.log('🏗️  Tables manquantes   → npm run migrate');
console.log('🔍 État de la base     → npm run db:check');

console.log('\n📁 FICHIERS GÉNÉRÉS:');
console.log('-'.repeat(20));
console.log('• database-diagnostic-*.json  (Rapports de diagnostic)');
console.log('• purge-report-*.json         (Rapports de purge)');

console.log('\n🎯 STATUT ACTUEL (Serveur distant):');
console.log('-'.repeat(40));

// Test rapide du statut avec les vraies infos
const { Pool } = require('pg');

const pool = new Pool({
  host: '*************',
  port: 5432,
  database: 'glive_db',
  user: 'postgres',
  password: 'SebbZ12342323!!'
});

(async () => {
  try {
    const startTime = Date.now();
    const client = await pool.connect();
    const connectTime = Date.now() - startTime;
    
    // Compter les tables
    const tables = await client.query(`
      SELECT COUNT(*) as count FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    
    // Compter les données si tables existent
    let dataStatus = '';
    if (parseInt(tables.rows[0].count) > 0) {
      try {
        const shifts = await client.query('SELECT COUNT(*) as count FROM shifts');
        const employees = await client.query('SELECT COUNT(*) as count FROM employees');
        dataStatus = ` (${employees.rows[0].count} employés, ${shifts.rows[0].count} quarts)`;
      } catch (e) {
        dataStatus = ' (erreur comptage données)';
      }
    }
    
    console.log(`✅ PostgreSQL: CONNECTÉ (${connectTime}ms)`);
    console.log(`🌐 Serveur: *************:5432`);
    console.log(`📊 Tables: ${tables.rows[0].count}${dataStatus}`);
    
    client.release();
    await pool.end();
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ PostgreSQL: CONNEXION REFUSÉE → Vérifier firewall/réseau');
    } else if (error.code === 'ENOTFOUND') {
      console.log('❌ Serveur: INTROUVABLE → Vérifier IP *************');
    } else if (error.code === '3D000') {
      console.log('⚠️  Base glive_db: INEXISTANTE → Créer la base');
    } else if (error.code === '28P01') {
      console.log('❌ Authentification: ÉCHOUÉE → Vérifier mot de passe');
    } else {
      console.log(`❌ Erreur: ${error.message}`);
    }
  }
})();

console.log('\n⚠️  IMPORTANT: Connexion à un serveur PostgreSQL DISTANT');
console.log('🔐 Credentials inclus dans les scripts pour faciliter les tests');
console.log('💡 Pour production: utilisez des variables d\'environnement');
console.log('='.repeat(60)); 