// 📝 Système de logs centralisé optimisé pour IA
// Compression intelligente, récupération efficace, pas de spam

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4
}

export interface LogConfig {
  level: LogLevel;
  enableColors: boolean;
  enableTimestamp: boolean;
  enableContext: boolean;
  maxHistorySize: number;
  // Nouvelles options pour l'optimisation IA
  enableCompression: boolean;
  aiOptimized: boolean;
  maxTokens: number;
  enableSmartFiltering: boolean;
}

export interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  context: string;
  message: string;
  data?: any;
  // Nouvelles propriétés pour l'optimisation
  hash?: string;
  count?: number;
  firstSeen?: Date;
  lastSeen?: Date;
}

export interface AIDebugPackage {
  summary: {
    totalLogs: number;
    errorCount: number;
    warningCount: number;
    timeRange: { start: Date; end: Date };
    topContexts: Array<{ context: string; count: number }>;
  };
  criticalIssues: LogEntry[];
  patternAnalysis: Array<{ pattern: string; count: number; examples: LogEntry[] }>;
  compressedLogs: LogEntry[];
  tokenEstimate: number;
}

class Logger {
  private config: LogConfig;
  private history: LogEntry[] = [];
  private compressedHistory: Map<string, LogEntry> = new Map();
  
  // Couleurs pour les différents niveaux
  private colors = {
    [LogLevel.DEBUG]: '\x1b[36m',    // Cyan
    [LogLevel.INFO]: '\x1b[32m',     // Vert
    [LogLevel.WARN]: '\x1b[33m',     // Jaune
    [LogLevel.ERROR]: '\x1b[31m',    // Rouge
    [LogLevel.CRITICAL]: '\x1b[35m', // Magenta
    reset: '\x1b[0m'
  };
  
  // Emojis pour les niveaux
  private emojis = {
    [LogLevel.DEBUG]: '🔍',
    [LogLevel.INFO]: '✅',
    [LogLevel.WARN]: '⚠️',
    [LogLevel.ERROR]: '❌',
    [LogLevel.CRITICAL]: '🚨'
  };
  
  // Labels pour les niveaux
  private labels = {
    [LogLevel.DEBUG]: 'DEBUG',
    [LogLevel.INFO]: 'INFO',
    [LogLevel.WARN]: 'WARN',
    [LogLevel.ERROR]: 'ERROR',
    [LogLevel.CRITICAL]: 'CRITICAL'
  };

  constructor(config: Partial<LogConfig> = {}) {
    // Niveau par défaut plus strict pour une console plus propre
    const defaultLevel = LogLevel.WARN; // Seulement warnings et erreurs par défaut
    
    this.config = {
      level: defaultLevel,
      enableColors: true,
      enableTimestamp: true,
      enableContext: true,
      maxHistorySize: 500, // Réduit pour l'optimisation
      enableCompression: true,
      aiOptimized: true,
      maxTokens: 8000, // Limite de tokens pour l'IA
      enableSmartFiltering: true,
      ...config
    };
  }

  // ✅ MÉTHODES PRINCIPALES DE LOG
  
  debug(context: string, message: string, data?: any) {
    this.log(LogLevel.DEBUG, context, message, data);
  }
  
  info(context: string, message: string, data?: any) {
    this.log(LogLevel.INFO, context, message, data);
  }
  
  warn(context: string, message: string, data?: any) {
    this.log(LogLevel.WARN, context, message, data);
  }
  
  error(context: string, message: string, data?: any) {
    this.log(LogLevel.ERROR, context, message, data);
  }
  
  critical(context: string, message: string, data?: any) {
    this.log(LogLevel.CRITICAL, context, message, data);
  }

  // ✅ MÉTHODE PRINCIPALE DE LOG OPTIMISÉE
  
  private log(level: LogLevel, context: string, message: string, data?: any) {
    // Vérifier si le niveau est suffisant
    if (level < this.config.level) {
      return;
    }
    
    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      context,
      message,
      data
    };

    // Compression intelligente pour éviter le spam
    if (this.config.enableCompression) {
      this.addCompressedEntry(entry);
    } else {
      this.addToHistory(entry);
    }
    
    // Affichage console uniquement pour les niveaux importants ou en mode debug
    if (level >= LogLevel.WARN || this.config.level <= LogLevel.DEBUG) {
      const formattedMessage = this.formatMessage(entry);
      
      switch (level) {
        case LogLevel.DEBUG:
          console.debug(formattedMessage, data || '');
          break;
        case LogLevel.INFO:
          console.info(formattedMessage, data || '');
          break;
        case LogLevel.WARN:
          console.warn(formattedMessage, data || '');
          break;
        case LogLevel.ERROR:
        case LogLevel.CRITICAL:
          console.error(formattedMessage, data || '');
          break;
      }
    }
  }

  // ✅ COMPRESSION INTELLIGENTE - ÉVITE LE SPAM
  
  private addCompressedEntry(entry: LogEntry) {
    const hash = this.generateLogHash(entry);
    entry.hash = hash;

    if (this.compressedHistory.has(hash)) {
      // Log existant - incrémenter le compteur
      const existing = this.compressedHistory.get(hash)!;
      existing.count = (existing.count || 1) + 1;
      existing.lastSeen = entry.timestamp;
    } else {
      // Nouveau log
      entry.count = 1;
      entry.firstSeen = entry.timestamp;
      entry.lastSeen = entry.timestamp;
      this.compressedHistory.set(hash, entry);
    }

    // Ajouter à l'historique standard aussi (limité)
    this.addToHistory(entry);
  }

  private generateLogHash(entry: LogEntry): string {
    // Créer un hash basé sur le contexte et le message (sans timestamp)
    const hashBase = `${entry.level}-${entry.context}-${entry.message}`;
    return btoa(hashBase).substring(0, 16);
  }

  // ✅ FORMATAGE DES MESSAGES OPTIMISÉ
  
  private formatMessage(entry: LogEntry): string {
    let parts: string[] = [];
    
    // Timestamp simplifié pour l'IA
    if (this.config.enableTimestamp) {
      const time = this.config.aiOptimized 
        ? entry.timestamp.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })
        : entry.timestamp.toLocaleTimeString('fr-FR', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            // fractionalSecondDigits removed for compatibility
          });
      parts.push(`[${time}]`);
    }
    
    // Niveau avec emoji
    const emoji = this.emojis[entry.level];
    const label = this.labels[entry.level];
    
    if (this.config.enableColors && typeof window === 'undefined') {
      const color = this.colors[entry.level];
      const reset = this.colors.reset;
      parts.push(`${color}${emoji} ${label}${reset}`);
    } else {
      parts.push(`${emoji} ${label}`);
    }
    
    // Contexte
    if (this.config.enableContext && entry.context) {
      parts.push(`[${entry.context}]`);
    }
    
    // Message avec compteur si compressé
    let message = entry.message;
    if (entry.count && entry.count > 1) {
      message += ` (×${entry.count})`;
    }
    parts.push(message);
    
    return parts.join(' ');
  }

  // ✅ GESTION DE L'HISTORIQUE OPTIMISÉE
  
  private addToHistory(entry: LogEntry) {
    this.history.push(entry);
    
    // Limiter la taille avec rotation intelligente
    if (this.history.length > this.config.maxHistorySize) {
      // Garder les erreurs critiques et récents
      this.history = this.history
        .filter(e => e.level >= LogLevel.ERROR || 
                    (Date.now() - e.timestamp.getTime()) < 300000) // 5 minutes
        .slice(-this.config.maxHistorySize);
    }
  }
  
  getHistory(level?: LogLevel): LogEntry[] {
    if (level !== undefined) {
      return this.history.filter(entry => entry.level >= level);
    }
    return [...this.history];
  }
  
  clearHistory() {
    this.history = [];
    this.compressedHistory.clear();
  }

  // ✅ PACKAGE OPTIMISÉ POUR IA - RÉCUPÉRATION INTELLIGENTE
  
  getAIDebugPackage(): AIDebugPackage {
    const now = new Date();
    const allLogs = Array.from(this.compressedHistory.values());
    
    // Analyse des patterns
    const contextCounts = new Map<string, number>();
    const patterns = new Map<string, LogEntry[]>();
    
    allLogs.forEach(log => {
      contextCounts.set(log.context, (contextCounts.get(log.context) || 0) + (log.count || 1));
      
      // Grouper par patterns similaires
      const pattern = `${log.level}-${log.context}`;
      if (!patterns.has(pattern)) {
        patterns.set(pattern, []);
      }
      patterns.get(pattern)!.push(log);
    });

    // Filtrage intelligent pour l'IA
    const criticalIssues = allLogs
      .filter(log => log.level >= LogLevel.ERROR)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 10);

    const patternAnalysis = Array.from(patterns.entries())
      .map(([pattern, logs]) => ({
        pattern,
        count: logs.reduce((sum, log) => sum + (log.count || 1), 0),
        examples: logs.slice(0, 2) // Limiter les exemples
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Logs compressés avec filtrage intelligent
    const compressedLogs = allLogs
      .filter(log => {
        // Garder les erreurs, warnings récents, et logs fréquents
        return log.level >= LogLevel.WARN || 
               (log.count && log.count >= 3) ||
               (now.getTime() - log.timestamp.getTime()) < 600000; // 10 minutes
      })
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 50); // Limiter pour les tokens

    const timeRange = allLogs.length > 0 ? {
      start: new Date(Math.min(...allLogs.map(l => l.timestamp.getTime()))),
      end: new Date(Math.max(...allLogs.map(l => l.timestamp.getTime())))
    } : { start: now, end: now };

    const topContexts = Array.from(contextCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([context, count]) => ({ context, count }));

    // Estimation des tokens (approximative)
    const tokenEstimate = this.estimateTokens(compressedLogs);

    return {
      summary: {
        totalLogs: allLogs.reduce((sum, log) => sum + (log.count || 1), 0),
        errorCount: allLogs.filter(l => l.level >= LogLevel.ERROR).length,
        warningCount: allLogs.filter(l => l.level === LogLevel.WARN).length,
        timeRange,
        topContexts
      },
      criticalIssues,
      patternAnalysis,
      compressedLogs,
      tokenEstimate
    };
  }

  private estimateTokens(logs: LogEntry[]): number {
    // Estimation approximative : 1 token ≈ 4 caractères
    return logs.reduce((total, log) => {
      const content = `${log.context} ${log.message} ${JSON.stringify(log.data || {})}`;
      return total + Math.ceil(content.length / 4);
    }, 0);
  }

  // ✅ MÉTHODES UTILITAIRES OPTIMISÉES

  createContextLogger(context: string) {
    return {
      debug: (message: string, data?: any) => this.debug(context, message, data),
      info: (message: string, data?: any) => this.info(context, message, data),
      warn: (message: string, data?: any) => this.warn(context, message, data),
      error: (message: string, data?: any) => this.error(context, message, data),
      critical: (message: string, data?: any) => this.critical(context, message, data)
    };
  }

  // ✅ CONFIGURATION DYNAMIQUE
  
  setLevel(level: LogLevel) {
    this.config.level = level;
    this.info('Logger', `Niveau de log changé vers ${this.labels[level]}`);
  }

  getLevel(): LogLevel {
    return this.config.level;
  }
  
  setConfig(newConfig: Partial<LogConfig>) {
    this.config = { ...this.config, ...newConfig };
  }

  // Performance monitoring
  time(label: string) {
    const start = performance.now();
    return {
      end: () => {
        const duration = performance.now() - start;
        this.debug('Performance', `${label}: ${duration.toFixed(2)}ms`);
        return duration;
      }
    };
  }
  
  // Log conditionnel
  logIf(condition: boolean, level: LogLevel, context: string, message: string, data?: any) {
    if (condition) {
      this.log(level, context, message, data);
    }
  }

  // Export pour sauvegarde/analyse
  exportForAnalysis(): string {
    const debugPackage = this.getAIDebugPackage();
    return JSON.stringify(debugPackage, null, 2);
  }
}

// ✅ INSTANCE GLOBALE OPTIMISÉE POUR IA
const isProduction = typeof window !== 'undefined' && window.location.hostname !== 'localhost';
const isDevelopment = typeof window !== 'undefined' && window.location.hostname === 'localhost';

export const logger = new Logger({
  // Configuration optimisée pour l'IA
  level: isProduction ? LogLevel.WARN : LogLevel.INFO,
  enableColors: !isProduction,
  enableTimestamp: true,
  enableContext: true,
  maxHistorySize: isProduction ? 200 : 500,
  enableCompression: true,
  aiOptimized: true,
  maxTokens: 8000,
  enableSmartFiltering: true
});

// ✅ LOGGERS SPÉCIALISÉS
export const apiLogger = logger.createContextLogger('API');
export const uiLogger = logger.createContextLogger('UI');
export const dbLogger = logger.createContextLogger('DB');

// ✅ EXPORT DES TYPES (déjà déclarés plus haut)

// ✅ CONFIGURATION GLOBALE POUR LE DÉVELOPPEMENT
if (typeof window !== 'undefined') {
  // Exposer le logger globalement pour le debug
  (window as any).logger = logger;
  
  // Commandes de debug optimisées pour l'IA
  (window as any).setLogLevel = (level: string) => {
    const levelMap: { [key: string]: LogLevel } = {
      'debug': LogLevel.DEBUG,
      'info': LogLevel.INFO,
      'warn': LogLevel.WARN,
      'error': LogLevel.ERROR,
      'critical': LogLevel.CRITICAL
    };
    
    if (levelMap[level.toLowerCase()]) {
      logger.setLevel(levelMap[level.toLowerCase()]);
    } else {
      logger.warn('Logger', `Niveau invalide: ${level}. Utilisez: debug, info, warn, error, critical`);
    }
  };
  
  (window as any).getLogHistory = () => logger.getHistory();
  (window as any).clearLogHistory = () => logger.clearHistory();
  
  // Nouvelle fonction pour récupérer le package IA
  (window as any).getAIDebugPackage = () => logger.getAIDebugPackage();
  (window as any).exportLogsForAI = () => {
    const debugPackage = logger.getAIDebugPackage();
    console.log('📊 Package de debug pour IA:', debugPackage);
    console.log('📋 Estimation tokens:', debugPackage.tokenEstimate);
    return logger.exportForAnalysis();
  };
}

logger.info('Logger', 'Système de logs optimisé IA initialisé');
