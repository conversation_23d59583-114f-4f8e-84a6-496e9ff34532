import json
from pathlib import Path
import shutil

# 📁 Dossier contenant le fichier `entries` et les fichiers *.js
history_dir = Path(r"C:\Users\<USER>\Desktop\BackupCursorUser\History\5548c1e8")
output_dir = Path.home() / "Desktop" / "Checkpoints_Extracted"
output_dir.mkdir(parents=True, exist_ok=True)

# 📄 Fichier entries
entries_path = history_dir / "entries.json"

if not entries_path.exists():
    print(f"[❌] Le fichier 'entries' est introuvable à cet emplacement : {entries_path}")
    exit(1)

# 🔁 Lecture du fichier entries.json
with open(entries_path, encoding="utf-8") as f:
    data = json.load(f)

entries = data.get("entries", [])
exported = 0
skipped = 0

for i, entry in enumerate(entries, start=1):
    file_id = entry["id"]  # ex: "IhbJ.js"
    source_file = history_dir / file_id

    if source_file.exists():
        dest_file = output_dir / f"checkpoint_{i:02d}_{file_id}"
        shutil.copy(source_file, dest_file)
        exported += 1
    else:
        print(f"[⚠️] Fichier manquant : {file_id}")
        skipped += 1

print(f"\n✅ {exported} checkpoint(s) exporté(s) dans : {output_dir}")
if skipped > 0:
    print(f"⚠️ {skipped} checkpoint(s) manquant(s) ignoré(s).")
