# Documentation - teamCalendarApp.ts

> **Fichier**: `src\teamCalendarApp.ts`

## 📚 Fonctions Documentées

### Fonction 2

**Description**: Nettoie le cache de rendu pour forcer un rafraîchissement complet
     *

**Exemple**:
```typescript
* ```typescript
     * TeamCalendarApp.clearRenderCache();
     * TeamCalendarApp.render(); // Rendu complet sans cache
     * ```
```

---

### Fonction 3

**Description**: Corrige les problèmes d'affichage des postes après un refresh
     *

**Retour**: `number` - Nombre de shifts corrigés
     *

**Exemple**:
```typescript
* ```typescript
     * const fixed = TeamCalendarApp.fixPostRefreshIssues();
     * console.log(`${fixed} shifts corrigés`);
     * ```
```

---

### Fonction 4

**Description**: Initialise l'application TeamCalendar
     *

**Retour**: `Promise<void>` - Promise résolue quand l'initialisation est terminée
     *

**Exemple**:
```typescript
* ```typescript
     * await TeamCalendarApp.init();
     * console.log('Application initialisée');
     * ```
```

---

## 🔧 Toutes les Fonctions

| Fonction | Ligne |
|----------|-------|
| `safeScheduleUpdate` | 654 |
| `renderSafe` | 673 |
| `checkDataIntegrity` | 689 |
| `quickRepair` | 696 |
| `clearRenderCache` | 745 |
| `fixPostRefreshIssues` | 763 |
| `emergencyFixUndefinedPostIds` | 828 |
| `saveFixedShifts` | 943 |
| `fixUndefinedPostIdsOnLoad` | 980 |
| `getEmployeeName` | 1071 |
| `fixInvalidAssignmentDates` | 1077 |
| `normalizeAssignment` | 1113 |
| `getPostById` | 1209 |
| `isSameDay` | 1265 |
| `getEmployeeRowHeightClass` | 1271 |
| `getEmployeeRowHeightValue` | 1281 |
| `isEmployeeOnVacation` | 1286 |
| `getAvailablePostsPerDay` | 1298 |
| `generateWeekDates` | 1366 |
| `getWeekNumber` | 1426 |
| `navigateWeek` | 1434 |
| `getCachedWeek` | 1472 |
| `setCachedWeek` | 1476 |
| `clearWeekCache` | 1492 |
| `saveCurrentWeek` | 1498 |
| `loadState` | 1606 |
| `loadStateFromLocalStorage` | 1849 |
| `init` | 1900 |
| `setupViewNavigationListeners` | 2116 |
| `goToToday` | 2163 |
| `setViewMode` | 2183 |
| `generateDayView` | 2205 |
| `generateMonthView` | 2222 |
| `updateDisplayText` | 2238 |
| `updateNavigationButtons` | 2279 |
| `checkConflictsBeforeDrop` | 2296 |
| `handleStrictSinglePostPolicy` | 2349 |
| `showReplacementOptions` | 2396 |
| `handleChoiceSelection` | 2417 |
| `handleStrictReplacement` | 2437 |
| `handleTemporaryReplacement` | 2462 |
| `handleAdvancedConflictResolution` | 2555 |
| `handleCompleteReplacement` | 2604 |
| `handleSimpleAddition` | 2621 |
| `handleSelectiveManagement` | 2654 |
| `handlePartialTimeManagement` | 2699 |
| `adjustTimeScheduling` | 2737 |
| `removeRegularAssignmentsFromDate` | 2756 |
| `setupRemoveAssignmentsModal` | 2777 |
| `confirmRemoveAssignments` | 2799 |
| `executeRemoveAssignments` | 2835 |
| `renderEmployeesForRemoval` | 2878 |
| `setupEmployeeCheckboxEvents` | 2924 |
| `openSingleAssignmentRemovalModal` | 2963 |
| `setupSingleRemovalModal` | 2995 |
| `confirmSingleRemoval` | 3016 |
| `executeSingleRemoval` | 3040 |
| `openTemplateModal` | 3079 |
| `renderTemplateFields` | 3116 |
| `addTemplateField` | 3163 |
| `removeTemplateField` | 3217 |
| `updateTemplatePreview` | 3223 |
| `saveEmployeeTemplate` | 3305 |
| `deleteEmployeeTemplate` | 3371 |
| `renderEmployeesManagementList` | 3385 |
| `openEmployeeAdvancedModal` | 3431 |
| `renderEmployeeFormFields` | 3468 |
| `handleAvatarUpload` | 3518 |
| `removeEmployeeAvatar` | 3534 |
| `saveEmployeeAdvanced` | 3544 |
| `deleteEmployee` | 3614 |
| `setupGlobalEscapeHandler` | 3635 |
| `setupNavigationListeners` | 3681 |
| `render` | 3711 |
| `actualRender` | 3723 |
| `renderEmployees` | 3743 |
| `renderScheduleGrid` | 3892 |
| `renderUnifiedCalendar` | 3988 |
| `diagnoseRegularAssignmentGrips` | 4379 |
| `ensureRegularAssignmentGrips` | 4454 |
| `createAndAttachGrip` | 4506 |
| `validateGripFunctionality` | 4524 |
| `attachGripEvents` | 4561 |
| `addSwapIconsToEmployees` | 4668 |
| `startEmployeeSwap` | 4711 |
| `attachSwapTargetEvents` | 4758 |
| `cleanupSwapMode` | 4782 |
| `setupEmployeeDragDrop` | 4811 |
| `isValidEmployeeName` | 4816 |
| `handleEmployeeReorder` | 4834 |
| `addSimplifiedEmployeeDragHandles` | 4844 |
| `diagnoseEmployeeDragState` | 4849 |
| `addDragHandlesToEmployees` | 4898 |
| `reorderEmployees` | 5297 |
| `reorderEmployeeRowsOnly` | 5315 |
| `reorderEmployeeRowsOnlyOptimized` | 5363 |
| `saveEmployeeOrder` | 5370 |
| `loadEmployeeOrder` | 5433 |
| `forceLoadEmployeeOrder` | 5551 |
| `diagnoseDragDropProtection` | 5558 |
| `createShiftElement` | 5574 |
| `createPostShiftElement` | 5796 |
| `createAvailablePostShiftHTML` | 5872 |
| `enableEmployeeSwapMode` | 5899 |
| `disableEmployeeSwapMode` | 5930 |
| `showSwapInstructions` | 5943 |
| `hideSwapInstructions` | 5974 |
| `attachSwapClickEvents` | 5981 |
| `detachSwapClickEvents` | 5993 |
| `handleSwapEmployeeClick` | 6002 |
| `executeEmployeeSwap` | 6031 |
| `handleAvailablePostShiftClick` | 6103 |
| `createPostShiftElementHTML` | 6130 |
| `handleDeleteShiftFromPost` | 6211 |
| `createAvailableShiftElement` | 6229 |
| `handleAvailableShiftClick` | 6247 |
| `getShiftState` | 6263 |
| `renderPostsForConfig` | 6274 |
| `isPostUsed` | 6402 |
| `_escapeHtml` | 6425 |
| `renderVacationPeriods` | 6441 |
| `renderAssignments` | 6693 |
| `renderEmployeeTemplates` | 6922 |
| `renderEmployeesManagement` | 7071 |
| `isEmployeeUsed` | 7221 |
| `renderGeneralSettings` | 7240 |
| `renderVacationsInSettingsModal` | 7455 |
| `attachAllEventListeners` | 7696 |
| `checkReplacementReintegration` | 7707 |
| `handleReplacementReintegration` | 7749 |
| `executeReplacementReintegration` | 7784 |
| `showSuccessModal` | 7843 |
| `showErrorModal` | 7853 |
| `showConfirmModal` | 7863 |
| `showCustomModal` | 7884 |
| `closeCustomModal` | 8020 |
| `setupDragAndDrop` | 8034 |
| `setupPostDragDrop` | 8323 |
| `isValidUUID` | 8449 |
| `enableAgendaPreviewMode` | 8456 |
| `addGhostPreviewsToCompatibleCells` | 8481 |
| `isPostCompatibleWithDay` | 8509 |
| `addGhostPreviewToCell` | 8525 |
| `enableCellHoverTracking` | 8552 |
| `highlightTargetCell` | 8587 |
| `unhighlightTargetCell` | 8606 |
| `disableAgendaPreviewMode` | 8616 |
| `disableCellHoverTracking` | 8644 |
| `setupCentralizedDropZone` | 8659 |
| `detectDropDateFromPosition` | 8981 |
| `ensureDropZonesExist` | 9038 |
| `createTemporaryDropZones` | 9071 |
| `findEmployeeAtPoint` | 9096 |
| `findClosestEmployee` | 9108 |
| `assignFullPostAndSwap` | 9113 |
| `handleAddShiftPrompt` | 9160 |
| `addShift` | 9203 |
| `addShiftByDateKey` | 9220 |
| `addStandardShift` | 9311 |
| `handleShiftClick` | 9324 |
| `handleDeleteShift` | 9350 |
| `updateStats` | 9389 |
| `initScrollIndicator` | 9524 |
| `checkScroll` | 9528 |
| `handleManageVacation` | 9544 |
| `openSettingsModal` | 9588 |
| `handleColorChange` | 9616 |
| `displayGlobalVacations` | 9656 |
| `handleVacationsInSettingsModal` | 9691 |
| `renderAssignmentsInSettingsModal` | 9696 |
| `handleAddVacationPeriod` | 9813 |
| `handleAddAssignment` | 9817 |
| `handlePostEdit` | 9822 |
| `handlePostDelete` | 9828 |
| `handleDeleteVacation` | 9877 |
| `handleEditAssignment` | 9887 |
| `handleDeleteAssignment` | 9931 |
| `handleMovePost` | 9984 |
| `handleDuplicatePost` | 9997 |
| `handlePurgeShifts` | 10013 |
| `confirmPurgeShifts` | 10034 |
| `executePurge` | 10099 |
| `handleEditEmployee` | 10136 |
| `renderExtraFields` | 10154 |
| `addExtraField` | 10165 |
| `addExtraFieldElement` | 10169 |
| `saveEmployee` | 10190 |
| `openAssignmentContextModal` | 10229 |
| `handleAssignmentContextConfirm` | 10303 |
| `assignFullPostOnce` | 10389 |
| `assignSingleCell` | 10450 |
| `createRegularAssignment` | 10554 |
| `testRegularAssignment` | 10659 |
| `applyRegularAssignment` | 10718 |
| `updateAssignmentIdInShifts` | 10758 |
| `shouldApplyAssignmentToDay` | 10781 |
| `applyRegularAssignmentsForCurrentWeek` | 10914 |
| `cleanupRegularAssignments` | 11210 |
| `applyRegularAssignmentsForWeek` | 11279 |
| `getWeekStartDayNumber` | 11398 |
| `setWeekStartDay` | 11420 |
| `getTimezone` | 11440 |
| `setTimezone` | 11446 |
| `synchronizeWeekStartSettings` | 11471 |
| `showLoadingIndicator` | 11497 |
| `hideLoadingIndicator` | 11558 |
| `getShiftsToSave` | 11566 |
| `getShiftsToSaveInRange` | 11618 |
| `saveShiftsInBatches` | 11667 |
| `addEmergencyFixButton` | 11722 |

