// 🔧 Script de débogage pour l'intégration modale
console.log('🚀 [DEBUG-MODAL] Script de débogage de l\'intégration modale chargé');

// Fonction principale de diagnostic
function diagnoseModalIntegration() {
    console.log('🔍 [DEBUG-MODAL] === DIAGNOSTIC COMPLET DE L\'INTÉGRATION MODALE ===');
    
    const results = {
        teamCalendarApp: false,
        modalFunctionalities: false,
        globalModalFunctionalities: false,
        assignmentModal: false,
        dragDropElements: false,
        errors: []
    };
    
    // 1. Vérifier TeamCalendarApp
    console.log('📋 [DEBUG-MODAL] 1. Vérification TeamCalendarApp...');
    if (window.TeamCalendarApp) {
        results.teamCalendarApp = true;
        console.log('✅ [DEBUG-MODAL] TeamCalendarApp disponible');
        console.log('🔍 [DEBUG-MODAL] Méthodes disponibles:', Object.keys(window.TeamCalendarApp).filter(key => typeof window.TeamCalendarApp[key] === 'function').slice(0, 10));
    } else if (window.teamCalendarApp) {
        results.teamCalendarApp = true;
        console.log('✅ [DEBUG-MODAL] teamCalendarApp (minuscule) disponible');
        console.log('🔍 [DEBUG-MODAL] Méthodes disponibles:', Object.keys(window.teamCalendarApp).filter(key => typeof window.teamCalendarApp[key] === 'function').slice(0, 10));
    } else {
        results.errors.push('TeamCalendarApp non disponible globalement');
        console.error('❌ [DEBUG-MODAL] TeamCalendarApp non disponible');
    }
    
    // 2. Vérifier modalFunctionalities local
    console.log('📋 [DEBUG-MODAL] 2. Vérification modalFunctionalities local...');
    try {
        // Essayer d'accéder au module importé
        if (typeof modalFunctionalities !== 'undefined') {
            results.modalFunctionalities = true;
            console.log('✅ [DEBUG-MODAL] modalFunctionalities local disponible');
        } else {
            results.errors.push('modalFunctionalities local non accessible');
            console.error('❌ [DEBUG-MODAL] modalFunctionalities local non accessible');
        }
    } catch (error) {
        results.errors.push(`Erreur modalFunctionalities local: ${error.message}`);
        console.error('❌ [DEBUG-MODAL] Erreur modalFunctionalities local:', error);
    }
    
    // 3. Vérifier modalFunctionalities global
    console.log('📋 [DEBUG-MODAL] 3. Vérification modalFunctionalities global...');
    if (window.modalFunctionalities) {
        results.globalModalFunctionalities = true;
        console.log('✅ [DEBUG-MODAL] window.modalFunctionalities disponible');
        console.log('🔍 [DEBUG-MODAL] Méthodes disponibles:', Object.keys(window.modalFunctionalities).filter(key => typeof window.modalFunctionalities[key] === 'function'));
    } else {
        results.errors.push('window.modalFunctionalities non disponible');
        console.error('❌ [DEBUG-MODAL] window.modalFunctionalities non disponible');
    }
    
    // 4. Vérifier le modal d'attribution
    console.log('📋 [DEBUG-MODAL] 4. Vérification modal d\'attribution...');
    const assignmentModal = document.getElementById('assignment-context-modal');
    if (assignmentModal) {
        results.assignmentModal = true;
        console.log('✅ [DEBUG-MODAL] Modal assignment-context-modal trouvé');
        console.log('🔍 [DEBUG-MODAL] Classes:', Array.from(assignmentModal.classList));
        console.log('🔍 [DEBUG-MODAL] Style display:', assignmentModal.style.display);
    } else {
        results.errors.push('Modal assignment-context-modal non trouvé');
        console.error('❌ [DEBUG-MODAL] Modal assignment-context-modal non trouvé');
    }
    
    // 5. Vérifier les éléments de drag & drop
    console.log('📋 [DEBUG-MODAL] 5. Vérification éléments drag & drop...');
    const calendarCells = document.querySelectorAll('[data-day-index]');
    const employeeRows = document.querySelectorAll('[data-employee-id]');
    const availablePosts = document.querySelectorAll('[data-post-id]');
    
    if (calendarCells.length > 0 && employeeRows.length > 0) {
        results.dragDropElements = true;
        console.log('✅ [DEBUG-MODAL] Éléments drag & drop trouvés');
        console.log(`🔍 [DEBUG-MODAL] Cellules calendrier: ${calendarCells.length}`);
        console.log(`🔍 [DEBUG-MODAL] Lignes employés: ${employeeRows.length}`);
        console.log(`🔍 [DEBUG-MODAL] Postes disponibles: ${availablePosts.length}`);
    } else {
        results.errors.push('Éléments drag & drop manquants');
        console.error('❌ [DEBUG-MODAL] Éléments drag & drop manquants');
        console.log(`🔍 [DEBUG-MODAL] Cellules: ${calendarCells.length}, Employés: ${employeeRows.length}, Postes: ${availablePosts.length}`);
    }
    
    // 6. Résumé
    console.log('📊 [DEBUG-MODAL] === RÉSUMÉ DU DIAGNOSTIC ===');
    console.log('🔍 [DEBUG-MODAL] Résultats:', results);
    
    if (results.errors.length > 0) {
        console.error('❌ [DEBUG-MODAL] Erreurs détectées:');
        results.errors.forEach((error, index) => {
            console.error(`   ${index + 1}. ${error}`);
        });
    }
    
    return results;
}

// Fonction pour forcer l'intégration modale
function forceModalIntegration() {
    console.log('🔧 [DEBUG-MODAL] === FORÇAGE DE L\'INTÉGRATION MODALE ===');
    
    try {
        // 1. Rendre modalFunctionalities disponible globalement si ce n'est pas déjà fait
        if (typeof modalFunctionalities !== 'undefined' && !window.modalFunctionalities) {
            window.modalFunctionalities = modalFunctionalities;
            console.log('✅ [DEBUG-MODAL] modalFunctionalities rendu disponible globalement');
        }
        
        // 2. Vérifier et initialiser avec TeamCalendarApp
        const app = window.TeamCalendarApp || window.teamCalendarApp;
        if (app && window.modalFunctionalities && typeof window.modalFunctionalities.init === 'function') {
            window.modalFunctionalities.init(app);
            console.log('✅ [DEBUG-MODAL] modalFunctionalities initialisé avec TeamCalendarApp');
        }
        
        // 3. Créer le modal d'attribution s'il n'existe pas
        if (!document.getElementById('assignment-context-modal') && window.modalFunctionalities) {
            if (typeof window.modalFunctionalities.createAssignmentContextModal === 'function') {
                const modal = window.modalFunctionalities.createAssignmentContextModal();
                document.body.appendChild(modal);
                console.log('✅ [DEBUG-MODAL] Modal d\'attribution créé');
            }
        }
        
        console.log('✅ [DEBUG-MODAL] Intégration modale forcée avec succès');
        return true;
        
    } catch (error) {
        console.error('❌ [DEBUG-MODAL] Erreur lors du forçage de l\'intégration:', error);
        return false;
    }
}

// Fonction de test du workflow complet
function testCompleteModalWorkflow() {
    console.log('🧪 [DEBUG-MODAL] === TEST DU WORKFLOW COMPLET ===');
    
    try {
        // 1. Diagnostic initial
        const diagnosis = diagnoseModalIntegration();
        
        // 2. Forcer l'intégration si nécessaire
        if (!diagnosis.globalModalFunctionalities) {
            console.log('🔧 [DEBUG-MODAL] Forçage de l\'intégration nécessaire...');
            forceModalIntegration();
        }
        
        // 3. Test d'ouverture du modal
        if (window.modalFunctionalities && typeof window.modalFunctionalities.openAssignmentContextModal === 'function') {
            console.log('🧪 [DEBUG-MODAL] Test d\'ouverture du modal...');
            
            const testData = {
                postData: { id: 'test-post', label: 'Test Post' },
                employeeId: 'test-employee',
                employeeName: 'Test Employee',
                dateKey: '2025-07-18',
                position: { x: 100, y: 100 }
            };
            
            window.modalFunctionalities.openAssignmentContextModal(testData);
            console.log('✅ [DEBUG-MODAL] Modal ouvert avec succès');
            
            // Fermer le modal après 2 secondes
            setTimeout(() => {
                const modal = document.getElementById('assignment-context-modal');
                if (modal) {
                    modal.classList.add('hidden');
                    modal.style.display = 'none';
                    console.log('✅ [DEBUG-MODAL] Modal fermé automatiquement');
                }
            }, 2000);
            
        } else {
            console.error('❌ [DEBUG-MODAL] Impossible de tester l\'ouverture du modal');
        }
        
    } catch (error) {
        console.error('❌ [DEBUG-MODAL] Erreur lors du test du workflow:', error);
    }
}

// Fonctions disponibles globalement
window.diagnoseModalIntegration = diagnoseModalIntegration;
window.forceModalIntegration = forceModalIntegration;
window.testCompleteModalWorkflow = testCompleteModalWorkflow;

console.log('✅ [DEBUG-MODAL] Fonctions de débogage disponibles:');
console.log('- diagnoseModalIntegration() : Diagnostic complet');
console.log('- forceModalIntegration() : Forcer l\'intégration');
console.log('- testCompleteModalWorkflow() : Test complet du workflow');

// Exécution automatique du diagnostic
setTimeout(() => {
    console.log('🚀 [DEBUG-MODAL] Lancement automatique du diagnostic...');
    diagnoseModalIntegration();
}, 1000);
