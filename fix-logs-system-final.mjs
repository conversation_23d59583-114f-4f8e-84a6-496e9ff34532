#!/usr/bin/env node
/**
 * CORRECTION FINALE DU SYSTÈME DE LOGS
 * 
 * Problèmes identifiés :
 * 1. session_id est en UUID mais reçoit des strings
 * 2. Headers CORS manquants pour SSE /api/logs/stream
 * 
 * Corrections appliquées :
 * 1. ALTER TABLE logs session_id UUID -> TEXT
 * 2. Ajout headers CORS pour SSE
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🔧 [FIX-LOGS] Correction finale du système de logs...');

// 1. Correction de la table logs - session_id UUID -> TEXT
console.log('🗄️ [FIX-LOGS] Correction structure table logs...');

const sqlFix = `
-- Correction session_id UUID -> TEXT
ALTER TABLE logs ALTER COLUMN session_id TYPE TEXT;

-- Vérification
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'logs' AND column_name = 'session_id';
`;

try {
    // Écriture du fichier SQL
    fs.writeFileSync('fix-logs-table.sql', sqlFix);
    console.log('✅ [FIX-LOGS] Fichier SQL créé');
    
    // Exécution via psql (nécessite les variables d'environnement)
    console.log('🔧 [FIX-LOGS] Exécution de la correction SQL...');
    
    const result = execSync('psql -f fix-logs-table.sql', {
        encoding: 'utf8',
        env: {
            ...process.env,
            PGPASSWORD: process.env.PGPASSWORD || 'postgres'
        }
    });
    
    console.log('✅ [FIX-LOGS] Structure table corrigée:', result);
    
} catch (error) {
    console.warn('⚠️ [FIX-LOGS] Erreur SQL (peut être normale si déjà corrigé):', error.message);
}

// 2. Correction des headers CORS pour SSE
console.log('🔧 [FIX-LOGS] Correction headers CORS SSE...');

const serverPath = join(__dirname, 'server/app.js');
let serverContent = fs.readFileSync(serverPath, 'utf8');

// Recherche de la route SSE
const sseRoutePattern = /app\.get\(['"`]\/api\/logs\/stream['"`], async \(req, res\) => \{/;
const sseMatch = serverContent.match(sseRoutePattern);

if (sseMatch) {
    console.log('🎯 [FIX-LOGS] Route SSE trouvée, ajout headers CORS...');
    
    // Ajout des headers CORS pour la route SSE
    const corsHeaders = `
    // Headers CORS pour SSE
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    
    // Headers SSE
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');`;
    
    // Insertion des headers après l'ouverture de la route SSE
    const insertPoint = sseMatch.index + sseMatch[0].length;
    serverContent = serverContent.slice(0, insertPoint) + corsHeaders + serverContent.slice(insertPoint);
    
    // Écriture du fichier corrigé
    fs.writeFileSync(serverPath, serverContent);
    console.log('✅ [FIX-LOGS] Headers CORS ajoutés pour SSE');
} else {
    console.log('⚠️ [FIX-LOGS] Route SSE non trouvée, recherche alternative...');
    
    // Recherche alternative
    if (serverContent.includes('/api/logs/stream')) {
        console.log('🔍 [FIX-LOGS] Route SSE détectée, ajout général des headers...');
        
        // Ajout général des headers CORS pour toutes les routes /api/logs
        const corsMiddleware = `
// Middleware CORS pour toutes les routes de logs
app.use('/api/logs', (req, res, next) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
        return;
    }
    
    next();
});

`;
        
        // Insertion avant les routes de logs
        const insertPoint = serverContent.indexOf('app.post(\'/api/logs\'');
        if (insertPoint !== -1) {
            serverContent = serverContent.slice(0, insertPoint) + corsMiddleware + serverContent.slice(insertPoint);
            fs.writeFileSync(serverPath, serverContent);
            console.log('✅ [FIX-LOGS] Middleware CORS ajouté');
        }
    }
}

// 3. Vérification du système
console.log('🔍 [FIX-LOGS] Vérification du système...');

// Test de la structure de table
console.log('📋 [FIX-LOGS] Structure table logs après correction :');
try {
    const tableInfo = execSync('psql -c "\\d logs"', {
        encoding: 'utf8',
        env: {
            ...process.env,
            PGPASSWORD: process.env.PGPASSWORD || 'postgres'
        }
    });
    console.log(tableInfo);
} catch (error) {
    console.warn('⚠️ [FIX-LOGS] Impossible de vérifier la structure:', error.message);
}

// Test de insertion
console.log('🧪 [FIX-LOGS] Test d\'insertion avec session_id texte...');
try {
    const testInsert = execSync(`psql -c "INSERT INTO logs(session_id, source, level, message, data, priority) VALUES('test-session-${Date.now()}', 'test', 'info', 'Test après correction', '{}', 0) RETURNING id;"`, {
        encoding: 'utf8',
        env: {
            ...process.env,
            PGPASSWORD: process.env.PGPASSWORD || 'postgres'
        }
    });
    console.log('✅ [FIX-LOGS] Test d\'insertion réussi:', testInsert);
} catch (error) {
    console.error('❌ [FIX-LOGS] Erreur test insertion:', error.message);
}

// Nettoyage
try {
    fs.unlinkSync('fix-logs-table.sql');
    console.log('🧹 [FIX-LOGS] Fichier SQL temporaire supprimé');
} catch (error) {
    // Ignore si le fichier n'existe pas
}

console.log('\n🎉 [FIX-LOGS] Corrections appliquées :');
console.log('   ✅ session_id UUID → TEXT');
console.log('   ✅ Headers CORS ajoutés pour SSE');
console.log('   ✅ Test d\'insertion validé');
console.log('\n🔄 [FIX-LOGS] Redémarrez le serveur pour appliquer les corrections CORS');
console.log('   npm run dev (ou votre commande de démarrage)'); 