/**
 * Utilitaires de gestion des dates pour TeamCalendarApp
 * Centralise toute la logique de manipulation des dates
 */

/**
 * Normalise une date en clé de date (YYYY-MM-DD)
 */
export const normalizeDateKey = (date: any): string => {
  if (typeof date === 'string') {
    // Si c'est déjà une chaîne, vérifier le format
    if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return date;
    }
    // Essayer de parser
    const parsed = new Date(date);
    if (!isNaN(parsed.getTime())) {
      return formatDateToKey(parsed);
    }
  }

  if (date instanceof Date) {
    return formatDateToKey(date);
  }

  // Fallback
  const now = new Date();
  return formatDateToKey(now);
};

/**
 * Formate une date en clé (YYYY-MM-DD)
 */
export const formatDateToKey = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * Vérifie si une date est dans une plage donnée
 */
export const isDateInRange = (date: string, start: string, end: string): boolean => {
  const dateObj = new Date(date);
  const startObj = new Date(start);
  const endObj = new Date(end);
  
  return dateObj >= startObj && dateObj <= endObj;
};

/**
 * Génère une clé de semaine (YYYY-WW)
 */
export const getWeekKey = (date: Date): string => {
  const year = date.getFullYear();
  const week = getWeekNumber(date);
  return `${year}-W${String(week).padStart(2, '0')}`;
};

/**
 * Calcule le numéro de semaine ISO
 */
export const getWeekNumber = (date: Date): number => {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  const dayNum = d.getUTCDay() || 7;
  d.setUTCDate(d.getUTCDate() + 4 - dayNum);
  const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
  return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
};

/**
 * Génère les dates d'une semaine
 */
export const generateWeekDates = (weekOffset: number = 0): Date[] => {
  const dates: Date[] = [];
  const now = new Date();
  const currentWeekStart = getWeekStartDate(now);
  
  // Ajuster pour l'offset de semaine
  const targetWeekStart = new Date(currentWeekStart);
  targetWeekStart.setDate(targetWeekStart.getDate() + (weekOffset * 7));
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(targetWeekStart);
    date.setDate(targetWeekStart.getDate() + i);
    dates.push(date);
  }
  
  return dates;
};

/**
 * Obtient la date de début de semaine
 */
export const getWeekStartDate = (date: Date): Date => {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Ajuster pour lundi
  return new Date(d.setDate(diff));
};

/**
 * Parse une clé de semaine
 */
export const parseWeekKey = (weekKey: string): { year: number; week: number } | null => {
  const match = weekKey.match(/^(\d{4})-W(\d{2})$/);
  if (!match) return null;
  
  return {
    year: parseInt(match[1]),
    week: parseInt(match[2])
  };
};

/**
 * Obtient la clé de semaine avec offset
 */
export const getWeekKeyWithOffset = (baseWeekKey: string, offset: number): string => {
  const parsed = parseWeekKey(baseWeekKey);
  if (!parsed) return baseWeekKey;
  
  const baseDate = getDateFromWeek(parsed.year, parsed.week);
  baseDate.setDate(baseDate.getDate() + (offset * 7));
  
  return getWeekKey(baseDate);
};

/**
 * Obtient une date à partir d'une année et semaine
 */
export const getDateFromWeek = (year: number, week: number): Date => {
  const januaryFirst = new Date(year, 0, 1);
  const days = (week - 1) * 7;
  const weekStart = new Date(januaryFirst);
  weekStart.setDate(januaryFirst.getDate() + days);
  
  // Ajuster pour que la semaine commence lundi
  const dayOfWeek = weekStart.getDay();
  const mondayOffset = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
  weekStart.setDate(weekStart.getDate() - mondayOffset);
  
  return weekStart;
};

/**
 * Vérifie si deux dates sont le même jour
 */
export const isSameDay = (date1: Date, date2: Date): boolean => {
  return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate();
};

/**
 * Obtient la clé de date d'aujourd'hui
 */
export const getTodayKey = (): string => {
  return formatDateToKey(new Date());
};

/**
 * Calcule la différence en jours entre deux dates
 */
export const getDaysDifference = (date1: string, date2: string): number => {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  const diffTime = Math.abs(d2.getTime() - d1.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

/**
 * Ajoute des jours à une date
 */
export const addDays = (date: string, days: number): string => {
  const d = new Date(date);
  d.setDate(d.getDate() + days);
  return formatDateToKey(d);
};

/**
 * Soustrait des jours à une date
 */
export const subtractDays = (date: string, days: number): string => {
  return addDays(date, -days);
};

/**
 * Vérifie si une date est dans le passé
 */
export const isDateInPast = (date: string): boolean => {
  const dateObj = new Date(date);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return dateObj < today;
};

/**
 * Vérifie si une date est aujourd'hui
 */
export const isToday = (date: string): boolean => {
  return date === getTodayKey();
};

/**
 * Vérifie si une date est dans le futur
 */
export const isDateInFuture = (date: string): boolean => {
  const dateObj = new Date(date);
  const today = new Date();
  today.setHours(23, 59, 59, 999);
  return dateObj > today;
}; 