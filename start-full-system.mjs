#!/usr/bin/env node

/**
 * Script de démarrage simple et stable du système
 * Basé sur la simplicité de "npm run server" qui fonctionne
 */

import { spawn } from 'child_process';

console.log('🚀 [STARTUP] Démarrage du système TeamCalendar (version simple et stable)...\n');

console.log('💡 [INFO] Si vous avez des processus Node.js qui traînent, exécutez:');
console.log('💡 [INFO] taskkill /F /IM node.exe (Windows) ou pkill node (Unix)\n');

// Variables pour surveiller les processus
let backendProcess = null;
let frontendProcess = null;
let isShuttingDown = false;

// Fonction de nettoyage simple
function cleanup() {
  if (isShuttingDown) return;
  isShuttingDown = true;
  
  console.log('\n🛑 [SHUTDOWN] Arrêt du système...');
  
  if (backendProcess) {
    console.log('🛑 [SHUTDOWN] Arrêt du backend...');
    backendProcess.kill('SIGTERM');
  }
  
  if (frontendProcess) {
    console.log('🛑 [SHUTDOWN] Arrêt du frontend...');
    frontendProcess.kill('SIGTERM');
  }
  
  setTimeout(() => {
    console.log('🛑 [SHUTDOWN] Arrêt terminé');
    process.exit(0);
  }, 2000);
}

// Gestion des signaux
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);

// Démarrage du backend (comme npm run server)
console.log('🔧 [BACKEND] Démarrage du serveur backend...');
backendProcess = spawn('node', ['server/app.js'], {
  stdio: 'inherit',
  env: { ...process.env, NODE_ENV: 'development' }
});

backendProcess.on('error', (error) => {
  console.error('❌ [BACKEND] Erreur:', error);
  process.exit(1);
});

backendProcess.on('close', (code) => {
  if (!isShuttingDown) {
    console.log(`🔧 [BACKEND] Backend fermé avec le code ${code}`);
    if (code !== 0) {
      console.log('❌ [BACKEND] Backend fermé de façon inattendue');
      process.exit(1);
    }
  }
});

// Attendre un peu puis démarrer le frontend
setTimeout(() => {
  console.log('\n🎨 [FRONTEND] Démarrage du serveur de développement...');
  
  frontendProcess = spawn('npm', ['run', 'dev'], {
    stdio: 'inherit',
    shell: true
  });

  frontendProcess.on('error', (error) => {
    console.error('❌ [FRONTEND] Erreur:', error);
    cleanup();
    process.exit(1);
  });

  frontendProcess.on('close', (code) => {
    if (!isShuttingDown) {
      console.log(`🎨 [FRONTEND] Frontend fermé avec le code ${code}`);
      cleanup();
    }
  });

  // Message de succès après un délai
  setTimeout(() => {
    console.log('\n🎉 ========================================');
    console.log('🎉 SYSTÈME TEAMCALENDAR DÉMARRÉ !');
    console.log('🎉 ========================================');
    console.log('🔧 Backend:  http://localhost:3001');
    console.log('🎨 Frontend: http://localhost:5173');
    console.log('📡 API:      http://localhost:5173/api/*');
    console.log('🎉 ========================================\n');
    console.log('💡 Appuyez sur Ctrl+C pour arrêter le système');
  }, 8000);

}, 3000); // Attendre 3 secondes pour que le backend démarre 