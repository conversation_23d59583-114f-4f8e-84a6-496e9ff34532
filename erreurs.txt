[18/07/2025 14:18:14] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] Script chargé. Utilisez runCriticalValidation() pour valider.
[18/07/2025 14:18:14] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 14:18:14] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 14:18:14] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:14.470Z"
}
[18/07/2025 14:18:14] [BACKEND] [INFO] [LOGS] Client connecté: 1752862694469
[18/07/2025 14:17:43] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[18/07/2025 14:17:43] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[18/07/2025 14:17:43] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[18/07/2025 14:17:43] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 14:17:43] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 14:17:43] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 14:17:43] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 14:17:43] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 14:17:43] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 14:17:43] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 14:17:43] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 14:17:43] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 14:17:43] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 14:17:43] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 14:17:43] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 14:17:43] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 14:17:43] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[18/07/2025 14:17:43] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 14:17:43] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 14:17:43] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 14:17:43] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 14:17:43] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[18/07/2025 14:17:43] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 14:17:43] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 14:17:43] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[18/07/2025 14:17:43] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 14:17:43] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 14:17:43] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 14:17:43] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 14:17:43] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 14:17:43] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 14:17:43] [FRONTEND] [LOG] 🧪 [TEST-DRAG-DROP] Démarrage des tests complets de drag & drop...
[18/07/2025 14:17:43] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 14:17:43] [FRONTEND] [LOG] ✅ [TEST-DRAG-DROP] Script chargé. Utilisez runComprehensiveDragDropTests() pour tester.
[18/07/2025 14:17:43] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] Script chargé. Utilisez runCriticalValidation() pour valider.
[18/07/2025 14:17:43] [FRONTEND] [LOG] 🚨 [VALIDATION-CRITIQUE] Démarrage de la validation des corrections...
[18/07/2025 14:17:43] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 14:17:43] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[18/07/2025 14:17:43] [BACKEND] [INFO] [LOGS] Client connecté: 1752862663596
[18/07/2025 14:17:43] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 14:17:43] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:17:43.597Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[18/07/2025 14:17:44] [BACKEND] [INFO] ✅ Requête exécutée en 145ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[18/07/2025 14:17:44] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 145ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:17:43.742Z",
  "originalArgs": [
    "✅ Requête exécutée en 145ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[18/07/2025 14:17:45] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 14:17:45] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 14:17:45] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":0}
[18/07/2025 14:17:45] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":false,"hasModalManager":false,"hasOpenFunction":false,"hasHandleFunction":false}
[18/07/2025 14:17:47] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 14:17:47] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 14:17:47] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 14:17:47] [FRONTEND] [ERROR] ❌ [VALIDATION] TeamCalendarApp non disponible
[18/07/2025 14:17:47] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] === VALIDATION 4: Éléments DOM critiques === 
[18/07/2025 14:17:47] [FRONTEND] [ERROR] ❌ [VALIDATION-CRITIQUE] Aucune cellule de calendrier trouvée - CRITIQUE pour drag & drop 
[18/07/2025 14:17:47] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 14:17:47] [FRONTEND] [ERROR] ❌ [VALIDATION] Erreur lors de setupAssignmentContextModal: Error: can't access property "ModalManager", window.TeamCalendarApp is undefined
validateListeners@http://localhost:5173/validate-modal-fix.js:87:9
runCompleteValidation@http://localhost:5173/validate-modal-fix.js:103:20
@http://localhost:5173/validate-modal-fix.js:186:5

[18/07/2025 14:17:47] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":false,"listeners":false}
[18/07/2025 14:17:47] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[18/07/2025 14:17:47] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[18/07/2025 14:17:47] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[18/07/2025 14:17:47] [FRONTEND] [LOG] - Vérifier que la modale assignment-context-modal existe dans le DOM
[18/07/2025 14:17:47] [FRONTEND] [LOG] - Vérifier les erreurs dans setupAssignmentContextModal
[18/07/2025 14:17:48] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 14:17:48] [FRONTEND] [ERROR] ❌ [DIAGNOSTIC] Fonctions manquantes détectées
[18/07/2025 14:17:48] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":false,"detectDropDateFunction":false,"showConfirmationMenuFunction":false,"handleRegularAssignmentDropFunction":false,"regularAssignments":0,"employees":0}
[18/07/2025 14:17:48] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 14:17:48] [FRONTEND] [LOG] ⏳ [TEST-DRAG-DROP] En attente du chargement de l'application...
[18/07/2025 14:17:48] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 14:17:48] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 14:17:48] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 14:17:48] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[18/07/2025 14:17:48] [FRONTEND] [ERROR] ❌ [DATE-TEST] Données insuffisantes pour le test
[18/07/2025 14:17:48] [FRONTEND] [ERROR] ❌ [DATE-TEST] formatDateToKey non disponible
[18/07/2025 14:17:48] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 14:17:48] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 14:17:48] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 14:17:48] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 14:17:48] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 14:17:48] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 14:17:48] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 14:17:48] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 14:17:48] [FRONTEND] [ERROR] ❌ [DATE-TEST] detectDropDateFromPosition non disponible
[18/07/2025 14:17:48] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 14:17:48] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 14:17:48] [FRONTEND] [LOG] ❌ Logique de fork avec dates: ÉCHOUÉ
[18/07/2025 14:17:48] [FRONTEND] [LOG] ❌ Détection de date avec timezone: ÉCHOUÉ
[18/07/2025 14:17:48] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 14:17:48] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 14:17:49] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":0,"regularShifts":0,"gripsInDOM":0,"gripsWithEvents":0,"gripsResponsive":0,"issues":[]}
[18/07/2025 14:17:49] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 14:17:49] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 14:17:49] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 0
[18/07/2025 14:17:49] [FRONTEND] [LOG] ⏳ [VALIDATION-CRITIQUE] En attente du chargement de TeamCalendarApp...
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] ✅ [Logger] Console patchée pour intégration unifiée
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:01.901Z",
  "originalArgs": [
    "✅ [Logger] Console patchée pour intégration unifiée"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] [query] Executing query: CREATE TABLE IF NOT EXISTS logs (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  source VARCHAR(50) NOT NULL,
  level VARCHAR(50) NOT NULL,
  message TEXT NOT NULL,
  data JSONB,
  priority INTEGER DEFAULT 0
) {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:01.908Z",
  "originalArgs": [
    "[query] Executing query: CREATE TABLE IF NOT EXISTS logs (\n  id SERIAL PRIMARY KEY,\n  session_id VARCHAR(255) NOT NULL,\n  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,\n  source VARCHAR(50) NOT NULL,\n  level VARCHAR(50) NOT NULL,\n  message TEXT NOT NULL,\n  data JSONB,\n  priority INTEGER DEFAULT 0\n)",
    {}
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] ============================================================
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:01.918Z",
  "originalArgs": [
    "============================================================"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] ============================================================
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:01.945Z",
  "originalArgs": [
    "============================================================"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔍 Logs détaillés: ACTIVÉS
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:01.949Z",
  "originalArgs": [
    "🔍 Logs détaillés: ACTIVÉS"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 📍 URL: http://localhost:3001
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:01.946Z",
  "originalArgs": [
    "📍 URL: http://localhost:3001"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🏥 Health Check: http://localhost:3001/health
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:01.947Z",
  "originalArgs": [
    "🏥 Health Check: http://localhost:3001/health"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] ✅ Serveur prêt à recevoir les requêtes
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:01.953Z",
  "originalArgs": [
    "✅ Serveur prêt à recevoir les requêtes"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:01.953Z",
  "originalArgs": [
    ""
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] ⏰ Démarré le: 2025-07-18T18:18:01.952Z
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:01.952Z",
  "originalArgs": [
    "⏰ Démarré le: 2025-07-18T18:18:01.952Z"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] ============================================================
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:01.951Z",
  "originalArgs": [
    "============================================================"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.099Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🛡️  Protection UUID: ACTIVÉE
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:01.948Z",
  "originalArgs": [
    "🛡️  Protection UUID: ACTIVÉE"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.100Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.101Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] ⚠️  Gestion erreurs: ROBUSTE
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:01.951Z",
  "originalArgs": [
    "⚠️  Gestion erreurs: ROBUSTE"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.104Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.105Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.121Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.124Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.120Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 242ms: SELECT 1 FROM logs LIMIT 1 {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.140Z",
  "originalArgs": [
    "✅ Requête exécutée en 242ms:",
    "SELECT 1 FROM logs LIMIT 1",
    {}
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] [query] Executing query: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 0 {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.138Z",
  "originalArgs": [
    "[query] Executing query: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 0",
    {}
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 230ms: CREATE TABLE IF NOT EXISTS logs (
  id SERIAL PRIM {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.137Z",
  "originalArgs": [
    "✅ Requête exécutée en 230ms:",
    "CREATE TABLE IF NOT EXISTS logs (\n  id SERIAL PRIM",
    {}
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] ✅ [Logger] Connexion PostgreSQL (distant) validée
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.141Z",
  "originalArgs": [
    "✅ [Logger] Connexion PostgreSQL (distant) validée"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.169Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.122Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.130Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.112Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] ============================================================
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.203Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 111ms: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.249Z",
  "originalArgs": [
    "✅ Requête exécutée en 111ms:",
    "ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority",
    {}
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.278Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.095Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.296Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.093Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.306Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.097Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🚀 SERVEUR API DÉMARRÉ AVEC PROTECTION UUID
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:01.944Z",
  "originalArgs": [
    "🚀 SERVEUR API DÉMARRÉ AVEC PROTECTION UUID"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:02.349Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:02] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 14:18:13] [FRONTEND] [DEBUG] [vite] connected.
[18/07/2025 14:18:13] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:12.874Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:13] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:12.735Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:13] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 14:18:13] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:12.934Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:13] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[18/07/2025 14:18:13] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[18/07/2025 14:18:13] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[18/07/2025 14:18:13] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[18/07/2025 14:18:13] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[18/07/2025 14:18:13] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[18/07/2025 14:18:13] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[18/07/2025 14:18:13] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[18/07/2025 14:18:13] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[18/07/2025 14:18:13] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:13.567Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:13] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[18/07/2025 14:18:13] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[18/07/2025 14:18:13] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:13.573Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:13] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:13.572Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:13] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[18/07/2025 14:18:14] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 14:18:14] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[18/07/2025 14:18:14] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[18/07/2025 14:18:14] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[18/07/2025 14:18:14] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 14:18:14] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 14:18:14] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 14:18:14] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 14:18:14] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[18/07/2025 14:18:14] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 14:18:14] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:13.869Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:14] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:13.868Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:14] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 14:18:14] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[18/07/2025 14:18:14] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:13.997Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:14] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[18/07/2025 14:18:14] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 14:18:14] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 14:18:14] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 14:18:14] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:13.998Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:18:14] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 14:18:14] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 14:18:14] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 14:18:14] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 14:18:14] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 14:18:14] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 14:18:14] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 14:18:14] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 14:18:14] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[18/07/2025 14:18:14] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[18/07/2025 14:18:14] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 14:18:14] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 14:18:14] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 14:18:14] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 14:18:14] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 14:18:14] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[18/07/2025 14:18:14] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 14:18:14] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 14:18:14] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 14:18:14] [FRONTEND] [LOG] ✅ [TEST-DRAG-DROP] Script chargé. Utilisez runComprehensiveDragDropTests() pour tester.
[18/07/2025 14:18:14] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 14:18:14] [FRONTEND] [LOG] 🚨 [VALIDATION-CRITIQUE] Démarrage de la validation des corrections...
[18/07/2025 14:18:14] [FRONTEND] [LOG] 🧪 [TEST-DRAG-DROP] Démarrage des tests complets de drag & drop...
[18/07/2025 14:18:14] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 14:18:14] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] Script chargé. Utilisez runCriticalValidation() pour valider.
[18/07/2025 14:18:14] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 205ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 205ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:18:14.676Z"
}
[18/07/2025 14:18:15] [BACKEND] [INFO] ✅ Requête exécutée en 205ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[18/07/2025 14:18:15] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 14:18:15] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 14:18:15] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":false,"hasModalManager":false,"hasOpenFunction":false,"hasHandleFunction":false}
[18/07/2025 14:18:15] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":0}
[18/07/2025 14:18:16] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 14:18:16] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 14:18:16] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 14:18:16] [FRONTEND] [ERROR] ❌ [VALIDATION] TeamCalendarApp non disponible
[18/07/2025 14:18:16] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] === VALIDATION 4: Éléments DOM critiques === 
[18/07/2025 14:18:16] [FRONTEND] [ERROR] ❌ [VALIDATION-CRITIQUE] Aucune cellule de calendrier trouvée - CRITIQUE pour drag & drop 
[18/07/2025 14:18:16] [FRONTEND] [ERROR] ❌ [VALIDATION] Erreur lors de setupAssignmentContextModal: Error: can't access property "ModalManager", window.TeamCalendarApp is undefined
validateListeners@http://localhost:5173/validate-modal-fix.js:87:9
runCompleteValidation@http://localhost:5173/validate-modal-fix.js:103:20
@http://localhost:5173/validate-modal-fix.js:186:5

[18/07/2025 14:18:16] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 14:18:16] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":false,"listeners":false}
[18/07/2025 14:18:16] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[18/07/2025 14:18:16] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[18/07/2025 14:18:16] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 14:18:16] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[18/07/2025 14:18:16] [FRONTEND] [LOG] - Vérifier les erreurs dans setupAssignmentContextModal
[18/07/2025 14:18:16] [FRONTEND] [LOG] - Vérifier que la modale assignment-context-modal existe dans le DOM
[18/07/2025 14:18:16] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 14:18:16] [FRONTEND] [ERROR] ❌ [DIAGNOSTIC] Fonctions manquantes détectées
[18/07/2025 14:18:16] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":false,"detectDropDateFunction":false,"showConfirmationMenuFunction":false,"handleRegularAssignmentDropFunction":false,"regularAssignments":0,"employees":0}
[18/07/2025 14:18:16] [FRONTEND] [LOG] ⏳ [TEST-DRAG-DROP] En attente du chargement de l'application...
[18/07/2025 14:18:17] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 14:18:17] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 14:18:17] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 14:18:17] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[18/07/2025 14:18:17] [FRONTEND] [ERROR] ❌ [DATE-TEST] formatDateToKey non disponible
[18/07/2025 14:18:17] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 14:18:17] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 14:18:17] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 14:18:17] [FRONTEND] [ERROR] ❌ [DATE-TEST] Données insuffisantes pour le test
[18/07/2025 14:18:17] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 14:18:17] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 14:18:17] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 14:18:17] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 14:18:17] [FRONTEND] [ERROR] ❌ [DATE-TEST] detectDropDateFromPosition non disponible
[18/07/2025 14:18:17] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 14:18:17] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 14:18:17] [FRONTEND] [LOG] ❌ Logique de fork avec dates: ÉCHOUÉ
[18/07/2025 14:18:17] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 14:18:17] [FRONTEND] [LOG] ❌ Détection de date avec timezone: ÉCHOUÉ
[18/07/2025 14:18:17] [FRONTEND] [LOG] ⏳ [VALIDATION-CRITIQUE] En attente du chargement de TeamCalendarApp...
[18/07/2025 14:18:17] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 14:18:17] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 14:18:18] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 0
[18/07/2025 14:18:18] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 14:18:18] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":0,"regularShifts":0,"gripsInDOM":0,"gripsWithEvents":0,"gripsResponsive":0,"issues":[]}
[18/07/2025 14:18:18] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...