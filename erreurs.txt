[vite] connecting... client:789:9
[CAPTURE-SIMPLIFIÉE] Démarrage. Session ID: 1752863411499-um3gjzqpv capture-logs-unified.js:18:13
[vite] connected. capture-logs-unified.js:69:36
unreachable code after return statement teamCalendarApp.ts:3577:5
Download the React DevTools for a better development experience: https://react.dev/link/react-devtools capture-logs-unified.js:69:36
🔧 [MODAL] Initialisation automatique désactivée - activation à la demande capture-logs-unified.js:69:36
✅ [MODAL] modalFunctionalities rendu disponible globalement capture-logs-unified.js:69:36
🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande capture-logs-unified.js:69:36
🚀 [INIT] Initialisation du nettoyage des logs... capture-logs-unified.js:69:36
🧹 [CLEANUP] Nettoyage des anciens logs localStorage... capture-logs-unified.js:69:36
🧹 [CLEANUP] 0 clés nettoyées du localStorage capture-logs-unified.js:69:36
⚙️ [SETUP] Configuration de l'optimisation console... capture-logs-unified.js:69:36
🔧 [SETUP] Application de l'optimisation console (dev mode) capture-logs-unified.js:69:36
✅ [SETUP] Optimisation console configurée capture-logs-unified.js:69:36
✅ [INIT] Nettoyage et optimisation terminés avec succès capture-logs-unified.js:69:36
🔧 Contrôle des logs chargé. Niveau par défaut: WARN capture-logs-unified.js:69:36
💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG") capture-logs-unified.js:69:36
📖 Aide complète: showLogHelp() capture-logs-unified.js:69:36
🎓 [Tutorial] Système de tutoriel interactif V2 initialisé capture-logs-unified.js:69:36
🧪 [TEST] Script de test de la modale drag & drop chargé capture-logs-unified.js:69:36
✅ [TEST] Fonctions de test disponibles: capture-logs-unified.js:69:36
- testAssignmentModal() : Tester l'ouverture de la modale capture-logs-unified.js:69:36
- testButtonListeners() : Tester les listeners des boutons capture-logs-unified.js:69:36
- diagnoseModalIssues() : Diagnostiquer les problèmes capture-logs-unified.js:69:36
- forceReconfigureListeners() : Reconfigurer les listeners capture-logs-unified.js:69:36
- testButtonClicks() : Tester les clics sur les boutons capture-logs-unified.js:69:36
🔍 [VALIDATION] Script de validation de la correction modale chargé capture-logs-unified.js:69:36
✅ [VALIDATION] Fonctions de validation disponibles: capture-logs-unified.js:69:36
- validateMainFix() : Valider la correction principale capture-logs-unified.js:69:36
- validateDOMElements() : Valider les éléments DOM capture-logs-unified.js:69:36
- validateListeners() : Valider les listeners capture-logs-unified.js:69:36
- runCompleteValidation() : Validation complète capture-logs-unified.js:69:36
- testEndToEnd() : Test end-to-end complet capture-logs-unified.js:69:36
🧪 [TEST] Script de test pour la correction du fork modal chargé capture-logs-unified.js:69:36
✅ [TEST] Fonctions de test disponibles: capture-logs-unified.js:69:36
- testDropDateDetection() : Tester la détection de date capture-logs-unified.js:69:36
- testForkModalWithDate() : Tester le modal avec date capture-logs-unified.js:69:36
- testCompleteForkFlow() : Tester le flux complet capture-logs-unified.js:69:36
- diagnoseForkModalIssues() : Diagnostic complet capture-logs-unified.js:69:36
🧪 [DATE-TEST] Script de validation des corrections de date chargé capture-logs-unified.js:69:36
✅ [DATE-TEST] Fonctions de validation des dates disponibles: capture-logs-unified.js:69:36
- testSafeDateConversion() : Tester la conversion sécurisée capture-logs-unified.js:69:36
- testForkDateLogic() : Tester la logique de fork capture-logs-unified.js:69:36
- testTimezoneEnhancements() : Tester les améliorations timezone capture-logs-unified.js:69:36
- testDropDateDetectionWithTimezone() : Tester la détection avec timezone capture-logs-unified.js:69:36
- runDateValidationSuite() : Suite complète de validation capture-logs-unified.js:69:36
🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé capture-logs-unified.js:69:36
✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles: capture-logs-unified.js:69:36
- diagnoseGripHandles() : Diagnostic complet capture-logs-unified.js:69:36
- forceRecreateGripHandles() : Recréation forcée capture-logs-unified.js:69:36
- testSpecificGrip(assignmentId) : Test spécifique capture-logs-unified.js:69:36
- autoFixGripIssues() : Correction automatique capture-logs-unified.js:69:36
- startGripMonitoring() : Surveillance continue capture-logs-unified.js:69:36
🧪 [TEST-DRAG-DROP] Démarrage des tests complets de drag & drop... capture-logs-unified.js:69:36
✅ [TEST-DRAG-DROP] Script chargé. Utilisez runComprehensiveDragDropTests() pour tester. capture-logs-unified.js:69:36
🚨 [VALIDATION-CRITIQUE] Démarrage de la validation des corrections... capture-logs-unified.js:69:36
✅ [VALIDATION-CRITIQUE] Script chargé. Utilisez runCriticalValidation() pour valider. capture-logs-unified.js:69:36
🚀 [DEBUG-MODAL] Script de débogage de l'intégration modale chargé capture-logs-unified.js:69:36
✅ [DEBUG-MODAL] Fonctions de débogage disponibles: capture-logs-unified.js:69:36
- diagnoseModalIntegration() : Diagnostic complet capture-logs-unified.js:69:36
- forceModalIntegration() : Forcer l'intégration capture-logs-unified.js:69:36
- testCompleteModalWorkflow() : Test complet du workflow capture-logs-unified.js:69:36
🚀 [TEST-MODAL-FIX] Script de test complet de la correction modale chargé capture-logs-unified.js:69:36
✅ [TEST-MODAL-FIX] Fonctions de test disponibles: capture-logs-unified.js:69:36
- testModalFixComplete() : Test complet de la correction capture-logs-unified.js:69:36
- testModalButtons() : Test spécifique des boutons du modal capture-logs-unified.js:69:36
🚀 [MANUAL-TEST] Script de test manuel de la modale chargé capture-logs-unified.js:69:36
✅ [MANUAL-TEST] Fonctions disponibles: capture-logs-unified.js:69:36
- testModalManually() : Test complet capture-logs-unified.js:69:36
- closeModal() : Fermer la modale capture-logs-unified.js:69:36
- testButton(action) : Tester un bouton spécifique capture-logs-unified.js:69:36
  Actions disponibles: "regular", "temporary", "replacement", "cancel", "close" capture-logs-unified.js:69:36
🚀 [FINAL-VALIDATION] Script de validation finale de la correction modale chargé capture-logs-unified.js:69:36
✅ [FINAL-VALIDATION] Fonctions de validation disponibles: capture-logs-unified.js:69:36
- runFinalModalValidation() : Validation complète capture-logs-unified.js:69:36
- quickModalTest() : Test rapide capture-logs-unified.js:69:36
🧹 [Agenda] Nettoyage composant Agenda capture-logs-unified.js:69:36
🔄 [Agenda] Réinitialisation nécessaire (employés non chargés) capture-logs-unified.js:69:36
🚀 [Agenda] Initialisation TeamCalendarApp... capture-logs-unified.js:69:36
🔍 [Agenda] TeamCalendarApp assigné à window: 
Object { teamCalendarApp: true, TeamCalendarApp: true }
capture-logs-unified.js:69:36
🚀 [init] Initialisation de TeamCalendarApp... capture-logs-unified.js:69:36
🧹 [clearRenderCache] Nettoyage du cache de rendu capture-logs-unified.js:69:36
✅ [clearRenderCache] Cache de rendu nettoyé capture-logs-unified.js:69:36
🚀 [DEBUG-MODAL] Lancement automatique du diagnostic... capture-logs-unified.js:69:36
🔍 [DEBUG-MODAL] === DIAGNOSTIC COMPLET DE L'INTÉGRATION MODALE === capture-logs-unified.js:69:36
📋 [DEBUG-MODAL] 1. Vérification TeamCalendarApp... capture-logs-unified.js:69:36
✅ [DEBUG-MODAL] TeamCalendarApp disponible capture-logs-unified.js:69:36
🔍 [DEBUG-MODAL] Méthodes disponibles: 
Array(10) [ "safeScheduleUpdate", "renderSafe", "checkDataIntegrity", "quickRepair", "clearRenderCache", "fixPostRefreshIssues", "emergencyFixUndefinedPostIds", "saveFixedShifts", "fixUndefinedPostIdsOnLoad", "getEmployeeName" ]
capture-logs-unified.js:69:36
📋 [DEBUG-MODAL] 2. Vérification modalFunctionalities local... capture-logs-unified.js:69:36
✅ [DEBUG-MODAL] modalFunctionalities local disponible capture-logs-unified.js:69:36
📋 [DEBUG-MODAL] 3. Vérification modalFunctionalities global... capture-logs-unified.js:69:36
✅ [DEBUG-MODAL] window.modalFunctionalities disponible capture-logs-unified.js:69:36
🔍 [DEBUG-MODAL] Méthodes disponibles: 
Array(5) [ "openAssignmentContextModal", "createAssignmentContextModal", "populateAssignmentModal", "setupAssignmentModalEvents", "handleAssignmentAction" ]
capture-logs-unified.js:69:36
📋 [DEBUG-MODAL] 4. Vérification modal d'attribution... capture-logs-unified.js:69:36
✅ [DEBUG-MODAL] Modal assignment-context-modal trouvé capture-logs-unified.js:69:36
🔍 [DEBUG-MODAL] Classes: 
Array(10) [ "hidden", "fixed", "inset-0", "bg-black/40", "backdrop-blur-sm", "z-50", "flex", "items-center", "justify-center", "p-4" ]
capture-logs-unified.js:69:36
🔍 [DEBUG-MODAL] Style display: <empty string> capture-logs-unified.js:69:36
📋 [DEBUG-MODAL] 5. Vérification éléments drag & drop... capture-logs-unified.js:69:36
❌ [DEBUG-MODAL] Éléments drag & drop manquants capture-logs-unified.js:69:36
🔍 [DEBUG-MODAL] Cellules: 0, Employés: 0, Postes: 0 capture-logs-unified.js:69:36
📊 [DEBUG-MODAL] === RÉSUMÉ DU DIAGNOSTIC === capture-logs-unified.js:69:36
🔍 [DEBUG-MODAL] Résultats: 
Object { teamCalendarApp: true, modalFunctionalities: true, globalModalFunctionalities: true, assignmentModal: true, dragDropElements: false, errors: (1) […] }
capture-logs-unified.js:69:36
❌ [DEBUG-MODAL] Erreurs détectées: capture-logs-unified.js:69:36
   1. Éléments drag & drop manquants capture-logs-unified.js:69:36
🔌 [ConnectionIndicator] Création de l'indicateur de connexion capture-logs-unified.js:69:36
🔌 [init] Indicateur de connexion initialisé capture-logs-unified.js:69:36
📂 [TeamCalendarApp] Chargement de l'état... capture-logs-unified.js:69:36
🔄 [TeamCalendarApp] API non disponible, fallback localStorage capture-logs-unified.js:69:36
⚙️ [loadStateFromLocalStorage] Aucun paramètre sauvegardé. capture-logs-unified.js:69:36
✅ [setupEmployeeNameDisplay] Styles CSS injectés pour l'affichage des noms capture-logs-unified.js:69:36
✅ [init] Icône paramètres trouvée: 
<button id="settings-btn" class="header-btn flex items-ce…70 hover:text-slate-700">
capture-logs-unified.js:69:36
🔍 [init] Recherche du bouton des paramètres dans le DOM capture-logs-unified.js:69:36
🔍 [init] Bouton des paramètres trouvé ? true capture-logs-unified.js:69:36
🔍 [init] Attachement du listener click sur le bouton des paramètres capture-logs-unified.js:69:36
🔍 [init] Recherche du bouton des paramètres header dans le DOM capture-logs-unified.js:69:36
🔍 [init] Bouton des paramètres header trouvé ? true capture-logs-unified.js:69:36
🔍 [init] Attachement du listener click sur le bouton des paramètres header capture-logs-unified.js:69:36
✅ [setupNavigationListeners] Écouteurs de navigation attachés capture-logs-unified.js:69:36
🎯 [setupViewNavigationListeners] Configuration des boutons de navigation... capture-logs-unified.js:69:36
✅ Boutons de navigation configurés capture-logs-unified.js:69:36
✅ [init] modalFunctionalities rendu disponible globalement capture-logs-unified.js:69:36
🔧 [ModalFunctionalitiesManager] Initialisation du gestionnaire de modales capture-logs-unified.js:69:36
✅ [init] Gestionnaire de modales initialisé capture-logs-unified.js:69:36
✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré capture-logs-unified.js:69:36
📋 [loadEmployeeOrder] Chargement ordre des employés capture-logs-unified.js:69:36
Erreur récupération ordre employés: Error: HTTP 500
    getEmployeeOrder http://localhost:5173/src/api.js:187
capture-logs-unified.js:69:36
✅ [loadEmployeeOrder] Ordre chargé depuis le cache local (âge: 137 minutes) capture-logs-unified.js:69:36
✅ [loadEmployeeOrder] Employés réorganisés selon l'ordre cache capture-logs-unified.js:69:36
🔧 [init] Forçage du rendu initial... capture-logs-unified.js:69:36
🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours capture-logs-unified.js:69:36
✅ [renderUnifiedCalendar] Conteneurs trouvés: 
Object { employeeRows: true, availablePosts: true }
capture-logs-unified.js:69:36
🔧 [init] Configuration du drag & drop des postes... capture-logs-unified.js:69:36
🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements capture-logs-unified.js:69:36
🔍 [ensureDropZonesExist] Vérification des zones de drop... capture-logs-unified.js:69:36
⚠️ [ensureDropZonesExist] Aucune zone trouvée, tentative de création... capture-logs-unified.js:69:36
🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours capture-logs-unified.js:69:36
⚠️ [renderUnifiedCalendar] Conteneur available-posts-container non trouvé, création... capture-logs-unified.js:69:36
✅ [renderUnifiedCalendar] Conteneur available-posts-container créé capture-logs-unified.js:69:36
✅ [renderUnifiedCalendar] Conteneurs trouvés: 
Object { employeeRows: true, availablePosts: true }
capture-logs-unified.js:69:36
🔄 [ensureDropZonesExist] renderUnifiedCalendar() exécuté capture-logs-unified.js:69:36
🔍 [ensureDropZonesExist] Après render: 0 zones trouvées capture-logs-unified.js:69:36
🚨 [ensureDropZonesExist] Création de zones temporaires pour debug... capture-logs-unified.js:69:36
🔧 [createTemporaryDropZones] 3 zones temporaires créées capture-logs-unified.js:69:36
🎯 [setupPostDragDrop] 0 zones de drop disponibles capture-logs-unified.js:69:36
🔧 [setupPostDragDrop] Configuration drag pour 0 postes (0 cachés + 0 visibles) capture-logs-unified.js:69:36
🔧 [setupCentralizedDropZone] Configuration simple des zones de drop capture-logs-unified.js:69:36
🎯 [setupCentralizedDropZone] Configuration de 3 lignes d'employés (via DOM direct) capture-logs-unified.js:69:36
✅ [setupCentralizedDropZone] Listeners configurés pour debug-employee-0 capture-logs-unified.js:69:36
✅ [setupCentralizedDropZone] Listeners configurés pour debug-employee-1 capture-logs-unified.js:69:36
✅ [setupCentralizedDropZone] Listeners configurés pour debug-employee-2 capture-logs-unified.js:69:36
✅ [setupCentralizedDropZone] 3 zones de drop configurées (via DOM direct) capture-logs-unified.js:69:36
✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (0 zones) capture-logs-unified.js:69:36
ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop capture-logs-unified.js:69:36
🔧 [addEmergencyFixButton] Ajout du bouton d'historique... capture-logs-unified.js:69:36
✅ [addEmergencyFixButton] Boutons ajoutés (emergency fix buttons supprimés) capture-logs-unified.js:69:36
✅ [init] Initialisation terminée avec succès capture-logs-unified.js:69:36
🔍 [Agenda] Vérification post-init: 
Object { teamCalendarApp: true, TeamCalendarApp: true, detectDropDateFromPosition: true, showConfirmationMenu: true, handleRegularAssignmentDrop: true }
capture-logs-unified.js:69:36
🎉 [Agenda] Application fullscreen initialisée avec succès capture-logs-unified.js:69:36
🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours capture-logs-unified.js:69:36
✅ [renderUnifiedCalendar] Conteneurs trouvés: 
Object { employeeRows: true, availablePosts: true }
capture-logs-unified.js:69:36
✅ [SETUP] Drag & drop des employés configuré (mode simplifié) capture-logs-unified.js:69:36
🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements capture-logs-unified.js:69:36
🔍 [ensureDropZonesExist] Vérification des zones de drop... capture-logs-unified.js:69:36
✅ [ensureDropZonesExist] 3 zones trouvées capture-logs-unified.js:69:36
🎯 [setupPostDragDrop] 3 zones de drop disponibles capture-logs-unified.js:69:36
🔧 [setupPostDragDrop] Configuration drag pour 0 postes (0 cachés + 0 visibles) capture-logs-unified.js:69:36
🔧 [setupCentralizedDropZone] Configuration simple des zones de drop capture-logs-unified.js:69:36
🎯 [setupCentralizedDropZone] Configuration de 3 lignes d'employés (via DOM direct) capture-logs-unified.js:69:36
🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour debug-employee-0 capture-logs-unified.js:69:36
✅ [setupCentralizedDropZone] Listeners configurés pour debug-employee-0 capture-logs-unified.js:69:36
🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour debug-employee-1 capture-logs-unified.js:69:36
✅ [setupCentralizedDropZone] Listeners configurés pour debug-employee-1 capture-logs-unified.js:69:36
🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour debug-employee-2 capture-logs-unified.js:69:36
✅ [setupCentralizedDropZone] Listeners configurés pour debug-employee-2 capture-logs-unified.js:69:36
✅ [setupCentralizedDropZone] 3 zones de drop configurées (via DOM direct) capture-logs-unified.js:69:36
✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (3 zones) capture-logs-unified.js:69:36
🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières capture-logs-unified.js:69:36
📊 [diagnoseRegularAssignmentGrips] Statistiques: 
Object { totalShifts: 0, regularShifts: 0, regularAssignments: 0 }
capture-logs-unified.js:69:36
🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 0 capture-logs-unified.js:69:36
⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions... capture-logs-unified.js:69:36
🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés capture-logs-unified.js:69:36
✅ [addSwapIconsToEmployees] Icônes de swap ajoutées capture-logs-unified.js:69:36
🚀 [TEST] Lancement du diagnostic automatique... capture-logs-unified.js:69:36
🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale... capture-logs-unified.js:69:36
📋 [DIAGNOSTIC] Éléments DOM: 
Object { modal: true, closeBtn: true, cancelBtn: true, confirmBtn: true, radioButtons: 4 }
capture-logs-unified.js:69:36
📋 [DIAGNOSTIC] TeamCalendarApp: 
Object { exists: true, hasModalManager: false, hasOpenFunction: false, hasHandleFunction: true }
capture-logs-unified.js:69:36
📋 [DIAGNOSTIC] État de la modale: 
Object { hidden: true, classes: (10) […], style: "" }
capture-logs-unified.js:69:36
📋 [DIAGNOSTIC] Données contextuelles: 
Object { currentContextAssignment: undefined, employees: 0, posts: 0 }
capture-logs-unified.js:69:36
🚀 [MANUAL-TEST] Lancement automatique du test... capture-logs-unified.js:69:36
🧪 [MANUAL-TEST] === TEST MANUEL DE LA MODALE === capture-logs-unified.js:69:36
📋 [MANUAL-TEST] 1. Vérification de la disponibilité... capture-logs-unified.js:69:36
TeamCalendarApp: true capture-logs-unified.js:69:36
teamCalendarApp: true capture-logs-unified.js:69:36
modalFunctionalities: true capture-logs-unified.js:69:36
🧪 [MANUAL-TEST] Test d'ouverture de la modale... capture-logs-unified.js:69:36
🎯 [openAssignmentContextModal] Ouverture du modal d'attribution 
Object { postData: {…}, employeeId: "test-employee", employeeName: "Employé Test", dateKey: "2025-07-18", position: {…} }
capture-logs-unified.js:69:36
📝 [populateAssignmentModal] Remplissage des données du modal capture-logs-unified.js:69:36
🔧 [setupAssignmentModalEvents] Configuration des événements du modal capture-logs-unified.js:69:36
✅ [openAssignmentContextModal] Modal d'attribution ouvert capture-logs-unified.js:69:36
✅ [MANUAL-TEST] Modale ouverte avec succès ! capture-logs-unified.js:69:36
✅ [MANUAL-TEST] Modale trouvée dans le DOM capture-logs-unified.js:69:36
Visible: true capture-logs-unified.js:69:36
Style display: block capture-logs-unified.js:69:36
✅ [MANUAL-TEST] 0 boutons trouvés capture-logs-unified.js:69:36
🚀 [VALIDATION] Lancement de la validation automatique... capture-logs-unified.js:69:36
🚀 [VALIDATION] Lancement de la validation complète... capture-logs-unified.js:69:36
🔍 [VALIDATION] Validation de la correction principale... capture-logs-unified.js:69:36
❌ [VALIDATION] ModalManager non disponible capture-logs-unified.js:69:36
✅ [VALIDATION-CRITIQUE] === VALIDATION 4: Éléments DOM critiques === <empty string> capture-logs-unified.js:69:36
❌ [VALIDATION-CRITIQUE] Aucune cellule de calendrier trouvée - CRITIQUE pour drag & drop <empty string> capture-logs-unified.js:69:36
🔍 [VALIDATION] Validation des listeners... capture-logs-unified.js:69:36
❌ [VALIDATION] Erreur lors de setupAssignmentContextModal: TypeError: can't access property "setupAssignmentContextModal", window.TeamCalendarApp.ModalManager is undefined
    validateListeners http://localhost:5173/validate-modal-fix.js:87
    runCompleteValidation http://localhost:5173/validate-modal-fix.js:103
    <anonymous> http://localhost:5173/validate-modal-fix.js:186
capture-logs-unified.js:69:36
📊 [VALIDATION] Résultats de validation: 
Object { mainFix: false, domElements: false, listeners: false }
capture-logs-unified.js:69:36
❌ [VALIDATION] Certaines validations ont échoué capture-logs-unified.js:69:36
🔧 [VALIDATION] Actions recommandées: capture-logs-unified.js:69:36
- Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés capture-logs-unified.js:69:36
- Vérifier que la modale assignment-context-modal existe dans le DOM capture-logs-unified.js:69:36
- Vérifier les erreurs dans setupAssignmentContextModal capture-logs-unified.js:69:36
🚀 [TEST] Lancement du diagnostic automatique... capture-logs-unified.js:69:36
🔍 [DIAGNOSTIC] Diagnostic complet du fork modal... capture-logs-unified.js:69:36
📊 [DIAGNOSTIC] Résultats: 
Object { teamCalendarApp: true, detectDropDateFunction: true, showConfirmationMenuFunction: true, handleRegularAssignmentDropFunction: true, regularAssignments: 0, employees: 0 }
capture-logs-unified.js:69:36
✅ [DIAGNOSTIC] Toutes les fonctions nécessaires sont disponibles capture-logs-unified.js:69:36
🚀 [TEST-DRAG-DROP] Lancement automatique des tests... capture-logs-unified.js:69:36
ℹ️ [TEST-DRAG-DROP] 🚀 Démarrage des tests complets de drag & drop <empty string> capture-logs-unified.js:69:36
ℹ️ [TEST-DRAG-DROP] Test 1: Vérification des prérequis <empty string> capture-logs-unified.js:69:36
✅ [TEST-DRAG-DROP] ✓ TeamCalendarApp disponible <empty string> capture-logs-unified.js:69:36
✅ [TEST-DRAG-DROP] ✓ modalFunctionalities disponible <empty string> capture-logs-unified.js:69:36
✅ [TEST-DRAG-DROP] ✓ Fonctions de drag & drop présentes <empty string> capture-logs-unified.js:69:36
✅ [TEST-DRAG-DROP] ✓ Modal d'attribution présent <empty string> capture-logs-unified.js:69:36
🚨 [TEST-DRAG-DROP] ✗ Postes disponibles (CRITIQUE) <empty string> capture-logs-unified.js:69:36
🚨 [TEST-DRAG-DROP] ✗ Employés disponibles (CRITIQUE) <empty string> capture-logs-unified.js:69:36
ℹ️ [TEST-DRAG-DROP] Prérequis: 4/6 réussis, 2 échecs critiques <empty string> capture-logs-unified.js:69:36
🚨 [TEST-DRAG-DROP] Tests arrêtés - Prérequis critiques non satisfaits <empty string> capture-logs-unified.js:69:36
📊 [TEST-DRAG-DROP] Résultats finaux: 
Object {  }
capture-logs-unified.js:69:36
🚀 [TEST-MODAL-FIX] Lancement automatique du test complet... capture-logs-unified.js:69:36
🧪 [TEST-MODAL-FIX] === TEST COMPLET DE LA CORRECTION MODALE === capture-logs-unified.js:69:36
📋 [TEST-MODAL-FIX] 1. Test de disponibilité globale... capture-logs-unified.js:69:36
✅ [TEST-MODAL-FIX] modalFunctionalities et TeamCalendarApp disponibles capture-logs-unified.js:69:36
📋 [TEST-MODAL-FIX] 2. Test de création de modal... capture-logs-unified.js:69:36
🔧 [createAssignmentContextModal] Création du modal d'attribution capture-logs-unified.js:69:36
✅ [TEST-MODAL-FIX] Modal créé avec succès capture-logs-unified.js:69:36
📋 [TEST-MODAL-FIX] 3. Test de gestion des événements... capture-logs-unified.js:69:36
🔧 [setupAssignmentModalEvents] Configuration des événements du modal capture-logs-unified.js:69:36
✅ [TEST-MODAL-FIX] Événements configurés avec succès capture-logs-unified.js:69:36
📋 [TEST-MODAL-FIX] 4. Test d'intégration drag & drop... capture-logs-unified.js:69:36
🎯 [openAssignmentContextModal] Ouverture du modal d'attribution 
Object { postData: {…}, employeeId: "test-employee", employeeName: "Test Employee", dateKey: "2025-07-18", position: {…} }
capture-logs-unified.js:69:36
📝 [populateAssignmentModal] Remplissage des données du modal capture-logs-unified.js:69:36
🔧 [setupAssignmentModalEvents] Configuration des événements du modal capture-logs-unified.js:69:36
✅ [openAssignmentContextModal] Modal d'attribution ouvert capture-logs-unified.js:69:36
✅ [TEST-MODAL-FIX] Intégration drag & drop fonctionnelle capture-logs-unified.js:69:36
📊 [TEST-MODAL-FIX] === RÉSUMÉ DES TESTS === capture-logs-unified.js:69:36
🔍 [TEST-MODAL-FIX] Résultats: 
Object { globalAvailability: true, modalCreation: true, eventHandling: true, dragDropIntegration: true, errors: [] }
capture-logs-unified.js:69:36
📊 [TEST-MODAL-FIX] Tests réussis: 4/4 (100.0%) capture-logs-unified.js:69:36
🎉 [TEST-MODAL-FIX] TOUS LES TESTS SONT PASSÉS ! La correction modale est fonctionnelle. capture-logs-unified.js:69:36
Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null). 5
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null). 3
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
🚀 [DATE-TEST] Lancement de la validation automatique des dates... capture-logs-unified.js:69:36
🚀 [DATE-TEST] Lancement de la suite de validation des dates... capture-logs-unified.js:69:36

🧪 [DATE-TEST] Exécution: Conversion de date sécurisée capture-logs-unified.js:69:36
🧪 [DATE-TEST] Test de la conversion de date sécurisée... capture-logs-unified.js:69:36
❌ [DATE-TEST] formatDateToKey non disponible capture-logs-unified.js:69:36

🧪 [DATE-TEST] Exécution: Logique de fork avec dates capture-logs-unified.js:69:36
🧪 [DATE-TEST] Test de la logique de fork avec dates... capture-logs-unified.js:69:36
❌ [DATE-TEST] Données insuffisantes pour le test capture-logs-unified.js:69:36

🧪 [DATE-TEST] Exécution: Améliorations timezone capture-logs-unified.js:69:36
🧪 [DATE-TEST] Test des améliorations de timezone... capture-logs-unified.js:69:36
⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé) capture-logs-unified.js:69:36

🧪 [DATE-TEST] Exécution: Détection de date avec timezone capture-logs-unified.js:69:36
🧪 [DATE-TEST] Test de la détection de date avec timezone... capture-logs-unified.js:69:36
🔍 [detectDropDateFromPosition] Analyse position pour employé test-employee capture-logs-unified.js:69:36
📍 [detectDropDateFromPosition] Position souris: (500, 300) capture-logs-unified.js:69:36
✅ [detectDropDateFromPosition] Date calculée via position: 2025-07-15 (jour 2) capture-logs-unified.js:69:36
✅ [DATE-TEST] Date détectée: 2025-07-15 capture-logs-unified.js:69:36
✅ [DATE-TEST] Format de date valide capture-logs-unified.js:69:36
✅ [DATE-TEST] Date raisonnable (3 jours de différence) capture-logs-unified.js:69:36

📊 [DATE-TEST] Résultats de la validation: capture-logs-unified.js:69:36
❌ Conversion de date sécurisée: ÉCHOUÉ capture-logs-unified.js:69:36
❌ Logique de fork avec dates: ÉCHOUÉ capture-logs-unified.js:69:36
✅ Améliorations timezone: PASSÉ capture-logs-unified.js:69:36
✅ Détection de date avec timezone: PASSÉ capture-logs-unified.js:69:36

⚠️ [DATE-TEST] Certaines validations ont échoué capture-logs-unified.js:69:36
🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails capture-logs-unified.js:69:36
🚀 [VALIDATION-CRITIQUE] Lancement automatique de la validation... capture-logs-unified.js:69:36
✅ [VALIDATION-CRITIQUE] 🚨 DÉMARRAGE DE LA VALIDATION CRITIQUE <empty string> capture-logs-unified.js:69:36
✅ [VALIDATION-CRITIQUE] === VALIDATION 1: Disponibilité TeamCalendarApp === <empty string> capture-logs-unified.js:69:36
✅ [VALIDATION-CRITIQUE] window.teamCalendarApp disponible <empty string> capture-logs-unified.js:69:36
✅ [VALIDATION-CRITIQUE] window.TeamCalendarApp disponible <empty string> capture-logs-unified.js:69:36
✅ [VALIDATION-CRITIQUE] Fonction detectDropDateFromPosition disponible <empty string> capture-logs-unified.js:69:36
✅ [VALIDATION-CRITIQUE] Fonction showConfirmationMenu disponible <empty string> capture-logs-unified.js:69:36
✅ [VALIDATION-CRITIQUE] Fonction handleRegularAssignmentDrop disponible <empty string> capture-logs-unified.js:69:36
✅ [VALIDATION-CRITIQUE] === VALIDATION 2: modalFunctionalities === <empty string> capture-logs-unified.js:69:36
✅ [VALIDATION-CRITIQUE] window.modalFunctionalities disponible <empty string> capture-logs-unified.js:69:36
✅ [VALIDATION-CRITIQUE] openAssignmentContextModal disponible <empty string> capture-logs-unified.js:69:36
✅ [VALIDATION-CRITIQUE] === VALIDATION 3: Données de l'application === <empty string> capture-logs-unified.js:69:36
❌ [VALIDATION-CRITIQUE] Aucun poste disponible - CRITIQUE pour drag & drop <empty string> capture-logs-unified.js:69:36
✅ [VALIDATION-CRITIQUE] === VALIDATION 4: Éléments DOM critiques === <empty string> capture-logs-unified.js:69:36
❌ [VALIDATION-CRITIQUE] Aucune cellule de calendrier trouvée - CRITIQUE pour drag & drop <empty string> capture-logs-unified.js:69:36
✅ [VALIDATION-CRITIQUE] === VALIDATION 5: Fonctionnalité drag & drop === <empty string> capture-logs-unified.js:69:36
🔍 [detectDropDateFromPosition] Analyse position pour employé 100 capture-logs-unified.js:69:36
📍 [detectDropDateFromPosition] Position souris: (undefined, undefined) capture-logs-unified.js:69:36
❌ [VALIDATION-CRITIQUE] Erreur lors du test fonctionnel Document.elementFromPoint: Argument 1 is not a finite floating-point value. capture-logs-unified.js:69:36
❌ [VALIDATION-CRITIQUE] ❌ VALIDATION ÉCHOUÉE: 4 erreur(s) critique(s) <empty string> capture-logs-unified.js:69:36
📊 [VALIDATION-CRITIQUE] RÉSUMÉ FINAL: 
Object { errors: 4, warnings: 0, successes: 14, total: 18, criticalIssues: 4, status: "FAILED", testsTotal: 5, testsPassed: 2, testsFailed: 3, endTime: Date Fri Jul 18 2025 14:43:15 GMT-0400 (heure avancée de l’Est), … }
capture-logs-unified.js:69:36
📋 [VALIDATION-CRITIQUE] ERREURS: 
Array(5) [ {…}, {…}, {…}, {…}, {…} ]
capture-logs-unified.js:69:36
⚠️ [VALIDATION-CRITIQUE] AVERTISSEMENTS: 
Array []
capture-logs-unified.js:69:36
❌ [VALIDATION-CRITIQUE] DES ERREURS CRITIQUES PERSISTENT ! capture-logs-unified.js:69:36
🚀 [FINAL-VALIDATION] Lancement de la validation finale... capture-logs-unified.js:69:36
🎯 [FINAL-VALIDATION] === VALIDATION FINALE DE LA CORRECTION MODALE === capture-logs-unified.js:69:36
📋 [FINAL-VALIDATION] 1. Validation de l'architecture... capture-logs-unified.js:69:36
✅ [FINAL-VALIDATION] Architecture externe correcte capture-logs-unified.js:69:36
📋 [FINAL-VALIDATION] 2. Validation de l'intégration... capture-logs-unified.js:69:36
✅ [FINAL-VALIDATION] Intégration avec TeamCalendarApp correcte capture-logs-unified.js:69:36
📋 [FINAL-VALIDATION] 3. Validation de la fonctionnalité... capture-logs-unified.js:69:36
🎯 [openAssignmentContextModal] Ouverture du modal d'attribution 
Object { postData: {…}, employeeId: "validation-employee", employeeName: "Employé de Validation", dateKey: "2025-07-18", position: {…} }
capture-logs-unified.js:69:36
🔧 [openAssignmentContextModal] Création du modal d'attribution capture-logs-unified.js:69:36
🔧 [createAssignmentContextModal] Création du modal d'attribution capture-logs-unified.js:69:36
📝 [populateAssignmentModal] Remplissage des données du modal capture-logs-unified.js:69:36
🔧 [setupAssignmentModalEvents] Configuration des événements du modal capture-logs-unified.js:69:36
✅ [openAssignmentContextModal] Modal d'attribution ouvert capture-logs-unified.js:69:36
📋 [FINAL-VALIDATION] 4. Validation du workflow drag & drop... capture-logs-unified.js:69:36
✅ [FINAL-VALIDATION] Workflow drag & drop disponible capture-logs-unified.js:69:36
📋 [FINAL-VALIDATION] 5. Validation de la gestion d'erreurs... capture-logs-unified.js:69:36
🎯 [openAssignmentContextModal] Ouverture du modal d'attribution null capture-logs-unified.js:69:36
📝 [populateAssignmentModal] Remplissage des données du modal capture-logs-unified.js:69:36
❌ [populateAssignmentModal] Erreur: TypeError: can't access property "postData", modalData is null
    populateAssignmentModal modalFunctionalities.ts:1212
    openAssignmentContextModal modalFunctionalities.ts:1115
    runFinalModalValidation final-modal-validation.js:136
    <anonymous> final-modal-validation.js:234
capture-logs-unified.js:69:36
🔧 [setupAssignmentModalEvents] Configuration des événements du modal capture-logs-unified.js:69:36
✅ [openAssignmentContextModal] Modal d'attribution ouvert capture-logs-unified.js:69:36
✅ [FINAL-VALIDATION] Gestion d'erreurs robuste capture-logs-unified.js:69:36
✅ [FINAL-VALIDATION] Fonctionnalité modale correcte capture-logs-unified.js:69:36
✅ [FINAL-VALIDATION] Boutons du modal présents capture-logs-unified.js:69:36
🚀 [GRIP-FIX] Lancement du diagnostic automatique... capture-logs-unified.js:69:36
🔍 [GRIP-FIX] Diagnostic complet des GRIP handles... capture-logs-unified.js:69:36
📊 [GRIP-FIX] Total shifts trouvés: 0 capture-logs-unified.js:69:36
📊 [GRIP-FIX] Résultats du diagnostic: 
Object { totalShifts: 0, regularShifts: 0, gripsInDOM: 0, gripsWithEvents: 0, gripsResponsive: 0, issues: [] }
capture-logs-unified.js:69:36
🎯 [FINAL-VALIDATION] === RÉSUMÉ FINAL === capture-logs-unified.js:69:36
📊 [FINAL-VALIDATION] Score: 100 /100 capture-logs-unified.js:69:36
🔍 [FINAL-VALIDATION] Détails: 
Object { architecture: true, integration: true, functionality: true, dragDropWorkflow: true, errorHandling: true, score: 100, errors: [], warnings: [] }
capture-logs-unified.js:69:36
🎉 [FINAL-VALIDATION] CORRECTION RÉUSSIE ! La modale fonctionne correctement. capture-logs-unified.js:69:36
✅ [FINAL-VALIDATION] L'architecture externe est maintenue et fonctionnelle. capture-logs-unified.js:69:36
Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null). 7
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
Propriété « -moz-osx-font-smoothing » inconnue.  Déclaration abandonnée. icon:22:27
Propriété « -moz-osx-font-smoothing » inconnue.  Déclaration abandonnée. localhost:5173:29:27
Erreur d’analyse de la valeur pour « -webkit-text-size-adjust ».  Déclaration abandonnée. localhost:5173:479:29
Jeu de règles ignoré suite à un mauvais sélecteur. localhost:5173:260:27
XHROPTIONS
http://localhost:3001/api/logs
CORS Failed

Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
XHROPTIONS
http://localhost:3001/api/logs
CORS Failed

Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
XHROPTIONS
http://localhost:3001/api/logs
CORS Failed

Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
XHROPTIONS
http://localhost:3001/api/logs
CORS Failed

Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
XHRPOST
http://localhost:3001/api/logs
NS_ERROR_DOM_BAD_URI

Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
XHRPOST
http://localhost:3001/api/logs
NS_ERROR_DOM_BAD_URI

Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
XHRPOST
http://localhost:3001/api/logs
NS_ERROR_DOM_BAD_URI

Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
XHRPOST
http://localhost:3001/api/logs
NS_ERROR_DOM_BAD_URI

[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
XHROPTIONS
http://localhost:3001/api/logs
CORS Failed

XHROPTIONS
http://localhost:3001/api/logs
CORS Failed

Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
XHRPOST
http://localhost:3001/api/logs
NS_ERROR_DOM_BAD_URI

Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
XHRPOST
http://localhost:3001/api/logs
NS_ERROR_DOM_BAD_URI

Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
[CAPTURE-SIMPLIFIÉE] Échec de l'envoi du log: TypeError: NetworkError when attempting to fetch resource. capture-logs-unified.js:62:29
XHROPTIONS
http://localhost:3001/api/logs
CORS Failed

Blocage d’une requête multiorigine (Cross-Origin Request) : la politique « Same Origin » ne permet pas de consulter la ressource distante située sur http://localhost:3001/api/logs. Raison : échec de la requête CORS. Code d’état : (null).
XHRPOST
http://localhost:3001/api/logs
NS_ERROR_DOM_BAD_URI

XHROPTIONS
http://localhost:3001/api/logs
CORS Failed

XHRPOST
http://localhost:3001/api/logs
NS_ERROR_DOM_BAD_URI

