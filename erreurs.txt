[18/07/2025 14:59:13] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Fonctions de validation disponibles:
[18/07/2025 14:59:13] [FRONTEND] [LOG] - runFinalModalValidation() : Validation complète
[18/07/2025 14:59:13] [FRONTEND] [LOG] - quickModalTest() : Test rapide
[18/07/2025 14:59:13] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 14:59:13] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:13.492Z"
}
[18/07/2025 14:59:13] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:13.623Z"
}
[18/07/2025 14:59:14] [BACKEND] [INFO] [LOGS] Client connecté: 1752865153490
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:13.640Z"
}
[18/07/2025 14:58:49] [FRONTEND] [LOG] ✅ [DATE-TEST] Format de date valide
[18/07/2025 14:58:49] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 14:58:49] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 14:58:49] [FRONTEND] [LOG] ✅ [DATE-TEST] Date raisonnable (3 jours de différence)
[18/07/2025 14:58:49] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 14:58:49] [FRONTEND] [LOG] ✅ Logique de fork avec dates: PASSÉ
[18/07/2025 14:58:49] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 14:58:49] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 14:58:49] [FRONTEND] [LOG] ✅ Détection de date avec timezone: PASSÉ
[18/07/2025 14:58:49] [FRONTEND] [LOG] 🚀 [VALIDATION-CRITIQUE] Lancement automatique de la validation...
[18/07/2025 14:58:49] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] 🚨 DÉMARRAGE DE LA VALIDATION CRITIQUE 
[18/07/2025 14:58:49] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] window.teamCalendarApp disponible 
[18/07/2025 14:58:49] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] === VALIDATION 1: Disponibilité TeamCalendarApp === 
[18/07/2025 14:58:49] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] window.TeamCalendarApp disponible 
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] Fonction detectDropDateFromPosition disponible 
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] Fonction showConfirmationMenu disponible 
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] === VALIDATION 3: Données de l'application === 
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] Fonction handleRegularAssignmentDrop disponible 
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] === VALIDATION 2: modalFunctionalities === 
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] window.modalFunctionalities disponible 
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] openAssignmentContextModal disponible 
[18/07/2025 14:58:50] [FRONTEND] [ERROR] ❌ [VALIDATION-CRITIQUE] Aucun poste disponible - CRITIQUE pour drag & drop 
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] === VALIDATION 4: Éléments DOM critiques === 
[18/07/2025 14:58:50] [FRONTEND] [ERROR] ❌ [VALIDATION-CRITIQUE] Aucune cellule de calendrier trouvée - CRITIQUE pour drag & drop 
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] === VALIDATION 5: Fonctionnalité drag & drop === 
[18/07/2025 14:58:50] [FRONTEND] [LOG] 🔍 [detectDropDateFromPosition] Analyse position pour employé 100
[18/07/2025 14:58:50] [FRONTEND] [LOG] 📋 [VALIDATION-CRITIQUE] ERREURS: [{"message":"Aucune cellule de calendrier trouvée - CRITIQUE pour drag & drop","details":null,"timestamp":"2025-07-18T18:58:44.933Z"},{"message":"Aucun poste disponible - CRITIQUE pour drag & drop","details":null,"timestamp":"2025-07-18T18:58:46.081Z"},{"message":"Aucune cellule de calendrier trouvée - CRITIQUE pour drag & drop","details":null,"timestamp":"2025-07-18T18:58:46.091Z"},{"message":"Erreur lors du test fonctionnel","details":"Document.elementFromPoint: Argument 1 is not a finite floating-point value.","timestamp":"2025-07-18T18:58:46.169Z"},{"message":"❌ VALIDATION ÉCHOUÉE: 4 erreur(s) critique(s)","details":null,"timestamp":"2025-07-18T18:58:46.171Z"}]
[18/07/2025 14:58:50] [FRONTEND] [LOG] 📍 [detectDropDateFromPosition] Position souris: (undefined, undefined)
[18/07/2025 14:58:50] [FRONTEND] [LOG] ❌ [VALIDATION-CRITIQUE] DES ERREURS CRITIQUES PERSISTENT !
[18/07/2025 14:58:50] [FRONTEND] [ERROR] ❌ [VALIDATION-CRITIQUE] Erreur lors du test fonctionnel Document.elementFromPoint: Argument 1 is not a finite floating-point value.
[18/07/2025 14:58:50] [FRONTEND] [ERROR] ❌ [VALIDATION-CRITIQUE] ❌ VALIDATION ÉCHOUÉE: 4 erreur(s) critique(s) 
[18/07/2025 14:58:50] [FRONTEND] [LOG] 📊 [VALIDATION-CRITIQUE] RÉSUMÉ FINAL: {"errors":4,"warnings":0,"successes":14,"total":18,"criticalIssues":4,"status":"FAILED","testsTotal":5,"testsPassed":2,"testsFailed":3,"endTime":"2025-07-18T18:58:46.171Z","duration":155}
[18/07/2025 14:58:50] [FRONTEND] [LOG] 🚀 [FINAL-VALIDATION] Lancement de la validation finale...
[18/07/2025 14:58:50] [FRONTEND] [LOG] ⚠️ [VALIDATION-CRITIQUE] AVERTISSEMENTS: []
[18/07/2025 14:58:50] [FRONTEND] [LOG] 🎯 [FINAL-VALIDATION] === VALIDATION FINALE DE LA CORRECTION MODALE ===
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Architecture externe correcte
[18/07/2025 14:58:50] [FRONTEND] [LOG] 📋 [FINAL-VALIDATION] 2. Validation de l'intégration...
[18/07/2025 14:58:50] [FRONTEND] [LOG] 📋 [FINAL-VALIDATION] 1. Validation de l'architecture...
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Intégration avec TeamCalendarApp correcte
[18/07/2025 14:58:50] [FRONTEND] [LOG] 📋 [FINAL-VALIDATION] 3. Validation de la fonctionnalité...
[18/07/2025 14:58:50] [FRONTEND] [LOG] 🔧 [setupAssignmentModalEvents] Configuration des événements du modal
[18/07/2025 14:58:50] [FRONTEND] [LOG] 🔧 [createAssignmentContextModal] Création du modal d'attribution
[18/07/2025 14:58:50] [FRONTEND] [LOG] 🔧 [openAssignmentContextModal] Création du modal d'attribution
[18/07/2025 14:58:50] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Ouverture du modal d'attribution {"postData":{"id":"validation-post","label":"Poste de Validation"},"employeeId":"validation-employee","employeeName":"Employé de Validation","dateKey":"2025-07-18","position":{"x":200,"y":200}}
[18/07/2025 14:58:50] [FRONTEND] [LOG] 📝 [populateAssignmentModal] Remplissage des données du modal
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [openAssignmentContextModal] Modal d'attribution ouvert
[18/07/2025 14:58:50] [FRONTEND] [LOG] 📋 [FINAL-VALIDATION] 4. Validation du workflow drag & drop...
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Workflow drag & drop disponible
[18/07/2025 14:58:50] [FRONTEND] [LOG] 📋 [FINAL-VALIDATION] 5. Validation de la gestion d'erreurs...
[18/07/2025 14:58:50] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Ouverture du modal d'attribution null
[18/07/2025 14:58:50] [FRONTEND] [LOG] 📝 [populateAssignmentModal] Remplissage des données du modal
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [openAssignmentContextModal] Modal d'attribution ouvert
[18/07/2025 14:58:50] [FRONTEND] [LOG] 🔧 [setupAssignmentModalEvents] Configuration des événements du modal
[18/07/2025 14:58:50] [FRONTEND] [ERROR] ❌ [populateAssignmentModal] Erreur: Error: can't access property "postData", modalData is null
modalFunctionalities.populateAssignmentModal@http://localhost:5173/src/modalFunctionalities.ts:944:21
modalFunctionalities.openAssignmentContextModal@http://localhost:5173/src/modalFunctionalities.ts:863:10
runFinalModalValidation@http://localhost:5173/final-modal-validation.js:136:41
@http://localhost:5173/final-modal-validation.js:234:5
setTimeout handler*@http://localhost:5173/final-modal-validation.js:232:11

[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Gestion d'erreurs robuste
[18/07/2025 14:58:50] [FRONTEND] [ERROR] ❌ [DATE-TEST] Modal non ouvert
[18/07/2025 14:58:50] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 14:58:50] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 14:58:50] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 1
[18/07/2025 14:58:50] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":1,"regularShifts":1,"gripsInDOM":1,"gripsWithEvents":0,"gripsResponsive":1,"issues":[]}
[18/07/2025 14:58:50] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Test responsivité ed906322-db11-4d06-aa28-437b06c445c4: {"hasMouseEvents":false,"isVisible":true,"hasCorrectCursor":true,"hasValidDimensions":true,"rect":{"width":130,"height":6}}
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Fonctionnalité modale correcte
[18/07/2025 14:58:50] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Boutons du modal présents
[18/07/2025 14:58:52] [FRONTEND] [LOG] 🔄 [showRegularAssignmentConfirmationMenu] Changement permanent demandé
[18/07/2025 14:58:52] [FRONTEND] [WARN] ⚠️ [showRegularAssignmentConfirmationMenu] handlePermanentRegularAssignmentChange non disponible
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:03] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"7674f144-fe6f-4f77-b054-34524d7603b8","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 7674f144-fe6f-4f77-b054-34524d7603b8 (Poste Matin)
[18/07/2025 14:59:03] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 25 cellules
[18/07/2025 14:59:03] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎯 [POST DRAG] 62 zones préparées pour le drop
[18/07/2025 14:59:03] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 7674f144-fe6f-4f77-b054-34524d7603b8 (Poste Matin)
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"7674f144-fe6f-4f77-b054-34524d7603b8","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 14:59:03] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 14:59:03] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[18/07/2025 14:59:03] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 50 cellules
[18/07/2025 14:59:03] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎯 [POST DRAG] 62 zones préparées pour le drop
[18/07/2025 14:59:03] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.814Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.632Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.621Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.630Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.629Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.635Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé cf78e945-72c7-48f3-a72d-bd0e245a284d, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé cf78e945-72c7-48f3-a72d-bd0e245a284d, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.818Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.808Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.985Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.983Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.981Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.986Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.987Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.816Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.821Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:02.819Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:03.013Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Lucas Bernard
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 14:59:03] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 14:59:03] [FRONTEND] [LOG] ✅ [DROP] PostId trouvé via application/x-post-id: "7674f144-fe6f-4f77-b054-34524d7603b8"
[18/07/2025 14:59:03] [FRONTEND] [LOG] 📦 [DROP] Nouveau poste: 7674f144-fe6f-4f77-b054-34524d7603b8 → cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:03] [FRONTEND] [ERROR] ❌ [DROP] Éléments manquants: ["assignment-post-name","assignment-employee-name"]
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 62 zones
[18/07/2025 14:59:03] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 14:59:03] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 62 zones
[18/07/2025 14:59:03] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 14:59:11] [BACKEND] [INFO] [LOGS] Client déconnecté: 1752864715270
[18/07/2025 14:59:11] [FRONTEND] [ERROR] [LogsPage] Erreur SSE: {"isTrusted":true}
[18/07/2025 14:59:12] [FRONTEND] [DEBUG] [vite] connected.
[18/07/2025 14:59:13] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[18/07/2025 14:59:13] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[18/07/2025 14:59:13] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[18/07/2025 14:59:13] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[18/07/2025 14:59:13] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[18/07/2025 14:59:13] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[18/07/2025 14:59:13] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[18/07/2025 14:59:13] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[18/07/2025 14:59:13] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 14:59:13] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 14:59:13] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 14:59:13] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 14:59:13] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 14:59:13] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 14:59:13] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 14:59:13] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 14:59:13] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 14:59:13] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[18/07/2025 14:59:13] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 14:59:13] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 14:59:13] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 14:59:13] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 14:59:13] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🧪 [TEST-DRAG-DROP] Démarrage des tests complets de drag & drop...
[18/07/2025 14:59:13] [FRONTEND] [LOG] ✅ [TEST-DRAG-DROP] Script chargé. Utilisez runComprehensiveDragDropTests() pour tester.
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🚨 [VALIDATION-CRITIQUE] Démarrage de la validation des corrections...
[18/07/2025 14:59:13] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] Script chargé. Utilisez runCriticalValidation() pour valider.
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🚀 [DEBUG-MODAL] Script de débogage de l'intégration modale chargé
[18/07/2025 14:59:13] [FRONTEND] [LOG] ✅ [DEBUG-MODAL] Fonctions de débogage disponibles:
[18/07/2025 14:59:13] [FRONTEND] [LOG] - diagnoseModalIntegration() : Diagnostic complet
[18/07/2025 14:59:13] [FRONTEND] [LOG] - forceModalIntegration() : Forcer l'intégration
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testCompleteModalWorkflow() : Test complet du workflow
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🚀 [TEST-MODAL-FIX] Script de test complet de la correction modale chargé
[18/07/2025 14:59:13] [FRONTEND] [LOG] ✅ [TEST-MODAL-FIX] Fonctions de test disponibles:
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testModalFixComplete() : Test complet de la correction
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testModalButtons() : Test spécifique des boutons du modal
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🚀 [MANUAL-TEST] Script de test manuel de la modale chargé
[18/07/2025 14:59:13] [FRONTEND] [LOG] ✅ [MANUAL-TEST] Fonctions disponibles:
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testModalManually() : Test complet
[18/07/2025 14:59:13] [FRONTEND] [LOG] - testButton(action) : Tester un bouton spécifique
[18/07/2025 14:59:13] [FRONTEND] [LOG] - closeModal() : Fermer la modale
[18/07/2025 14:59:13] [FRONTEND] [LOG]   Actions disponibles: "regular", "temporary", "replacement", "cancel", "close"
[18/07/2025 14:59:13] [FRONTEND] [LOG] 🚀 [FINAL-VALIDATION] Script de validation finale de la correction modale chargé
[18/07/2025 14:59:13] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Fonctions de validation disponibles:
[18/07/2025 14:59:13] [FRONTEND] [LOG] - runFinalModalValidation() : Validation complète
[18/07/2025 14:59:13] [FRONTEND] [LOG] - quickModalTest() : Test rapide
[18/07/2025 14:59:13] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 14:59:13] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[18/07/2025 14:59:13] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:13.492Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[18/07/2025 14:59:13] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 14:59:14] [BACKEND] [INFO] ✅ Requête exécutée en 239ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 239ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 239ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:13.731Z"
}
[18/07/2025 14:59:15] [FRONTEND] [LOG] 🚀 [DEBUG-MODAL] Lancement automatique du diagnostic...
[18/07/2025 14:59:15] [FRONTEND] [LOG] 🔍 [DEBUG-MODAL] === DIAGNOSTIC COMPLET DE L'INTÉGRATION MODALE ===
[18/07/2025 14:59:15] [FRONTEND] [LOG] 📋 [DEBUG-MODAL] 1. Vérification TeamCalendarApp...
[18/07/2025 14:59:15] [FRONTEND] [LOG] 📋 [DEBUG-MODAL] 2. Vérification modalFunctionalities local...
[18/07/2025 14:59:15] [FRONTEND] [ERROR] ❌ [DEBUG-MODAL] TeamCalendarApp non disponible
[18/07/2025 14:59:15] [FRONTEND] [ERROR] ❌ [DEBUG-MODAL] modalFunctionalities local non accessible
[18/07/2025 14:59:15] [FRONTEND] [LOG] 📋 [DEBUG-MODAL] 3. Vérification modalFunctionalities global...
[18/07/2025 14:59:15] [FRONTEND] [ERROR] ❌ [DEBUG-MODAL] window.modalFunctionalities non disponible
[18/07/2025 14:59:15] [FRONTEND] [LOG] 📋 [DEBUG-MODAL] 4. Vérification modal d'attribution...
[18/07/2025 14:59:15] [FRONTEND] [LOG] 📋 [DEBUG-MODAL] 5. Vérification éléments drag & drop...
[18/07/2025 14:59:15] [FRONTEND] [ERROR] ❌ [DEBUG-MODAL] Modal assignment-context-modal non trouvé
[18/07/2025 14:59:15] [FRONTEND] [ERROR] ❌ [DEBUG-MODAL] Éléments drag & drop manquants
[18/07/2025 14:59:15] [FRONTEND] [LOG] 🔍 [DEBUG-MODAL] Cellules: 0, Employés: 0, Postes: 0
[18/07/2025 14:59:15] [FRONTEND] [LOG] 📊 [DEBUG-MODAL] === RÉSUMÉ DU DIAGNOSTIC ===
[18/07/2025 14:59:15] [FRONTEND] [ERROR] ❌ [DEBUG-MODAL] Erreurs détectées:
[18/07/2025 14:59:15] [FRONTEND] [ERROR]    1. TeamCalendarApp non disponible globalement
[18/07/2025 14:59:15] [FRONTEND] [LOG] 🔍 [DEBUG-MODAL] Résultats: {"teamCalendarApp":false,"modalFunctionalities":false,"globalModalFunctionalities":false,"assignmentModal":false,"dragDropElements":false,"errors":["TeamCalendarApp non disponible globalement","modalFunctionalities local non accessible","window.modalFunctionalities non disponible","Modal assignment-context-modal non trouvé","Éléments drag & drop manquants"]}
[18/07/2025 14:59:15] [FRONTEND] [ERROR]    2. modalFunctionalities local non accessible
[18/07/2025 14:59:15] [FRONTEND] [ERROR]    3. window.modalFunctionalities non disponible
[18/07/2025 14:59:16] [FRONTEND] [ERROR]    4. Modal assignment-context-modal non trouvé
[18/07/2025 14:59:16] [FRONTEND] [ERROR]    5. Éléments drag & drop manquants
[18/07/2025 14:59:16] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 14:59:16] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 14:59:16] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":0}
[18/07/2025 14:59:16] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":false,"hasModalManager":false,"hasOpenFunction":false,"hasHandleFunction":false}
[18/07/2025 14:59:16] [FRONTEND] [LOG] 🚀 [MANUAL-TEST] Lancement automatique du test...
[18/07/2025 14:59:16] [FRONTEND] [LOG] 🧪 [MANUAL-TEST] === TEST MANUEL DE LA MODALE ===
[18/07/2025 14:59:16] [FRONTEND] [LOG] 📋 [MANUAL-TEST] 1. Vérification de la disponibilité...
[18/07/2025 14:59:16] [FRONTEND] [LOG] teamCalendarApp: false
[18/07/2025 14:59:16] [FRONTEND] [LOG] TeamCalendarApp: false
[18/07/2025 14:59:16] [FRONTEND] [LOG] modalFunctionalities: false
[18/07/2025 14:59:16] [FRONTEND] [ERROR] ❌ [MANUAL-TEST] Fonction openAssignmentContextModal non disponible
[18/07/2025 14:59:17] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 14:59:17] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 14:59:17] [FRONTEND] [ERROR] ❌ [VALIDATION] TeamCalendarApp non disponible
[18/07/2025 14:59:17] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 14:59:17] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] === VALIDATION 4: Éléments DOM critiques === 
[18/07/2025 14:59:17] [FRONTEND] [ERROR] ❌ [VALIDATION-CRITIQUE] Aucune cellule de calendrier trouvée - CRITIQUE pour drag & drop 
[18/07/2025 14:59:17] [FRONTEND] [ERROR] ❌ [VALIDATION] Erreur lors de setupAssignmentContextModal: Error: can't access property "ModalManager", window.TeamCalendarApp is undefined
validateListeners@http://localhost:5173/validate-modal-fix.js:87:9
runCompleteValidation@http://localhost:5173/validate-modal-fix.js:103:20
@http://localhost:5173/validate-modal-fix.js:186:5

[18/07/2025 14:59:17] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 14:59:17] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[18/07/2025 14:59:17] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":false,"listeners":false}
[18/07/2025 14:59:17] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[18/07/2025 14:59:17] [FRONTEND] [LOG] - Vérifier que la modale assignment-context-modal existe dans le DOM
[18/07/2025 14:59:17] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 14:59:17] [FRONTEND] [LOG] - Vérifier les erreurs dans setupAssignmentContextModal
[18/07/2025 14:59:17] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[18/07/2025 14:59:17] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 14:59:17] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":false,"detectDropDateFunction":false,"showConfirmationMenuFunction":false,"handleRegularAssignmentDropFunction":false,"regularAssignments":0,"employees":0}
[18/07/2025 14:59:18] [FRONTEND] [ERROR] ❌ [DIAGNOSTIC] Fonctions manquantes détectées
[18/07/2025 14:59:18] [FRONTEND] [LOG] ⏳ [TEST-DRAG-DROP] En attente du chargement de l'application...
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🚀 [TEST-MODAL-FIX] Lancement automatique du test complet...
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🧪 [TEST-MODAL-FIX] === TEST COMPLET DE LA CORRECTION MODALE ===
[18/07/2025 14:59:18] [FRONTEND] [LOG] 📋 [TEST-MODAL-FIX] 1. Test de disponibilité globale...
[18/07/2025 14:59:18] [FRONTEND] [ERROR] ❌ [TEST-MODAL-FIX] modalFunctionalities ou TeamCalendarApp manquants
[18/07/2025 14:59:18] [FRONTEND] [LOG] 📋 [TEST-MODAL-FIX] 2. Test de création de modal...
[18/07/2025 14:59:18] [FRONTEND] [ERROR] ❌ [TEST-MODAL-FIX] Fonction createAssignmentContextModal non disponible
[18/07/2025 14:59:18] [FRONTEND] [LOG] 📋 [TEST-MODAL-FIX] 3. Test de gestion des événements...
[18/07/2025 14:59:18] [FRONTEND] [ERROR] ❌ [TEST-MODAL-FIX] Modal ou fonction setupAssignmentModalEvents non disponible
[18/07/2025 14:59:18] [FRONTEND] [LOG] 📋 [TEST-MODAL-FIX] 4. Test d'intégration drag & drop...
[18/07/2025 14:59:18] [FRONTEND] [ERROR] ❌ [TEST-MODAL-FIX] Fonction openAssignmentContextModal non disponible
[18/07/2025 14:59:18] [FRONTEND] [LOG] 📊 [TEST-MODAL-FIX] === RÉSUMÉ DES TESTS ===
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🔍 [TEST-MODAL-FIX] Résultats: {"globalAvailability":false,"modalCreation":false,"eventHandling":false,"dragDropIntegration":false,"errors":["modalFunctionalities ou TeamCalendarApp non disponibles","Fonction createAssignmentContextModal non disponible","Modal ou fonction setupAssignmentModalEvents non disponible","Fonction openAssignmentContextModal non disponible"]}
[18/07/2025 14:59:18] [FRONTEND] [ERROR] ❌ [TEST-MODAL-FIX] Erreurs détectées:
[18/07/2025 14:59:18] [FRONTEND] [LOG] 📊 [TEST-MODAL-FIX] Tests réussis: 0/4 (0.0%)
[18/07/2025 14:59:18] [FRONTEND] [ERROR]    1. modalFunctionalities ou TeamCalendarApp non disponibles
[18/07/2025 14:59:18] [FRONTEND] [ERROR]    2. Fonction createAssignmentContextModal non disponible
[18/07/2025 14:59:18] [FRONTEND] [ERROR]    3. Modal ou fonction setupAssignmentModalEvents non disponible
[18/07/2025 14:59:18] [FRONTEND] [ERROR]    4. Fonction openAssignmentContextModal non disponible
[18/07/2025 14:59:18] [FRONTEND] [LOG] ❌ [TEST-MODAL-FIX] Plusieurs tests ont échoué. La correction nécessite des ajustements.
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 14:59:18] [FRONTEND] [ERROR] ❌ [DATE-TEST] formatDateToKey non disponible
[18/07/2025 14:59:18] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[18/07/2025 14:59:18] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 14:59:18] [FRONTEND] [ERROR] ❌ [DATE-TEST] Données insuffisantes pour le test
[18/07/2025 14:59:18] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 14:59:18] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 14:59:18] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 14:59:18] [FRONTEND] [ERROR] ❌ [DATE-TEST] detectDropDateFromPosition non disponible
[18/07/2025 14:59:18] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 14:59:18] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 14:59:18] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 14:59:18] [FRONTEND] [LOG] ❌ Logique de fork avec dates: ÉCHOUÉ
[18/07/2025 14:59:18] [FRONTEND] [LOG] ❌ Détection de date avec timezone: ÉCHOUÉ
[18/07/2025 14:59:18] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 14:59:18] [FRONTEND] [LOG] ⏳ [VALIDATION-CRITIQUE] En attente du chargement de TeamCalendarApp...
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🚀 [FINAL-VALIDATION] Lancement de la validation finale...
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🎯 [FINAL-VALIDATION] === VALIDATION FINALE DE LA CORRECTION MODALE ===
[18/07/2025 14:59:18] [FRONTEND] [LOG] 📋 [FINAL-VALIDATION] 1. Validation de l'architecture...
[18/07/2025 14:59:18] [FRONTEND] [ERROR] ❌ [FINAL-VALIDATION] modalFunctionalities ou TeamCalendarApp non disponibles
[18/07/2025 14:59:18] [FRONTEND] [LOG] 📋 [FINAL-VALIDATION] 2. Validation de l'intégration...
[18/07/2025 14:59:18] [FRONTEND] [ERROR] ❌ [FINAL-VALIDATION] showConfirmationMenu non disponible
[18/07/2025 14:59:18] [FRONTEND] [LOG] 📋 [FINAL-VALIDATION] 3. Validation de la fonctionnalité...
[18/07/2025 14:59:18] [FRONTEND] [ERROR] ❌ [FINAL-VALIDATION] Erreur test fonctionnalité: Error: can't access property "openAssignmentContextModal", window.modalFunctionalities is undefined
runFinalModalValidation@http://localhost:5173/final-modal-validation.js:76:13
@http://localhost:5173/final-modal-validation.js:234:5

[18/07/2025 14:59:18] [FRONTEND] [LOG] 📋 [FINAL-VALIDATION] 5. Validation de la gestion d'erreurs...
[18/07/2025 14:59:18] [FRONTEND] [LOG] 📋 [FINAL-VALIDATION] 4. Validation du workflow drag & drop...
[18/07/2025 14:59:18] [FRONTEND] [WARN] ⚠️ [FINAL-VALIDATION] handleRegularAssignmentDrop non disponible
[18/07/2025 14:59:18] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Erreurs gérées proprement
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 14:59:18] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 0
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 14:59:18] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":0,"regularShifts":0,"gripsInDOM":0,"gripsWithEvents":0,"gripsResponsive":0,"issues":[]}
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🎯 [FINAL-VALIDATION] === RÉSUMÉ FINAL ===
[18/07/2025 14:59:18] [FRONTEND] [LOG] 📊 [FINAL-VALIDATION] Score: 20 /100
[18/07/2025 14:59:18] [FRONTEND] [LOG] 🔍 [FINAL-VALIDATION] Détails: {"architecture":false,"integration":false,"functionality":false,"dragDropWorkflow":false,"errorHandling":true,"score":20,"errors":["modalFunctionalities ou TeamCalendarApp non disponibles","showConfirmationMenu non disponible","Erreur test fonctionnalité: can't access property \"openAssignmentContextModal\", window.modalFunctionalities is undefined"],"warnings":["handleRegularAssignmentDrop non disponible"]}
[18/07/2025 14:59:18] [FRONTEND] [ERROR] ❌ [FINAL-VALIDATION] Erreurs critiques:
[18/07/2025 14:59:18] [FRONTEND] [ERROR]    1. modalFunctionalities ou TeamCalendarApp non disponibles
[18/07/2025 14:59:18] [FRONTEND] [ERROR]    2. showConfirmationMenu non disponible
[18/07/2025 14:59:18] [FRONTEND] [WARN] ⚠️ [FINAL-VALIDATION] Avertissements:
[18/07/2025 14:59:18] [FRONTEND] [ERROR]    3. Erreur test fonctionnalité: can't access property "openAssignmentContextModal", window.modalFunctionalities is undefined
[18/07/2025 14:59:18] [FRONTEND] [WARN]    1. handleRegularAssignmentDrop non disponible
[18/07/2025 14:59:18] [FRONTEND] [LOG] ❌ [FINAL-VALIDATION] CORRECTION INSUFFISANTE. Des problèmes majeurs subsistent.
[18/07/2025 14:59:24] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[18/07/2025 14:59:24] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[18/07/2025 14:59:24] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[18/07/2025 14:59:24] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[18/07/2025 14:59:24] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[18/07/2025 14:59:24] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[18/07/2025 14:59:24] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[18/07/2025 14:59:24] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[18/07/2025 14:59:24] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[18/07/2025 14:59:24] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[18/07/2025 14:59:24] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[18/07/2025 14:59:24] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[18/07/2025 14:59:24] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[18/07/2025 14:59:24] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[18/07/2025 14:59:24] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[18/07/2025 14:59:25] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[18/07/2025 14:59:25] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[18/07/2025 14:59:25] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 14:59:25] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 14:59:25] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 14:59:25] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 14:59:25] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 14:59:25] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 14:59:25] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 14:59:25] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[18/07/2025 14:59:25] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 14:59:25] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 14:59:25] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 14:59:25] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 14:59:25] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 14:59:25] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🚨 [VALIDATION-CRITIQUE] Démarrage de la validation des corrections...
[18/07/2025 14:59:25] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🧪 [TEST-DRAG-DROP] Démarrage des tests complets de drag & drop...
[18/07/2025 14:59:25] [FRONTEND] [LOG] ✅ [TEST-DRAG-DROP] Script chargé. Utilisez runComprehensiveDragDropTests() pour tester.
[18/07/2025 14:59:25] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] Script chargé. Utilisez runCriticalValidation() pour valider.
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🚀 [DEBUG-MODAL] Script de débogage de l'intégration modale chargé
[18/07/2025 14:59:25] [FRONTEND] [LOG] ✅ [DEBUG-MODAL] Fonctions de débogage disponibles:
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testCompleteModalWorkflow() : Test complet du workflow
[18/07/2025 14:59:25] [FRONTEND] [LOG] - diagnoseModalIntegration() : Diagnostic complet
[18/07/2025 14:59:25] [FRONTEND] [LOG] - forceModalIntegration() : Forcer l'intégration
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🚀 [TEST-MODAL-FIX] Script de test complet de la correction modale chargé
[18/07/2025 14:59:25] [FRONTEND] [LOG] ✅ [TEST-MODAL-FIX] Fonctions de test disponibles:
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testModalFixComplete() : Test complet de la correction
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testModalButtons() : Test spécifique des boutons du modal
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🚀 [MANUAL-TEST] Script de test manuel de la modale chargé
[18/07/2025 14:59:25] [FRONTEND] [LOG] ✅ [MANUAL-TEST] Fonctions disponibles:
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testModalManually() : Test complet
[18/07/2025 14:59:25] [FRONTEND] [LOG] - closeModal() : Fermer la modale
[18/07/2025 14:59:25] [FRONTEND] [LOG] - testButton(action) : Tester un bouton spécifique
[18/07/2025 14:59:25] [FRONTEND] [LOG]   Actions disponibles: "regular", "temporary", "replacement", "cancel", "close"
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🚀 [FINAL-VALIDATION] Script de validation finale de la correction modale chargé
[18/07/2025 14:59:25] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Fonctions de validation disponibles:
[18/07/2025 14:59:25] [FRONTEND] [LOG] - runFinalModalValidation() : Validation complète
[18/07/2025 14:59:25] [FRONTEND] [LOG] - quickModalTest() : Test rapide
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🔍 [Agenda] TeamCalendarApp assigné à window: {"teamCalendarApp":true,"TeamCalendarApp":true}
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[18/07/2025 14:59:25] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🚀 [DEBUG-MODAL] Lancement automatique du diagnostic...
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🔍 [DEBUG-MODAL] === DIAGNOSTIC COMPLET DE L'INTÉGRATION MODALE ===
[18/07/2025 14:59:25] [FRONTEND] [LOG] 📋 [DEBUG-MODAL] 1. Vérification TeamCalendarApp...
[18/07/2025 14:59:25] [FRONTEND] [LOG] 📋 [DEBUG-MODAL] 2. Vérification modalFunctionalities local...
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🔍 [DEBUG-MODAL] Méthodes disponibles: ["safeScheduleUpdate","renderSafe","checkDataIntegrity","quickRepair","clearRenderCache","fixPostRefreshIssues","emergencyFixUndefinedPostIds","saveFixedShifts","fixUndefinedPostIdsOnLoad","getEmployeeName"]
[18/07/2025 14:59:25] [FRONTEND] [LOG] ✅ [DEBUG-MODAL] TeamCalendarApp disponible
[18/07/2025 14:59:25] [FRONTEND] [ERROR] ❌ [DEBUG-MODAL] modalFunctionalities local non accessible
[18/07/2025 14:59:25] [FRONTEND] [LOG] 📋 [DEBUG-MODAL] 3. Vérification modalFunctionalities global...
[18/07/2025 14:59:25] [FRONTEND] [LOG] 📋 [DEBUG-MODAL] 4. Vérification modal d'attribution...
[18/07/2025 14:59:25] [FRONTEND] [ERROR] ❌ [DEBUG-MODAL] window.modalFunctionalities non disponible
[18/07/2025 14:59:25] [FRONTEND] [LOG] ✅ [DEBUG-MODAL] Modal assignment-context-modal trouvé
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🔍 [DEBUG-MODAL] Classes: ["hidden","fixed","inset-0","bg-black/40","backdrop-blur-sm","z-50","flex","items-center","justify-center","p-4"]
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🔍 [DEBUG-MODAL] Style display: 
[18/07/2025 14:59:25] [FRONTEND] [LOG] 📋 [DEBUG-MODAL] 5. Vérification éléments drag & drop...
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🔍 [DEBUG-MODAL] Cellules: 0, Employés: 0, Postes: 0
[18/07/2025 14:59:25] [FRONTEND] [ERROR] ❌ [DEBUG-MODAL] Éléments drag & drop manquants
[18/07/2025 14:59:25] [FRONTEND] [LOG] 📊 [DEBUG-MODAL] === RÉSUMÉ DU DIAGNOSTIC ===
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🔍 [DEBUG-MODAL] Résultats: {"teamCalendarApp":true,"modalFunctionalities":false,"globalModalFunctionalities":false,"assignmentModal":true,"dragDropElements":false,"errors":["modalFunctionalities local non accessible","window.modalFunctionalities non disponible","Éléments drag & drop manquants"]}
[18/07/2025 14:59:25] [FRONTEND] [ERROR]    1. modalFunctionalities local non accessible
[18/07/2025 14:59:25] [FRONTEND] [ERROR] ❌ [DEBUG-MODAL] Erreurs détectées:
[18/07/2025 14:59:25] [FRONTEND] [ERROR]    2. window.modalFunctionalities non disponible
[18/07/2025 14:59:25] [FRONTEND] [ERROR]    3. Éléments drag & drop manquants
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[18/07/2025 14:59:25] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[18/07/2025 14:59:25] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:25.368Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:25.501Z"
}
[18/07/2025 14:59:25] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 168ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 168ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:25.536Z"
}
[18/07/2025 14:59:26] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[18/07/2025 14:59:26] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:25.636Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 49ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 49ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:25.684Z"
}
[18/07/2025 14:59:26] [BACKEND] [INFO] ✅ Requête exécutée en 49ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[18/07/2025 14:59:26] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":4}
[18/07/2025 14:59:26] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 14:59:26] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":false,"hasOpenFunction":false,"hasHandleFunction":true}
[18/07/2025 14:59:26] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 14:59:26] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":5,"posts":0}
[18/07/2025 14:59:26] [FRONTEND] [LOG] 📋 [MANUAL-TEST] 1. Vérification de la disponibilité...
[18/07/2025 14:59:26] [FRONTEND] [LOG] 🚀 [MANUAL-TEST] Lancement automatique du test...
[18/07/2025 14:59:26] [FRONTEND] [LOG] 🧪 [MANUAL-TEST] === TEST MANUEL DE LA MODALE ===
[18/07/2025 14:59:26] [FRONTEND] [LOG] TeamCalendarApp: true
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:25.847Z"
}
[18/07/2025 14:59:26] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 14:59:26] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] État de la modale: {"hidden":true,"classes":["hidden","fixed","inset-0","bg-black/40","backdrop-blur-sm","z-50","flex","items-center","justify-center","p-4"],"style":""}
[18/07/2025 14:59:26] [FRONTEND] [LOG] teamCalendarApp: true
[18/07/2025 14:59:26] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[18/07/2025 14:59:26] [FRONTEND] [LOG] modalFunctionalities: false
[18/07/2025 14:59:26] [FRONTEND] [ERROR] ❌ [MANUAL-TEST] Fonction openAssignmentContextModal non disponible
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:25.886Z"
}
[18/07/2025 14:59:26] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 46ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 46ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:25.932Z"
}
[18/07/2025 14:59:26] [BACKEND] [INFO] ✅ Requête exécutée en 46ms: SELECT * FROM standard_posts ORDER BY label {}
[18/07/2025 14:59:26] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:26.137Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 49ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 49ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:26.187Z"
}
[18/07/2025 14:59:26] [BACKEND] [INFO] ✅ Requête exécutée en 49ms: SELECT * FROM app_settings ORDER BY setting_key {}
[18/07/2025 14:59:26] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:26.223Z"
}
[18/07/2025 14:59:26] [BACKEND] [INFO] ✅ Requête exécutée en 50ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 50ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 50ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:26.272Z"
}
[18/07/2025 14:59:26] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"86f97857-d86a-4e2a-aec3-831bf92012d4","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-28","endDate":"2025-07-29","excludedDates":[]}
[18/07/2025 14:59:26] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"6830f981-f642-4364-9b1d-0ff92e12fad2","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-08-01","endDate":null,"excludedDates":[]}
[18/07/2025 14:59:26] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"6830f981-f642-4364-9b1d-0ff92e12fad2","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-01T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[18/07/2025 14:59:26] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"86f97857-d86a-4e2a-aec3-831bf92012d4","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-28T04:00:00.000Z","end_date":"2025-07-29T04:00:00.000Z","excluded_dates":[]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:26.575Z"
}
[18/07/2025 14:59:26] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-23T04:00:00.000Z","end_date":"2025-07-23T04:00:00.000Z","excluded_dates":[]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:26.574Z"
}
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-23","endDate":"2025-07-23","excludedDates":[]}
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"5400f0bd-d560-4167-89ea-a6da77478469","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-22T04:00:00.000Z","end_date":"2025-07-22T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"42c64a69-ab23-451f-a314-c5c87622675d","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-30T04:00:00.000Z","end_date":"2025-07-31T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"42c64a69-ab23-451f-a314-c5c87622675d","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-30","endDate":"2025-07-31","excludedDates":[]}
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"5400f0bd-d560-4167-89ea-a6da77478469","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-22","endDate":"2025-07-22","excludedDates":[]}
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"316031c4-9402-4394-970e-e598022f5118","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-25T04:00:00.000Z","end_date":"2025-07-27T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"316031c4-9402-4394-970e-e598022f5118","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-25","endDate":"2025-07-27","excludedDates":[]}
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-18T04:00:00.000Z","end_date":"2025-07-21T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"d546a50b-43af-4f53-afd0-4a43c1f3959a","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-24T04:00:00.000Z","end_date":"2025-07-24T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-18","endDate":"2025-07-21","excludedDates":[]}
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"d546a50b-43af-4f53-afd0-4a43c1f3959a","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-24","endDate":"2025-07-24","excludedDates":[]}
[18/07/2025 14:59:27] [FRONTEND] [LOG] 📋 [loadState] 8/8 assignations régulières valides chargées et normalisées
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[18/07/2025 14:59:27] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:26.737Z"
}
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] 0 doublons supprimés
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[18/07/2025 14:59:27] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:26.748Z"
}
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [setupEmployeeNameDisplay] Styles CSS injectés pour l'affichage des noms
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 213ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 213ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:26.788Z"
}
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [GET /api/employee-order] Ordre récupéré (array): [{"id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","name":"Marie Martin","order":0},{"id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","name":"Lucas Bernard","order":1},{"id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","name":"Pierre Durand","order":2},{"id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","name":"Sophie Leblanc","order":3},{"id":"42b53805-18ba-425e-bb00-52bb8d6ce76b","name":"Jean Dupont","order":4}]
  Data: {
  "originalArgs": [
    "✅ [GET /api/employee-order] Ordre récupéré (array):",
    [
      {
        "id": "604a1d0c-2141-4543-b7f9-7fc9adfa7b9a",
        "name": "Marie Martin",
        "order": 0
      },
      {
        "id": "cf78e945-72c7-48f3-a72d-bd0e245a284d",
        "name": "Lucas Bernard",
        "order": 1
      },
      {
        "id": "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
        "name": "Pierre Durand",
        "order": 2
      },
      {
        "id": "59f5df3a-33ef-425c-bfa2-adca818cf94f",
        "name": "Sophie Leblanc",
        "order": 3
      },
      {
        "id": "42b53805-18ba-425e-bb00-52bb8d6ce76b",
        "name": "Jean Dupont",
        "order": 4
      }
    ]
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:59:26.790Z"
}
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [init] modalFunctionalities rendu disponible globalement
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔧 [ModalFunctionalitiesManager] Initialisation du gestionnaire de modales
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [init] Gestionnaire de modales initialisé
[18/07/2025 14:59:27] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 14:59:27] [FRONTEND] [ERROR] ❌ [VALIDATION] ModalManager non disponible
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] === VALIDATION 4: Éléments DOM critiques === 
[18/07/2025 14:59:27] [FRONTEND] [ERROR] ❌ [VALIDATION-CRITIQUE] Aucune cellule de calendrier trouvée - CRITIQUE pour drag & drop 
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 14:59:27] [FRONTEND] [ERROR] ❌ [VALIDATION] Erreur lors de setupAssignmentContextModal: Error: can't access property "setupAssignmentContextModal", window.TeamCalendarApp.ModalManager is undefined
validateListeners@http://localhost:5173/validate-modal-fix.js:87:9
runCompleteValidation@http://localhost:5173/validate-modal-fix.js:103:20
@http://localhost:5173/validate-modal-fix.js:186:5
setTimeout handler*@http://localhost:5173/validate-modal-fix.js:184:11

[18/07/2025 14:59:27] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":false,"listeners":false}
[18/07/2025 14:59:27] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[18/07/2025 14:59:27] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[18/07/2025 14:59:27] [FRONTEND] [LOG] - Vérifier que la modale assignment-context-modal existe dans le DOM
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 14:59:27] [FRONTEND] [LOG] - Vérifier les erreurs dans setupAssignmentContextModal
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 14:59:27] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":true,"detectDropDateFunction":true,"showConfirmationMenuFunction":true,"handleRegularAssignmentDropFunction":true,"regularAssignments":8,"employees":5}
[18/07/2025 14:59:27] [FRONTEND] [LOG] ℹ️ [TEST-DRAG-DROP] 🚀 Démarrage des tests complets de drag & drop 
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [DIAGNOSTIC] Toutes les fonctions nécessaires sont disponibles
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🚀 [TEST-DRAG-DROP] Lancement automatique des tests...
[18/07/2025 14:59:27] [FRONTEND] [LOG] ℹ️ [TEST-DRAG-DROP] Test 1: Vérification des prérequis 
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [TEST-DRAG-DROP] ✓ TeamCalendarApp disponible 
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [TEST-DRAG-DROP] ✓ modalFunctionalities disponible 
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [TEST-DRAG-DROP] ✓ Modal d'attribution présent 
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [TEST-DRAG-DROP] ✓ Fonctions de drag & drop présentes 
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🚨 [TEST-DRAG-DROP] ✗ Postes disponibles (CRITIQUE) 
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [TEST-DRAG-DROP] ✓ Employés disponibles 
[18/07/2025 14:59:27] [FRONTEND] [LOG] ℹ️ [TEST-DRAG-DROP] Prérequis: 5/6 réussis, 1 échecs critiques 
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🚨 [TEST-DRAG-DROP] Tests arrêtés - Prérequis critiques non satisfaits 
[18/07/2025 14:59:27] [FRONTEND] [LOG] 📊 [TEST-DRAG-DROP] Résultats finaux: {}
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🚀 [TEST-MODAL-FIX] Lancement automatique du test complet...
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🧪 [TEST-MODAL-FIX] === TEST COMPLET DE LA CORRECTION MODALE ===
[18/07/2025 14:59:27] [FRONTEND] [LOG] 📋 [TEST-MODAL-FIX] 1. Test de disponibilité globale...
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [TEST-MODAL-FIX] modalFunctionalities et TeamCalendarApp disponibles
[18/07/2025 14:59:27] [FRONTEND] [LOG] 📋 [TEST-MODAL-FIX] 2. Test de création de modal...
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔧 [createAssignmentContextModal] Création du modal d'attribution
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [TEST-MODAL-FIX] Modal créé avec succès
[18/07/2025 14:59:27] [FRONTEND] [LOG] 📋 [TEST-MODAL-FIX] 3. Test de gestion des événements...
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔧 [setupAssignmentModalEvents] Configuration des événements du modal
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [TEST-MODAL-FIX] Événements configurés avec succès
[18/07/2025 14:59:27] [FRONTEND] [LOG] 📋 [TEST-MODAL-FIX] 4. Test d'intégration drag & drop...
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Ouverture du modal d'attribution {"postData":{"id":"test-post","label":"Test Post"},"employeeId":"test-employee","employeeName":"Test Employee","dateKey":"2025-07-18","position":{"x":100,"y":100}}
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔧 [setupAssignmentModalEvents] Configuration des événements du modal
[18/07/2025 14:59:27] [FRONTEND] [LOG] 📝 [populateAssignmentModal] Remplissage des données du modal
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [openAssignmentContextModal] Modal d'attribution ouvert
[18/07/2025 14:59:27] [FRONTEND] [LOG] ✅ [TEST-MODAL-FIX] Intégration drag & drop fonctionnelle
[18/07/2025 14:59:27] [FRONTEND] [LOG] 📊 [TEST-MODAL-FIX] === RÉSUMÉ DES TESTS ===
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🔍 [TEST-MODAL-FIX] Résultats: {"globalAvailability":true,"modalCreation":true,"eventHandling":true,"dragDropIntegration":true,"errors":[]}
[18/07/2025 14:59:27] [FRONTEND] [LOG] 📊 [TEST-MODAL-FIX] Tests réussis: 4/4 (100.0%)
[18/07/2025 14:59:27] [FRONTEND] [LOG] 🎉 [TEST-MODAL-FIX] TOUS LES TESTS SONT PASSÉS ! La correction modale est fonctionnelle.
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [loadEmployeeOrder] Employés réorganisés selon l'ordre api
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [loadEmployeeOrder] Ordre API récupéré: Marie Martin (0), Lucas Bernard (1), Pierre Durand (2), Sophie Leblanc (3), Jean Dupont (4)
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:59:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:59:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[18/07/2025 14:59:29] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton d'historique...
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Boutons ajoutés (emergency fix buttons supprimés)
[18/07/2025 14:59:29] [FRONTEND] [LOG] 📋 [init] Application des attributions régulières après initialisation
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[18/07/2025 14:59:29] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W28
[18/07/2025 14:59:29] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 6830f981-f642-4364-9b1d-0ff92e12fad2 commence après cette semaine (startDate: 2025-08-01, semaine se termine: 2025-07-19)
[18/07/2025 14:59:29] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 86f97857-d86a-4e2a-aec3-831bf92012d4 commence après cette semaine (startDate: 2025-07-28, semaine se termine: 2025-07-19)
[18/07/2025 14:59:29] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b commence après cette semaine (startDate: 2025-07-23, semaine se termine: 2025-07-19)
[18/07/2025 14:59:29] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 42c64a69-ab23-451f-a314-c5c87622675d commence après cette semaine (startDate: 2025-07-30, semaine se termine: 2025-07-19)
[18/07/2025 14:59:29] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 5400f0bd-d560-4167-89ea-a6da77478469 commence après cette semaine (startDate: 2025-07-22, semaine se termine: 2025-07-19)
[18/07/2025 14:59:29] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 316031c4-9402-4394-970e-e598022f5118 commence après cette semaine (startDate: 2025-07-25, semaine se termine: 2025-07-19)
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution ed906322-db11-4d06-aa28-437b06c445c4 a une intersection avec cette semaine (2025-07-18 → 2025-07-21) vs (2025-07-13 → 2025-07-19)
[18/07/2025 14:59:29] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions actives sur 8 total
[18/07/2025 14:59:29] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution d546a50b-43af-4f53-afd0-4a43c1f3959a commence après cette semaine (startDate: 2025-07-24, semaine se termine: 2025-07-19)
[18/07/2025 14:59:29] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-07-18","endDate":"2025-07-21","excludedDates":[]}
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution ed906322-db11-4d06-aa28-437b06c445c4: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 14:59:29] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 14:59:29] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 14:59:29] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 14:59:29] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 14:59:29] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 14:59:29] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-14 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 14:59:29] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 14:59:29] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 14:59:29] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-15 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 14:59:29] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 14:59:29] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 14:59:29] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-16 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 14:59:29] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 14:59:29] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[18/07/2025 14:59:29] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 14:59:29] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-17 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-18
[18/07/2025 14:59:29] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 validé, shifts existants: 0
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-18 pour Sophie Leblanc
[18/07/2025 14:59:29] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 14:59:29] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 14:59:29] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 14:59:29] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions appliquées pour la semaine 2025-W28
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 1 shifts créés
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [Agenda] Vérification post-init: {"teamCalendarApp":true,"TeamCalendarApp":true,"detectDropDateFromPosition":true,"showConfirmationMenu":true,"handleRegularAssignmentDrop":true}
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 0 validés, 0 corrigés
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[18/07/2025 14:59:29] [FRONTEND] [WARN] ⚠️ [renderUnifiedCalendar] Conteneur available-posts-container non trouvé, création...
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneur available-posts-container créé
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 62 zones trouvées
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 62 zones de drop disponibles
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 14:59:29] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:59:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (62 zones)
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 62 zones trouvées
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 62 zones de drop disponibles
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (62 zones)
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 1 validés, 0 corrigés
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 14:59:30] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 14:59:30] [FRONTEND] [ERROR] ❌ [DATE-TEST] formatDateToKey non disponible
[18/07/2025 14:59:30] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test avec date: 2025-07-25
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🧪 [DATE-TEST] Simulation du changement permanent...
[18/07/2025 14:59:30] [FRONTEND] [LOG] - Assignment: 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 14:59:30] [FRONTEND] [LOG] - De: Lucas Bernard
[18/07/2025 14:59:30] [FRONTEND] [LOG] - Vers: Marie Martin
[18/07/2025 14:59:30] [FRONTEND] [LOG] - Date: 2025-07-25
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🎯 [showRegularAssignmentConfirmationMenu] Menu pour assignment 86f97857-d86a-4e2a-aec3-831bf92012d4 → Marie Martin
[18/07/2025 14:59:30] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🔍 [showRegularAssignmentConfirmationMenu] Attribution trouvée: Poste Nuit pour Lucas Bernard
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 14:59:30] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 14:59:30] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🔍 [detectDropDateFromPosition] Analyse position pour employé test-employee
[18/07/2025 14:59:30] [FRONTEND] [LOG] 📍 [detectDropDateFromPosition] Position souris: (500, 300)
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [DATE-TEST] Date détectée: 2025-07-15
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [detectDropDateFromPosition] Date calculée via position: 2025-07-15 (jour 2)
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [DATE-TEST] Format de date valide
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [DATE-TEST] Date raisonnable (3 jours de différence)
[18/07/2025 14:59:30] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 14:59:30] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ Logique de fork avec dates: PASSÉ
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ Détection de date avec timezone: PASSÉ
[18/07/2025 14:59:30] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 14:59:30] [FRONTEND] [LOG] 🚀 [VALIDATION-CRITIQUE] Lancement automatique de la validation...
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] 🚨 DÉMARRAGE DE LA VALIDATION CRITIQUE 
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] === VALIDATION 1: Disponibilité TeamCalendarApp === 
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] window.teamCalendarApp disponible 
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] window.TeamCalendarApp disponible 
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] Fonction detectDropDateFromPosition disponible 
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] Fonction showConfirmationMenu disponible 
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] Fonction handleRegularAssignmentDrop disponible 
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] === VALIDATION 2: modalFunctionalities === 
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] window.modalFunctionalities disponible 
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] openAssignmentContextModal disponible 
[18/07/2025 14:59:30] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] === VALIDATION 3: Données de l'application === 
[18/07/2025 14:59:31] [FRONTEND] [ERROR] ❌ [VALIDATION-CRITIQUE] Aucun poste disponible - CRITIQUE pour drag & drop 
[18/07/2025 14:59:31] [FRONTEND] [ERROR] ❌ [VALIDATION-CRITIQUE] Aucune cellule de calendrier trouvée - CRITIQUE pour drag & drop 
[18/07/2025 14:59:31] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] === VALIDATION 4: Éléments DOM critiques === 
[18/07/2025 14:59:31] [FRONTEND] [LOG] ✅ [VALIDATION-CRITIQUE] === VALIDATION 5: Fonctionnalité drag & drop === 
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🔍 [detectDropDateFromPosition] Analyse position pour employé 100
[18/07/2025 14:59:31] [FRONTEND] [LOG] 📍 [detectDropDateFromPosition] Position souris: (undefined, undefined)
[18/07/2025 14:59:31] [FRONTEND] [ERROR] ❌ [VALIDATION-CRITIQUE] Erreur lors du test fonctionnel Document.elementFromPoint: Argument 1 is not a finite floating-point value.
[18/07/2025 14:59:31] [FRONTEND] [ERROR] ❌ [VALIDATION-CRITIQUE] ❌ VALIDATION ÉCHOUÉE: 4 erreur(s) critique(s) 
[18/07/2025 14:59:31] [FRONTEND] [LOG] 📊 [VALIDATION-CRITIQUE] RÉSUMÉ FINAL: {"errors":4,"warnings":0,"successes":14,"total":18,"criticalIssues":4,"status":"FAILED","testsTotal":5,"testsPassed":2,"testsFailed":3,"endTime":"2025-07-18T18:59:27.720Z","duration":11}
[18/07/2025 14:59:31] [FRONTEND] [LOG] 📋 [VALIDATION-CRITIQUE] ERREURS: [{"message":"Aucune cellule de calendrier trouvée - CRITIQUE pour drag & drop","details":null,"timestamp":"2025-07-18T18:59:26.671Z"},{"message":"Aucun poste disponible - CRITIQUE pour drag & drop","details":null,"timestamp":"2025-07-18T18:59:27.715Z"},{"message":"Aucune cellule de calendrier trouvée - CRITIQUE pour drag & drop","details":null,"timestamp":"2025-07-18T18:59:27.716Z"},{"message":"Erreur lors du test fonctionnel","details":"Document.elementFromPoint: Argument 1 is not a finite floating-point value.","timestamp":"2025-07-18T18:59:27.719Z"},{"message":"❌ VALIDATION ÉCHOUÉE: 4 erreur(s) critique(s)","details":null,"timestamp":"2025-07-18T18:59:27.720Z"}]
[18/07/2025 14:59:31] [FRONTEND] [LOG] ⚠️ [VALIDATION-CRITIQUE] AVERTISSEMENTS: []
[18/07/2025 14:59:31] [FRONTEND] [LOG] ❌ [VALIDATION-CRITIQUE] DES ERREURS CRITIQUES PERSISTENT !
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🚀 [FINAL-VALIDATION] Lancement de la validation finale...
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🎯 [FINAL-VALIDATION] === VALIDATION FINALE DE LA CORRECTION MODALE ===
[18/07/2025 14:59:31] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Architecture externe correcte
[18/07/2025 14:59:31] [FRONTEND] [LOG] 📋 [FINAL-VALIDATION] 1. Validation de l'architecture...
[18/07/2025 14:59:31] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Intégration avec TeamCalendarApp correcte
[18/07/2025 14:59:31] [FRONTEND] [LOG] 📋 [FINAL-VALIDATION] 2. Validation de l'intégration...
[18/07/2025 14:59:31] [FRONTEND] [LOG] 📋 [FINAL-VALIDATION] 3. Validation de la fonctionnalité...
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🔧 [openAssignmentContextModal] Création du modal d'attribution
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Ouverture du modal d'attribution {"postData":{"id":"validation-post","label":"Poste de Validation"},"employeeId":"validation-employee","employeeName":"Employé de Validation","dateKey":"2025-07-18","position":{"x":200,"y":200}}
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🔧 [createAssignmentContextModal] Création du modal d'attribution
[18/07/2025 14:59:31] [FRONTEND] [LOG] 📝 [populateAssignmentModal] Remplissage des données du modal
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🔧 [setupAssignmentModalEvents] Configuration des événements du modal
[18/07/2025 14:59:31] [FRONTEND] [LOG] ✅ [openAssignmentContextModal] Modal d'attribution ouvert
[18/07/2025 14:59:31] [FRONTEND] [LOG] 📋 [FINAL-VALIDATION] 4. Validation du workflow drag & drop...
[18/07/2025 14:59:31] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Workflow drag & drop disponible
[18/07/2025 14:59:31] [FRONTEND] [LOG] 📋 [FINAL-VALIDATION] 5. Validation de la gestion d'erreurs...
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Ouverture du modal d'attribution null
[18/07/2025 14:59:31] [FRONTEND] [LOG] 📝 [populateAssignmentModal] Remplissage des données du modal
[18/07/2025 14:59:31] [FRONTEND] [ERROR] ❌ [populateAssignmentModal] Erreur: Error: can't access property "postData", modalData is null
modalFunctionalities.populateAssignmentModal@http://localhost:5173/src/modalFunctionalities.ts:944:21
modalFunctionalities.openAssignmentContextModal@http://localhost:5173/src/modalFunctionalities.ts:863:10
runFinalModalValidation@http://localhost:5173/final-modal-validation.js:136:41
@http://localhost:5173/final-modal-validation.js:234:5
setTimeout handler*@http://localhost:5173/final-modal-validation.js:232:11

[18/07/2025 14:59:31] [FRONTEND] [LOG] 🔧 [setupAssignmentModalEvents] Configuration des événements du modal
[18/07/2025 14:59:31] [FRONTEND] [LOG] ✅ [openAssignmentContextModal] Modal d'attribution ouvert
[18/07/2025 14:59:31] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Gestion d'erreurs robuste
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[18/07/2025 14:59:31] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":1,"regularShifts":1,"regularAssignments":8}
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 1
[18/07/2025 14:59:31] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 1: {"employee":"Sophie Leblanc","date":"2025-07-18","assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","text":"00:00-08:00"}
[18/07/2025 14:59:31] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 14:59:31] [FRONTEND] [ERROR] ❌ [DATE-TEST] Modal non ouvert
[18/07/2025 14:59:31] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Fonctionnalité modale correcte
[18/07/2025 14:59:31] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] Boutons du modal présents
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Test responsivité ed906322-db11-4d06-aa28-437b06c445c4: {"hasMouseEvents":false,"isVisible":true,"hasCorrectCursor":true,"hasValidDimensions":true,"rect":{"width":130,"height":6}}
[18/07/2025 14:59:31] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 1
[18/07/2025 14:59:31] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":1,"regularShifts":1,"gripsInDOM":1,"gripsWithEvents":0,"gripsResponsive":1,"issues":[]}
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🎯 [FINAL-VALIDATION] === RÉSUMÉ FINAL ===
[18/07/2025 14:59:31] [FRONTEND] [LOG] 📊 [FINAL-VALIDATION] Score: 100 /100
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🔍 [FINAL-VALIDATION] Détails: {"architecture":true,"integration":true,"functionality":true,"dragDropWorkflow":true,"errorHandling":true,"score":100,"errors":[],"warnings":[]}
[18/07/2025 14:59:31] [FRONTEND] [LOG] ✅ [FINAL-VALIDATION] L'architecture externe est maintenue et fonctionnelle.
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🎉 [FINAL-VALIDATION] CORRECTION RÉUSSIE ! La modale fonctionne correctement.
[18/07/2025 14:59:31] [FRONTEND] [WARN] ⚠️ [showRegularAssignmentConfirmationMenu] handlePermanentRegularAssignmentChange non disponible
[18/07/2025 14:59:31] [FRONTEND] [LOG] 🔄 [showRegularAssignmentConfirmationMenu] Changement permanent demandé
[18/07/2025 14:59:32] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 7674f144-fe6f-4f77-b054-34524d7603b8 (Poste Matin)
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"7674f144-fe6f-4f77-b054-34524d7603b8","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[18/07/2025 14:59:33] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 14:59:33] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[18/07/2025 14:59:33] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🎯 [POST DRAG] 62 zones préparées pour le drop
[18/07/2025 14:59:33] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 25 cellules
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 7674f144-fe6f-4f77-b054-34524d7603b8 (Poste Matin)
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"7674f144-fe6f-4f77-b054-34524d7603b8","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[18/07/2025 14:59:33] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 14:59:33] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[18/07/2025 14:59:33] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🎯 [POST DRAG] 62 zones préparées pour le drop
[18/07/2025 14:59:33] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 50 cellules
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 42b53805-18ba-425e-bb00-52bb8d6ce76b, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé cf78e945-72c7-48f3-a72d-bd0e245a284d, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé cf78e945-72c7-48f3-a72d-bd0e245a284d, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 14:59:33] [FRONTEND] [LOG] 📦 [DROP] Nouveau poste: 7674f144-fe6f-4f77-b054-34524d7603b8 → cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:59:33] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 14:59:33] [FRONTEND] [LOG] ✅ [DROP] PostId trouvé via application/x-post-id: "7674f144-fe6f-4f77-b054-34524d7603b8"
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Lucas Bernard
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:33] [FRONTEND] [ERROR] ❌ [DROP] Éléments manquants: ["assignment-post-name","assignment-employee-name"]
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 14:59:33] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 14:59:33] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 62 zones
[18/07/2025 14:59:34] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:59:34] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 14:59:34] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 62 zones
[18/07/2025 14:59:34] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé