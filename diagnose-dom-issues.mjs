#!/usr/bin/env node

/**
 * Diagnostic des problèmes DOM spécifiques
 */

import fs from 'fs';

console.log('🔍 [DIAGNOSE-DOM-ISSUES] Diagnostic des problèmes DOM...\n');

const filePath = './src/teamCalendarApp.ts';
const content = fs.readFileSync(filePath, 'utf8');

// Recherche des problèmes spécifiques
const issues = [];

// 1. Vérifier addPostButton
if (!content.includes('addPostButton: document.getElementById')) {
    issues.push('❌ addPostButton n\'est pas initialisé avec getElementById');
} else {
    console.log('✅ addPostButton est correctement initialisé');
}

// 2. Vérifier availablePostsContainer
if (!content.includes('availablePostsContainer: document.getElementById')) {
    issues.push('❌ availablePostsContainer n\'est pas initialisé avec getElementById');
} else {
    console.log('✅ availablePostsContainer est correctement initialisé');
}

// 3. Vérifier les appels de fonctions critiques
if (!content.includes('this.setupEmployeeDragDrop()')) {
    issues.push('❌ setupEmployeeDragDrop() n\'est pas appelé');
} else {
    console.log('✅ setupEmployeeDragDrop() est appelé');
}

if (!content.includes('this.createAvailablePostsContainer()')) {
    issues.push('❌ createAvailablePostsContainer() n\'est pas appelé');
} else {
    console.log('✅ createAvailablePostsContainer() est appelé');
}

if (!content.includes('this.ModalManager.init(this)')) {
    issues.push('❌ ModalManager.init() n\'est pas appelé');
} else {
    console.log('✅ ModalManager.init() est appelé');
}

console.log('\n📋 Problèmes détectés :');
issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue}`);
});

if (issues.length > 0) {
    console.log('\n🔧 Application des corrections automatiques...');
    
    let contentFixed = content;
    
    // Correction 1: Initialiser addPostButton
    if (!content.includes('addPostButton: document.getElementById')) {
        const elementsPattern = /(elements:\s*\{[\s\S]*?)(\s*\},)/;
        if (elementsPattern.test(contentFixed)) {
            contentFixed = contentFixed.replace(elementsPattern, (match, before, after) => {
                if (!before.includes('addPostButton:')) {
                    return before + '\n        addPostButton: document.getElementById(\'add-post-btn\'),\n        ' + after;
                }
                return match;
            });
            console.log('✅ addPostButton ajouté dans elements');
        }
    }
    
    // Correction 2: Initialiser availablePostsContainer
    if (!content.includes('availablePostsContainer: document.getElementById')) {
        const elementsPattern = /(elements:\s*\{[\s\S]*?)(\s*\},)/;
        if (elementsPattern.test(contentFixed)) {
            contentFixed = contentFixed.replace(elementsPattern, (match, before, after) => {
                if (!before.includes('availablePostsContainer:')) {
                    return before + '\n        availablePostsContainer: document.getElementById(\'available-posts-container\'),\n        ' + after;
                }
                return match;
            });
            console.log('✅ availablePostsContainer ajouté dans elements');
        }
    }
    
    // Correction 3: Ajouter setupEmployeeDragDrop dans attachAllEventListeners
    if (!content.includes('this.setupEmployeeDragDrop()')) {
        const attachPattern = /(attachAllEventListeners:\s*function\(\)\s*\{[\s\S]*?)(\s*console\.log\('✅ \[attachAllEventListeners\] Écouteurs attachés avec succès'\);\s*\})/;
        if (attachPattern.test(contentFixed)) {
            contentFixed = contentFixed.replace(attachPattern, (match, before, after) => {
                return before + '\n        // ✅ CORRECTION : Configuration du drag & drop des employés\n        this.setupEmployeeDragDrop();\n        ' + after;
            });
            console.log('✅ setupEmployeeDragDrop() ajouté dans attachAllEventListeners');
        }
    }
    
    // Correction 4: Ajouter createAvailablePostsContainer dans renderEmployees
    if (!content.includes('this.createAvailablePostsContainer()')) {
        const renderPattern = /(renderEmployees:\s*function\(\)\s*\{[\s\S]*?)(\s*console\.log\('✅ \[renderEmployees\] Employés rendus avec succès'\);\s*\})/;
        if (renderPattern.test(contentFixed)) {
            contentFixed = contentFixed.replace(renderPattern, (match, before, after) => {
                return before + '\n        // ✅ CORRECTION : Créer le conteneur des postes disponibles\n        if (!this.elements.availablePostsContainer) {\n            this.elements.availablePostsContainer = this.createAvailablePostsContainer();\n        }\n        ' + after;
            });
            console.log('✅ createAvailablePostsContainer() ajouté dans renderEmployees');
        }
    }
    
    // Correction 5: Ajouter ModalManager.init dans attachAllEventListeners
    if (!content.includes('this.ModalManager.init(this)')) {
        const attachPattern = /(attachAllEventListeners:\s*function\(\)\s*\{[\s\S]*?)(\s*console\.log\('✅ \[attachAllEventListeners\] Écouteurs attachés avec succès'\);\s*\})/;
        if (attachPattern.test(contentFixed)) {
            contentFixed = contentFixed.replace(attachPattern, (match, before, after) => {
                return before + '\n        // ✅ CORRECTION : Initialiser le ModalManager\n        if (this.ModalManager && !this.ModalManager._initialized) {\n            this.ModalManager.init(this);\n            this.ModalManager._initialized = true;\n        }\n        ' + after;
            });
            console.log('✅ ModalManager.init() ajouté dans attachAllEventListeners');
        }
    }
    
    // Écrire les corrections
    fs.writeFileSync(filePath, contentFixed);
    console.log('\n✅ Toutes les corrections ont été appliquées !');
} else {
    console.log('✅ Aucun problème détecté - le code est correct');
}

console.log('\n🚀 Instructions pour tester :');
console.log('1. Redémarrer le serveur : npm run dev');
console.log('2. Ouvrir http://localhost:5173');
console.log('3. Vérifier que :');
console.log('   - Le drag & drop des employés fonctionne');
console.log('   - Les modales des paramètres s\'ouvrent');
console.log('   - Les postes disponibles sont visibles');
console.log('4. Si problèmes persistants, exécuter dans la console :');
console.log('   - checkDOMStructure()');
console.log('   - checkTeamCalendarApp()');

console.log('\n✅ [DIAGNOSE-DOM-ISSUES] Diagnostic terminé !'); 