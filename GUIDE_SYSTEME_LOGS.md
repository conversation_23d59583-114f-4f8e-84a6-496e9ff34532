# Guide Système de Logs - Interface de Diagnostic

## 🎯 Fonctionnalité Implémentée

Un système complet de capture, tri et export des logs avec mode IA "compact étendu" accessible via `http://localhost:5173/logs`.

## 📋 Composants Implémentés

### 1. Base de Données (PostgreSQL)
- **Table `logs`** : Stockage centralisé des logs (backend, frontend, browser)
- **Fonction `ai_ranked_logs()`** : Scoring intelligent par importance
- **Migration** : `database/migrations/009_create_logs_table.sql`

### 2. Backend (Express + Node.js)
- **Logger avancé** : `server/config/logger.js` avec capture PostgreSQL
- **5 endpoints REST** :
  - `GET /api/debug/sessions` - Liste des sessions
  - `GET /api/debug/sessions/:id` - Logs d'une session (modes full/ai)
  - `POST /api/debug/browser` - Capture logs navigateur
  - `GET /api/debug/current-session` - Session actuelle
  - `DELETE /api/debug/sessions/cleanup/:days` - Nettoyage automatique

### 3. Frontend (React + TypeScript)
- **Page complète** : `src/pages/Logs.tsx` avec interface moderne
- **Routing** : React Router intégré dans `src/App.tsx`
- **Navigation** : Lien "Logs" ajouté dans la sidebar d'Agenda

### 4. Capture Navigateur
- **Script automatique** : `public/capture-logs.js`
- **Interception console** : console.log, warn, error, debug
- **Gestion erreurs** : window.onerror et unhandledrejection

## 🚀 Démarrage Rapide

### Étape 1 : Migration Base de Données
```bash
# Démarrer PostgreSQL d'abord
npm run db:start

# Exécuter la migration
node run-logs-migration.mjs
```

### Étape 2 : Démarrage Système
```bash
# Démarrer le système complet
npm run dev:system
```

### Étape 3 : Accès Interface
- **URL principale** : http://localhost:5173/
- **Interface logs** : http://localhost:5173/logs
- Navigation : Sidebar → "Logs" (icône bug_report)

## 🎛️ Interface Utilisateur

### Contrôles Principaux
1. **Sélecteur Session** : Choisir parmi les sessions disponibles
2. **Mode de Tri** :
   - **Standard** : Chronologique simple
   - **IA Compact** : Tri par importance avec scoring intelligent
3. **Limite Lignes** : Slider 50-1000 lignes
4. **Actions** :
   - **Copier IA** : Exporte logs optimisés pour prompts IA
   - **Nettoyage** : Supprime logs anciens (1/7/30 jours)

### Fonctionnalités Avancées
- **Fusion répétitions** : Messages identiques groupés avec compteur
- **Colorisation** : Niveaux et sources avec codes couleur
- **Statistiques** : Erreurs, warnings, total en temps réel
- **Responsive** : Interface adaptative mobile/desktop

## 🧠 Mode IA "Compact Étendu"

### Algorithme de Scoring
```sql
-- Priorités par niveau
fatal: 100 points
error: 80 points  
warn:  60 points
info:  30 points
debug: 10 points

-- Pénalité répétitions
score -= (nb_répétitions - 1) * 5
```

### Avantages
- **Filtrage intelligent** : Logs les plus critiques en premier
- **Déduplication** : Évite la répétition des messages identiques
- **Export optimisé** : Format idéal pour analyse IA
- **Limite tokens** : Respecte les quotas LLM

## 📊 Utilisation Pratique

### Pour Débogage
1. Reproduire le problème
2. Aller sur /logs
3. Sélectionner session récente
4. Mode "IA Compact" + 300 lignes
5. Examiner erreurs/warnings en priorité

### Pour Analyse IA
1. Sélectionner session problématique
2. Mode "IA Compact" + 500-1000 lignes
3. Cliquer "Copier IA"
4. Coller dans prompt : "Analyse ces logs et trouve le problème:"

### Pour Maintenance
1. Utiliser nettoyage automatique régulièrement
2. Surveiller statistiques erreurs/warnings
3. Sessions multiples pour comparaison

## 🔧 Configuration Avancée

### Variables d'Environnement
```bash
# Dans server/config/logger.js
LOG_LEVEL=debug          # Niveau minimum
LOG_RETENTION_DAYS=7     # Rétention automatique
DB_LOGS_ENABLED=true     # Capture PostgreSQL
```

### Personnalisation Scoring
Modifier la fonction `ai_ranked_logs()` dans la migration pour ajuster les priorités selon vos besoins.

## 🛠️ Maintenance

### Nettoyage Automatique
- Via interface : Bouton "Nettoyage" → Choisir période
- Via API : `DELETE /api/debug/sessions/cleanup/7`
- Via SQL : `SELECT cleanup_old_logs(7);`

### Monitoring
- Surveillez la taille table `logs`
- Vérifiez performances avec gros volumes
- Sessions multiples = plusieurs instances serveur

## 🚨 Dépannage

### Problèmes Fréquents
1. **"Aucune session"** → Vérifier connexion DB + logs générés
2. **Interface vide** → Vérifier endpoints /api/debug/*
3. **Capture browser** → Script capture-logs.js chargé ?
4. **Performances** → Nettoyer logs anciens + indexer

### Tests de Validation
```bash
# Test migration
node run-logs-migration.mjs

# Test endpoints
curl http://localhost:3001/api/debug/sessions

# Test capture browser
// Dans console navigateur
console.log("Test capture logs");
```

## 🎉 Résultat Final

Système complet de diagnostic des logs avec :
- ✅ Capture multi-sources (backend/frontend/browser)
- ✅ Tri intelligent par IA avec scoring
- ✅ Interface moderne et intuitive
- ✅ Export optimisé pour analyse IA
- ✅ Gestion automatique des sessions
- ✅ Nettoyage et maintenance intégrés

**Route d'accès** : http://localhost:5173/logs 