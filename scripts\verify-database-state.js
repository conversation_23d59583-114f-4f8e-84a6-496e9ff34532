import pg from 'pg';

const { Pool } = pg;

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: '*************',
  database: 'glive_db',
  password: 'SebbZ12342323!!',
  port: 5432,
});

async function verifyDatabaseState() {
  console.log('🔍 [VÉRIFICATION] État de la base de données...\n');
  
  try {
    const client = await pool.connect();
    
    // 1. Vérifier les employés
    console.log('👥 [EMPLOYÉS] Vérification...');
    const employees = await client.query('SELECT id, name, status FROM employees ORDER BY name');
    console.log(`   📊 Total: ${employees.rows.length} employés`);
    employees.rows.forEach((emp, i) => {
      console.log(`   ${i+1}. ${emp.name} (${emp.status}) - ID: ${emp.id.substring(0, 8)}...`);
    });
    
    // 2. Vérifier les postes standards
    console.log('\n🏢 [POSTES STANDARDS] Vérification...');
    const posts = await client.query('SELECT id, label, color, hours, type, category FROM standard_posts ORDER BY label');
    console.log(`   📊 Total: ${posts.rows.length} postes`);
    posts.rows.forEach((post, i) => {
      console.log(`   ${i+1}. ${post.label} (${post.hours}) - Type: ${post.type} - Couleur: ${post.color || 'N/A'}`);
    });
    
    // 3. Vérifier les shifts
    console.log('\n📅 [SHIFTS] Vérification...');
    const shifts = await client.query('SELECT COUNT(*) as total FROM shifts');
    console.log(`   📊 Total: ${shifts.rows[0].total} shifts`);
    
    // 4. Vérifier les attributions régulières
    console.log('\n🔄 [ATTRIBUTIONS RÉGULIÈRES] Vérification...');
    const assignments = await client.query('SELECT COUNT(*) as total FROM regular_assignments');
    console.log(`   📊 Total: ${assignments.rows[0].total} attributions`);
    
    // 5. Vérifier les paramètres
    console.log('\n⚙️ [PARAMÈTRES] Vérification...');
    const settings = await client.query('SELECT setting_key, setting_value FROM app_settings ORDER BY setting_key');
    console.log(`   📊 Total: ${settings.rows.length} paramètres`);
    settings.rows.forEach((setting, i) => {
      console.log(`   ${i+1}. ${setting.setting_key}: ${setting.setting_value}`);
    });
    
    // 6. Vérifier les colonnes des tables principales
    console.log('\n🗂️ [STRUCTURE] Vérification des colonnes...');
    
    const employeeColumns = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'employees' 
      ORDER BY ordinal_position
    `);
    console.log(`   👥 Employees (${employeeColumns.rows.length} colonnes):`, 
      employeeColumns.rows.map(col => col.column_name).join(', '));
    
    const postColumns = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'standard_posts' 
      ORDER BY ordinal_position
    `);
    console.log(`   🏢 Standard_posts (${postColumns.rows.length} colonnes):`, 
      postColumns.rows.map(col => col.column_name).join(', '));
    
    const shiftColumns = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'shifts' 
      ORDER BY ordinal_position
    `);
    console.log(`   📅 Shifts (${shiftColumns.rows.length} colonnes):`, 
      shiftColumns.rows.map(col => col.column_name).join(', '));
    
    // 7. Résumé final
    console.log('\n✅ [RÉSUMÉ] État de la base de données:');
    console.log(`   👥 Employés: ${employees.rows.length}/5 attendus`);
    console.log(`   🏢 Postes: ${posts.rows.length}/5 attendus`);
    console.log(`   📅 Shifts: ${shifts.rows[0].total}/0 attendus`);
    console.log(`   🔄 Attributions: ${assignments.rows[0].total}/0 attendues`);
    console.log(`   ⚙️ Paramètres: ${settings.rows.length}/5 attendus`);
    
    const isValid = employees.rows.length === 5 && 
                   posts.rows.length === 5 && 
                   parseInt(shifts.rows[0].total) === 0 && 
                   parseInt(assignments.rows[0].total) === 0 &&
                   settings.rows.length === 5;
    
    if (isValid) {
      console.log('\n🎉 [SUCCÈS] Base de données dans l\'état attendu !');
    } else {
      console.log('\n⚠️ [ATTENTION] Base de données pas dans l\'état attendu');
    }
    
    client.release();
    
  } catch (error) {
    console.error('❌ [ERREUR] Vérification échouée:', error);
  } finally {
    await pool.end();
  }
}

// Exécuter la vérification
verifyDatabaseState(); 