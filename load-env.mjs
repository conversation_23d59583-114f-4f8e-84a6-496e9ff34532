#!/usr/bin/env node

/**
 * Utilitaire pour charger les variables d'environnement depuis .env
 * Usage: node load-env.mjs puis votre commande
 */

import { readFileSync, existsSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __dirname = dirname(fileURLToPath(import.meta.url));
const envPath = join(__dirname, '.env');

export function loadEnv() {
  if (!existsSync(envPath)) {
    console.log('⚠️  [ENV] Fichier .env non trouvé');
    return false;
  }

  try {
    const envContent = readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    let loaded = 0;
    
    for (const line of lines) {
      // Ignorer les commentaires et lignes vides
      if (line.trim() === '' || line.trim().startsWith('#')) {
        continue;
      }
      
      // Parse VAR=value
      const match = line.match(/^([^=]+)=(.*)$/);
      if (match) {
        const [, key, value] = match;
        
        // Nettoyer les guillemets si présents
        const cleanValue = value.replace(/^["']|["']$/g, '');
        
        // Définir la variable d'environnement
        process.env[key.trim()] = cleanValue;
        loaded++;
      }
    }
    
    console.log(`✅ [ENV] ${loaded} variables chargées depuis .env`);
    console.log(`📡 [ENV] PostgreSQL: ${process.env.DB_USER}@${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME}`);
    return true;
    
  } catch (error) {
    console.error('❌ [ENV] Erreur lecture .env:', error.message);
    return false;
  }
}

// Auto-chargement si exécuté directement
if (import.meta.url === `file://${process.argv[1]}`) {
  loadEnv();
  console.log('🎯 [ENV] Variables d\'environnement chargées et prêtes');
  console.log('💡 [ENV] Vous pouvez maintenant exécuter vos scripts de migration');
} 