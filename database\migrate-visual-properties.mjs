// ========================================
// 🗃️ MIGRATION : Propriétés Visuelles des Remplacements Ponctuels (ES Modules)
// ========================================

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🗃️ [MIGRATION] Début de la migration des propriétés visuelles...');

async function runVisualPropertiesMigration() {
    try {
        // Lire le fichier de migration
        const migrationPath = path.join(__dirname, 'migrations', '007_add_visual_properties_columns.sql');
        
        if (!fs.existsSync(migrationPath)) {
            console.error('❌ [MIGRATION] Fichier de migration non trouvé:', migrationPath);
            return false;
        }
        
        const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
        console.log('📄 [MIGRATION] Fichier de migration chargé');
        
        // Afficher le SQL qui serait exécuté
        console.log('📋 [MIGRATION] SQL à exécuter:');
        console.log('─'.repeat(50));
        console.log(migrationSQL);
        console.log('─'.repeat(50));
        
        console.log('✅ [MIGRATION] Script de migration prêt !');
        console.log('📋 [MIGRATION] Colonnes à ajouter :');
        console.log('   - shape (VARCHAR(50))');
        console.log('   - is_temporary (BOOLEAN)');
        console.log('   - original_assignment_id (VARCHAR(36))');
        console.log('   - replacement_date (DATE)');
        console.log('   - replacement_reason (TEXT)');
        console.log('   - visual_style (VARCHAR(50))');
        console.log('   - border_style (VARCHAR(50))');
        console.log('   - color_override (VARCHAR(50))');
        
        console.log('');
        console.log('💡 [MIGRATION] Exécutez ce SQL dans votre base de données pour activer la persistance');
        
        return true;
        
    } catch (error) {
        console.error('❌ [MIGRATION] Erreur lors de la lecture de la migration:', error);
        return false;
    }
}

// Alternative : Migration via API
async function runMigrationViaAPI() {
    console.log('🌐 [MIGRATION] Tentative de migration via API...');
    
    try {
        const response = await fetch('http://localhost:3001/api/migrate/visual-properties', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                migration: '007_add_visual_properties_columns'
            })
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ [MIGRATION] Migration via API réussie:', result);
            return true;
        } else {
            console.error('❌ [MIGRATION] Échec migration via API:', response.status);
            return false;
        }
        
    } catch (error) {
        console.error('❌ [MIGRATION] Erreur migration via API:', error.message);
        return false;
    }
}

// Fonction principale
async function main() {
    console.log('🚀 [MIGRATION] === MIGRATION DES PROPRIÉTÉS VISUELLES ===');
    
    // Afficher le SQL de migration
    let success = await runVisualPropertiesMigration();
    
    // Si on ne peut pas se connecter à la DB directement, essayer via API
    if (success) {
        console.log('🔄 [MIGRATION] Tentative alternative via API...');
        await runMigrationViaAPI();
    }
    
    console.log('');
    console.log('📋 [MIGRATION] INSTRUCTIONS MANUELLES:');
    console.log('1. Copiez le SQL affiché ci-dessus');
    console.log('2. Exécutez-le dans votre outil de gestion de base de données');
    console.log('3. Ou utilisez l\'interface d\'administration de votre serveur');
    console.log('4. Redémarrez l\'application après la migration');
    console.log('');
    console.log('🎉 [MIGRATION] Une fois la migration appliquée, la persistance sera activée !');
}

// Exécuter
main().catch(console.error); 