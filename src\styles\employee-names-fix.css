/* ===== CORRECTION AFFICHAGE NOMS D'EMPLOYÉS ===== */

/* Assurer l'affichage complet des noms d'employés sans troncature */
.employee-name-full {
  /* Supprimer toute troncature */
  overflow: visible !important;
  text-overflow: unset !important;
  white-space: normal !important;
  
  /* Permettre le retour à la ligne si nécessaire */
  word-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  
  /* Assurer une hauteur minimale pour les noms longs */
  min-height: 1.2em;
  line-height: 1.2;
  
  /* Espacement approprié */
  margin-bottom: 2px;
}

/* Conteneur de l'info employé avec hauteur flexible */
.employee-info-flexible {
  min-height: 80px !important;
  height: auto !important;
  padding: 12px 16px !important;
  
  /* Assurer que le contenu ne déborde pas sur les autres éléments */
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

/* Avatar avec position fixe */
.employee-avatar-fixed {
  flex-shrink: 0;
  width: 48px !important;
  height: 48px !important;
  margin-top: 2px; /* Légère compensation pour l'alignement */
}

/* Zone de texte avec largeur flexible */
.employee-text-area {
  flex-grow: 1;
  min-width: 0;
  max-width: none !important;
  
  /* Assurer un espacement vertical approprié */
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* Boutons d'action avec position fixe */
.employee-actions-fixed {
  flex-shrink: 0;
  display: flex;
  gap: 4px;
  align-items: flex-start;
  margin-top: 2px; /* Alignement avec l'avatar */
}

/* Ligne d'employé avec hauteur adaptative */
.employee-row-adaptive {
  min-height: 80px !important;
  height: auto !important;
  
  /* Assurer que la grille s'adapte au contenu */
  align-items: stretch;
}

/* Cellules de jour avec alignement vertical approprié */
.employee-row-adaptive .day-cell {
  align-items: flex-start !important;
  padding-top: 12px !important;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Écrans moyens */
@media (max-width: 1024px) {
  .employee-info-flexible {
    padding: 10px 12px !important;
    gap: 10px;
  }
  
  .employee-avatar-fixed {
    width: 40px !important;
    height: 40px !important;
  }
  
  .employee-name-full {
    font-size: 0.875rem; /* text-sm */
  }
}

/* Écrans petits */
@media (max-width: 768px) {
  .employee-info-flexible {
    padding: 8px 10px !important;
    gap: 8px;
  }
  
  .employee-avatar-fixed {
    width: 36px !important;
    height: 36px !important;
  }
  
  .employee-name-full {
    font-size: 0.8125rem; /* Légèrement plus petit */
    line-height: 1.1;
  }
  
  /* Masquer certaines informations supplémentaires sur mobile */
  .employee-extra-info {
    display: none;
  }
}

/* ===== CORRECTIONS SPÉCIFIQUES POUR LES GRILLES ===== */

/* Grille unifiée (Agenda.tsx) */
.employee-row.grid {
  grid-template-columns: 240px repeat(7, 1fr) !important;
  align-items: stretch !important;
}

/* Colonne info employé dans la grille */
.employee-row.grid .employee-info {
  display: flex !important;
  align-items: flex-start !important;
  padding: 12px 16px !important;
  min-height: 80px !important;
  height: auto !important;
}

/* ===== CORRECTIONS POUR LA SIDEBAR (FULLSCREEN) ===== */

/* Liste d'employés dans la sidebar */
.employees-list .employee-row-info {
  min-height: 70px !important;
  height: auto !important;
  padding: 10px 16px !important;
}

.employees-list .employee-info-cell-content {
  align-items: flex-start !important;
  gap: 10px !important;
}

/* ===== PRÉVENTION DES DÉBORDEMENTS ===== */

/* Assurer que les noms longs ne cassent pas la mise en page */
.employee-row {
  overflow: visible !important;
}

.employee-info {
  overflow: visible !important;
}

/* Conteneur principal avec gestion du débordement */
.employees-section {
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/* ===== AMÉLIORATION DE LA LISIBILITÉ ===== */

/* Contraste amélioré pour les noms */
.employee-name-full {
  color: #1e293b !important; /* text-slate-800 plus foncé */
  font-weight: 600 !important; /* font-semibold */
}

/* Statut avec espacement approprié */
.employee-status {
  color: #64748b !important; /* text-slate-500 */
  font-size: 0.75rem !important; /* text-xs */
  margin-top: 2px;
}

/* Informations supplémentaires avec espacement */
.employee-extra-info {
  color: #64748b !important;
  font-size: 0.6875rem !important; /* Plus petit que text-xs */
  line-height: 1.3;
  margin-top: 4px;
}

/* ===== ANIMATIONS ET TRANSITIONS ===== */

/* Transition douce pour les changements de hauteur */
.employee-row-adaptive {
  transition: height 0.2s ease-in-out, min-height 0.2s ease-in-out;
}

/* Hover avec respect de la hauteur */
.employee-row-adaptive:hover {
  background-color: rgba(248, 250, 252, 0.8);
  transform: none; /* Éviter les transformations qui peuvent casser la mise en page */
}

/* ===== ACCESSIBILITÉ ===== */

/* Focus visible pour les éléments interactifs */
.employee-row-adaptive:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Amélioration du contraste pour les boutons d'action */
.employee-actions-fixed button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

/* ===== CORRECTIONS POUR LES MODALES ===== */

/* Modal de gestion des employés */
.employee-management-modal .employee-name-full {
  max-width: none !important;
  overflow: visible !important;
}

/* Liste des employés dans les modales */
.modal .employee-list .employee-name-full {
  font-size: 0.875rem !important;
  line-height: 1.25 !important;
}
