/**
 * Utilitaires de validation pour TeamCalendarApp
 * Centralise toute la logique de validation commune
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Valide un objet employé
 */
export const validateEmployee = (employee: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!employee) {
    errors.push('Employee object is required');
    return { isValid: false, errors, warnings };
  }

  if (!employee.id) {
    errors.push('Employee ID is required');
  }

  if (!employee.name || typeof employee.name !== 'string') {
    errors.push('Employee name is required and must be a string');
  }

  if (employee.name && employee.name.length < 2) {
    warnings.push('Employee name is very short');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Valide un objet shift
 */
export const validateShift = (shift: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!shift) {
    errors.push('Shift object is required');
    return { isValid: false, errors, warnings };
  }

  if (!shift.id) {
    errors.push('Shift ID is required');
  }

  if (!shift.employee_id) {
    errors.push('Employee ID is required');
  }

  if (!shift.post_id && !shift.postId) {
    errors.push('Post ID is required');
  }

  if (shift.text === 'undefined' || shift.text === undefined) {
    warnings.push('Shift text is undefined');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Valide un objet post
 */
export const validatePost = (post: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!post) {
    errors.push('Post object is required');
    return { isValid: false, errors, warnings };
  }

  if (!post.id) {
    errors.push('Post ID is required');
  }

  if (!post.label || typeof post.label !== 'string') {
    errors.push('Post label is required and must be a string');
  }

  if (post.workingDays && !Array.isArray(post.workingDays)) {
    warnings.push('Working days should be an array');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Valide une date au format string
 */
export const validateDateKey = (dateKey: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!dateKey) {
    errors.push('Date key is required');
    return { isValid: false, errors, warnings };
  }

  if (typeof dateKey !== 'string') {
    errors.push('Date key must be a string');
  }

  // Validation du format YYYY-MM-DD
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(dateKey)) {
    errors.push('Date key must be in YYYY-MM-DD format');
  }

  // Validation que la date est valide
  const date = new Date(dateKey);
  if (isNaN(date.getTime())) {
    errors.push('Date key must be a valid date');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Valide un UUID
 */
export const validateUUID = (uuid: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!uuid) {
    errors.push('UUID is required');
    return { isValid: false, errors, warnings };
  }

  if (typeof uuid !== 'string') {
    errors.push('UUID must be a string');
  }

  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(uuid)) {
    errors.push('UUID must be in valid format');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Valide un tableau d'objets avec une fonction de validation
 */
export const validateArray = <T>(
  array: T[],
  validator: (item: T) => ValidationResult
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!Array.isArray(array)) {
    errors.push('Input must be an array');
    return { isValid: false, errors, warnings };
  }

  array.forEach((item, index) => {
    const result = validator(item);
    if (!result.isValid) {
      errors.push(`Item ${index}: ${result.errors.join(', ')}`);
    }
    warnings.push(...result.warnings.map(w => `Item ${index}: ${w}`));
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}; 