-- =====================================================
-- SEED 002: DONNÉES INITIALES TEAM CALENDAR APP
-- Date: 2025-06-11
-- Description: Insertion des données par défaut
-- =====================================================

-- =====================================================
-- 1. MODÈLE DE FICHE EMPLOYÉ PAR DÉFAUT
-- =====================================================
INSERT INTO employee_templates (name, description, fields, is_default) VALUES 
('Modèle Standard', 'Modèle de fiche employé par défaut avec les champs essentiels', 
'[
  {"id": "name", "label": "Nom complet", "type": "text", "required": true, "order": 1, "placeholder": "ex: Jean Dupont"},
  {"id": "status", "label": "Statut", "type": "select", "required": true, "order": 2, "options": ["Temps Plein", "Temps Partiel", "Stagiaire", "Freelance"]},
  {"id": "email", "label": "Email professionnel", "type": "email", "required": false, "order": 3, "placeholder": "ex: <EMAIL>"},
  {"id": "phone", "label": "Téléphone", "type": "tel", "required": false, "order": 4, "placeholder": "ex: 01 23 45 67 89"},
  {"id": "department", "label": "Département", "type": "select", "required": false, "order": 5, "options": ["Administration", "Production", "Maintenance", "Qualité", "Logistique"]},
  {"id": "position", "label": "Poste", "type": "text", "required": false, "order": 6, "placeholder": "ex: Opérateur de production"},
  {"id": "hire_date", "label": "Date d\'embauche", "type": "date", "required": false, "order": 7},
  {"id": "notes", "label": "Notes", "type": "textarea", "required": false, "order": 8, "placeholder": "Informations complémentaires..."}
]'::jsonb, true)
ON CONFLICT DO NOTHING;

-- =====================================================
-- 2. POSTES STANDARDS PAR DÉFAUT
-- =====================================================
INSERT INTO standard_posts (label, hours, duration, type, category, is_custom) VALUES 
('Poste Matin', '08:00-16:00', 8, 'sky', NULL, false),
('Poste Soir', '16:00-24:00', 8, 'amber', NULL, false),
('Poste Nuit', '00:00-08:00', 8, 'sky', 'night', false),
('Poste WE1', '00:00-12:00', 12, 'emerald', 'weekend', false),
('Poste WE2', '12:00-24:00', 12, 'emerald', 'weekend', false)
ON CONFLICT DO NOTHING;

-- =====================================================
-- 3. EMPLOYÉS D'EXEMPLE
-- =====================================================
DO $$
DECLARE
    template_id UUID;
BEGIN
    -- Récupérer l'ID du modèle par défaut
    SELECT id INTO template_id FROM employee_templates WHERE is_default = true LIMIT 1;
    
    -- Insérer des employés d'exemple
    INSERT INTO employees (name, status, template_id, extra_fields) VALUES 
    ('Jean Dupont', 'Temps Plein', template_id, '{"email": "<EMAIL>", "department": "Production"}'::jsonb),
    ('Marie Martin', 'Temps Plein', template_id, '{"email": "<EMAIL>", "department": "Administration"}'::jsonb),
    ('Pierre Durand', 'Temps Partiel', template_id, '{"email": "<EMAIL>", "department": "Maintenance"}'::jsonb),
    ('Sophie Leblanc', 'Temps Plein', template_id, '{"email": "<EMAIL>", "department": "Qualité"}'::jsonb),
    ('Lucas Bernard', 'Stagiaire', template_id, '{"email": "<EMAIL>", "department": "Logistique"}'::jsonb)
    ON CONFLICT DO NOTHING;
END $$;

-- =====================================================
-- 4. PARAMÈTRES D'APPLICATION PAR DÉFAUT
-- ✅ CORRECTION: Dimanche par défaut lors de la réinitialisation
-- =====================================================
INSERT INTO app_settings (setting_key, setting_value, description) VALUES
('weekStartsOn', '"sunday"'::jsonb, 'Premier jour de la semaine - Dimanche par défaut'),
('weekStartDay', '0'::jsonb, 'Premier jour de la semaine (numérique) - 0=Dimanche par défaut'),
('viewMode', '"week"'::jsonb, 'Mode d''affichage par défaut'),
('currentWeekOffset', '0'::jsonb, 'Décalage de semaine actuel'),
('appVersion', '"1.0.0"'::jsonb, 'Version de l''application'),
('lastBackup', 'null'::jsonb, 'Timestamp de la dernière sauvegarde')
ON CONFLICT (setting_key) DO NOTHING;

-- =====================================================
-- 5. VACANCES GLOBALES D'EXEMPLE (JOURS FÉRIÉS 2025)
-- =====================================================
INSERT INTO global_vacations (name, start_date, end_date, description) VALUES 
('Jour de l''An', '2025-01-01', '2025-01-01', 'Jour férié'),
('Fête du Travail', '2025-05-01', '2025-05-01', 'Jour férié'),
('Fête de la Victoire', '2025-05-08', '2025-05-08', 'Jour férié'),
('Ascension', '2025-05-29', '2025-05-29', 'Jour férié'),
('Lundi de Pentecôte', '2025-06-09', '2025-06-09', 'Jour férié'),
('Fête Nationale', '2025-07-14', '2025-07-14', 'Jour férié'),
('Assomption', '2025-08-15', '2025-08-15', 'Jour férié'),
('Toussaint', '2025-11-01', '2025-11-01', 'Jour férié'),
('Armistice', '2025-11-11', '2025-11-11', 'Jour férié'),
('Noël', '2025-12-25', '2025-12-25', 'Jour férié')
ON CONFLICT DO NOTHING;

-- =====================================================
-- FIN DU SEED 002
-- ===================================================== 