# Documentation - validationUtils.ts

> **Fichier**: `src\components\utils\validationUtils.ts`

## 📚 Fonctions Documentées

### Fonction 1

**Description**: Fonctions de validation pour l'application
 *

---

### Fonction 2

**Description**: Valide un UUID v4
 *

**Exemple**:
```typescript
* ```typescript
 * const isValid = isValidUUID('123e4567-e89b-12d3-a456-************'); // true
 * ```
```

---

### Fonction 3

**Description**: Génère un UUID v4
 *

**Exemple**:
```typescript
* ```typescript
 * const id = generateUUID(); // "123e4567-e89b-12d3-a456-************"
 * ```
```

---

### Fonction 4

**Description**: Valide un nom d'employé
 *

---

### Fonction 5

**Description**: Valide une adresse email
 *

---

### Fonction 6

**Description**: Valide un numéro de téléphone
 *

---

### Fonction 7

**Description**: Valide une date au format YYYY-MM-DD
 *

---

### Fonction 8

**Description**: Valide un format d'heure HH:MM
 *

---

### Fonction 9

**Description**: Valide un format d'heures de poste HH:MM-HH:MM
 *

---

### Fonction 10

**Description**: Échappe les caractères HTML dangereux
 *

---

### Fonction 11

**Description**: Valide les jours de travail d'un poste
 *

---

### Fonction 12

**Description**: Valide une couleur hexadécimale
 *

---

### Fonction 13

**Description**: Valide les données d'un employé
 *

---

### Fonction 14

**Description**: Valide les données d'un poste
 *

---

## 🔧 Toutes les Fonctions

| Fonction | Ligne |
|----------|-------|
| `isValidUUID` | 17 |
| `generateUUID` | 30 |
| `isValidEmployeeName` | 43 |
| `isValidEmail` | 79 |
| `isValidPhone` | 93 |
| `isValidDateString` | 111 |
| `isValidTimeString` | 130 |
| `isValidPostHours` | 144 |
| `escapeHtml` | 176 |
| `isValidWorkingDays` | 194 |
| `isValidHexColor` | 217 |
| `validateEmployee` | 231 |
| `validatePost` | 269 |

