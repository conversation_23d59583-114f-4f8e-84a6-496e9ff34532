{"numTotalTestSuites": 3, "numPassedTestSuites": 3, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 10, "numPassedTests": 10, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "startTime": 1751909727310, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["", "TeamCalendarApp - shouldApplyAssignmentToDay"], "fullName": " TeamCalendarApp - shouldApplyAssignmentToDay devrait appliquer assignment pour le bon jour de semaine", "status": "passed", "title": "devrait appliquer assignment pour le bon jour de semaine", "duration": 2, "failureMessages": []}, {"ancestorTitles": ["", "TeamCalendarApp - shouldApplyAssignmentToDay"], "fullName": " TeamCalendarApp - shouldApplyAssignmentToDay ne devrait pas appliquer assignment pour mauvais jour de semaine", "status": "passed", "title": "ne devrait pas appliquer assignment pour mauvais jour de semaine", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "TeamCalendarApp - shouldApplyAssignmentToDay"], "fullName": " TeamCalendarApp - shouldApplyAssignmentToDay ne devrait pas appliquer assignment avant startDate", "status": "passed", "title": "ne devrait pas appliquer assignment avant startDate", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "TeamCalendarApp - shouldApplyAssignmentToDay"], "fullName": " TeamCalendarApp - shouldApplyAssignmentToDay ne devrait pas appliquer assignment après endDate", "status": "passed", "title": "ne devrait pas appliquer assignment après endDate", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "TeamCalendarApp - shouldApplyAssignmentToDay"], "fullName": " TeamCalendarApp - shouldApplyAssignmentToDay devrait appliquer assignment sans dayOfWeek spécifique", "status": "passed", "title": "devrait appliquer assignment sans dayOfWeek spécifique", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "TeamCalendarApp - shouldApplyAssignmentToDay"], "fullName": " TeamCalendarApp - shouldApplyAssignmentToDay ne devrait pas appliquer assignment null", "status": "passed", "title": "ne devrait pas appliquer assignment null", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "TeamCalendarApp - shouldApplyAssignmentToDay"], "fullName": " TeamCalendarApp - shouldApplyAssignmentToDay ne devrait pas appliquer avec dayOfWeek invalide", "status": "passed", "title": "ne devrait pas appliquer avec dayOfWeek invalide", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "TeamCalendarApp - shouldApplyAssignmentToDay"], "fullName": " TeamCalendarApp - shouldApplyAssignmentToDay devrait appliquer assignment sans dates de restriction", "status": "passed", "title": "devrait appliquer assignment sans dates de restriction", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "TeamCalendarApp - Render <PERSON>"], "fullName": " TeamCalendarApp - Render Debounce devrait debouncer les appels multiples à render", "status": "passed", "title": "devrait debouncer les appels multiples à render", "duration": 2, "failureMessages": []}, {"ancestorTitles": ["", "TeamCalendarApp - Render <PERSON>"], "fullName": " TeamCalendarApp - Render Debounce devrait annuler le timer précédent lors de nouveaux appels", "status": "passed", "title": "devrait annuler le timer précédent lors de nouveaux appels", "duration": 0, "failureMessages": []}], "startTime": 1751909729119, "endTime": 1751909729125, "status": "passed", "message": "", "name": "C:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/__tests__/teamCalendarApp.test.js"}]}