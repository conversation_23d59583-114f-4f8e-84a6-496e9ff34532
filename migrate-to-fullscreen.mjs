#!/usr/bin/env node

/**
 * Migration vers l'interface fullscreen
 * Remplace l'ancienne interface par la nouvelle version fullscreen
 */

import { promises as fs } from 'fs';
import path from 'path';

console.log('🚀 [MIGRATION] Migration vers l\'interface fullscreen...\n');

async function migrateToFullscreen() {
  try {
    // 1. Sauvegarder l'ancien Agenda.tsx
    console.log('📋 [ÉTAPE 1] Sauvegarde de l\'ancien Agenda.tsx...');
    try {
      const oldContent = await fs.readFile('src/Agenda.tsx', 'utf8');
      await fs.writeFile('src/Agenda-Old-Backup.tsx', oldContent);
      console.log('✅ Sauvegarde créée : src/Agenda-Old-Backup.tsx');
    } catch (error) {
      console.log('⚠️  Aucun ancien fichier à sauvegarder');
    }

    // 2. Remplacer par la version fullscreen
    console.log('\n🔄 [ÉTAPE 2] Remplacement par la version fullscreen...');
    const fullscreenContent = await fs.readFile('src/Agenda-Fullscreen.tsx', 'utf8');
    await fs.writeFile('src/Agenda.tsx', fullscreenContent);
    console.log('✅ Agenda.tsx remplacé par la version fullscreen');

    // 3. Mettre à jour App.tsx si nécessaire
    console.log('\n🔧 [ÉTAPE 3] Vérification d\'App.tsx...');
    try {
      const appContent = await fs.readFile('src/App.tsx', 'utf8');
      if (appContent.includes('AgendaFullscreen')) {
        const updatedAppContent = appContent.replace('AgendaFullscreen', 'Agenda');
        await fs.writeFile('src/App.tsx', updatedAppContent);
        console.log('✅ App.tsx mis à jour');
      } else {
        console.log('✅ App.tsx déjà à jour');
      }
    } catch (error) {
      console.log('⚠️  Impossible de mettre à jour App.tsx:', error.message);
    }

    // 4. Nettoyer les fichiers temporaires de diagnostic
    console.log('\n🧹 [ÉTAPE 4] Nettoyage des fichiers de diagnostic...');
    const filesToClean = [
      'debug-regular-assignments.mjs',
      'public/test-settings-button.js',
      'public/test-grips-detection.js'
    ];

    let cleanedFiles = 0;
    for (const file of filesToClean) {
      try {
        await fs.access(file);
        await fs.unlink(file);
        cleanedFiles++;
        console.log(`🗑️  Supprimé : ${file}`);
      } catch (error) {
        // Fichier n'existe pas, pas de problème
      }
    }
    console.log(`✅ ${cleanedFiles} fichiers de diagnostic supprimés`);

    // 5. Mettre à jour index.html pour retirer les scripts de test
    console.log('\n🔧 [ÉTAPE 5] Nettoyage d\'index.html...');
    try {
      let htmlContent = await fs.readFile('index.html', 'utf8');
      
      // Supprimer les scripts de test
      const scriptsToRemove = [
        'test-settings-button.js',
        'test-grips-detection.js',
        'debug-drag-drop.js',
        'debug-fork-logic.js'
      ];
      
      let htmlUpdated = false;
      for (const script of scriptsToRemove) {
        const scriptRegex = new RegExp(`\\s*<script[^>]*${script}[^>]*></script>`, 'g');
        if (htmlContent.match(scriptRegex)) {
          htmlContent = htmlContent.replace(scriptRegex, '');
          htmlUpdated = true;
          console.log(`🗑️  Script retiré d'index.html : ${script}`);
        }
      }
      
      if (htmlUpdated) {
        await fs.writeFile('index.html', htmlContent);
        console.log('✅ index.html nettoyé');
      } else {
        console.log('✅ index.html déjà propre');
      }
    } catch (error) {
      console.log('⚠️  Impossible de nettoyer index.html:', error.message);
    }

    // 6. Vérifier les dépendances CSS
    console.log('\n🎨 [ÉTAPE 6] Vérification des styles...');
    try {
      await fs.access('src/styles/fullscreen.css');
      console.log('✅ Styles fullscreen disponibles');
    } catch (error) {
      console.log('❌ ERREUR : Styles fullscreen manquants !');
      throw new Error('Les styles fullscreen sont requis');
    }

    // 7. Afficher les caractéristiques de la nouvelle interface
    console.log('\n🎉 [MIGRATION TERMINÉE AVEC SUCCÈS] 🎉');
    console.log('\n📋 CARACTÉRISTIQUES DE LA NOUVELLE INTERFACE :');
    console.log('   🖥️  Interface fullscreen adaptative');
    console.log('   🚫 Aucun scrollbar visible');
    console.log('   📱 Responsive sur toutes les résolutions');
    console.log('   🎛️  Sidebar rétractable fixe');
    console.log('   📊 Segments compartimentés');
    console.log('   ⚡ Performances optimisées');
    console.log('   🎨 Design moderne et cohérent');
    
    console.log('\n🚀 PROCHAINES ÉTAPES :');
    console.log('   1. Redémarrer le serveur de développement');
    console.log('   2. Tester sur différentes résolutions');
    console.log('   3. Vérifier le bon fonctionnement du menu paramètres');
    
    console.log('\n💡 ROLLBACK SI BESOIN :');
    console.log('   cp src/Agenda-Old-Backup.tsx src/Agenda.tsx');

  } catch (error) {
    console.error('\n❌ [ERREUR] Migration échouée:', error.message);
    console.error('\n🔄 ROLLBACK AUTOMATIQUE...');
    
    try {
      await fs.access('src/Agenda-Old-Backup.tsx');
      const backupContent = await fs.readFile('src/Agenda-Old-Backup.tsx', 'utf8');
      await fs.writeFile('src/Agenda.tsx', backupContent);
      console.log('✅ Rollback effectué avec succès');
    } catch (rollbackError) {
      console.error('❌ Rollback impossible:', rollbackError.message);
    }
    
    process.exit(1);
  }
}

migrateToFullscreen(); 