-- Migration 009: Création de la table logs pour le système de diagnostic
-- Date: 2025-01-30

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE IF NOT EXISTS logs (
    id           BIGSERIAL PRIMARY KEY,
    session_id   UUID      NOT NULL,
    source       TEXT      NOT NULL,          -- frontend, backend, browser
    level        TEXT      NOT NULL,          -- trace|debug|info|warn|error|fatal
    message      TEXT      NOT NULL,
    data         JSONB     DEFAULT '{}'::jsonb, -- payload libre
    ts           TIMESTAMPTZ DEFAULT now(),
    capture_mode TEXT      DEFAULT 'full'     -- full | ai | …
);

CREATE INDEX IF NOT EXISTS logs_session_idx ON logs(session_id);
CREATE INDEX IF NOT EXISTS logs_level_idx ON logs(level);
CREATE INDEX IF NOT EXISTS logs_ts_idx ON logs(ts);
CREATE INDEX IF NOT EXISTS logs_source_idx ON logs(source);

-- Fonction de scoring ai_ranked_logs pour l'intelligence artificielle
CREATE OR REPLACE FUNCTION ai_ranked_logs(
        p_session  UUID,
        p_mode     TEXT DEFAULT 'ai'
) RETURNS TABLE(
        id     BIGINT,
        ts     TIMESTAMPTZ,
        level  TEXT,
        source TEXT,
        message TEXT,
        score  INT
) AS $$
WITH base AS (
   SELECT *,
          CASE level                                   -- priorités
            WHEN 'fatal' THEN 100
            WHEN 'error' THEN 80
            WHEN 'warn'  THEN 60
            WHEN 'info'  THEN 30
            ELSE 10
          END                              AS lvl_score,
          ROW_NUMBER() OVER (PARTITION BY message ORDER BY id)
                                           AS dup_rank
   FROM   logs
   WHERE  session_id   = p_session
   AND    capture_mode = p_mode
)
SELECT id, ts, level, source, message,
       lvl_score - GREATEST(dup_rank-1,0)*5 AS score
FROM   base
ORDER  BY score DESC, id DESC;
$$ LANGUAGE sql STABLE;

-- Fonction pour nettoyer les logs anciens (optionnel)
CREATE OR REPLACE FUNCTION cleanup_old_logs(days_to_keep INTEGER DEFAULT 7)
RETURNS VOID AS $$
BEGIN
    DELETE FROM logs WHERE ts < NOW() - INTERVAL '1 day' * days_to_keep;
END;
$$ LANGUAGE plpgsql; 