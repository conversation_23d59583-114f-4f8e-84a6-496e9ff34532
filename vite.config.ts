import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],

  // ✅ CONFIGURATION CDN ET OPTIMISATIONS
  build: {
    // Code splitting optimisé
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks pour cache optimal
          'react-vendor': ['react', 'react-dom'],
          'query-vendor': ['@tanstack/react-query'],
          'ui-vendor': ['framer-motion', 'lucide-react'],
          'utils-vendor': ['sortablejs', 'uuid']
        },
        // Noms de fichiers avec hash pour cache busting
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    },

    // Optimisations de build
    target: 'es2020',
    minify: 'terser',

    // Taille des chunks
    chunkSizeWarningLimit: 1000,

    // Assets inline threshold
    assetsInlineLimit: 4096 // 4KB
  },

  // ✅ CONFIGURATION CDN
  define: {
    __CDN_ENABLED__: JSON.stringify(process.env.NODE_ENV === 'production'),
    __CDN_BASE_URL__: JSON.stringify(process.env.VITE_CDN_URL || ''),
  },

  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false
      }
    },

    // ✅ OPTIMISATIONS DEV
    warmup: {
      clientFiles: [
        './src/main.tsx',
        './src/App.tsx',
        './src/teamCalendarApp.ts'
      ]
    }
  },

  // ✅ OPTIMISATION DES DÉPENDANCES
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@tanstack/react-query',
      'framer-motion',
      'lucide-react'
    ]
  }
})
