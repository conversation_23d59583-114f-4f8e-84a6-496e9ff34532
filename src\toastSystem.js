// 🎯 SYSTÈME DE TOASTS AVANCÉ
// Remplace les popups par des notifications élégantes

class ToastSystem {
    constructor() {
        this.toasts = [];
        this.container = null;
        this.init();
    }

    init() {
        // C<PERSON>er le conteneur de toasts s'il n'existe pas
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'toast-container';
            this.container.className = 'fixed top-4 right-4 z-[10000] space-y-3 max-w-md';
            document.body.appendChild(this.container);
        }
    }

    // Types de toasts disponibles
    show(message, type = 'info', options = {}) {
        const defaultOptions = {
            duration: type === 'error' ? 8000 : 5000, // Erreurs restent plus longtemps
            closable: true,
            persistent: false,
            onConfirm: null,
            onCancel: null,
            confirmText: 'OK',
            cancelText: 'Annuler'
        };

        const config = { ...defaultOptions, ...options };
        const toast = this.createToast(message, type, config);
        
        this.toasts.push(toast);
        this.container.appendChild(toast.element);

        // Animation d'entrée
        setTimeout(() => toast.element.classList.add('toast-show'), 10);

        // Auto-suppression (sauf si persistant)
        if (!config.persistent && config.duration > 0) {
            setTimeout(() => this.removeToast(toast.id), config.duration);
        }

        return toast.id;
    }

    createToast(message, type, config) {
        const id = 'toast-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        
        const toast = document.createElement('div');
        toast.id = id;
        toast.className = `toast-item transform translate-x-full transition-all duration-300 ease-in-out`;
        
        // Styles selon le type
        const typeStyles = {
            info: 'bg-blue-50 border-blue-200 text-blue-800',
            success: 'bg-green-50 border-green-200 text-green-800',
            warning: 'bg-amber-50 border-amber-200 text-amber-800',
            error: 'bg-red-50 border-red-200 text-red-800',
            conflict: 'bg-purple-50 border-purple-200 text-purple-800'
        };

        const iconMap = {
            info: '🔍',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            conflict: '🚫'
        };

        toast.innerHTML = `
            <div class="flex items-start p-4 border-l-4 rounded-lg shadow-lg backdrop-blur-sm ${typeStyles[type] || typeStyles.info}">
                <div class="flex-shrink-0 text-2xl mr-3">
                    ${iconMap[type] || iconMap.info}
                </div>
                <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium leading-relaxed">
                        ${message.replace(/\n/g, '<br>')}
                    </div>
                    ${this.createActionButtons(config, id)}
                </div>
                ${config.closable ? `
                    <button onclick="window.toastSystem.removeToast('${id}')" 
                            class="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600 transition-colors">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                ` : ''}
            </div>
        `;

        return { id, element: toast, config };
    }

    createActionButtons(config, toastId) {
        if (!config.onConfirm && !config.onCancel) return '';

        let buttons = '<div class="mt-3 flex gap-2">';
        
        if (config.onConfirm) {
            buttons += `
                <button onclick="window.toastSystem.handleConfirm('${toastId}')" 
                        class="px-3 py-1.5 text-xs font-medium bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                    ${config.confirmText}
                </button>
            `;
        }
        
        if (config.onCancel) {
            buttons += `
                <button onclick="window.toastSystem.handleCancel('${toastId}')" 
                        class="px-3 py-1.5 text-xs font-medium bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors">
                    ${config.cancelText}
                </button>
            `;
        }
        
        buttons += '</div>';
        return buttons;
    }

    handleConfirm(toastId) {
        const toast = this.toasts.find(t => t.id === toastId);
        if (toast && toast.config.onConfirm) {
            toast.config.onConfirm();
        }
        this.removeToast(toastId);
    }

    handleCancel(toastId) {
        const toast = this.toasts.find(t => t.id === toastId);
        if (toast && toast.config.onCancel) {
            toast.config.onCancel();
        }
        this.removeToast(toastId);
    }

    removeToast(toastId) {
        const toast = this.toasts.find(t => t.id === toastId);
        if (!toast) return;

        // Animation de sortie
        toast.element.classList.remove('toast-show');
        toast.element.classList.add('translate-x-full');

        setTimeout(() => {
            if (toast.element.parentNode) {
                toast.element.parentNode.removeChild(toast.element);
            }
            this.toasts = this.toasts.filter(t => t.id !== toastId);
        }, 300);
    }

    // Méthodes de raccourci
    success(message, options = {}) {
        return this.show(message, 'success', options);
    }

    error(message, options = {}) {
        return this.show(message, 'error', options);
    }

    warning(message, options = {}) {
        return this.show(message, 'warning', options);
    }

    info(message, options = {}) {
        return this.show(message, 'info', options);
    }

    conflict(message, options = {}) {
        return this.show(message, 'conflict', {
            duration: 8000, // Conflits restent plus longtemps
            ...options
        });
    }

    // Toast pour confirmation avec Promise
    confirm(message, options = {}) {
        return new Promise((resolve) => {
            this.show(message, 'warning', {
                persistent: true,
                onConfirm: () => resolve(true),
                onCancel: () => resolve(false),
                confirmText: options.confirmText || 'Confirmer',
                cancelText: options.cancelText || 'Annuler',
                ...options
            });
        });
    }

    // Nettoyer tous les toasts
    clear() {
        this.toasts.forEach(toast => this.removeToast(toast.id));
    }
}

// Ajouter les styles CSS nécessaires
const style = document.createElement('style');
style.textContent = `
    .toast-show {
        transform: translateX(0) !important;
    }
    
    .toast-item {
        max-width: 400px;
        word-wrap: break-word;
    }
    
    @media (max-width: 640px) {
        #toast-container {
            left: 1rem;
            right: 1rem;
            top: 1rem;
            max-width: none;
        }
        
        .toast-item {
            max-width: none;
        }
    }
`;
document.head.appendChild(style);

// Instance globale
window.toastSystem = new ToastSystem();

export default window.toastSystem; 