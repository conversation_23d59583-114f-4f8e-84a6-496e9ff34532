/**
 * SCRIPT DE RÉPARATION D'URGENCE
 * Ce script est conçu pour être exécuté depuis la console du navigateur
 * afin de corriger les problèmes d'initialisation et de persistance sans
 * avoir à modifier directement le code source complexe.
 */

(function() {
    console.log("🚀 [EMERGENCY-FIX] Démarrage du script de réparation d'urgence...");

    // === Point 1 & 2 : Verrou d'initialisation et nettoyage SortableJS ===
    function fixInitialization() {
        console.log("🔧 [FIX-1] Tentative de correction de l'initialisation...");
        if (!window.TeamCalendarApp) {
            console.error("❌ [FIX-1] TeamCalendarApp non trouvé. Le script ne peut pas continuer.");
            return;
        }

        const app = window.TeamCalendarApp;

        // Destruction propre des instances SortableJS
        if (app.employeeSortable && typeof app.employeeSortable.destroy === 'function') {
            app.employeeSortable.destroy();
            app.employeeSortable = null;
            console.log("✅ [FIX-2] Instance SortableJS des employés détruite.");
        }
        
        // Forcer la ré-initialisation des listeners de drag & drop
        try {
            app.setupEmployeeDragDrop();
            console.log("✅ [FIX-2] Listeners de drag & drop des employés ré-initialisés.");
        } catch(e) {
            console.error("❌ [FIX-2] Erreur lors de la ré-initialisation de setupEmployeeDragDrop", e);
        }


        // Ré-appliquer le verrou global pour les futurs chargements
        window.__TEAM_CALENDAR_READY__ = true;
        console.log("✅ [FIX-1] Verrou d'initialisation global positionné.");
    }


    // === Point 3 : Vérification et création de safeWrite (côté frontend, pour la simulation) ===
    // Note : La correction réelle est côté serveur, ceci est un placeholder.
    function checkSafeWrite() {
        console.log("🔧 [FIX-3] Vérification de la logique SSE (simulation côté client)...");
        console.log("✅ [FIX-3] Le correctif pour `safeWrite` doit être appliqué sur le serveur. Ce script ne peut que valider la connectivité.");
    }


    // === Point 4 : Validation de la requête SQL des postes standards ===
    async function checkPostsQuery() {
        console.log("🔧 [FIX-4] Validation de la requête des postes...");
        if (!window.TeamCalendarApp || !window.TeamCalendarApp.config.standardPosts) {
            console.error("❌ [FIX-4] Impossible de valider : `standardPosts` non trouvés.");
            return;
        }

        const posts = window.TeamCalendarApp.config.standardPosts;
        const postWithWorkingDays = posts.find(p => p.workingDays && Array.isArray(p.workingDays));

        if (postWithWorkingDays) {
            console.log("✅ [FIX-4] Succès : Au moins un poste a la propriété `workingDays`.", postWithWorkingDays);
            if(window.toastSystem) window.toastSystem.success("Vérification SQL réussie!");
        } else {
            console.error("❌ [FIX-4] Échec : Aucun poste ne contient de `workingDays`. La requête SQL est probablement incorrecte.");
             if(window.toastSystem) window.toastSystem.error("Échec de la vérification SQL. `working_days` manquants.");
        }
    }

    // === Exécution des correctifs ===
    try {
        fixInitialization();
        checkSafeWrite();
        checkPostsQuery();
        console.log("🎉 [EMERGENCY-FIX] Script de réparation terminé. Veuillez rafraîchir l'application (CTRL+R).");
        if(window.toastSystem) window.toastSystem.info("Correctif d'urgence appliqué. Veuillez rafraîchir la page.", { duration: 10000 });

    } catch (error) {
        console.error("💥 [EMERGENCY-FIX] Une erreur critique est survenue durant la réparation:", error);
    }

})(); 