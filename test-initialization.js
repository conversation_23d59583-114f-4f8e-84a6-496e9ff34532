/**
 * Script de test pour valider l'initialisation correcte de l'application
 * À exécuter dans la console du navigateur
 */

console.log('🧪 [TEST-INIT] Démarrage des tests d\'initialisation...');

// Test 1: Vérifier que TeamCalendarApp existe
function testTeamCalendarAppExists() {
    console.log('🔍 [TEST-INIT] Test 1: Existence de TeamCalendarApp');
    
    if (typeof window.TeamCalendarApp !== 'undefined') {
        console.log('✅ [TEST-INIT] TeamCalendarApp existe');
        return true;
    } else {
        console.error('❌ [TEST-INIT] TeamCalendarApp n\'existe pas');
        return false;
    }
}

// Test 2: Vérifier les fonctions critiques
function testCriticalFunctions() {
    console.log('🔍 [TEST-INIT] Test 2: Fonctions critiques');
    
    const criticalFunctions = [
        'init',
        'generateUUID',
        'closeSettingsModal',
        'cleanupDuplicateRegularShifts',
        'getEmployeeName'
    ];
    
    let allFunctionsExist = true;
    
    criticalFunctions.forEach(funcName => {
        if (typeof window.TeamCalendarApp[funcName] === 'function') {
            console.log(`✅ [TEST-INIT] ${funcName} existe`);
        } else {
            console.error(`❌ [TEST-INIT] ${funcName} manquante`);
            allFunctionsExist = false;
        }
    });
    
    return allFunctionsExist;
}

// Test 3: Vérifier l'initialisation
function testInitialization() {
    console.log('🔍 [TEST-INIT] Test 3: Initialisation');
    
    try {
        // Vérifier si l'application est déjà initialisée
        if (window.TeamCalendarApp.config && window.TeamCalendarApp.config._isInitialized) {
            console.log('✅ [TEST-INIT] Application déjà initialisée');
            return true;
        } else {
            console.log('⚠️ [TEST-INIT] Application non encore initialisée');
            return false;
        }
    } catch (error) {
        console.error('❌ [TEST-INIT] Erreur lors du test d\'initialisation:', error);
        return false;
    }
}

// Test 4: Vérifier les éléments DOM
function testDOMElements() {
    console.log('🔍 [TEST-INIT] Test 4: Éléments DOM');
    
    const requiredElements = [
        'employee-list-container',
        'schedule-grid-content',
        'current-week-display',
        'schedule-container'
    ];
    
    let allElementsExist = true;
    
    requiredElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            console.log(`✅ [TEST-INIT] Élément ${elementId} trouvé`);
        } else {
            console.error(`❌ [TEST-INIT] Élément ${elementId} manquant`);
            allElementsExist = false;
        }
    });
    
    return allElementsExist;
}

// Test 5: Vérifier modalFunctionalities
function testModalFunctionalities() {
    console.log('🔍 [TEST-INIT] Test 5: modalFunctionalities');
    
    if (typeof window.modalFunctionalities !== 'undefined') {
        console.log('✅ [TEST-INIT] modalFunctionalities existe');
        
        if (typeof window.modalFunctionalities.openSettingsModal === 'function') {
            console.log('✅ [TEST-INIT] openSettingsModal disponible');
            return true;
        } else {
            console.error('❌ [TEST-INIT] openSettingsModal manquante');
            return false;
        }
    } else {
        console.error('❌ [TEST-INIT] modalFunctionalities n\'existe pas');
        return false;
    }
}

// Test 6: Test de génération UUID
function testUUIDGeneration() {
    console.log('🔍 [TEST-INIT] Test 6: Génération UUID');
    
    try {
        if (window.TeamCalendarApp && typeof window.TeamCalendarApp.generateUUID === 'function') {
            const uuid1 = window.TeamCalendarApp.generateUUID();
            const uuid2 = window.TeamCalendarApp.generateUUID();
            
            if (uuid1 && uuid2 && uuid1 !== uuid2 && uuid1.length === 36) {
                console.log('✅ [TEST-INIT] Génération UUID fonctionne:', uuid1);
                return true;
            } else {
                console.error('❌ [TEST-INIT] Génération UUID défaillante');
                return false;
            }
        } else {
            console.error('❌ [TEST-INIT] generateUUID non disponible');
            return false;
        }
    } catch (error) {
        console.error('❌ [TEST-INIT] Erreur lors du test UUID:', error);
        return false;
    }
}

// Fonction principale de test
function runAllTests() {
    console.log('🚀 [TEST-INIT] Lancement de tous les tests...');
    
    const tests = [
        { name: 'TeamCalendarApp existe', func: testTeamCalendarAppExists },
        { name: 'Fonctions critiques', func: testCriticalFunctions },
        { name: 'Initialisation', func: testInitialization },
        { name: 'Éléments DOM', func: testDOMElements },
        { name: 'modalFunctionalities', func: testModalFunctionalities },
        { name: 'Génération UUID', func: testUUIDGeneration }
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    tests.forEach(test => {
        console.log(`\n🧪 [TEST-INIT] Exécution: ${test.name}`);
        if (test.func()) {
            passedTests++;
        }
    });
    
    console.log(`\n📊 [TEST-INIT] Résultats: ${passedTests}/${totalTests} tests réussis`);
    
    if (passedTests === totalTests) {
        console.log('🎉 [TEST-INIT] Tous les tests sont passés ! Application prête.');
        return true;
    } else {
        console.error('⚠️ [TEST-INIT] Certains tests ont échoué. Vérifiez les erreurs ci-dessus.');
        return false;
    }
}

// Auto-exécution si dans le navigateur
if (typeof window !== 'undefined') {
    // Attendre que l'application soit chargée
    setTimeout(() => {
        runAllTests();
    }, 2000);
}

// Export pour utilisation manuelle
window.testInitialization = {
    runAllTests,
    testTeamCalendarAppExists,
    testCriticalFunctions,
    testInitialization,
    testDOMElements,
    testModalFunctionalities,
    testUUIDGeneration
};

console.log('✅ [TEST-INIT] Script de test chargé. Utilisez runAllTests() pour tester manuellement.');
