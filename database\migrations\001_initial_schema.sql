-- =====================================================
-- MIGRATION 001: SCHEMA INITIAL TEAM CALENDAR APP
-- Date: 2025-06-11
-- Description: Création des tables principales pour 
--              l'application de gestion des plannings
-- =====================================================

-- Activer l'extension UUID si pas déjà fait
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. TABLE DES MODÈLES DE FICHES EMPLOYÉS
-- =====================================================
CREATE TABLE IF NOT EXISTS employee_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    fields JSONB NOT NULL DEFAULT '[]',
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour la recherche par nom
CREATE INDEX IF NOT EXISTS idx_employee_templates_name ON employee_templates(name);
CREATE INDEX IF NOT EXISTS idx_employee_templates_default ON employee_templates(is_default);

-- =====================================================
-- 2. TABLE DES EMPLOYÉS
-- =====================================================
CREATE TABLE IF NOT EXISTS employees (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    status VARCHAR(100) NOT NULL DEFAULT 'Temps Plein',
    avatar TEXT,
    template_id UUID REFERENCES employee_templates(id) ON DELETE SET NULL,
    extra_fields JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour la recherche par nom et statut
CREATE INDEX IF NOT EXISTS idx_employees_name ON employees(name);
CREATE INDEX IF NOT EXISTS idx_employees_status ON employees(status);
CREATE INDEX IF NOT EXISTS idx_employees_template ON employees(template_id);

-- =====================================================
-- 3. TABLE DES POSTES STANDARDS
-- =====================================================
CREATE TABLE IF NOT EXISTS standard_posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    label VARCHAR(255) NOT NULL,
    hours VARCHAR(50) NOT NULL, -- Format "08:00-16:00"
    duration INTEGER NOT NULL, -- Durée en heures
    type VARCHAR(50) NOT NULL, -- Couleur/catégorie (sky, amber, etc.)
    category VARCHAR(100), -- night, weekend, etc.
    is_custom BOOLEAN DEFAULT FALSE, -- Distinguer les postes personnalisés
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour la recherche par type et catégorie
CREATE INDEX IF NOT EXISTS idx_standard_posts_type ON standard_posts(type);
CREATE INDEX IF NOT EXISTS idx_standard_posts_category ON standard_posts(category);

-- =====================================================
-- 4. TABLE DES QUARTS/SHIFTS
-- =====================================================
CREATE TABLE IF NOT EXISTS shifts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    post_id UUID REFERENCES standard_posts(id) ON DELETE SET NULL,
    date_key DATE NOT NULL, -- Format YYYY-MM-DD
    shift_data JSONB NOT NULL, -- Données complètes du shift (ShiftData)
    is_regular BOOLEAN DEFAULT FALSE, -- Attribution régulière
    is_punctual BOOLEAN DEFAULT FALSE, -- Attribution ponctuelle
    assignment_id UUID, -- Référence à l'attribution régulière
    week_offset INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les recherches fréquentes
CREATE INDEX IF NOT EXISTS idx_shifts_employee_date ON shifts(employee_id, date_key);
CREATE INDEX IF NOT EXISTS idx_shifts_date ON shifts(date_key);
CREATE INDEX IF NOT EXISTS idx_shifts_post ON shifts(post_id);
CREATE INDEX IF NOT EXISTS idx_shifts_assignment ON shifts(assignment_id);

-- =====================================================
-- 5. TABLE DES ATTRIBUTIONS RÉGULIÈRES
-- =====================================================
CREATE TABLE IF NOT EXISTS regular_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    post_id UUID NOT NULL REFERENCES standard_posts(id) ON DELETE CASCADE,
    is_limited BOOLEAN DEFAULT FALSE,
    start_date DATE NOT NULL,
    end_date DATE, -- NULL si illimité
    selected_days INTEGER[] DEFAULT '{}', -- Jours de la semaine (0-6)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les recherches d'attributions
CREATE INDEX IF NOT EXISTS idx_regular_assignments_employee ON regular_assignments(employee_id);
CREATE INDEX IF NOT EXISTS idx_regular_assignments_post ON regular_assignments(post_id);
CREATE INDEX IF NOT EXISTS idx_regular_assignments_dates ON regular_assignments(start_date, end_date);

-- =====================================================
-- 6. TABLE DES PÉRIODES DE VACANCES INDIVIDUELLES
-- =====================================================
CREATE TABLE IF NOT EXISTS vacation_periods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'CP', -- CP, RTT, Maladie, etc.
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les recherches de vacances
CREATE INDEX IF NOT EXISTS idx_vacation_periods_employee ON vacation_periods(employee_id);
CREATE INDEX IF NOT EXISTS idx_vacation_periods_dates ON vacation_periods(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_vacation_periods_type ON vacation_periods(type);

-- =====================================================
-- 7. TABLE DES VACANCES GLOBALES
-- =====================================================
CREATE TABLE IF NOT EXISTS global_vacations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les recherches de vacances globales
CREATE INDEX IF NOT EXISTS idx_global_vacations_dates ON global_vacations(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_global_vacations_name ON global_vacations(name);

-- =====================================================
-- 8. TABLE DES PARAMÈTRES DE L'APPLICATION
-- =====================================================
CREATE TABLE IF NOT EXISTS app_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    setting_key VARCHAR(255) UNIQUE NOT NULL,
    setting_value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour les recherches de paramètres
CREATE UNIQUE INDEX IF NOT EXISTS idx_app_settings_key ON app_settings(setting_key);

-- =====================================================
-- 9. FONCTION DE MISE À JOUR AUTOMATIQUE DES TIMESTAMPS
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 10. TRIGGERS POUR LA MISE À JOUR AUTOMATIQUE
-- =====================================================
CREATE OR REPLACE TRIGGER update_employee_templates_updated_at BEFORE UPDATE ON employee_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE OR REPLACE TRIGGER update_employees_updated_at BEFORE UPDATE ON employees FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE OR REPLACE TRIGGER update_standard_posts_updated_at BEFORE UPDATE ON standard_posts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE OR REPLACE TRIGGER update_shifts_updated_at BEFORE UPDATE ON shifts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE OR REPLACE TRIGGER update_regular_assignments_updated_at BEFORE UPDATE ON regular_assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE OR REPLACE TRIGGER update_vacation_periods_updated_at BEFORE UPDATE ON vacation_periods FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE OR REPLACE TRIGGER update_global_vacations_updated_at BEFORE UPDATE ON global_vacations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE OR REPLACE TRIGGER update_app_settings_updated_at BEFORE UPDATE ON app_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 11. COMMENTAIRES SUR LES TABLES
-- =====================================================
COMMENT ON TABLE employee_templates IS 'Modèles de fiches employés avec champs personnalisables';
COMMENT ON TABLE employees IS 'Informations des employés avec référence au modèle de fiche';
COMMENT ON TABLE standard_posts IS 'Postes de travail standards et personnalisés';
COMMENT ON TABLE shifts IS 'Quarts de travail assignés aux employés par date';
COMMENT ON TABLE regular_assignments IS 'Attributions automatiques récurrentes';
COMMENT ON TABLE vacation_periods IS 'Périodes de congés individuels des employés';
COMMENT ON TABLE global_vacations IS 'Jours fériés et congés collectifs';
COMMENT ON TABLE app_settings IS 'Paramètres de configuration de l''application';

-- =====================================================
-- FIN DE LA MIGRATION 001
-- =====================================================
