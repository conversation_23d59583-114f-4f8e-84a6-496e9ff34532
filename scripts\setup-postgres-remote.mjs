#!/usr/bin/env node

/**
 * Script d'aide pour configurer PostgreSQL distant
 * Usage: node scripts/setup-postgres-remote.mjs
 */

import { writeFileSync, existsSync } from 'fs';
import { createInterface } from 'readline';

const rl = createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function setupRemotePostgres() {
  console.log('🔧 [Setup] Configuration PostgreSQL distant');
  console.log('='.repeat(50));
  
  try {
    const config = {};
    
    console.log('\n📡 [Connexion] Informations du serveur PostgreSQL:');
    
    config.DB_HOST = await question('🔗 Adresse du serveur (ex: myserver.com): ');
    if (!config.DB_HOST) {
      console.log('❌ Adresse serveur requise');
      process.exit(1);
    }
    
    config.DB_PORT = await question('📍 Port (défaut 5432): ') || '5432';
    config.DB_NAME = await question('🗄️  Nom de la base (défaut team_calendar): ') || 'team_calendar';
    config.DB_USER = await question('👤 Nom d\'utilisateur: ');
    
    if (!config.DB_USER) {
      console.log('❌ Nom d\'utilisateur requis');
      process.exit(1);
    }
    
    config.DB_PASSWORD = await question('🔐 Mot de passe: ');
    
    if (!config.DB_PASSWORD) {
      console.log('❌ Mot de passe requis');
      process.exit(1);
    }
    
    const sslChoice = await question('🔒 SSL requis ? (y/N): ');
    config.DB_SSL = sslChoice.toLowerCase().startsWith('y') ? 'true' : 'false';
    
    console.log('\n📄 [Fichier] Création du fichier .env...');
    
    const envContent = `# Configuration PostgreSQL distant - Généré le ${new Date().toISOString()}
DB_HOST=${config.DB_HOST}
DB_PORT=${config.DB_PORT}
DB_NAME=${config.DB_NAME}
DB_USER=${config.DB_USER}
DB_PASSWORD=${config.DB_PASSWORD}
DB_SSL=${config.DB_SSL}

# Pour charger ces variables:
# - Windows: Get-Content .env | ForEach-Object { if ($_ -match '^([^#].*)=(.*)$') { [Environment]::SetEnvironmentVariable($matches[1], $matches[2], 'Process') } }
# - Linux/Mac: source .env
# - Ou utilisez un package comme dotenv
`;

    // Sauvegarder le fichier .env
    if (existsSync('.env')) {
      const backup = await question('⚠️  Fichier .env existe. Sauvegarder ? (Y/n): ');
      if (!backup.toLowerCase().startsWith('n')) {
        writeFileSync('.env.backup', envContent);
        console.log('💾 Sauvegarde créée: .env.backup');
      }
    }
    
    writeFileSync('.env', envContent);
    console.log('✅ Fichier .env créé avec succès');
    
    console.log('\n🧪 [Test] Test de la connexion...');
    
    // Tester la connexion
    await testConnection(config);
    
    console.log('\n🎉 [Succès] Configuration PostgreSQL distant terminée!');
    console.log('\n📋 [Prochaines étapes]:');
    console.log('1. Chargez les variables d\'environnement');
    console.log('2. Exécutez: node migrate-logs-remote.mjs');
    console.log('3. Redémarrez votre serveur: npm run dev:system');
    console.log('4. Accédez à: http://localhost:5173/logs');
    
  } catch (error) {
    console.error('❌ [Erreur] Configuration échouée:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

async function testConnection(config) {
  try {
    const { default: pg } = await import('pg');
    const { Client } = pg;
    
    const client = new Client({
      host: config.DB_HOST,
      port: parseInt(config.DB_PORT),
      database: config.DB_NAME,
      user: config.DB_USER,
      password: config.DB_PASSWORD,
      ssl: config.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
      connectionTimeoutMillis: 5000
    });
    
    await client.connect();
    await client.query('SELECT version()');
    await client.end();
    
    console.log('✅ [Test] Connexion PostgreSQL réussie!');
    
  } catch (error) {
    console.log('⚠️  [Test] Connexion échouée:', error.message);
    console.log('💡 [Info] Vérifiez les paramètres et réessayez');
  }
}

// Auto-exécution
setupRemotePostgres().catch(console.error); 